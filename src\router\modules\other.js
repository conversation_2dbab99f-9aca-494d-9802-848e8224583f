export default [
	// 数据修复
	{
		path: '/data-repair',
		name: 'data-repair',
		component: (/* webpackChunkName: 'data-repair' */) => import('@/views/data-repair/index'),
	},

	// 测试页
	{
		path: '/test-video',
		name: 'test-video',
		component: (/* webpackChunkName: 'test-video' */) => import('@/views/test-page/video'),
	},
	{
		path: '/signin-record',
		name: 'signin-record',
		component: (/* webpackChunkName: 'signin-record' */) => import('@/views/record/LoginRecord'),
	},
	{
		path: '/operate-record',
		name: 'operate-record',
		component: (/* webpackChunkName: 'operate-record' */) => import('@/views/record/OperateRecord'),
	},
	{
		path: '/legend-setting',
		name: 'legend-setting',
		component: (/* webpackChunkName: 'legend-setting' */) => import('@/views/legend-setting/index'),
	},
	// 工作指南分类
	{
		path: '/guide-classify',
		name: 'guide-classify',
		component: (/* webpackChunkName: 'guide-classify' */) => import('@/views/job-guide/guide-classify/index'),
	},
	{
		path: '/guide-manage',
		name: 'guide-manage',
		component: (/* webpackChunkName: 'guide-manage' */) => import('@/views/job-guide/guide-manage/index'),
	},
]
