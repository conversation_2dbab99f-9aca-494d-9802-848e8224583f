<!--
 * @Description: 权限管理
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-03-14 16:32:31
 * @LastEditors: houyan
 * @LastEditTime: 2024-08-21 13:44:22
-->
<template lang="pug">
WaterRow
	.role-list
		.role(
			v-for='(item, index) in roleList',
			:key='index',
			:class='{ active: activeIndex === index }',
			@click='handleChangeRole(index)'
		) {{ item.name }}
	.type-list
		Button.save-button(type='primary', @click='handleSave') 保存
		.type-container
			Tree(ref='treeRef', :data='typeList', show-checkbox)
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import { queryRoles, getGuideTypeList, setRoleGuideConfig } from '@/api/other'
export default {
	components: {
		WaterRow,
	},
	data() {
		return {
			activeIndex: 0,
			roleList: [],
			guideIds: [],
			typeList: [],
			treeOriginData: [],
		}
	},
	methods: {
		getTreeData(data) {
			return data.map(item => {
				const { name, id, guideInfos, checked = false } = item

				let obj = {
					title: name,
					id,
					expand: true,
					checked,
				}

				// 递归处理子节点
				if (guideInfos && guideInfos.length > 0) {
					const newGuideInfos = guideInfos.map(guide => {
						const { title, id } = guide
						return {
							name: title,
							id,
							checked: this.guideIds.includes(id),
						}
					})
					obj.children = this.getTreeData(newGuideInfos)
				}

				return obj
			})
		},
		_queryRoles() {
			queryRoles().then(res => {
				const { result } = res

				if (result && result.length) {
					this.roleList = result
					this.guideIds = this.roleList[this.activeIndex].guideIds
				}
				this._getGuideTypeList()
			})
		},
		_getGuideTypeList() {
			getGuideTypeList({
				includeGuideInfo: true,
			}).then(res => {
				const { result } = res

				if (result && result.length) {
					this.treeOriginData = this.$common.deepCopy(result)
					this.typeList = this.getTreeData(result).filter(item => item.children && item.children.length)
				}
			})
		},
		async _saveOrUpdateRoleGuide() {
			const guideInfos = this.$refs.treeRef.getCheckedNodes()
			const guides = guideInfos
				.filter(item => !item.children)
				.map(item => {
					return {
						id: item.id,
						title: item.title,
					}
				})

			const params = {
				id: this.roleList[this.activeIndex].id,
				guideInfos: guides,
			}
			await setRoleGuideConfig(params)
			this.$Message.success('保存成功')
			this._queryRoles()
		},
		handleChangeRole(index) {
			this.activeIndex = index
			this.guideIds = this.roleList[this.activeIndex].guideIds
			this.typeList = this.getTreeData(this.treeOriginData).filter(item => item.children && item.children.length)
		},
		handleSave() {
			this._saveOrUpdateRoleGuide()
		},
	},
	mounted() {
		this._queryRoles()
	},
}
</script>
<style lang="less" scoped>
.water-layout-flex {
	height: 100%;
}
.role-list {
	margin-right: 10px;
	padding: 10px;
	width: 240px;
	height: 100%;
	overflow: auto;
	border: 1px solid #e4e7ed;
	.role {
		padding: 4px 10px;
		cursor: pointer;
		font-size: 14px;
	}
	.active {
		background-color: #ecf5ff;
	}
}
.type-list {
	display: flex;
	flex-direction: column;
	flex: 1;
	padding: 10px;
	height: 100%;
	overflow: auto;
	border: 1px solid #e4e7ed;
	.save-button {
		margin-left: auto;
		margin-bottom: 10px;
	}
	.type-container {
		flex: 1;
		overflow: auto;
	}
}
</style>
