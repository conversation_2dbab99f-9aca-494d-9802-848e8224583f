/*
 * @Description:
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2021-07-28 10:15:49
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2025-01-03 09:27:17
 */
import { POST, GET } from '@/utils/request'

// 2d工艺新增/编辑
export function saveOrUpdate2dProcess(params, requestType = 'json') {
	return POST({
		url: '/waterPlat/2dProcess/saveOrUpdate',
		params,
		requestType,
	})
}
// 2d工艺流程图数据点编辑
export function edit2dProcessConfig(params, requestType = 'json') {
	return POST({
		url: '/waterPlat/2dProcessConfig/edit',
		params,
		requestType,
	})
}
//站点列表
export function getStationList(params) {
	return GET({
		url: '/waterPlat/station/list',
		params,
	})
}
//2d工艺流程图数据点列表
export function get2dProcessConfigList(params, requestType = 'json') {
	return GET({
		url: '/waterPlat/2dProcessConfig/list',
		params,
		requestType,
	})
}

//2d工艺流程图数据点列表
export function getEquipmentList(params, requestType = 'json') {
	return GET({
		url: '/waterPlat/station/equipment/list',
		params,
		requestType,
	})
}

//2d工艺流程图数据点列表
export function getEquipmentTypeList(params, requestType = 'json') {
	return POST({
		// url: '/screen/monitorDataState/monitorType/list',
		url: '/waterPlat/2dProcessConfig/monitorDataState/monitorType/list',
		params,
		requestType,
	})
}
// 站点工艺路程图列表
export function get2dProcessList(params) {
	return GET({
		url: '/waterPlat/2dProcess/list',
		params,
	})
}
// 3d工艺流程图列表
export function get3dList(params) {
	return GET({
		url: '/screen/3d/list',
		params,
	})
}
// 3d工艺流程图列表
export function addDiagram(params, requestType = 'json') {
	return POST({
		url: '/waterPlat/show/process/flow/diagram/add',
		params,
		requestType,
	})
}
// 上传
export function upload(params, config) {
	return POST({
		url: '/screen/common/oss/batch/upload',
		params,
		config,
	})
}
export function labelSiteSaveModel(params, config) {
	return POST({
		url: '/waterPlat/label/site/saveModel',
		params,
		config,
	})
}
// 删除点
export function deletePoint(params) {
	return POST({
		url: '/waterPlat/2dProcessConfig/delete',
		params,
	})
}

//上传
export function save3dData(params, requestType = 'json') {
	return POST({
		url: '/screen/3d/save',
		params,
		requestType,
	})
}
export function getTemplateList(params, requestType = 'json') {
	return GET({
		url: '/waterPlat/technology/template/list',
		params,
		requestType,
	})
}
export function deleteLabel(params) {
	return POST({
		url: '/waterPlat/label/site/label/delete',
		params,
	})
}
export function getTechnologySiteList(params, requestType = 'json') {
	return GET({
		url: '/waterPlat/technology/site/list',
		params,
		requestType,
	})
}
export function getLabelSiteList(params, requestType = 'json') {
	return GET({
		url: '/waterPlat/label/site/list',
		params,
		requestType,
	})
}
export function getLabelPublicList(params, requestType = 'json') {
	return GET({
		url: '/waterPlat/label/public/list',
		params,
		requestType,
	})
}
export function screen3DbatchSave(params, requestType = 'json') {
	return POST({
		url: '/screen/3d/batch/save',
		params,
		requestType,
	})
}
export function getSearchForIdList(params) {
	return GET({
		url: '/waterPlat/stationInfo/searchForId',
		params,
	})
}
export function getTemplateForName(params) {
	return GET({
		url: '/waterPlat/technology/template/searchForName',
		params,
	})
}
export function getSiteSave(params) {
	return POST({
		url: '/waterPlat/technology/site/save',
		params,
	})
}
export function deleteSite(params) {
	return POST({
		url: '/waterPlat/technology/site/delete',
		params,
	})
}
export function deleteModel(params) {
	return POST({
		url: '/waterPlat/diagram/delete',
		params,
	})
}
export function getSiteLabelList(params) {
	return GET({
		url: '/waterPlat/label/site/label/list',
		params,
	})
}
export function getModelEquipmentList(params) {
	return GET({
		url: '/waterPlat/model/equipment/type',
		params,
	})
}
export function getStationOtherDiagram(params) {
	return GET({
		url: '/waterPlat/station/other/diagram',
		params,
	})
}
export function labelSave(params) {
	return POST({
		url: '/waterPlat/label/site/label/save',
		params,
	})
}
export function getProcessFlowDiagramList(params) {
	return GET({
		url: '/waterPlat/show/process/flow/diagram/list',
		params,
	})
}
export function getTemplateModels(params) {
	return GET({
		url: '/waterPlat/technology/template/getModel',
		params,
	})
}
export function associatedList(params) {
	return GET({
		// url: '/iwater/technology/template/relationList',
		url: '/waterPlat/technology/site/list',
		params,
	})
}
// export function associate(params) {
// 	return POST({
// 		// url: '/iwater/technology/template/relationList',
// 		url: '/waterPlat/technology/site/saveList',
// 		params,
// 	})
// }
export function notAssociatedList(params) {
	return GET({
		// url: '/iwater/technology/template/relationList',
		url: '/waterPlat/technology/template/relationList',
		params,
	})
}
export function templateRelation(params, requestType = 'json') {
	return POST({
		// url: '/iwater/technology/template/relation',
		url: '/waterPlat/technology/site/saveList',
		params,
		requestType,
	})
}
export function modelExtraAttribute(params) {
	return POST({
		url: '/iwater/diagram/model/extraAttribute',
		params,
	})
}
export function diagramEdit(params) {
	return POST({
		url: '/waterPlat/show/process/flow/diagram/edit',
		params,
	})
}
export function queryStationInfoByType(params) {
	return POST({
		url: '/screen/common/queryStationInfoByType',
		params,
	})
}

// 删除
export function delete2dProcess(params) {
	return POST({
		url: '/waterPlat/2dProcess/delete',
		params,
	})
}

// 设备最新数据列表
export function queryMonitorDataStateList(params) {
	return POST({
		url: '/pos/equipment/monitorDataState/list',
		params,
	})
}
// 更新name
export function updateMonitorDataState(params) {
	return GET({
		url: '/pos/monitorDataState/update',
		params,
	})
}
export function groupList(params) {
	return GET({
		url: '/waterPlat/process/point/group',
		params,
	})
}
export function groupUpdate(params) {
	return POST({
		url: '/waterPlat/process/point/group/update',
		params,
	})
}

// 查询站点工艺列表
export function queryStationProcessList(params) {
	return POST({
		url: '/iwater/station/process/queryStationProcessList',
		params,
	})
}

// 新增或修改站点工艺
export function insertOrUpdateMtStationProcess(params) {
	return POST({
		url: '/iwater/station/process/insertOrUpdateMtStationProcess',
		params,
	})
}

// 批量新增工艺站点流程
export function saveBatchMtStationProcess(params) {
	return POST({
		url: '/iwater/station/process/saveBatchMtStationProcess',
		params,
	})
}

// 删除某条站点下某种工艺
export function deleteMtStationProcess(params) {
	return POST({
		url: '/iwater/station/process/deleteMtStationProcess',
		params,
	})
}
// 查询站点工艺列表
export function saveOrUpdateMtStationProcessConfig(params) {
	return POST({
		url: '/iwater/station/processconfig/saveOrUpdateMtStationProcessConfig',
		params,
	})
}
// 查询站点工艺列表
export function deleteMtStationProcessConfig(params) {
	return POST({
		url: '/iwater/station/processconfig/deleteMtStationProcessConfig',
		params,
	})
}
// 查询站点工艺配置数据项列表
export function queryStationProcessConfigList(params) {
	return POST({
		url: '/iwater/station/processconfig/queryStationProcessConfigList',
		params,
	})
}
// 模型数据多位置标签配置
export function savePositionLabel(params, requestType = 'json') {
	return POST({
		url: '/waterPlat/label/site/positionLabel/save',
		params,
		requestType,
	})
}
// 保存多个点视频
export function savePositionVideo(params, requestType = 'json') {
	return POST({
		url: '/waterPlat/label/site/positionVideo/save',
		params,
		requestType,
	})
}
export function deletPositionLabel(params) {
	return POST({
		url: '/waterPlat/label/site/positionLabel/delete',
		params,
	})
}
// 模型数据多位置标签配置
export function getPositionLabel(params) {
	return GET({
		url: '/waterPlat/label/site/positionLabel/list',
		params,
	})
}
//查询模型绑定的视频
export function getPositionVideo(params) {
	return GET({
		url: '/waterPlat/label/site/positionVideo/list',
		params,
	})
}
// 站点绑定模型是否禁用
export function setSiteEnable(params) {
	const { id, enable } = params
	return GET({
		url: `/waterPlat/technology/site/enable/${id}/${enable}`,
	})
}

// 删除模型
export function deleteModels(params) {
	return GET({
		url: `/waterPlat/models/delete`,
		params,
	})
}
// 更新模型
export function saveModels(params) {
	return POST({
		url: `/waterPlat/model/save`,
		params,
		requestType: 'json',
	})
}
// 删除模型
export function deleteModelData(params) {
	return POST({
		url: '/waterPlat/model/delete',
		params,
	})
}

// 推送分页查询
export function queryMqttNotifyConfig(params) {
	return POST({
		url: '/waterPlat/mqttNotifyConfig/page',
		params,
		requestType: 'json',
	})
}
export function addMqttNotifyConfig(params) {
	return POST({
		url: '/waterPlat/mqttNotifyConfig/add',
		params,
		requestType: 'json',
	})
}
export function updateMqttNotifyConfig(params) {
	return POST({
		url: '/waterPlat/mqttNotifyConfig/update',
		params,
		requestType: 'json',
	})
}
// 获取短信配置
export function smsConfigInfo(params) {
	return GET({
		url: `/waterPlat/smsConfig/info`,
		params,
	})
}
// 编辑短信配置

export function insertOrUpdate(params) {
	return POST({
		url: '/waterPlat/smsConfig/insertOrUpdate',
		params,
		requestType: 'json',
	})
}
