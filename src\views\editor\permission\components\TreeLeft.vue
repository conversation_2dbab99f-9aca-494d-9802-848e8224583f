<template lang="pug">
.fill
	.left-title.flex_b
		.title 角色列表
		Icon(type='ios-create-outline', color='blue', size='18')
	Tree.tree(:data='baseData')
</template>

<script>
export default {
	name: 'limitManage',
	components: {},
	props: {},
	data() {
		return {
			baseData: [
				{
					expand: true,
					title: 'parent 1',
				},
				{
					expand: true,
					title: 'parent 1',
				},
				{
					expand: true,
					title: 'parent 1',
				},
				{
					expand: true,
					title: 'parent 1',
				},
			],
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {},
}
</script>

<style lang="less" scoped>
.fill {
	width: 100%;
	height: 100%;
	background: #fff;
	display: flex;
	flex-direction: column;
}
.left-title {
	padding: 0 16px;
	height: 32px;
	border-bottom: 1px solid #f5f5ff;

	.title {
		position: relative;
		&::before {
			position: absolute;
			content: '';
			background: blue;
			width: 4px;
			height: 20px;
			left: -5px;
		}
	}
}

.tree {
	flex: 1;
}
.flex_b {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
</style>
