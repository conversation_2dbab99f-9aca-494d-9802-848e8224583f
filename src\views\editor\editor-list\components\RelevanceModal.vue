<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-09-14 09:02:39
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-09-14 14:24:54
-->
<template lang="pug">
.add-tag-modal
	water-row.water-margin-top-8.water-margin-right-4(justify='flex-end', align='center')
		Icon(type='md-close', size='24', style='cursor: pointer', @click='handleClose')
	tagTree(@check-change='checkChange', :height='360', ref='tagTree', :multiple='false')
	.type-select.water-margin-top-8
		.label 应用端：
		Select.water-margin-left-4(v-model='type', placeholder='请选择应用端')
			Option(value='pc') PC
			Option(value='app') APP
			Option(value='app/pc') APP/PC
	water-row.footer(justify='space-between', align='center')
		div 已经勾选({{ selectedArr.length }})
		Button(type='primary', :disabled='selectedArr.length === 0', @click='relevData') 关联
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import { stationList, bind } from '@/api/editor'
import tagTree from '../../components/TagTree.vue'
export default {
	name: '',
	components: { tagTree, WaterRow },
	props: {
		bindId: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			selectedArr: [],
			// 应用端
			type: 'pc',
		}
	},
	mounted() {
		this.initData()
	},
	methods: {
		// 初始化
		initData() {
			this.stationList()
		},

		// 站点
		stationList() {
			stationList({ showItemCode: false }).then(res => {
				this.$nextTick(() => {
					this.$refs.tagTree.setTreeData(res.result, this.code, false)
				})
			})
		},

		// 关联数据
		relevData() {
			const arr = this.selectedArr.map(({ stationCode, stationName, sysCode }) => {
				return {
					bindId: this.bindId,
					stationCode,
					stationName,
					sysCode,
					type: this.type,
				}
			})
			bind(arr).then(() => {
				this.$Message.info('关联成功！')
				this.handleClose()
				this.$emit('bindSuccess')
			})
		},
		// 关闭弹框
		handleClose() {
			this.selectedArr = []
			this.$emit('close')
		},

		// 选中
		checkChange({ selectedArr }) {
			this.selectedArr = [...selectedArr]
		},
	},
}
</script>
<style lang="less" scoped>
.add-tag-modal {
	position: fixed;
	left: 50%;
	top: 160px;
	margin-left: -200px;
	width: 400px;
	// height: 440px;
	background-color: #fff;
	border: 1px solid #e8e8e8;
	z-index: 10;
	box-shadow: 0px 8px 24px -4px rgba(24, 39, 75, 0.08);
}
.type-select {
	display: flex;
	align-items: center;
	padding: 0 8px;
	.label {
		flex-shrink: 0;
	}
}
.footer {
	padding: 8px;
}
</style>
