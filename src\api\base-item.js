/*
 * @Description: 数据项配置
 * @Author: lrr
 */

import { GET, POST } from '@/utils/request'

// 获取特定方案信息
export function deleteByBatch(params) {
	return POST({
		url: '/waterPlat/baseDataItem/deleteByBatch',
		params,
		requestType: 'json',
	})
}

// 查询报警方案配置
export function queryPage(params) {
	return POST({
		url: '/waterPlat/baseDataItem/queryPage',
		params,
		requestType: 'json',
	})
}

// 保存更新报警方案
export function saveBaseDataItem(params) {
	return POST({
		url: '/waterPlat/baseDataItem/saveBaseDataItem',
		params,
		requestType: 'json',
	})
}
// 更新数据
export function updateBaseDataItem(params) {
	return POST({
		url: '/waterPlat/baseDataItem/updateBaseDataItem',
		params,
		requestType: 'json',
	})
}
//模板查詢(设备类型字典接口)
export function queryTemplateByName(params) {
	return GET({
		url: '/waterPlat/baseEquipmentDataItemTemplate/queryTemplateByName',
		params,
	})
}
//查詢模板详情
export function queryItemTemplateById(params) {
	return GET({
		url: '/waterPlat/baseEquipmentDataItemTemplate/queryItemTemplateById',
		params,
	})
}
export function deleteItemTemplateById(params) {
	return POST({
		url: '/waterPlat/baseEquipmentDataItemTemplate/deleteItemTemplateById',
		params,
	})
}
export function saveItemTemplateAndDatails(params) {
	return POST({
		url: '/waterPlat/baseEquipmentDataItemTemplate/saveItemTemplateAndDatails',
		params,
		requestType: 'json',
	})
}
export function updateItemTemplateAndDatails(params) {
	return POST({
		url: '/waterPlat/baseEquipmentDataItemTemplate/updateItemTemplateAndDatails',
		params,
		requestType: 'json',
	})
}
//查詢所有站点
export function queryStations(params) {
	return GET({
		url: '/waterPlat/common/queryStations',
		params,
	})
}
//分页查询所有站点
export function queryStationPage(params) {
	return POST({
		url: '/waterPlat/stationDataItem/queryStationPage',
		params,
		requestType: 'json',
	})
}

//查詢模板详情
export function getStationTemp(params) {
	return POST({
		url: '/waterPlat/stationDataItem/getStationDataItemByStationCode',
		params,
		requestType: 'json',
	})
}
//更换站点数据
export function updateStationData(params) {
	return POST({
		url: '/waterPlat/stationDataItem/updateStationData',
		params,
		requestType: 'json',
	})
}
//更换站点模板
export function updateStationTemp(params) {
	return POST({
		url: '/waterPlat/stationDataItem/updateStationTemp',
		params,
		requestType: 'json',
	})
}

//更换站点模板
export function relatedStationTemps(params) {
	return POST({
		url: '/waterPlat/stationDataItem/relatedStationTemps',
		params,
		requestType: 'json',
	})
}
export function deleteByStationCode(params) {
	return POST({
		url: '/waterPlat/stationDataItem/deleteByStationCode',
		params,
	})
}
export function deleteById(params) {
	return POST({
		url: '/waterPlat/stationDataItem/deleteById',
		params,
	})
}
export function saveStationData(params) {
	return POST({
		url: '/waterPlat/stationDataItem/saveStationData',
		params,
		requestType: 'json',
	})
}
export function updateStationDataItem(params) {
	return POST({
		url: '/waterPlat/stationDataItem/updateStationDataItem',
		params,
		requestType: 'json',
	})
}
export function deleteTemplateDetailById(params) {
	return POST({
		url: '/waterPlat/baseTemplateDetail/deleteTemplateDetailById',
		params,
	})
}
export function updateBaseTemplateDetail(params) {
	return POST({
		url: '/waterPlat/baseTemplateDetail/updateBaseTemplateDetail',
		params,
		requestType: 'json',
	})
}
export function saveBaseTemplateDetail(params) {
	return POST({
		url: '/waterPlat/baseTemplateDetail/saveBaseTemplateDetail',
		params,
		requestType: 'json',
	})
}

// 查询所有配置
export function getAllSysConfig(params) {
	return GET({
		url: '/sysConfig/getAll',
		params,
	})
}

// 新增配置
export function addSysConfig(params) {
	return POST({
		url: '/sysConfig/add',
		params,
	})
}
// 更改配置
export function updateSysConfig(params) {
	return POST({
		url: '/sysConfig/update',
		params,
	})
}

// 删除配置
export function deleteSysConfig(params) {
	return POST({
		url: '/sysConfig/delete/' + params.id,
	})
}
// 类别方案树
export function getSchemeTree(params) {
	return GET({
		url: '/waterPlat/scheme/tree',
		params,
	})
}
// 新增类别
export function saveCategory(params) {
	return POST({
		url: '/waterPlat/scheme/saveCategory',
		params,
	})
}
// 新增方案
export function saveScheme(params) {
	return POST({
		url: '/waterPlat/scheme/saveScheme',
		headers: { 'Content-type': 'application/json' },
		params,
	})
}
// 更新方案
export function updateScheme(params) {
	return POST({
		url: '/waterPlat/scheme/updateScheme',
		headers: { 'Content-type': 'application/json' },
		params,
	})
}
// 删除方案
export function delScheme(params) {
	return GET({
		url: '/waterPlat/scheme/delete',
		params,
	})
}
// h获取方案详情
export function getInfo(params) {
	return GET({
		url: '/waterPlat/scheme/getInfo',
		params,
	})
}
// 查询角色列表
export function apiGetRole(params) {
	return GET({
		url: '/waterPlat/common/role',
		params,
	})
}
