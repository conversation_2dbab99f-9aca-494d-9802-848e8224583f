<template lang="pug">
// 数据绑定
Modal(:value='show', :mask-closable='false', width='1150', @on-cancel='handleCancel', title='设置多个点标签')
	Form(ref='formDynamic', :model='formDynamic', :label-width='80', height='500')
		.formItem(v-for='(item, index) in formDynamic.items', :key='index')
			Form-item(
				:label='"名称"',
				:prop='"items." + index + ".name"',
				:rules=`{
						required: true,
						message: '名称'  + '不能为空',
						trigger: 'blur',
					}`
			)
				Input(type='text', v-model='item.name', placeholder='请输入...')
			Form-item(
				:label='"位置点"',
				:prop='"items." + index + ".position"',
				:rules=`{
						required: true,
						message: '位置点'  + '不能为空',
						trigger: 'blur',
					}`
			)
				Input(type='text', v-model='item.position', placeholder='请输入...')
			Form-item(
				:label-width='120',
				:label='"关联设备名称"',
				:prop='"items." + index + ".deviceId"',
				:rules=`{
						required: true,
						message: '关联设备名称'  + '不能为空',
						trigger: 'blur',
					}`
			)
				Select(v-model='item.deviceId', filterable, :transfer='true', @on-change='noFun($event, item)')
					Option(v-for='(option, index) in equipmentList', :value='option.equipmentUniqueRepresentation', :key='index') {{ option.equipmentUniqueRepresentation }}
			template(v-for='(content, cIndex) in item.contentList') 
				Form-item(
					:key='index + cIndex',
					:label-width='120',
					:label='"关联数据类型"',
					:prop='"items." + index + ".contentList." + cIndex + ".dataId"',
					:rules=`{
							required: true,
							message: '关联数据类型' + '不能为空',
							trigger: 'blur',
						}`
				)
					Select(v-model='content.dataId', filterable, :transfer='true')
						Option(v-for='(item, i) in item.typeList', :value='item.itemRealCode', :key='index + cIndex + i')
							| {{ item.itemName }}
			Button(@click='addSmallItem(index)', icon='md-add')
			Button(@click='handleRemove(index, item)')
				| 删除
		Form-item
			Button(style='width: 120px', type='dashed', long, @click='handleAdd', icon='plus-round') 新增
	div(slot='footer')
		Button(type='primary', @click='handleSubmit')
			| 保存
</template>

<script>
import {
	getEquipmentList,
	getEquipmentTypeList,
	savePositionLabel,
	deletPositionLabel,
	getPositionLabel,
} from '@/api/setting'

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '新增',
		},
	},
	computed: {
		stationListLength() {
			return this.pointList.length
		},
	},
	mounted() {
		// this.initPage()
	},
	data() {
		return {
			formDynamic: {
				items: [
					{
						name: '',
						position: '',
						deviceId: '',
						contentList: [{ dataId: '' }],
						id: '',
						typeList: [],
					},
				],
			},
			equipmentList: [],
		}
	},
	methods: {
		async init(row) {
			const { stationId, modelId, applicationName } = row
			this.stationId = stationId
			this.modelId = modelId
			this.applicationName = applicationName
			this.initPage()
			// 详情
			this.formDynamic.items = []
			const res = await this.getPositionLabel(modelId, stationId)
			let obj = {}
			for (let index = 0; index < res.result.length; index++) {
				const item = res.result[index]
				// debugger
				const result = await this.getEquipmentTypeList(item.equipmentCode)
				if (obj[item.position]) {
					obj[item.position].contentList.push({
						dataId: item.itemCode,
						id: item.id,
					})
				} else {
					obj[item.position] = {
						...item,
						id: item.id,
						typeList: result,
						contentList: [{ dataId: item.itemCode, id: item.id }],
						deviceId: item.equipmentCode,
					}
				}
			}
			for (const key in obj) {
				if (Object.hasOwnProperty.call(obj, key)) {
					const element = obj[key]
					this.formDynamic.items.push(element)
				}
			}
		},
		getPositionLabel(modelId, stationId) {
			return getPositionLabel({ modelId, stationId }).then(res => {
				return res
			})
		},
		getEquipmentTypeList(equipmentCode) {
			return getEquipmentTypeList({
				stationCodes: equipmentCode,
			}).then(type => {
				return type.result
			})
		},
		initPage() {
			getEquipmentList({
				stationCode: this.stationId,
				applicationName: this.applicationName,
			}).then(res => {
				this.loading = false
				this.equipmentList = res.result
			})
		},
		noFun(val, item) {
			getEquipmentTypeList({
				stationCodes: val,
			}).then(res => {
				item.typeList = res.result
			})
		},
		handleSubmit() {
			this.$refs.formDynamic.validate(valid => {
				if (valid) {
					let params = []
					this.formDynamic.items.forEach(item => {
						item.contentList.forEach(con => {
							let param = {
								name: item.name,
								position: item.position,
								stationCode: this.stationId,
								stationId: this.stationId,
								modelId: this.modelId,
								monitorTypeId: con.dataId,
								equipmentId: item.deviceId,
							}
							con.id && (param.id = con.id)
							params.push(param)
						})
					})
					savePositionLabel(params).then(() => {
						this.$emit('update:show', false)
						this.$Message.success('保存成功！')
					})
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
		handleReset() {
			this.$refs.formDynamic.resetFields()
		},
		handleAdd() {
			this.formDynamic.items.push({
				name: '',
				position: '',
				deviceId: '',
				contentList: [{ dataId: '' }],
				id: '',
				typeList: [],
			})
		},
		addSmallItem(index) {
			this.formDynamic.items[index].contentList.push({
				dataId: '',
			})
		},
		handleRemove(index, item) {
			const { id } = item
			if (id) {
				this.$Modal.confirm({
					title: '提示',
					content: '确定要删除这条数据?',
					loading: true,
					onOk: () => {
						this.$Modal.remove()
						deletPositionLabel({ id }).then(() => {
							this.formDynamic.items.splice(index, 1)
							this.$Message.info('删除成功')
						})
					},
				})
			} else {
				this.formDynamic.items.splice(index, 1)
			}
		},
		// 弹窗取消事件
		handleCancel() {
			this.handleReset()
			this.$emit('update:show', false)
		},
	},
}
</script>
<style lang="less" scoped>
.flex {
	display: flex;
	justify-content: space-around;
}
.formItem {
	display: flex;
}
::v-deep {
	.ivu-modal-body {
		height: 500px;
		overflow: auto;
	}
	.ivu-form-item {
		min-width: 240px;
	}
}
</style>
