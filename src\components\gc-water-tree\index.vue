<!--
 * @Description: 树形组件
 * @Author: shenxh
 * @Date: 2022-03-07 10:17:29
 * @LastEditors: shenxh
 * @LastEditTime: 2024-11-26 13:36:34
-->
<template lang="pug">
.water-tree
	//- :multiple="sysCode === 'dma' && stationType == 'AREA' && $route.name != 'special-use-alarm'",
	.water-tree-content
		es-huge-tree.es-huge-tree(
			ref='huge-tree',
			showCheckbox,
			:hasInput='true',
			checked-action='click',
			expand-level='all',
			placeholder='请输入站点',
			filter-key='fullTitle',
			:is-loading='isLoading',
			check-striclty,
			:default-checked-keys='checkedKeys',
			:multiple='false',
			@onClickCheckbox='onClickCheckbox',
			@onChange='onChange'
		)
			span(slot-scope='{ slotScope }')
				i(:class='_getIcon(slotScope.nodeType)')
				span(@click='handleNodeLabel(slotScope)') {{ slotScope.label }}
			i(slot='loading') 加载中...
</template>

<script>
import { querySysConfigTree } from '@/api/common.js'

let isCheckboxAll = false

export default {
	name: 'water-tree',
	components: {},
	model: {
		prop: 'value',
		event: 'set-value',
	},
	props: {
		value: Array,
		sysCode: {
			type: String,
			default: '',
		},
		stationType: {
			type: String,
			default: '',
		},
		sysList: Array,
		multiple: Boolean,
		search: Boolean,
	},
	data() {
		return {
			isChangeCheckbox: false,
			isLoading: false,
			checkedKeys: [],
			subCode: '',
		}
	},
	computed: {},
	watch: {
		value(val) {
			if (isCheckboxAll) {
				isCheckboxAll = false
				return
			}

			if (!this.isChangeCheckbox && val && val.length) {
				let arr = []

				val.forEach(item => {
					const uniqueCode = item.nodeType + '$_$' + (item.oldId || item.nodeId) + 'link' + item.link

					arr.push(uniqueCode)
				})
				this.checkedKeys = arr

				if (this.sysCode !== 'dma') {
					setTimeout(() => {
						this.$refs['huge-tree'].setCheckedKeys(arr)
					}, 500)
				} else {
					setTimeout(() => {
						if (this.stationType == 'STATION') {
							this.$refs['huge-tree'].setCheckedKeys(arr)
						} else {
							this.$refs['huge-tree'].setStricltyChecked(arr)
						}
					}, 500)
				}
			}
		},
		sysCode() {
			this._querySysConfigTree()
		},
	},
	created() {},
	mounted() {
		this._querySysConfigTree()
	},
	beforeDestroy() {},
	methods: {
		// 点击复选框时触发
		onClickCheckbox(node) {
			let currentArr = this.value && this.value.length > 0 ? [...this.value] : []
			let idx = null
			this.isChangeCheckbox = true
			if (this.multiple) {
				const hasNode = currentArr.some((item, index) => {
					const uniqueCode = item.nodeType + '$_$' + (item.oldId || item.nodeId) + 'link' + item.link
					if (uniqueCode === node.id) {
						idx = index
						return true
					} else {
						return false
					}
				})
				if (node.checked) {
					if (!hasNode) {
						currentArr.push(node)
					}
				} else {
					if (idx !== null) {
						currentArr.splice(idx, 1)
					}
					idx = null
				}
			} else {
				if (node.checked) {
					currentArr = [node]
					this.$refs['huge-tree'].clearChecked()
					this.$refs['huge-tree'].setCheckedKeys([node.id])
					node.checked = true
				} else {
					currentArr = []
					this.$refs['huge-tree'].clearChecked()
				}
			}
			if (this.$route.name == 'special-use-alarm') {
				this.$emit('set-value', currentArr)
			} else {
				this.$emit('set-value', this.$refs['huge-tree'].getCheckedNodes())
			}

			this.$emit('check-change', {
				selectedArr: currentArr,
				currentNode: node,
			})
			setTimeout(() => {
				this.isChangeCheckbox = false
			}, 200)
		},
		onChange({ checkedNodes }) {
			if (this.sysCode === 'dma') {
				if (this.stationType == 'AREA') {
					checkedNodes.forEach(node => {
						this.$set(node, 'indeterminate', false)
					})
				}
			}
		},

		// 获取icon
		_getIcon(type) {
			let icon = 'iconfont '

			switch (type) {
				case 'system': // 系统
					icon += 'icon-xitong'
					break
				case 'area': // 区域
					icon += 'icon-quyu'
					break
				case 'AREA': // 站点
					icon += 'icon-quyu'
					break
				case 'station': // 站点
					icon += 'icon-zhandian-01'
					break
				default:
					// 设备
					if (this.stationType === 'STATION' && type != 'AREA') {
						// 此处专门针对兼容DMA的站点配置时站点图标不一样
						icon = ''
					} else {
						icon += 'icon-shebei-01'
					}
			}

			return icon
		},

		// 查询系统【站点树】
		_querySysConfigTree() {
			this.isLoading = true
			const code = this.getRealCode(this.sysCode === '_all' ? this.sysList[0].code : this.sysCode)
			querySysConfigTree({
				// 此处showDevice专门针对兼容DMA的站点配置
				showDevice: this.stationType == 'STATION' ? true : '',
				sysCode: code || this.sysList[0].code,
				subSysCode: this.subCode || this.$route.query.subSysCode,
			})
				.then(res => {
					const data = res.result
					this.$refs['huge-tree'].setData(this._getTree([data]))
					this.isLoading = false
					this.$emit('loadFinish')
				})
				.catch(() => {
					this.isLoading = false
				})
		},

		// 获取树状结构
		_getTree(list, parentId = null) {
			if (!list || !list.length) return []

			let arr = []

			arr = list.map(item => {
				const uniqueCode = item.nodeType + '$_$' + item.id + 'link' + (item.parentId || '')

				const obj = {
					...item,
					label: item.title,
					fullTitle: item.fullTitle.trim(),
					id: uniqueCode,
					oldId: item.id,
					disabled:
						this.$route.query.type === 'read' ||
						!item.nodeCode ||
						(this.stationType == 'STATION' &&
							item.nodeType == 'AREA' &&
							this.$route.name == 'special-use-alarm'),
					hideCheckbox: !item.nodeCode,
					parentId,
					link: item.parentId || '',
					children: this._getTree(item.children, uniqueCode),
				}
				if (this.sysCode === 'dma') {
					obj.checked = false
					if (this.stationType == 'STATION') {
						obj.hideCheckbox = item.nodeType === 'AREA'
					}
				}
				return obj
			})

			return arr
		},
		getRealCode(sysCode) {
			if (!sysCode) {
				return ''
			}
			if (sysCode.includes('&&')) {
				this.subCode = sysCode.split('&&')[1]
				return sysCode.split('&&')[0]
			}
			return sysCode
		},

		// 判断A数组是否包含B数组的所有项
		isArrayContained(arrA, arrB) {
			return arrB.every(item => arrA.includes(item))
		},

		// 删除数组A中包含的数组B的所有项
		removeItemsFromArray(arrA, arrB) {
			return arrA.filter(item => !arrB.includes(item))
		},

		handleNodeLabel(data) {
			if (!this.multiple) return

			const { children = [] } = data || {}
			const filterChildren = children.filter(item => item.nodeCode && !item.disabled)

			if (!filterChildren.length) return

			const checkedNodes = this.$refs['huge-tree'].getCheckedNodes()
			const checkedIds = checkedNodes.map(item => item.id)
			const childrenIds = filterChildren.map(item => item.id)
			let ids = [...checkedIds, ...childrenIds]

			if (this.isArrayContained(checkedIds, childrenIds)) {
				ids = this.removeItemsFromArray(checkedIds, childrenIds)
			}

			isCheckboxAll = true
			this.$refs['huge-tree'].setCheckedKeys(ids)
			this.$emit('set-value', this.$refs['huge-tree'].getCheckedNodes())
		},
	},
}
</script>

<style lang="less" scoped>
.water-tree {
	display: flex;
	flex-direction: column;
	height: 100%;
	.water-tree-search {
		flex-shrink: 0;
		margin: 16px 0;
	}
	.water-tree-content {
		position: relative;
		flex-grow: 1;
		overflow: auto;
		/deep/ .es-huge-tree {
			border: none;
			.search-bar {
				padding: 0;
				margin-bottom: 10px;
			}
			.expand-node {
				&::before {
					font-size: 13px !important;
					color: #999 !important;
				}
			}
		}
	}
}
</style>
