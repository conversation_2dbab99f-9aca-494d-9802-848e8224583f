<!--
 * @Description: 水源地指标
 * @Version: 2.0
 * @Autor: z<PERSON>yi
 * @Date: 2022-05-31 11:00:24
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2022-12-12 15:11:11
-->
<template lang="pug">
.drug-fill 
	water-row(justify='flex-end', align='center')
		.product-report-record-form-title 日期:
		//- 温州填报水源地指标 日期改为区间
		DatePicker(
			v-if='isWenzhouRecord',
			:editable='false',
			v-model='rangeDate',
			format='yyyy-MM-dd',
			:clearable='false',
			type='daterange',
			style='width: 215px',
			@on-change='handleQuery()',
			:options='options',
			placement='bottom-end'
		)
		DatePicker(
			v-else,
			:editable='false',
			v-model='date',
			format='yyyy-MM-dd',
			:clearable='false',
			type='date',
			style='width: 215px',
			@on-change='handleQuery()',
			:options='options',
			placement='bottom-end'
		)
		Button.water-margin-left-16(v-if='state === "fill"', type='primary', @click='handleSave()', :loading='buttonLoading') 提交
		Button.water-margin-left-16(v-if='state === "record"', type='primary', @click='handleQuery()') 查询
		Button.water-margin-left-16(v-if='state === "record"', type='primary', @click='handleExport()') 导出
	#qrcodeDowm(style='display: none')
	WaterTable.drug-fill-table(border, :columns='columns', :data='tableData', :loading='loading')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { EventBus } from '@/utils/eventBus.js'
import { exportFile } from '@/utils/function.js'
import { queryWaterSourceIndex, UpdateWaterSource, getDict, getCapacityByElevation } from '@/api/water-screen-data.js'

import { getUserConfig } from '@/api/security-manage'
export default {
	components: { WaterTable, WaterRow },
	props: ['state'],
	data() {
		return {
			date: this.$moment(new Date()).format('YYYY-MM-DD'),
			rangeDate: [this.$moment(new Date()).format('YYYY-MM-DD'), this.$moment(new Date()).format('YYYY-MM-DD')], // 温州水源地指标填报记录日期
			buttonLoading: false,
			loading: false,
			tableData: [],
			columns: [],
			options: {
				disabledDate(date) {
					return date.getTime() > new Date().getTime()
				},
			},
			outletList: [],
			isWenZhou: false, // 温州环境
		}
	},
	computed: {
		isWenzhouRecord() {
			return this.isWenZhou && this.state === 'record'
		},
	},
	async mounted() {
		await this.getUserConfig()
		this.initPage()
		EventBus.$on('fresh-record', () => {
			this.state === 'record' && this.handleQuery()
		})
	},
	methods: {
		// 判断是温州还是乐清
		getUserConfig() {
			return new Promise((resolve, reject) => {
				getUserConfig({
					code: 'video.type',
				})
					.then(res => {
						const { result = [] } = res

						if (result.length) {
							this.isWenZhou = result[0].ownership === '2W01'
						}
						resolve(res)
					})
					.catch(error => {
						reject(error)
					})
			})
		},
		async initPage() {
			const res = await this.getDict()
			const { result = [] } = res
			this.outletList = result.map(item => {
				return {
					label: item.memo,
					value: item.val,
				}
			})
			this.handleQuery()
		},
		getDict() {
			return getDict({ code: 'water.source.outlet' })
		},
		handleQuery() {
			const params = this.isWenzhouRecord
				? {
						startDate: this.$moment(this.rangeDate[0]).format('YYYY-MM-DD'),
						endDate: this.$moment(this.rangeDate[1]).format('YYYY-MM-DD'),
				  }
				: {
						date: this.$moment(this.date).format('YYYY-MM-DD'),
				  }
			queryWaterSourceIndex(params)
				.then(res => {
					const { result = '' } = res
					const { columns = [], values = [] } = result
					this.getColumns(columns)
					this.tableData = values.map(item => {
						const {
							stationName = '',
							currentCapacity = '',
							maxCapacity = '',
							currentLevel = '',
							maxLevel = '',
							positionValue = '',
							useDays = '',
							waterOutlet = '',
							date = '',
						} = item
						return {
							stationName,
							currentCapacity,
							maxCapacity,
							currentLevel,
							maxLevel,
							positionValue,
							useDays,
							editable: this.state === 'fill',
							waterOutlet: `${waterOutlet}`,
							date,
						}
					})
				})
				.catch(() => {
					const defaultColums = [
						{
							title: '名称',
							key: 'stationName',
							align: 'center',
							minWidth: 160,
						},
					]
					this.getColumns(defaultColums)
					this.tableData = []
				})
		},
		handleSave() {
			try {
				this.buttonLoading = true
				const list = []
				this.tableData.forEach(item => {
					const {
						stationName = '',
						currentCapacity = '',
						maxCapacity = '',
						currentLevel = '',
						maxLevel = '',
						positionValue = '',
						useDays = '',
						waterOutlet = '',
					} = item
					list.push({
						stationName,
						currentCapacity: currentCapacity !== '' ? Number(currentCapacity).toFixed(2) : currentCapacity,
						maxCapacity: maxCapacity !== '' ? Number(maxCapacity).toFixed(2) : maxCapacity,
						currentLevel: currentLevel !== '' ? Number(currentLevel).toFixed(2) : currentLevel,
						maxLevel: maxLevel !== '' ? Number(maxLevel).toFixed(2) : maxLevel,
						positionValue,
						useDays,
						waterOutlet,
					})
				})
				const params = {
					date: this.$moment(this.date).format('YYYY-MM-DD'),
					list,
				}
				UpdateWaterSource(params)
					.then(() => {
						this.$Message.success('提交成功!')
						this.buttonLoading = false
						this.handleQuery()
						EventBus.$emit('fresh-record')
					})
					.catch(() => {
						this.buttonLoading = false
					})
			} catch {
				this.buttonLoading = false
			}
		},
		//输入值
		async handleInputValue(index, value, key) {
			this.tableData[index][key] = value
			// 温州泽雅水库 实际库容量根据输入的实时水位变化
			// TOFIX: 泽雅水库判断只有stationName
			console.log(Boolean(value))
			if (key === 'currentLevel' && this.tableData[index].stationName === '泽雅水库' && value) {
				const res = await this.getCapacityByElevationApi({
					stationName: '泽雅水库',
					elevation: value,
				})
				if (res.result !== undefined) {
					this.tableData[index]['currentCapacity'] = res.result
				}
			}
		},
		//正整数-正则
		valueChange(e, key) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			// ‘际库容量’字段不限制位数
			if (key !== 'currentCapacity') {
				e.target.value = e.target.value.replace(/^(([0-9]{5}\d))(.\d{1,2})?$/, '')
			}
		},
		//正则
		valueMathChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// // 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d{0}).*$/, '$1$2')
			e.target.value = e.target.value.replace(/^(([1-9]{6}\d))?$/, '')
		},
		handleExport() {
			const baseUrl = `${location.origin}/api`
			let url = this.isWenzhouRecord
				? baseUrl +
				  '/waterPlat/fillData/queryWaterSourceIndexExport?startDate=' +
				  this.$moment(this.rangeDate[0]).format('YYYY-MM-DD') +
				  '&endDate=' +
				  this.$moment(this.rangeDate[1]).format('YYYY-MM-DD')
				: baseUrl +
				  '/waterPlat/fillData/queryWaterSourceIndexExport?date=' +
				  this.$moment(this.date).format('YYYY-MM-DD')
			exportFile(url)
		},
		getColumns(list = []) {
			const columns = []
			list.map(item => {
				let obj = {}
				if (item.column !== 'stationName' && item.column !== 'waterOutlet') {
					obj = {
						title: item.columnName,
						key: item.column,
						align: 'center',
						minWidth: 160,
						render: (h, params) => {
							const key = item.column
							const { editable } = params.row
							return editable
								? h('Input', {
										props: {
											value: params.row[key],
											maxlength: 20,
											autofocus: true,
										},
										nativeOn: {
											input: e => {
												params.row[key] = e.target.value
											},
										},
										on: {
											'on-change': e => {
												key === 'useDays' ? this.valueMathChange(e) : this.valueChange(e, key)
												const value = e.target.value
												this.handleInputValue(params.index, value, key)
											},
											'on-keyup': e => {
												key === 'useDays' ? this.valueMathChange(e) : this.valueChange(e, key)
											},
										},
								  })
								: h('span', params.row[key])
						},
					}
				}
				if (item.column === 'waterOutlet') {
					obj = {
						title: item.columnName,
						key: item.column,
						align: 'center',
						minWidth: 160,
						render: (h, params) => {
							const key = item.column
							const { editable } = params.row
							return h(
								'Select',
								{
									props: {
										value: params.row[key],
										'label-in-value': true,
										clearable: true,
										disabled: !editable,
										placeholder: !editable ? '' : '请选择',
										placement: this.tableData.length > 9 ? 'top' : 'bottom',
									},
									on: {
										'on-change': e => {
											const value = e.value
											e && this.handleInputValue(params.index, value, key)
										},
									},
								},
								this.outletList.map(item => {
									return h('Option', {
										props: {
											value: item.value,
											label: item.label,
										},
									})
								}),
							)
						},
					}
				}
				if (item.column == 'stationName') {
					obj = {
						title: item.columnName,
						key: item.column,
						width: 120,
						align: 'center',
					}
				}
				columns.push(obj)
			})
			this.columns = columns
			!this.isWenzhouRecord && this.columns.splice(columns.length - 1, 1)
		},
		// 接口泽雅水库实时库容
		getCapacityByElevationApi(params) {
			return new Promise((resolve, reject) => {
				getCapacityByElevation(params)
					.then(res => {
						resolve(res)
					})
					.catch(error => {
						reject(error)
					})
			})
		},
	},
}
</script>
<style lang="less" scoped>
.drug-fill {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-title {
		margin-right: 4px;
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
	::v-deep {
		.ivu-select-disabled .ivu-select-selection {
			background-color: transparent;
			cursor: auto;
			color: #535568;
			border: none;
		}
		.ivu-select-disabled .ivu-icon-ios-arrow-down {
			display: none;
		}
	}
}
</style>
