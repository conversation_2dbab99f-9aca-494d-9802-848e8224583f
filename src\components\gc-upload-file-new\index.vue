<!--
 * @Description: 图片上传
 * @Author: shenxh
 * @Date: 2024-03-12 11:25:29
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2024-08-14 15:25:45
-->

<template>
	<div>
		<Upload
			:multiple="needMultiple"
			ref="upload"
			:show-upload-list="true"
			:default-file-list="uploadList"
			:before-upload="handleBeforeUpload"
			:type="type"
			:disabled="disabled"
			action=""
			style="display: inline-block"
			:on-remove="handleRemove"
		>
			<div class="upload-container">
				<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
				<p>拖动到此区域上传</p>
			</div>
		</Upload>
		<div class="tip">
			<Icon type="ios-information-circle-outline" />
			<span>支持{{ format.join('、') }}等，最大不超过{{ maxSize }}M</span>
		</div>
	</div>
</template>

<script>
export default {
	name: 'upload-file-new',
	components: {},
	model: {
		prop: 'value',
		event: 'set-value',
	},
	props: {
		value: Array,
		format: {
			type: Array,
			default: () => ['xlsx', 'pdf', 'doc', 'docx'],
		},
		disabled: Boolean,

		maxSize: {
			type: Number,
			default: 10, // Mb
		},
		type: {
			type: String,
			default: 'select',
		},
		needMultiple: {
			type: Boolean,
			default: false,
		},
		limitNum: {
			type: Number,
			default: 1,
		},
	},
	data() {
		return {
			uploadList: [],
		}
	},
	computed: {},
	watch: {
		value: {
			handler(val) {
				this.uploadList = val || []
			},
			deep: true,
			immediate: true,
		},
	},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		handleBeforeUpload(file) {
			if (this.uploadList.length >= this.limitNum) {
				this.$Message.warning(`最多上传${this.limitNum}个文件`)
				return false
			}
			if (file.size / 1024 > this.maxSize * 1024) {
				this.$Message.warning('文件大小超过限制')
				return false
			}

			if (!this.format.includes(file.name.split('.')[1])) {
				this.$Message.warning('文件格式不正确')
				return false
			}

			const data = new FormData()

			data.append('files', file)

			this.$axios.post('/backgroundImage/image/upload', data).then((res = {}) => {
				const { imageUrl, name } = res.result[0] || {}

				this.uploadList.push({
					name,
					url: imageUrl,
				})
				this.$emit('set-value', this.uploadList)
			})

			return false
		},
		handleRemove(file, fileList) {
			this.uploadList = fileList
			this.$emit('set-value', this.uploadList)
		},
	},
}
</script>
<style lang="less" scoped>
.upload-container {
	width: 350px;
	height: 100%;
	border: 1px dashed #e1e2e3;
	display: flex;
	flex-direction: column;
	align-items: center;
}
</style>
