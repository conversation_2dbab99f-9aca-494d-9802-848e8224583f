<template>
	<div class="shellPadding h100 tableFont-size">
		<div class="h100 whiteBg">
			<Header>3D工艺流程图配置</Header>
			<div>
				<Tabs v-model="tabsName" @on-click="tabsFun">
					<TabPane label="工艺流程模版管理" name="modelTemplate"></TabPane>
					<TabPane label="站点工艺流程管理" name="siteProcess"></TabPane>
					<TabPane label="数据管网标签配置" name="siteProcessDataLabel"></TabPane>
					<TabPane label="已配数据标签" name="configuredDataLabel"></TabPane>
					<TabPane label="已配视频标签" name="configuredVideoLabel"></TabPane>
					<!-- <TabPane
						label="已配数据标签"
						name="pipelineDataManagement"
					></TabPane> -->
					<!-- <TabPane
						label="全局工艺流程数据标签管理"
						name="globalProcessDataLabel"
					></TabPane> -->
				</Tabs>
				<table-template ref="tableTemplateRef" @tableClick="handleTableClick"></table-template>

				<!-- <model-template
					v-if="tabsName === 'modelTemplate'"
					@tableClick="handleTableClick"
				></model-template> -->
			</div>
			<!-- 工艺流程图关联站点配置 -->
			<!-- @add-success="handleDealCurdEvent" -->
			<station-context ref="stationContextRef" :show.sync="stationContextShow"></station-context>

			<!-- 工艺预览 -->
			<modal-preview ref="previewRef" :show.sync="previewModalShow"></modal-preview>

			<!-- 新增 编辑站点工艺流程 -->
			<add-site-process
				:show.sync="addSiteProcessShow"
				ref="siteProcessref"
				@successCallBack="successCallBack"
			></add-site-process>

			<!-- 编辑模型 -->
			<editModel :show.sync="editModelShow" ref="editModelref" @successCallBack="successCallBack"></editModel>

			<!-- 编辑模型视角 -->
			<editView :show.sync="editViewlShow" ref="editViewref" @successCallBack="successCallBack"></editView>

			<!-- 编辑模型属性 -->
			<modalProcessMessage
				:show.sync="modalProcessMessageShow"
				@successCallBack="successCallBack"
				ref="modalProcessMessageRef"
			></modalProcessMessage>
			<!-- 添加多点标签 -->
			<modalPointsLabel
				:show.sync="modalPointsLabelShow"
				@successCallBack="handleTableClick"
				ref="modalPointsLabelRef"
			></modalPointsLabel>
			<!-- 添加多点视频 -->
			<modalPointsVideo
				:show.sync="modalPointsVideoShow"
				@successCallBack="handleTableClick"
				ref="modalPointsVideoRef"
			></modalPointsVideo>
			<!-- 模型数据标签配置 -->
			<modal-config
				:show.sync="modalConfigShow"
				ref="modalConfigRef"
				@successCallBack="successCallBack"
			></modal-config>
		</div>
	</div>
</template>
<script>
import Header from './components/Head'

import modalPreview from './moudle/ModalPreview.vue'
import tableTemplate from './moudle/TableTemplate.vue'
import addSiteProcess from './moudle/AddSiteProcess.vue'
import modalConfig from './moudle/ModalConfig.vue'
import stationContext from './moudle/StationContext.vue'
import editModel from './moudle/EditModel.vue'
import editView from './moudle/EditView.vue'
import modalProcessMessage from './moudle/ModalProcessMessage.vue'
import modalPointsLabel from './moudle/ModalPointsLabel.vue'
import modalPointsVideo from './moudle/ModalPointsVideo.vue'
// import ModelTemplate from './moudle/model-template'
import { deleteModels } from '@/api/setting'

export default {
	components: {
		Header,
		modalPreview,
		modalConfig,
		tableTemplate,
		addSiteProcess,
		stationContext,
		editModel,
		editView,
		modalProcessMessage,
		modalPointsLabel,
		modalPointsVideo,
		// ModelTemplate,
	},
	data() {
		return {
			modalPointsLabelShow: false,
			modalPointsVideoShow: false,
			tab3QxInfo: {},
			tab2Obj: {},
			tab2Station: false,
			tableH: 200,
			isAddStatus: false,
			isEditStatus: false,
			editLabelID: '',
			qs: require('qs'),
			tab2Delete: {
				processId: '',
				stationId: '',
			},
			tab2Cancel: false,
			loading: false,
			loading1: false,
			loading2: false,
			options: [],
			options1: [],
			options2: [],
			cityList2: [],
			stationList: [],
			relationID: '',
			color1: '#19be6b',
			tab2ModalInfo: {
				id: '',
				stationName: '',
				processName: '',
				stationProcessName: '',
			},

			tab2Edit: false,

			leftSelectList: [],
			rightSelectList: [],
			processType: '',
			processName: '',
			tab2Title: '新增站点工艺流程',
			tabsName: 'modelTemplate',
			title: '',
			editIndex: null,
			value: '',
			model1: '',

			tab3EditIndex: -1,
			editName: '',
			editNo: '',
			editType: '',
			tab3DeleteId: '',
			modelInfo: {},
			pageInfo: {
				pageNum: 1,
				total: 19,
				pageSize: 10,
			},
			pageInfo1: {
				pageNum: 1,
				total: 1900,
				pageSize: 10,
			},
			previewModalShow: false,
			stationContextShow: false,
			editModelShow: false,
			editViewlShow: false,
			addSiteProcessShow: false,
			modalConfigShow: false,
			modalProcessMessageShow: false,
		}
	},
	methods: {
		// 格式化数据
		typeInit(t) {
			if (t === 1) return '全局'
			if (t === 2) return '站点'
		},
		// 关联站点
		leftPageFun(s) {
			this.pageInfo1.pageNum = s
			this.stationLeftBtn()
		},
		rightPageFun(s) {
			this.pageInfo.pageNum = s
			this.stationRightBtn()
		},
		//废弃
		deldeArryFun(list, selsctList) {
			selsctList.forEach(item => {
				list.forEach((v, i) => {
					if (item.name === v.name) {
						list.splice(i, 1)
					}
				})
			})
		},
		// 工艺预览
		previewFun(id) {
			this.title = '工艺流程模型预览'
			let params = {
				id: id,
			}
			this.$axios.get('/api/iwater/technology/template/getModel', { params }).then(res => {
				if (res.data.responseCode === '100000') {
					this.$refs.preview.modalShowFun()
					this.$nextTick(() => {
						this.$refs.preview.flowInfoFun(res.data.result)
					})
				}
			})
		},
		// // 站点工艺预览
		// stationPreviewFun(v) {
		// 	this.title = '站点模型预览'
		// 	let params = {
		// 		stationId: v.stationId,
		// 		diagramId: v.processId,
		// 	}
		// 	console.log(v)
		// 	this.$axios
		// 		.get('/api/iwater/show/process/flow/diagram/list', { params })
		// 		.then(res => {
		// 			if (res.data.responseCode === '100000') {
		// 				this.$refs.preview.modalShowFun()
		// 				this.$nextTick(() => {
		// 					this.$refs.preview.flowInfoFun(res.data.result[0])
		// 				})
		// 			}
		// 		})
		// },
		// tab2 编辑 tab2EditShow
		editTuFun(v) {
			this.tab2Obj = v
			this.tab2Station = true
			this.tab2Cancel = false
			this.tab2Edit = true
			this.options1 = [
				{
					id: v.stationId,
					stationName: v.stationName,
				},
			]
			this.options = [
				{
					processFlowDiagramName: v.processName,
					id: v.processId,
				},
			]
			// this.tab2ModalInfo.stationName = v.stationId;
			// this.tab2ModalInfo.processName = v.processId;
			this.$nextTick(() => {
				this.tab2ModalInfo.stationName = v.stationId
				this.tab2ModalInfo.processName = v.processId
			})
			this.tab2ModalInfo.id = v.id
			this.tab2ModalInfo.stationProcessName = v.stationProcessName
			this.tab2Title = '编辑站点工艺流程'
			this.$refs.tab2EditShow.modal = true
		},
		// 取消
		cancelFun() {
			if (this.tab2Cancel) {
				this.$refs.tab2EditShow.modal = false
			} else {
				this.tab2Edit = true
				// this.tab2ModalInfo.stationName
				// this.tab2ModalInfo.processName
				// this.tab2ModalInfo.stationProcessName
				this.editTuFun(this.tab2Obj)
			}
		},
		clearValue() {
			this.options = []
		},
		clearValue1() {
			this.options1 = []
		},
		// 删除弹框
		deleftFun(v) {
			this.$refs.deleteRef.modalShowFun()
			this.tab2Delete.processId = v.processId
			this.tab2Delete.stationId = v.stationId
		},
		modelThreeQx() {
			console.log(this.tab3EditInfo)
			this.editModel = true
			this.tab3EditInfo = JSON.parse(this.tab3QxInfo)
			this.tab3EditInfo.nextDiagramId = this.tab3EditInfo.id
			if (!this.tab3EditInfo.clickColor) this.tab3EditInfo.clickColor = ''
		},

		// tab跳转
		successCallBack(from) {
			// this.$refs.tableTemplateRef.initSearch(this.tabsName, row)
			//
			switch (from) {
				case 'editorMeaasg':
					this.$refs.tableTemplateRef.refreshTableData(this.tabsName)
					break
				default:
					this.tabsFun(this.tabsName)
					break
			}
		},
		tabsFun(key) {
			this.$refs.tableTemplateRef.init(key)
		},
		handleTableClick(form, row) {
			switch (form) {
				case 'templatePreview':
					this.previewModalShow = true
					this.$nextTick(() => {
						this.$refs.previewRef.viewModel(row)
					})
					break
				case 'technologyPreview':
					this.previewModalShow = true
					this.$nextTick(() => {
						this.$refs.previewRef.init(row)
					})
					break
				case 'editProcessModelMessage':
					this.modalProcessMessageShow = true
					this.$nextTick(() => {
						this.$refs.modalProcessMessageRef.init(row)
					})
					break

				// 设置多个点标签
				case 'setMorePointLabel':
					this.modalPointsLabelShow = true
					this.$nextTick(() => {
						this.$refs.modalPointsLabelRef.init(row)
					})
					break
				// 设置多个点视频
				case 'setMorePointVideo':
					this.modalPointsVideoShow = true
					this.$nextTick(() => {
						this.$refs.modalPointsVideoRef.init(row)
					})
					break
				// 删除模型部件
				case 'deleteModelParts':
					this.$Modal.confirm({
						title: '提示',
						content: '删除模型部件?',
						loading: true,
						onOk: () => {
							this.$Modal.remove()

							deleteModels({ ids: row.modelId }).then(() => {
								this.$Message.info('删除成功！')
								// this.$refs.tableTemplateRef.initSearch(this.tabsName, row)
							})
						},
					})
					break
				case 'optionsiteProcess':
					this.addSiteProcessShow = true
					break
				case 'tagConfig':
					this.tabsName = 'globalProcessDataLabel'
					this.$refs.tableTemplateRef.initSearch(this.tabsName, row)
					break
				case 'dataLabelConfig':
					this.tabsName = 'siteProcessDataLabel'
					this.$refs.tableTemplateRef.initSearch(this.tabsName, row)
					break
				case 'edit':
					this.addSiteProcessShow = true
					this.$refs.siteProcessref.updateInit(row)
					break
				case 'modelPresentationData':
					this.modalConfigShow = true
					this.$refs.modalConfigRef.init(row)
					break
				case 'associatedSite':
					this.stationContextShow = true
					this.$refs.stationContextRef.init(row)
					break
				case 'editModel':
					this.editModelShow = true
					this.$refs.editModelref.init(row)
					break
				case 'editView':
					this.editViewlShow = true
					this.$refs.editViewref.init(row)
					break
				default:
					break
			}
		},
		initString(int) {
			let a = int.toString()
			let b = a.split('')
			console.log(b)
		},
	},
	mounted() {
		this.$refs.tableTemplateRef.init(this.tabsName)
	},
}
</script>
<style lang="less" scoped>
.flex {
	display: flex;
	align-items: center;
}
.tableFont-size ::v-deep {
	th .ivu-table-cell {
		font-size: 12px;
	}
	.ivu-table-cell {
		font-size: 12px;
	}
	.ivu-tabs-nav .ivu-tabs-tab {
		font-size: 12px;
	}
	.ivu-table td,
	.ivu-table th {
		height: 40px;
	}
}
.shellPadding {
	padding: 10px;

	background: #f2f2f2;
}
.h100 {
	height: 100%;
}
.whiteBg {
	background: #fff;
	padding: 10px;
}
.mr10 {
	margin-right: 10px;
}
.mt10 {
	margin-top: 10px;
}
.contextH {
	height: 350px;
}
.editContextH {
	height: 150px;
}
.contextW {
	width: 525px;
}
.centerW {
	width: 45px;
}
.tr {
	text-align: right;
}
.leftB {
	cursor: pointer;
	width: 20px;
	height: 20px;
	border: 1px solid #e6e7f0;
	text-align: center;
	line-height: 16px;
	color: #8385a3;
	margin-left: 12px;
}
.mt20 {
	margin-top: 20px;
}
.lineC {
	position: absolute;
	top: 45%;
}
.w280 {
	width: 280px;
	text-align: right;
}
.mr8 {
	margin-right: 8px;
}
</style>
<style lang="less">
#tableFont-size ::v-deep {
	.tableFont-size {
		font-size: 12px;
	}
}
.requiredRed {
	color: brown;
	position: relative;
	top: 3px;
}
.leftLable {
	width: 140px;
	text-align: right;
	margin-right: 10px;
}
.formBt10 {
	margin-bottom: 20px;
}
.mt10 {
	margin-top: 10px;
}
</style>
