<template>
	<!--数据绑定-->
	<div>
		<Modal
			:value="show"
			id="stationModel"
			:styles="{ top: '15px' }"
			:mask-closable="false"
			:width="modalWidth"
			@on-cancel="handleCancel"
			title="数据绑定"
		>
			<div style="display: flex; flex-direction: row">
				<!-- 左侧图形 -->
				<div
					id="faderId"
					@dragover="allowDrop"
					@drop="drop($event)"
					:style="{
						width: imgw + 'px',
						height: imgh + 'px',
						position: 'relative',
					}"
				>
					<img :src="processImage" id="bottomImg" style="position: relative" width="100%" height="100%" />
					<template v-if="pointList.length">
						<img
							v-for="(item, index) in pointList"
							:key="index"
							:class="`hlp${index}`"
							@click="getInfo(item, index, $event)"
							src="https://eslink-iot.oss-cn-beijing.aliyuncs.com/WDS-postion.png"
							style="position: absolute; cursor: pointer"
							:style="getStyles(item)"
						/>
						<!-- {{ getStyles(item) }} -->
					</template>
				</div>
				<!-- 右侧数据展示 -->
				<div id="Right">
					<Row
						class="form-content"
						:style="{
							height: imgh + 'px',
						}"
					>
						<i-col span="24" class="table_border" v-if="equipmentList.length > 0">
							<i-col span="24">
								<i-col span="8" class="table_right">标识名称</i-col>
								<i-col span="16">
									<Input
										v-model="editForm.remarkName"
										:disabled="clickImg"
										style="width: 120px"
									></Input>
								</i-col>
							</i-col>
							<i-col span="24" class="table_top">
								<i-col span="8" class="table_right">X坐标</i-col>
								<i-col span="16">
									<Input v-model="editForm.x" :disabled="clickImg" style="width: 120px"></Input>
								</i-col>
							</i-col>
							<i-col span="24" class="table_top">
								<i-col span="8" class="table_right">Y坐标</i-col>
								<i-col span="16">
									<Input v-model="editForm.y" :disabled="clickImg" style="width: 120px"></Input>
								</i-col>
							</i-col>
							<template v-for="(device, index) in deviceContexts">
								<i-col span="24" class="table_top" :key="index">
									<i-col span="8" class="table_right">
										{{ device.label }}
									</i-col>
									<i-col span="12">
										<Select
											transfer
											v-model="device.value"
											:disabled="clickImg"
											@on-change="getType($event, device)"
										>
											<Option
												v-for="(item, equipmentIndex) in equipmentList"
												:value="item.equipmentUniqueRepresentation"
												:key="index + equipmentIndex"
												:disabled="item.disabled"
											>
												{{ item.equipmentUniqueRepresentation }}
											</Option>
										</Select>
									</i-col>
									<i-col span="4" class="table-button" v-if="index === 0">
										<Button
											type="primary"
											shape="circle"
											icon="md-add"
											:disabled="clickImg"
											@click="butClick('addDeviceContext')"
											size="small"
										></Button>
									</i-col>
									<i-col span="4" class="table-button" v-else>
										<Button
											type="error"
											shape="circle"
											icon="md-remove"
											:disabled="clickImg"
											@click="butClick('deleteDeviceContext', index)"
											size="small"
										></Button>
									</i-col>
								</i-col>
								<template v-for="(dataType, dataTypeIndex) in device.dataTypes">
									<i-col :key="dataType.label + index + dataTypeIndex" span="24" class="table_top">
										<i-col span="8" class="table_right">
											{{ dataType.label }}
										</i-col>
										<i-col span="12">
											<Select
												transfer
												v-model="dataType.value"
												:disabled="
													clickImg || editForm.showType === 2 || editForm.showType === 3
												"
											>
												<Option
													v-for="item in device.typeList"
													:value="item.itemRealCode"
													:key="item.itemRealCode + index + dataTypeIndex"
													:disabled="item.disabled"
												>
													{{ item.itemName }}
												</Option>
											</Select>
										</i-col>
										<i-col span="4" class="table-button" v-if="dataTypeIndex === 0">
											<Button
												type="text"
												shape="circle"
												icon="md-add"
												:disabled="clickImg"
												@click="butClick('addDataType', index)"
												size="small"
											></Button>
										</i-col>
										<i-col span="4" class="table-button" v-else>
											<Button
												type="text"
												shape="circle"
												icon="md-remove"
												:disabled="clickImg"
												@click="butClick('deleteDataType', index, dataTypeIndex)"
												size="small"
											></Button>
										</i-col>
									</i-col>
									<i-col :key="dataType + index + dataTypeIndex" span="24" class="table_top">
										<i-col span="8" class="table_right">展示方式</i-col>
										<i-col span="16">
											<Select
												transfer
												v-model="dataType.displayValue"
												:disabled="clickImg"
												style="width: 120px"
											>
												<Option
													v-for="(item, showIndex) in cityList"
													:value="item.value"
													:key="index + dataTypeIndex + showIndex"
												>
													{{ item.label }}
												</Option>
											</Select>
										</i-col>
									</i-col>
								</template>
							</template>
						</i-col>
						<i-col v-else>
							<div style="margin-top: 50px; font-size: 14px">
								&nbsp;
								&nbsp;&nbsp;&nbsp;站点还未绑定设备，暂时无法进行工艺流程数据配置操作，请在”站点信息管理“功能中先绑定设备。
							</div>
						</i-col>
						<i-col span="24" style="text-align: center; margin-top: 10px" v-if="equipmentList.length > 0">
							<div v-if="isClickImg">
								<div v-if="eidtImgUp">
									<Button type="primary" @click="butClick('editImg')">编辑</Button>
									<Button type="primary" @click="butClick('deldetIMg')">删除</Button>
									<Button type="primary" @click="butClick('editCancelImg')">取消</Button>
								</div>
								<div v-else>
									<Button type="primary" @click="butClick('upEditBtn')">确认编辑</Button>
									<Button type="primary" @click="butClick('editCancelImg')">取消</Button>
								</div>
							</div>
							<div v-else>
								<div v-if="isreser">
									<Button type="primary" @click="butClick('reserveBtn')">保存</Button>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<Button type="primary" @click="butClick('addCancelImg')">取消</Button>
								</div>
								<div v-else>
									<Button type="primary" @click="newImg">增加数据点</Button>
								</div>
							</div>
						</i-col>
						<i-col
							span="24"
							id="imgb"
							style="height: 20px; position: relative; margin-left: 86px; margin-top: 10px; width: 20%"
							class="mt10"
						></i-col>
					</Row>
				</div>
			</div>
			<div slot="footer" style="height: 0; border-bottom: none"></div>
		</Modal>
		<EsConfirmModal
			v-model="deleteShow"
			title="提示"
			content="是否删除当前项"
			@on-ok="handleDelete"
			@on-cancel="deleteShow = false"
		></EsConfirmModal>
	</div>
</template>

<script>
import {
	get2dProcessConfigList,
	getEquipmentList,
	getEquipmentTypeList,
	edit2dProcessConfig,
	deletePoint,
} from '@/api/setting'
import { EsConfirmModal } from '@eslink/esvcp-pc-ui'

export default {
	components: { EsConfirmModal },
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '新增',
		},
	},
	computed: {
		stationListLength() {
			return this.pointList.length
		},
		getStyles() {
			return function (item) {
				return {
					left: item.x + 'px',
					top: item.y + 'px',
				}
			}
		},
	},
	mounted() {
		this.screenWidth = document.body.clientWidth //屏幕可用宽度
		this.screenHeight = document.body.clientHeight //屏幕可用高度
		// debugger
		this.ableImgWidth = this.screenWidth - 15 * 2 - 10 * 2 - 200
		this.ableImgHeight = this.screenHeight - 15 * 2 - 10 * 2 - 26
	},
	data() {
		return {
			deleteShow: false,
			listLoading: true,
			pointList: [],
			processImage: '',
			modalWidth: 500,
			imgw: null,
			imgh: null,
			imgList: [],
			equipmentList: [],
			editForm: {
				x: '',
				y: '',
				remarkName: '',
			},
			cityList: [
				{
					value: 0,
					label: '展示数据',
				},
				{
					value: 1,
					label: '展示图片',
				},
				{
					value: 2,
					label: '展示日期',
				},
				{
					value: 3,
					label: '展示时间',
				},
				{
					value: 4,
					label: '无标题数据',
				},
				{
					value: 5,
					label: '无边框数据',
				},
				{
					value: 6,
					label: '状态展示',
				},
			],
			screenWidth: '',
			screenHeight: '',
			ableImgWidth: '',
			ableImgHeight: '',
			deviceContexts: [
				{
					label: '关联设备',
					value: '',
					typeList: [],
					dataTypes: [
						{
							displayValue: '',
							label: '数据类型',
							value: '',
						},
					],
				},
			],
			processId: '',
		}
	},
	methods: {
		allowDrop(e) {
			e.preventDefault()
		},
		//放置
		drop(e) {
			// let modal = document.getElementsByClassName('ivu-modal')
			// let left = modal[0].offsetLeft
			// let top = modal[0].offsetTop
			let data = e.dataTransfer.getData('Text')
			if (data === 'newImgid') {
				if (e.offsetX < 20) return
				let faderId = document.getElementById('faderId')
				this.imgLeft = (e.offsetX - this.initw).toFixed(2)
				this.imgTop = (e.offsetY - this.inith).toFixed(2)
				console.log(e.offsetX, this.initw, e.offsetY, this.inith)
				var img = document.getElementById('newImgid')
				img.style.display = 'block'
				img.style.position = 'absolute'
				img.style.left = this.imgLeft + 'px'
				img.style.top = this.imgTop + 'px'

				this.editForm.x = this.imgLeft
				this.editForm.y = this.imgTop
				faderId.appendChild(document.getElementById(data))
			} else {
				if (document.getElementById('newImgid')) {
					const img = document.getElementById('newImgid')
					img.style.position = 'absolute'
					img.style.display = 'block'
				}
			}
		},

		// 新增数据点图标
		newImg() {
			this.currentStatus = true
			const _this = this
			this.isreser = true
			this.clickImg = false
			let imgid = document.getElementById('imgb')
			//创建一个新的点在右侧，准备拖拽用
			var img = document.createElement('img')
			img.setAttribute('draggable', true)
			img.setAttribute('id', 'newImgid')
			img.src = 'https://eslink-iot.oss-cn-beijing.aliyuncs.com/20190826112320769_Oval.png'
			img.style.position = 'absolute'
			img.style.cursor = 'pointer'
			img.ondragstart = function (e) {
				const modalW = document.getElementsByClassName('ivu-modal')[1].offsetLeft
				const modalT = document.getElementsByClassName('ivu-modal')[1].offsetTop
				const RightL = document.getElementById('Right').offsetLeft
				const RightT = document.getElementById('Right').offsetTop
				const dragBlockL = document.getElementById('imgb').offsetLeft
				const dragBlockT = document.getElementById('imgb').offsetTop
				const mainPicL = document.getElementById('faderId').offsetLeft
				const mainPicT = document.getElementById('faderId').offsetTop
				// debugger
				if (e.path[1].id === 'imgb') {
					_this.initw = e.pageX - RightL - dragBlockL - modalW
					_this.inith = e.pageY - RightT - dragBlockT - modalT
					console.log('initw:' + _this.initw, 'inith:' + _this.inith)
				} else if (e.path[1].id === 'faderId') {
					_this.initw = e.pageX - e.target.offsetLeft - modalW - mainPicL
					_this.inith = e.pageY - e.target.offsetTop - modalT - mainPicT
					console.log('initw:' + _this.initw, 'inith:' + _this.inith)
				}
				e.dataTransfer.setData('Text', e.target.id)
			}
			img.onclick = function () {
				_this.editForm.x = _this.imgLeft
				_this.editForm.y = _this.imgTop
				// _this.editForm = {
				// 	x: _this.imgLeft,
				// 	y: _this.imgTop,
				// 	remarkName: '',
				// 	mtMonitorObjectId: '',
				// 	mtMonitorTypeId: '',
				// 	showType: '',
				// }
			}
			imgid.appendChild(img)
		},

		//保存新增数据点
		reserveBtn(from) {
			if (this.isdata() === false) {
				return
			}
			console.log(1111111, this.deviceContexts)
			let parmas = {
				...this.editForm,
				x: ((this.editForm.x * this.naturalWidth) / this.imgw).toFixed(2),
				y: ((this.editForm.y * this.naturalHeight) / this.imgh).toFixed(2),
			}
			let arr = []
			this.deviceContexts.forEach(device => {
				device.dataTypes.forEach(type => {
					arr.push({
						mtMonitorObjectId: device.value,
						mtMonitorTypeId: type.value,
						showType: type.displayValue,
					})
				})
			})
			parmas.groupConfigs = arr

			edit2dProcessConfig(parmas).then(() => {
				// this.getImgList()
				this.$Message.info('操作成功')
				this.clickImg = true
				this.eidtImgUp = true
				this.isClickImg = false
				this.isreser = false
				this.currentStatus = false
				if (from === 'add') {
					this.handleReset()
					// this.currentStatus = false
					// this.isreser = false
					var box = document.getElementById('newImgid')
					box.parentNode.removeChild(box)
				} else {
					this.editImgDom.setAttribute('draggable', false)
					this.editImgDom.setAttribute('id', '')
					this.editImgDom.setAttribute(
						'src',
						'https://eslink-iot.oss-cn-beijing.aliyuncs.com/WDS-postion.png',
					)
				}
				this.getDian(this.editForm.processId, false)
			})
		},
		handleReset() {
			this.editForm = {
				processId: this.processId,
				x: '',
				y: '',
				remarkName: '',
			}
			this.deviceContexts = [
				{
					label: '关联设备',
					value: '',
					dataTypes: [
						{
							displayValue: '',
							label: '数据类型',
							value: '',
						},
					],
				},
			]
		},

		// 编辑数据点
		editImg() {
			let _this = this
			// 放开编辑权限
			this.clickImg = false
			this.eidtImgUp = false
			this.currentStatus = true
			this.editImgDom.setAttribute('draggable', true)
			this.editImgDom.setAttribute('id', 'newImgid')
			this.editImgDom.setAttribute(
				'src',
				'https://eslink-iot.oss-cn-beijing.aliyuncs.com/20190826112320769_Oval.png',
			)
			this.editImgDom.ondragstart = function (e) {
				const modalW = document.getElementsByClassName('ivu-modal')[1].offsetLeft
				const modalT = document.getElementsByClassName('ivu-modal')[1].offsetTop
				const mainPicL = document.getElementById('faderId').offsetLeft
				const mainPicT = document.getElementById('faderId').offsetTop
				_this.initw = e.pageX - e.target.offsetLeft - modalW - mainPicL
				_this.inith = e.pageY - e.target.offsetTop - modalT - mainPicT
				e.dataTransfer.setData('Text', e.target.id)
			}
		},
		isdata() {
			if (this.editForm.remarkName === '') {
				this.$Message.warning('标识名称不能为空')
				return false
			}
			if (this.editForm.x === '') {
				this.$Message.warning('X坐标不能为空')
				return false
			}
			if (this.editForm.y === '') {
				this.$Message.warning('Y坐标不能为空')
				return false
			}

			return this.judgeDevice()
		},
		judgeDevice() {
			let flag = true
			for (let index = 0; index < this.deviceContexts.length; index++) {
				const device = this.deviceContexts[index]
				if (device.value === '') {
					this.$Message.warning('关联设备不能为空')
					flag = false
					break
				}
				if (!this.judgeType(index)) {
					flag = false
					break
				}
			}
			return flag
		},
		judgeType(index) {
			let device = this.deviceContexts[index]
			let flag = true
			for (let dataindex = 0; dataindex < device.dataTypes.length; dataindex++) {
				const type = device.dataTypes[dataindex]
				if (type.value === '') {
					this.$Message.warning('数据类型不能为空')
					flag = false
					break
				}
				if (type.displayValue === '') {
					this.$Message.warning('展示方式不能为空')
					flag = false
					break
				}
			}
			return flag
		},

		// 取消新增数据点
		unreserveBtn() {
			this.isreser = false
			this.currentStatus = false
			this.editForm = {}
			this.clickImg = true
			var box = document.getElementById('newImgid')
			box.parentNode.removeChild(box)
		},

		// 获取站点列表
		getStationList() {
			this.listLoading = true
		},
		// 弹窗显隐事件
		handleVisibleChange(value) {
			// 每次展示时
			if (value) {
				// 获取站点列表
				this.getStationList()
			}
		},

		//新增点入口
		openGy(data) {
			this.processImage = data.processImage
			this.editForm.processId = data.id
			this.processId = data.id
			this.handleReset()
			this.deal(data)
		},
		deal(data) {
			const _this = this
			this.isClickImg = false
			this.isreser = false
			this.equipmentList = []
			this.eidtImgUp = true
			this.clickImg = true
			this.processImage = data.processImage
			this.techProcessId = data.id
			this.parentID = data.id
			if (document.getElementById('newImgid')) {
				var box = document.getElementById('newImgid')
				box.parentNode.removeChild(box)
				this.currentStatus = false
			}
			debugger
			//获取绑定的设备数据信息
			this.shebList(data.applicationName, data.stationCode)
			let img = new Image()
			// let showImg = document.getElementById('bottomImg')
			img.src = data.processImage
			img.onload = function () {
				// console.log('图片实际宽度----', img.naturalWidth, _this.ableImgWidth)
				// console.log('图片实际高度----', img.naturalHeight, _this.ableImgHeight)
				// console.log('modal宽度-------',_this.modalWidth)
				// console.log('浏览器可用宽度-----',_this.screenWidth)
				// img.naturalWidth   图片实际宽度
				// img.naturalHeight   图片实际高度
				// this.screenWidth      浏览器可用宽度
				// this.modalWidth     modal宽度
				let conversion = false
				// if (
				// 	img.naturalWidth <= _this.ableImgWidth &&
				// 	img.naturalHeight <= _this.ableImgHeight
				// ) {
				//第一种情况,图片长宽小于可容图片的长宽，按实际图片长宽来显示
				_this.modalWidth = img.naturalWidth + 10 * 2 + 200
				_this.imgw = img.naturalWidth
				_this.imgh = img.naturalHeight
				// } else if (
				// 	_this.ableImgWidth / img.naturalWidth <
				// 	_this.ableImgHeight / img.naturalHeight
				// ) {
				// 	// debugger
				// 	//第二种情况,按照可容宽计算出fix图片的长度
				// 	_this.imgw = _this.ableImgWidth
				// 	_this.imgh =
				// 		(_this.ableImgWidth * img.naturalHeight) /
				// 		img.naturalWidth
				// 	_this.modalWidth = _this.ableImgWidth + 10 * 2 + 200
				// 	conversion = true
				// } else if (
				// 	_this.ableImgWidth / img.naturalWidth >
				// 	_this.ableImgHeight / img.naturalHeight
				// ) {
				// 	//第三情况,按照可容高度算出fix图片的宽度
				// 	_this.imgh = _this.ableImgHeight
				// 	_this.imgw =
				// 		(_this.ableImgHeight * img.naturalWidth) /
				// 		img.naturalHeight
				// 	_this.modalWidth = _this.imgw + 10 * 2 + 200
				// 	conversion = true
				// }
				_this.naturalWidth = img.naturalWidth
				_this.naturalHeight = img.naturalHeight
				//已有数据坐标
				_this.getDian(data.id, conversion)
			}
		},

		//在工艺图上已有数据坐标打点
		getDian(id, conversion) {
			console.log('222222222222', id)
			console.log('33333333', conversion)
			this.addGYmodel = true
			const parmas = {
				processId: id,
			}
			get2dProcessConfigList(parmas).then(res => {
				// console.log(res)
				// debugger
				// this.pointList = []
				this.$set(this, 'pointList', res.result.dataList)
				// this.pointList = []
				// res.result.forEach(item => {
				// 	this.pointList.push({ ...item })
				// })
				// if (conversion) {
				// }
				// else {
				// 	this.pointList.forEach(item => {
				// 		item.x = item.x * 1
				// 		item.y = item.y * 1
				// 	})
				// }
			})
		},
		butClick(from, index, typeIndex) {
			switch (from) {
				case 'addDeviceContext':
					this.addDevice()
					break
				case 'deleteDeviceContext':
					this.deleteDevice(index)
					break
				case 'addDataType':
					this.addDataType(index)
					break
				case 'deleteDataType':
					this.deleteType(index, typeIndex)
					break
				case 'editImg':
					this.editImg()
					break
				case 'reserveBtn':
					this.reserveBtn('add')
					break
				case 'editCancelImg':
					this.cancelImg('edit')
					break
				case 'addCancelImg':
					this.cancelImg('add')
					break
				case 'upEditBtn':
					this.reserveBtn('edit')
					break
				case 'deldetIMg':
					this.deldetIMg()
					break
				default:
					break
			}
		},
		// 增加关联设备
		addDevice() {
			if (!this.judgeDevice()) {
				return
			}
			this.deviceContexts.forEach(device => {
				this.equipmentList.forEach(equipment => {
					if (device.value === equipment.equipmentUniqueRepresentation) {
						equipment.disabled = true
					}
				})
			})
			this.deviceContexts.push({
				label: '关联设备',
				value: '',
				dataTypes: [
					{
						displayValue: '',
						label: '数据类型',
						value: '',
					},
				],
			})
		},
		// 删除关联设备
		deleteDevice(index) {
			const deleteObject = this.deviceContexts[index]
			this.equipmentList.forEach(item => {
				if (item.equipmentUniqueRepresentation === deleteObject.value) {
					item.disabled = false
				}
			})
			this.deviceContexts.splice(index, 1)
		},
		// 增加数据类型
		addDataType(index) {
			if (!this.judgeType(index)) {
				return
			}
			// debugger
			this.deviceContexts[index].dataTypes.forEach(item => {
				this.deviceContexts[index].typeList.forEach(type => {
					if (type.itemRealCode === item.value) {
						type.disabled = true
					}
				})
			})
			this.deviceContexts[index].dataTypes.push({
				displayValue: '',
				label: '数据类型',
				value: '',
			})
		},
		// 删除数据累心
		deleteType(index, typeIndex) {
			const deleteTypeObject = this.deviceContexts[index].dataTypes[typeIndex]
			const typeList = this.deviceContexts[index].typeList

			typeList.forEach(item => {
				if (item.itemRealCode === deleteTypeObject.value) {
					item.disabled = false
				}
			})
			this.deviceContexts[index].dataTypes.splice(typeIndex, 1)
		},
		// 取消编辑数据点
		cancelImg(from) {
			// this.clickImg = false
			// this.isClickImg = false
			// this.clickImg = true
			// this.editImgDom.setAttribute('draggable', false)
			// this.editImgDom.setAttribute('id', '')
			// this.editForm = {}
			// this.getImgList()
			// this.$Message.info('操作成功')
			// this.currentStatus = false
			// this.isreser = false
			this.handleReset()
			this.clickImg = true
			this.eidtImgUp = true
			this.isClickImg = false
			this.isreser = false
			this.currentStatus = false
			if (from === 'add') {
				var box = document.getElementById('newImgid')
				box.parentNode.removeChild(box)
			} else {
				this.editImgDom.setAttribute('draggable', false)
				this.editImgDom.setAttribute('id', '')
				this.editImgDom.setAttribute('src', 'https://eslink-iot.oss-cn-beijing.aliyuncs.com/WDS-postion.png')
			}
			this.getDian(this.editForm.processId, false)
		},
		shebList(name, code) {
			getEquipmentList({
				applicationName: name,
				stationCode: code,
			}).then(res => {
				this.equipmentList = res.result
			})
		},
		//删除
		deldetIMg() {
			debugger
			this.deleteShow = true
			// this.$Modal.confirm({
			// 	title: '提示',
			// 	content: '确定要删除这条数据?',
			// 	loading: true,
			// 	onOk: () => {

			// 	},
			// })
		},
		handleDelete() {
			deletePoint({ id: this.editForm.id }).then(() => {
				this.deleteShow = false
				this.$Message.info('删除成功')
				this.isClickImg = false
				this.deleteShow = false
				this.handleReset()
				this.clickImg = false
				this.editImgDom.parentNode.removeChild(this.editImgDom)
			})
		},
		//已打点数据点击编辑
		async getInfo(data, imgIndex, e) {
			console.log(imgIndex)
			// if (this.currentStatus) {
			// 	this.$Message.warning('请先处理新增的数据点')
			// 	return
			// }
			this.editForm.remarkName = data.remarkName
			this.editForm.x = data.x
			this.editForm.y = data.y
			this.editForm.id = data.id
			let arr = []
			for (let index = 0; index < data.values.length; index++) {
				const item = data.values[index]
				debugger
				let imgIndex = arr.findIndex(aitem => {
					return aitem.value === item.stationCode
				})
				if (imgIndex === -1) {
					let res = await this.getType(item.stationCode)
					// debugger
					// this.getType(item.mtMonitorObjectId).then(res => {
					// res.forEach(typeItem => {
					// 	if (
					// 		typeItem.mtMonitorObjectId ===
					// 		item.mtMonitorObjectId
					// 	) {
					// 		typeItem.disabled = true
					// 	}
					// })
					arr.push({
						label: '关联设备',
						value: item.stationCode,
						typeList: res,
						dataTypes: [
							{
								displayValue: item.showType,
								label: '数据类型',
								value: item.itemCode,
							},
						],
					})
					// })
				} else {
					arr[imgIndex].dataTypes.push({
						displayValue: item.showType,
						label: '数据类型',
						value: item.itemCode,
					})
				}
			}
			// data.groupConfigs.forEach(item => {
			// 	// if (item.mtMonitorObjectId === )
			// 	let index = arr.findIndex(aitem => {
			// 		return aitem.value === item.mtMonitorObjectId
			// 	})
			// 	if (index === -1) {
			// 		this.getType(item.mtMonitorObjectId).then(res => {
			// 			arr.push({
			// 				label: '关联设备',
			// 				value: item.mtMonitorObjectId,
			// 				typeList: res,
			// 				dataTypes: [
			// 					{
			// 						displayValue: item.showType,
			// 						label: '数据类型',
			// 						value: item.mtMonitorTypeId,
			// 					},
			// 				],
			// 			})
			// 		})
			// 	} else {
			// 		arr[index].dataTypes.push({
			// 			displayValue: item.showType,
			// 			label: '数据类型',
			// 			value: item.mtMonitorTypeId,
			// 		})
			// 	}
			// })
			debugger
			this.deviceContexts = arr
			// this.imgID = data.id
			this.clickImg = true
			this.isClickImg = true
			// this.equipmentList.forEach(item => {
			// 	if (item.id === data.equipmentId) {
			// 		this.typeList = item.monitorTypeRespList
			// 	}
			// })
			this.editImgDom = e.target
			// this.editForm = data
			this.currentX = data.x
			this.currenty = data.y
		},
		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},
		// 确定
		handleCheck() {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					console.log(111)
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
		// 根据关联设备获取数据类型列表
		getType(val, device) {
			debugger
			return new Promise(re => {
				getEquipmentTypeList({
					stationCodes: val,
				}).then(res => {
					if (device) {
						this.$set(device, 'typeList', res.result)
					}
					re(res.result)
				})
			})
		},
	},
}
</script>
<style lang="less">
.station-modal {
	.custom-modal {
		.ivu-modal {
			height: 48% !important;
		}
		.ivu-modal-body {
			height: calc(100% - 96px);
		}
	}
}
</style>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
.demo-upload-list {
	display: inline-block;
	width: 60px;
	height: 60px;
	text-align: center;
	line-height: 60px;
	border: 1px solid transparent;
	border-radius: 4px;
	overflow: hidden;
	background: #fff;
	position: relative;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
	margin-right: 4px;
}
.demo-upload-list img {
	width: 100%;
	height: 100%;
}
::v-deep {
	.ivu-modal-header {
		margin-top: 0;
	}
	.ivu-modal-body {
		padding: 0;
	}
	.ivu-modal-content {
		padding: 10px !important;
		display: flex;
		flex-direction: column;
		flex: 1;
		width: 100%;
	}
	.ivu-modal-footer {
		border-top: none;
		height: auto;
		padding: 0;
		overflow: hidden;
	}
	.ivu-modal-header {
		border-bottom: none;
		height: auto;
		padding: 0;
		margin-top: -2px;
	}
}
.table_right {
	height: 32px;
	line-height: 32px;
}
.demo-upload-list-cover {
	display: none;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.6);
}
.demo-upload-list:hover .demo-upload-list-cover {
	display: block;
}
.demo-upload-list-cover i {
	color: #fff;
	font-size: 20px;
	cursor: pointer;
	margin: 0 2px;
}
.table_top {
	// border-top: 1px solid #f2f2f2;
	margin-top: 10px;
}
// #Right {
// 	width: 200px;
// 	overflow-x: auto;
// }
.form-content {
	width: 200px;
	padding-left: 10px;
	padding-top: 10px;
	overflow-y: auto;
	overflow-x: auto;
	&::-webkit-scrollbar {
		width: 2px;
	}
	&::-webkit-scrollbar-thumb {
		// height: 64px;
		background: linear-gradient(180deg, #117cae 0%, #124078 100%) !important;
	}
	&::-webkit-scrollbar-track,
	&::-webkit-scrollbar-track:hover {
		background: transparent;
	}
	&::-webkit-scrollbar-corner {
		background-color: #00101f;
	}
}
.table-button {
	height: 32px;
	line-height: 32px;
	padding-left: 5px;
	// i {
	// 	font-size: 20px;
	// 	cursor: pointer;
	// }
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	.ivu-input-number-input {
		color: #fff;
		background: #133a5e;
		border: none;
	}
	.ivu-btn {
		padding: 0 8px;
	}
}
</style>
