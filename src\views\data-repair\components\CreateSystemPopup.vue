<!--
 * @Description: 创建弹窗
 * @Author: shenxh
 * @Date: 2022-12-26 16:43:42
 * @LastEditors: shenxh
 * @LastEditTime: 2023-01-04 14:43:39
-->
<template lang="pug">
.create-system-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='600',
		v-model='showModal',
		:title='formData && formData.id ? "编辑" : "添加"',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='110')
					FormItem(label='修复类型', prop='type')
						Select(v-model='formData.type', transfer, filterable, placeholder='请选择')
							Option(v-for='(item, index) in repairTypeList', :key='index', :value='item.value') {{ item.label }}
					FormItem(label='修复目标', prop='notifyUserIdsArrXF')
						//- Select 用来修复校验bug
						Select(v-show='false', v-model='formData.notifyUserIdsArrXF')
						personnel-config-select(
							:selected-type='selectedDataLY.type || ""',
							:selected-value='formData.notifyUserIdsArrXF',
							:selected-label='formData.notifyUserNamesArrXF',
							@on-current-change='currentChangeXF',
							@close-tag='selectedDataXF = {}'
						)
					FormItem(label='数据来源目标', prop='notifyUserIdsArrLY')
						//- Select 用来修复校验bug
						Select(v-show='false', v-model='formData.notifyUserIdsArrLY')
						personnel-config-select(
							:selected-type='selectedDataXF.type || ""',
							:selected-value='formData.notifyUserIdsArrLY',
							:selected-label='formData.notifyUserNamesArrLY',
							@on-current-change='currentChangeLY',
							@close-tag='selectedDataLY = {}'
						)
					FormItem(label='数据时间段', prop='daterange')
						DatePicker(
							v-model='formData.daterange',
							type='daterange',
							format='yyyy-MM-dd',
							separator=' ~ ',
							placeholder='请选择',
							transfer
						)
		template(slot='footer')
			Button(type='primary', :disabled='repairBtnDisabled', @click='handleSubForm') 开始修复
</template>

<script>
import PersonnelConfigSelect from '@/components/gc-site-data/personnel-config-select.vue'
import { getStationItemDataRepairTypes, createStationItemDataRepair } from '@/api/other'

export default {
	name: 'create-system-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		PersonnelConfigSelect,
	},
	props: {
		showModal: Boolean,
		data: Object,
	},
	data() {
		return {
			repairBtnDisabled: false,
			testBtnLoading: false,
			repairTypeList: [],
			formData: {
				notifyUserIdsArrXF: [],
				notifyUserNamesArrXF: [],
				notifyUserIdsArrLY: [],
				notifyUserNamesArrLY: [],
			},
			selectedDataXF: {},
			selectedDataLY: {},
			formRules: {
				type: [
					{
						required: true,
						message: '请选择',
						type: 'string',
						trigger: 'change',
					},
				],
				notifyUserIdsArrXF: [
					{
						required: true,
						message: '请选择',
						type: 'array',
						trigger: 'change',
					},
				],
				notifyUserIdsArrLY: [
					{
						required: true,
						message: '请选择',
						type: 'array',
						trigger: 'change',
					},
				],
				sourceItemCode: [
					{
						required: true,
						message: '请选择',
						type: 'string',
						trigger: 'change',
					},
				],
				daterange: [
					{
						required: true,
						type: 'array',
						fields: {
							0: {
								required: true,
								type: 'date',
								message: '请选择',
								trigger: 'change',
							},
							1: {
								required: true,
								type: 'date',
								message: '请选择',
								trigger: 'change',
							},
						},
					},
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		this.getStationItemDataRepairTypes()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (!isShow) {
				this.$refs.form.resetFields()
			}
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.createStationItemDataRepair()
				}
			})
		},

		currentChangeXF(val) {
			this.selectedDataXF = val
		},
		currentChangeLY(val) {
			this.selectedDataLY = val
		},

		// 数据修复新增
		createStationItemDataRepair() {
			const dataBeginTime = this.$moment(this.formData.daterange[0].getTime()).format('YYYY-MM-DD') + ' 00:00:00'
			const dateEndTime = this.$moment(this.formData.daterange[1].getTime()).format('YYYY-MM-DD') + ' 23:59:59'
			const sourceItemCode =
				this.selectedDataLY.stationCode + '.' + this.selectedDataLY.itemRealCode.split('.')[1]
			const targetItemCode =
				this.selectedDataXF.stationCode + '.' + this.selectedDataXF.itemRealCode.split('.')[1]
			const sourceName = this.selectedDataLY.stationCode + '.' + this.selectedDataLY.itemName
			const targetName = this.selectedDataXF.stationCode + '.' + this.selectedDataXF.itemName

			this.repairBtnDisabled = true
			createStationItemDataRepair({
				sysCode: this.selectedDataXF.type || this.selectedDataLY.type,
				dataBeginTime,
				dateEndTime,
				sourceItemCode,
				sourceName,
				targetItemCode,
				targetName,
				type: this.formData.type,
			}).then(res => {
				const { responseCode, result } = res

				this.repairBtnDisabled = false

				this.$emit('set-value', false)
				this.$emit('submit-form', responseCode === '100000', result)
			})
		},

		// 修复类型列表
		getStationItemDataRepairTypes() {
			getStationItemDataRepairTypes().then(res => {
				const { result = {} } = res
				const data = Object.entries(result)
				let arr = data.map(item => {
					return {
						label: item[1],
						value: item[0],
					}
				})

				this.repairTypeList = arr
			})
		},
	},
}
</script>

<style lang="less" scoped>
.create-system-popup {
	.es-modal {
		/deep/ .ivu-modal {
			transform: translate(0, -70px);
			.ivu-modal-body {
				overflow: visible !important;
			}
		}
		.popup-content {
			width: 100%;
			padding: 0 40px;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
