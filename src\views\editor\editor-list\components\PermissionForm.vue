<!--
 * @Description:
 * @Version: 2.0
 * @Autor: heliping
 * @Date: 2023-09-18 09:02:39
 * @LastEditors: shenxh
 * @LastEditTime: 2023-09-25 09:48:05
-->
<template lang="pug">
.allocation-form
	water-row.water-margin-top-8.water-margin-left-8.water-margin-right-8(justify='space-between', align='center')
		span.title {{ `${formValidate.id ? '编辑' : '添加'}权限` }}
		Icon(type='md-close', size='24', style='cursor: pointer', @click='handleClose')
	Form.water-margin-left-8(ref='formValidate', :model='formValidate', :rules='ruleValidate', :label-width='80')
		FormItem(label='角色名称', prop='roleName')
			Input(v-model='formValidate.roleName', placeholder='请输入角色名称', style='width: 280px')
		FormItem(label='权限类型', prop='authType')
			Select(v-model='formValidate.authType', placeholder='请选择权限类型', style='width: 280px')
				Option(value='READ') 只读
				Option(value='WRITE') 读写
		FormItem(label='工艺图', prop='processFlowCharts')
			Checkbox(v-model='formValidate.checkAll', @on-change='handleCheckChange')
				.checkTitle(name='label')
					span 全部
					span.title-vline
					span 已选择 {{ formValidate.processFlowCharts.length }} / {{ proTotal }} 个工艺图
			Select(
				v-model='formValidate.processFlowCharts',
				multiple,
				label-in-value,
				@on-change='handleProChange',
				style='width: 280px'
			)
				Option(v-for='item in proList', :value='item.value', :key='item.value') {{ item.label }}
		FormItem(label='人员', prop='notifyUserIdsArr')
			personnel-config-select(
				:key='selectKey',
				:selected-value='formValidate.notifyUserIdsArr',
				:selected-label='formValidate.notifyUserNamesArr',
				style='width: 280px'
			)
	water-row.footer(justify='end', align='center')
		Button(type='primary', @click='relevData') 保存
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import PersonnelConfigSelect from '@/views/alarm-config/components/PersonnelConfigSelect.vue'
import { stationList, saveOrUpdateRole, queryPage } from '@/api/editor'

let paramProList = []
export default {
	name: 'permission-form',
	components: { WaterRow, PersonnelConfigSelect },
	props: {
		bindId: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			selectKey: 0,
			formValidate: {
				id: '',
				roleName: '',
				notifyUserIdsArr: [],
				notifyUserNamesArr: [],
				authType: '',
				processFlowCharts: [],
				checkAll: false,
			},
			ruleValidate: {
				roleName: [
					{
						required: true,
						message: '必填项',
						trigger: 'blur',
					},
				],
				authType: [
					{
						required: true,
						message: '必填项',
						trigger: 'blur, change',
					},
				],
				notifyUserIdsArr: [
					{
						required: true,
						message: '必填项',
					},
				],
				processFlowCharts: [
					{
						required: true,
						message: '必填项',
					},
				],
			},
			proList: [],
			proValues: [],
			proTotal: 0,
		}
	},
	mounted() {},
	methods: {
		// 初始化
		initData(data) {
			this.getData().then(total => {
				if (data) {
					const { id, users, roleName, processFlowCharts, authType } = data
					let checkAll = false
					let processFlowChartsids = []
					let notifyUserIdsArr = []
					let notifyUserNamesArr = []

					if (id) {
						if (processFlowCharts && total === processFlowCharts.length) {
							checkAll = true
						}
						users &&
							users.forEach(item => {
								notifyUserIdsArr.push(item.id)
								notifyUserNamesArr.push(item.name)
							})
					}
					processFlowCharts &&
						processFlowCharts.forEach(item => {
							processFlowChartsids.push(item.id)
						})

					this.formValidate = {
						id,
						users,
						roleName,
						processFlowCharts: processFlowChartsids,
						notifyUserIdsArr,
						notifyUserNamesArr,
						authType,
						checkAll,
					}

					this.selectKey++
				}
			})
		},
		// 获取工艺图列表信息
		getData(state) {
			return new Promise(resolve => {
				queryPage({
					state: state,
					pageNum: 1,
					pageSize: 999,
					name: this.value4,
				}).then(res => {
					const { result } = res
					if (result) {
						const { list, total } = result
						this.proValues = []
						this.proList = list.map(item => {
							this.proValues.push(item.id)
							return {
								label: item.name,
								value: item.id,
							}
						})
						this.proTotal = total
						// console.log('this.prolist', this.proList)
					} else {
						this.proList = []
						this.proTotal = 0
					}
					resolve(this.proTotal)
				})
			})
		},

		// 站点
		stationList() {
			stationList({ showItemCode: false }).then(res => {
				this.$nextTick(() => {
					this.$refs.tagTree.setTreeData(res.result, this.code, false)
				})
			})
		},
		// 选中框改变事件
		handleCheckChange(value) {
			if (value) {
				this.formValidate.processFlowCharts = this.proValues
			} else {
				this.formValidate.processFlowCharts = []
			}
		},
		// 工艺图选择框改变事件
		handleProChange(val) {
			console.log('111', val)
			if (val.length !== this.proTotal) {
				this.formValidate.checkAll = false
			} else {
				this.formValidate.checkAll = true
			}
			if (val.length) {
				paramProList = val.map(item => {
					return {
						name: item.label,
						id: item.value,
					}
				})
			}
		},
		// 新增操作
		relevData() {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					const { id, roleName, authType, notifyUserIdsArr, notifyUserNamesArr } = this.formValidate
					let params = { id, roleName, authType, users: [] }
					notifyUserIdsArr.forEach((item, index) => {
						params.users.push({
							id: item,
							name: notifyUserNamesArr[index],
						})
					})
					params.processFlowCharts = paramProList
					// console.log(this.formValidate)
					// debugger
					this.addRole(params)
				}
				// else {
				// 	this.$Message.error('Fail!');
				// }
			})
		},
		addRole(params) {
			saveOrUpdateRole(params).then(() => {
				this.$Message.info(`${this.formValidate.id ? '编辑' : '添加'}成功`)
				this.handleReset()
				// this.handleClose()
				this.$emit('close', 'saveSuc')
			})
		},
		handleReset() {
			this.$refs.formValidate.resetFields()
		},
		// 删除操作
		// handleDelete() {
		// 	// const { id } = item
		// 	// if (id) {
		// 	this.$Modal.confirm({
		// 		title: '提示',
		// 		content: '确定要删除这条数据?',
		// 		loading: true,
		// 		onOk: () => {
		// 			this.$Modal.remove()
		// 			deleteDir({ id }).then(() => {
		// 				this.$Message.info('删除成功')
		// 				this.dirList()
		// 			})
		// 		},
		// 	})
		// 	// }
		// },
		// 关闭弹框
		handleClose() {
			this.$emit('close', 'closeForm')
		},
	},
}
</script>
<style lang="less" scoped>
.allocation-form {
	position: fixed;
	right: 518px;
	top: 150px;
	width: 395px;
	background-color: #fff;
	border: 1px solid #e8e8e8;
	z-index: 10;
	box-shadow: 0px 8px 24px -4px rgba(24, 39, 75, 0.08);
}
.title {
	font-size: 18px;
	font-weight: 700;
}
.footer {
	padding: 8px;
}
.checkTitle {
	display: inline-block;
	// display: flex;
	margin-left: 8px;
	// .title-vline {
	// 	margin: 0 6px 0 6px;
	// }
	.title-vline::before {
		content: '';
		display: inline-block;
		width: 1px;
		height: 10px;
		margin-right: 8px;
		margin-left: 8px;
		background-color: #c2bdbd;
	}
}
</style>
