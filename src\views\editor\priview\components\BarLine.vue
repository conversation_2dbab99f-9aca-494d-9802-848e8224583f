<!--
 * @Author: shenxh
 * @Date: 2020-08-27 16:24:15
 * @LastEditors: shenxh
 * @LastEditTime: 2022-07-13 17:16:53
 * @Description: 组件-柱线图
-->

<template>
	<div :id="id || myId" class="xx-bar-line" :style="{ width, height }"></div>
</template>

<script>
import * as echarts from 'echarts/core'
import { Bar<PERSON>hart } from 'echarts/charts'
import { LineChart } from 'echarts/charts'
import {
	TitleComponent,
	LegendComponent,
	TooltipComponent,
	DataZoomComponent,
	GridComponent,
	DatasetComponent,
	TransformComponent,
} from 'echarts/components'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { uuid } from '@/utils/util'

echarts.use([
	TitleComponent,
	LegendComponent,
	TooltipComponent,
	DataZoomComponent,
	GridComponent,
	DatasetComponent,
	TransformComponent,
	<PERSON><PERSON>hart,
	<PERSON><PERSON><PERSON>,
	LabelLayout,
	UniversalTransition,
	CanvasRenderer,
])

export default {
	name: 'xx-bar-line',
	components: {},
	props: {
		id: [String, Number],
		width: {
			type: String,
			default: '100%',
		},
		height: {
			type: String,
			default: '100%',
		},
		seriesType: {
			type: String,
			default: 'line', // bar line
		},
		horizontal: Boolean, // 横向展示(XY轴交换)

		title: Object,
		titleText: String,
		grid: [Array, Object],
		legend: Object,
		tooltip: Object,
		dataZoom: Array,
		xAxis: Object,
		xAxis1: [Object, Boolean],
		yAxis: Object,
		yAxis1: [Object, Boolean],
		series: [Array, Object],
		// 表格数据
		chartData: {
			type: Array,
			default() {
				return [
					// [
					// 	{ name: '01', value: 100 },
					// 	{ name: '02', value: 50 },
					// 	{ name: '03', value: 200 },
					// ],
					// [
					// 	{ name: '01', value: 80 },
					// 	{ name: '02', value: 120 },
					// 	{ name: '03', value: 60 },
					// ],
				]
			},
		},
		disabledInit: Boolean, // 禁止自动更新
		loading: Boolean,
	},
	data() {
		return {
			myId: uuid(),
			myChart: null,
		}
	},
	computed: {
		option() {
			return {
				title: this._title,
				grid: this._grid,
				legend: this._legend,
				tooltip: this._tooltip,
				dataZoom: this._dataZoom,
				xAxis: this._xAxis,
				yAxis: this._yAxis,
				series: this._series,
			}
		},
		_title() {
			let title = Object.assign(
				{
					show: this.titleText ? true : false,
					text: this.titleText,
				},
				this.title,
			)

			return title
		},
		_grid() {
			let grid =
				this.grid && Array.isArray(this.grid)
					? this.grid
					: Object.assign(
							{
								left: 15,
								top: 70,
								right: 20,
								bottom: 0,
								containLabel: true, // 包含坐标轴的刻度标签
							},
							this.grid || {},
					  )

			return grid
		},
		_legend() {
			let legend = Object.assign(
				{
					show: true,
					top: 15,
					textStyle: {
						fontSize: 14,
						color: '#fff',
					},
					itemGap: 20,
					icon: 'rect',
					itemWidth: 8,
					itemHeight: 8,
					pageIconColor: '#44BCFF',
					pageIconInactiveColor: '#44BCFF33',
					pageTextStyle: {
						color: '#fff',
						fontSize: 16, // 解决文字顶部遮罩问题
					},
					// data: this.dataNames,
				},
				this.legend,
			)

			return legend
		},
		_tooltip() {
			let tooltip = Object.assign(
				{
					trigger: 'axis',
					axisPointer: {
						type: 'line', // line shadow
					},
					confine: true,
				},
				this.tooltip,
			)

			return tooltip
		},
		_dataZoom() {
			return this.dataZoom
		},
		_xAxis() {
			let xAxisData = {
				name: '',
				axisTick: {
					show: false,
				},
				nameTextStyle: {
					color: '#C0C1C8',
					fontSize: 14,
				},
				axisLine: {
					lineStyle: {
						color: '#FFFFFF4D',
					},
				},
				axisLabel: {
					color: '#FFFFFFBF',
					fontSize: 14,
					// rotate: 20
				},
				boundaryGap: this.seriesType === 'bar',
				data: this.dataNames,
			}
			let xAxis = [Object.assign(xAxisData, this.xAxis)]
			if (this.xAxis1) {
				let _xAxisData = JSON.parse(JSON.stringify(xAxisData))
				xAxis.push(Object.assign(_xAxisData, this.xAxis1 && typeof this.xAxis1 === 'object' ? this.xAxis1 : {}))
			}

			return xAxis
		},
		_yAxis() {
			let yAxisData = {
				name: '',
				axisTick: {
					show: false,
				},
				nameTextStyle: {
					color: '#C0C1C8',
					fontSize: 14,
				},
				axisLine: {
					lineStyle: {
						color: '#FFFFFF4D',
					},
				},
				axisLabel: {
					color: '#FFFFFFBF',
					fontSize: 14,
				},
				splitLine: {
					color: '#fff',
					lineStyle: {
						opacity: 0.3,
					},
				},
			}
			let yAxis = [Object.assign(yAxisData, this.yAxis)]
			if (this.yAxis1) {
				let _yAxisData = JSON.parse(JSON.stringify(yAxisData))
				yAxis.push(Object.assign(_yAxisData, this.yAxis1 && typeof this.yAxis1 === 'object' ? this.yAxis1 : {}))
			}

			return yAxis
		},
		_series() {
			let series =
				this.series && Array.isArray(this.series)
					? this.series.map((item, index) => {
							return {
								...{
									symbol: 'none', // 去除拐角圈
									smooth: true,
								},
								...item,
								data: this.chartData[index],
							}
					  })
					: [
							Object.assign(
								{
									type: this.seriesType,
									smooth: true,
									itemStyle: {
										color: '#a1c4fd',
									},
									lineStyle: {
										color: '#a1c4fd',
									},
									areaStyle: {
										color: {
											type: 'linear',
											x: 0,
											y: 0,
											x2: 0,
											y2: 1,
											colorStops: [
												{
													offset: 0,
													color: '#00C1DE99',
												},
												{
													offset: 1,
													color: '#0080DE0D',
												},
											],
										},
									},
									data: this.chartData[0],
								},
								this.series,
							),
					  ]

			return series
		},
		dataNames() {
			let data = []

			for (let i = 0; i < this.chartData.length; i++) {
				if (this.chartData[i] && this.chartData[i].length) {
					data = this.chartData[i].map(item => {
						return item.name
					})

					break
				}
			}

			return data
		},
	},
	watch: {
		chartData() {
			if (this.disabledInit) return

			this.initChart()
		},
		loading(val) {
			if (val) {
				this.myChart &&
					this.myChart.showLoading({
						text: 'loading...',
						color: '#c23531',
						textColor: '#fff',
						maskColor: '#04162799',
						zlevel: 0,

						// 字体大小。从 `v4.8.0` 开始支持。
						fontSize: 16,
						// 是否显示旋转动画（spinner）。从 `v4.8.0` 开始支持。
						showSpinner: true,
						// 旋转动画（spinner）的半径。从 `v4.8.0` 开始支持。
						spinnerRadius: 10,
						// 旋转动画（spinner）的线宽。从 `v4.8.0` 开始支持。
						lineWidth: 5,
					})
			} else {
				this.myChart && this.myChart.hideLoading()
			}
		},
	},
	created() {},
	mounted() {
		window.addEventListener('resize', this.initChart)
		if (this.chartData && this.chartData.length) {
			this.initChart()
		}
	},
	beforeDestroy() {
		this.disposeChart()
		window.removeEventListener('resize', this.initChart)
	},
	methods: {
		initChart() {
			this.setOption()
			this.disposeChart()

			const id = this.id || this.myId

			this.myChart = echarts.init(document.getElementById(id))

			// 设置配置项, 刷新图表
			this.myChart.setOption(this.option, true)

			// 点击事件
			this.myChart.off('click')
			this.myChart.on('click', evt => {
				this.$emit('click', evt)

				this.myChart.setOption(this.option, true)
			})
		},
		// 销毁图表实例
		disposeChart() {
			if (this.myChart && !this.myChart.isDisposed()) {
				this.myChart.clear() // 释放图形资源
				this.myChart.dispose() // 销毁实例对象
				this.myChart = null
			}
		},
		// 数据处理
		setOption() {
			// 横向
			if (this.horizontal) {
				let _xAxis = this.option.xAxis
				let _yAxis = this.option.yAxis

				this.option.xAxis = _yAxis
				this.option.yAxis = _xAxis
			}
		},
	},
}
</script>

<style scoped lang="less"></style>
