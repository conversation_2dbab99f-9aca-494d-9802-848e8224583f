<template>
	<div class="search-box">
		<Input v-model="search">
			<template #prepend>
				<Select v-model="keyWord" style="width: 120px">
					<Option :value="item.value" v-for="item in keyWordOption" :key="item.value">
						{{ item.label }}
					</Option>
				</Select>
			</template>
			<template #append>
				<Button class="search-icon" icon="ios-search" @click="searchList"></Button>
			</template>
		</Input>
	</div>
</template>

<script>
export default {
	name: 'Search',
	props: {},
	data() {
		return {
			search: '',
			keyWord: 'nodeCode',
			keyWordOption: [
				{
					label: '设备编号',
					value: 'nodeCode',
				},
				{
					label: '用户名称',
					value: 'userName',
				},
				{
					label: '设备地址',
					value: 'address',
				},
			],
		}
	},
	created() {},
	mounted() {},
	methods: {
		searchList() {
			this.$parent.$refs.category.$refs.list.showList = true
			this.$parent.$refs.category.$refs.list.updateDeviceList(1, true, {
				[this.keyWord]: this.search,
			})
		},
	},
}
</script>
<style lang="less" scoped>
.search-box {
	position: absolute;
	top: 20px;
	left: 20px;
	display: flex;
	z-index: 100;
	.run-board-search {
		background: #fff;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
		border-right-color: #fff;
	}
	.search-icon {
		background: #4d6bff;
		cursor: pointer;
		color: #ffffff;
		i {
			color: #fff;
			font-size: 18px;
		}
	}
}
::v-deep {
	.ivu-input-group-append,
	.ivu-input-group-prepend {
		background-color: #ffffff;
	}
}
</style>
