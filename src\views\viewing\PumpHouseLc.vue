<!--
 * @Description: 乐昌泵房VR
 * @Author: shenxh
 * @Date: 2022-12-15 13:50:53
 * @LastEditors: shenxh
 * @LastEditTime: 2022-12-19 14:04:15
-->

<template>
	<div :key="pumpKey" class="pump-house-lc">
		<div class="view-container" ref="threeDBox"></div>
		<div class="full-screen-btn" @click="handleScreenBtn">
			<img v-if="!isFull" src="@/assets/icon/full_screen.svg" alt="全屏" />
			<img v-else src="@/assets/icon/exit_full_screen.svg" alt="退出全屏" />
		</div>
	</div>
</template>

<script>
import * as THREE from 'three'
import TWEEN from '@tweenjs/tween.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js'
import { CSS3DRenderer, CSS3DObject } from 'three/examples/jsm/renderers/CSS3DRenderer.js'

import { queryProcessItems } from '@/api/secondary-supply.js'
import { fullScreen, exitFullScreen, isFullScreen } from '@/utils/util.js'

let animationFrame = null
let css2DRenderer = null
let css3DRenderer = null
let css3DRendererDom = null
let css2DRendererDom = null
let processItems = []

export default {
	name: 'pump-house-lc',
	components: {},
	props: {},
	data() {
		return {
			pumpKey: 0,
			isFull: false,
			scene: null,
			camera: null,
			controls: null,
			renderer: null,
			sphere: null,
			labelDataListTmp: [],
			lightPointListTmp: [],
			lightPointList: [],
			currentViewName: 'C1', // 当前视角名称 http://zentao.eslink.cc/zentao/story-view-12004.html
			dataList: [
				{
					viewName: 'A',
					image: require('@/assets/image/pump-house-lc/A.jpg'),
					lightPointList: [
						{
							name: 'B1',
							// position: { x: 227.8947125, y: -100, z: 60 },
							position: { x: 246.2437816, y: 0, z: 70 },
							lookAt: { x: 0, y: 0, z: 0 },
							cameraEndPosition: {
								x: 246.2437816,
								y: 20,
								z: -70,
							},
						},
					],
				},
				{
					viewName: 'B1',
					image: require('@/assets/image/pump-house-lc/B1.jpg'),
					lightPointList: [
						{
							name: 'A',
							position: { x: -221.666416, y: -100, z: 70 },
							lookAt: { x: 0, y: -100, z: 0 },
						},
						{
							name: 'B2',
							position: { x: -70, y: -160, z: -187.1790587 },
						},
						{
							name: 'C1',
							position: { x: 211.5088651, y: -120, z: -80 },
							cameraEndPosition: {
								x: -50,
								y: 20,
								z: 100,
							},
						},
					],
				},
				{
					viewName: 'B2',
					image: require('@/assets/image/pump-house-lc/B2.jpg'),
					lightPointList: [
						{
							name: 'B1',
							position: { x: -140, y: -135, z: 166.4662128 },
						},
						{
							name: 'C2',
							position: { x: 183.9456441, y: -110, z: 140 },
							cameraEndPosition: {
								x: -50,
								y: 20,
								z: -100,
							},
						},
					],
				},
				{
					viewName: 'C1',
					image: require('@/assets/image/pump-house-lc/C1.jpg'),
					tipsList: [
						{
							position: { x: -247.0546498, y: 60, z: -30 },
							content: {
								title: '低区',
								type: 1, // 公共-0 低区-1 中区-2 高区-3 特高区-4
								list: [
									{
										label: '瞬时流量',
										name: '出水瞬时流量',
										code: 'SSLL_CS',
									},
								],
							},
						},
						{
							position: { x: -238.9100249, y: 62, z: -65 },
							content: {
								title: '高区',
								type: 3, // 公共-0 低区-1 中区-2 高区-3 特高区-4
								list: [
									{
										label: '瞬时流量',
										name: '出水瞬时流量',
										code: 'SSLL_CS',
									},
								],
							},
						},
						{
							position: { x: 90, y: 107, z: -215.4321239 },
							content: {
								title: '视频状态',
							},
						},
						{
							position: { x: 230.6750962, y: 68, z: 90 },
							content: {
								title: '水箱',
							},
						},
						{
							position: { x: 212.2168702, y: 33, z: -130 },
							content: {
								title: '高区',
								type: 3, // 公共-0 低区-1 中区-2 高区-3 特高区-4
								list: [
									{
										label: '出水压力',
										name: '出水压力',
										code: 'YL_CS',
									},
								],
							},
						},
						{
							position: { x: 214.0934375, y: -10, z: -140 },
							content: {
								title: '1#水泵',
								type: 3, // 公共-0 低区-1 中区-2 高区-3 特高区-4
								list: [
									{
										label: '运行状态',
										name: '1#水泵运行状态',
										code: 'YXZT_SB1',
									},
									{
										label: '运行时间',
										name: '1#水泵运行时间',
										code: 'YXSJ_SB1',
									},
								],
							},
						},
						{
							position: { x: 230.9458811, y: -10, z: -120 },
							content: {
								title: '2#水泵',
								type: 3, // 公共-0 低区-1 中区-2 高区-3 特高区-4
								list: [
									{
										label: '运行状态',
										name: '2#水泵运行状态',
										code: 'YXZT_SB2',
									},
									{
										label: '运行时间',
										name: '2#水泵运行时间',
										code: 'YXSJ_SB2',
									},
								],
							},
						},
						{
							position: { x: 70, y: 52, z: 241.1140809 },
							content: {
								title: '低区',
								type: 1, // 公共-0 低区-1 中区-2 高区-3 特高区-4
								list: [
									{
										label: '出水压力',
										name: '出水压力',
										code: 'YL_CS',
									},
								],
							},
						},
						{
							position: { x: 176.9491452, y: 0, z: 185 },
							content: {
								title: '1#水泵',
								type: 1, // 公共-0 低区-1 中区-2 高区-3 特高区-4
								list: [
									{
										label: '运行状态',
										name: '1#水泵运行状态',
										code: 'YXZT_SB1',
									},
									{
										label: '运行时间',
										name: '1#水泵运行时间',
										code: 'YXSJ_SB1',
									},
								],
							},
						},
						{
							position: { x: 133.8357202, y: 2, z: 218 },
							content: {
								title: '2#水泵',
								type: 1, // 公共-0 低区-1 中区-2 高区-3 特高区-4
								list: [
									{
										label: '运行状态',
										name: '2#水泵运行状态',
										code: 'YXZT_SB2',
									},
									{
										label: '运行时间',
										name: '2#水泵运行时间',
										code: 'YXSJ_SB2',
									},
								],
							},
						},
						{
							position: { x: 88.52118391, y: 4, z: 240 },
							content: {
								title: '3#水泵',
								type: 1, // 公共-0 低区-1 中区-2 高区-3 特高区-4
								list: [
									{
										label: '运行状态',
										name: '3#水泵运行状态',
										code: 'YXZT_SB3',
									},
									{
										label: '运行时间',
										name: '3#水泵运行时间',
										code: 'YXSJ_SB3',
									},
								],
							},
						},
						{
							position: { x: 60, y: 125, z: 215.1999071 },
							content: {
								title: '进水电动阀',
							},
						},
						{
							position: { x: -150, y: -120, z: 169.221748 },
							content: {
								title: '排污泵',
							},
						},
						{
							position: { x: -229.2073297, y: 70, z: 90 },
							content: {
								title: '水质检测仪',
								list: [
									{
										label: 'PH',
										name: 'PH',
										code: 'PH_YS',
									},
									{
										label: '浊度',
										name: '浊度',
										code: 'ZD_YS',
									},
									{
										label: '余氯',
										name: '余氯',
										code: 'YLv_YS',
									},
								],
							},
						},
						{
							position: { x: -244.5628754, y: -75, z: -10 },
							content: {
								title: '除湿机',
							},
						},
					],
					lightPointList: [
						{
							name: 'B1',
							position: { x: 85, y: -90, z: -224.0781114 },
							cameraEndPosition: {
								x: -85,
								y: 20,
								z: 44.0781114,
							},
						},
						{
							name: 'C2',
							position: { x: 223.2397814, y: -110, z: 60 },
							cameraEndPosition: {
								x: -223.2397814,
								y: 20,
								z: -40,
							},
						},
						{
							name: 'D',
							position: { x: -55, y: -165, z: 187.8456813 },
							cameraEndPosition: {
								x: 55,
								y: 20,
								z: 10,
							},
						},
					],
				},
				{
					viewName: 'C2',
					image: require('@/assets/image/pump-house-lc/C2.jpg'),
					lightPointList: [
						{
							name: 'B2',
							position: { x: 10, y: -110, z: 230.9458811 },
							cameraEndPosition: {
								x: 10,
								y: 20,
								z: -180,
							},
						},
						{
							name: 'C1',
							position: { x: 223.2397814, y: -110, z: 60 },
							cameraEndPosition: {
								x: -100,
								y: 20,
								z: -30,
							},
						},
						{
							name: 'E',
							position: { x: 60, y: -60, z: -241.5284662 },
							cameraEndPosition: {
								x: -100,
								y: 20,
								z: -30,
							},
						},
					],
				},
				{
					viewName: 'D',
					image: require('@/assets/image/pump-house-lc/D.jpg'),
					lightPointList: [
						{
							name: 'C1',
							position: { x: -180.3080697, y: -180, z: -25 },
							cameraEndPosition: {
								x: 30,
								y: 20,
								z: -60,
							},
						},
					],
				},
				{
					viewName: 'E',
					image: require('@/assets/image/pump-house-lc/E.jpg'),
					lightPointList: [
						{
							name: 'C2',
							position: { x: 247.5600937, y: -55, z: -35 },
							cameraEndPosition: {
								x: -80,
								y: 20,
								z: 90,
							},
						},
						{
							name: 'F',
							position: { x: 10, y: -130, z: 220.3088741 },
						},
					],
				},
				{
					viewName: 'F',
					image: require('@/assets/image/pump-house-lc/F.jpg'),
					lightPointList: [
						{
							name: 'E',
							position: { x: -25, y: -135, z: -216.0694333 },
						},
					],
				},
			],
		}
	},
	computed: {
		// 当前视角
		currentViewData() {
			const current = this.dataList.filter(item => item.viewName === this.currentViewName)

			return current && current.length ? current[0] : {}
		},
	},
	watch: {},
	created() {},
	mounted() {
		this.init()
	},
	beforeDestroy() {
		animationFrame && cancelAnimationFrame(animationFrame)
	},
	methods: {
		init() {
			const threeDBoxDom = this.$refs.threeDBox

			this.initScene()
			this.initCamera(threeDBoxDom)
			this.initControls(threeDBoxDom)
			this.initRenderer(threeDBoxDom)
			this.render()
			this.initContent()
			this.create2DRenderer()
			this.create3DRenderer()
			this.getLabelData()
		},

		initScene() {
			const axes = new THREE.AxesHelper(100000)

			this.scene = new THREE.Scene()
			this.scene.background = new THREE.Color(0x101010)
			this.scene.add(axes)
		},

		initCamera(element) {
			this.camera = new THREE.PerspectiveCamera(45, element.clientWidth / element.clientHeight, 0.1, 1000)
			this.camera.position.set(-70, 20, 50)
		},

		initControls(element) {
			this.controls = new OrbitControls(this.camera, element)
			this.controls.minDistance = 0.1
			this.controls.maxDistance = 200
			this.controls.enablePan = false
		},

		initRenderer(element) {
			this.renderer = new THREE.WebGLRenderer()
			this.renderer.setSize(element.offsetWidth, element.offsetHeight)
			element.appendChild(this.renderer.domElement)
		},

		render() {
			this.controls.update()
			this.renderer.render(this.scene, this.camera)
			this.renderer.sortObjects = false
			animationFrame = requestAnimationFrame(this.render)
			css2DRenderer && css2DRenderer.render(this.scene, this.camera)
			css3DRenderer && css3DRenderer.render(this.scene, this.camera)

			TWEEN.update()
		},

		initContent() {
			let sphereGeometry = new THREE.SphereGeometry(16, 50, 50)
			sphereGeometry.scale(16, 16, -16)
			let texture = new THREE.TextureLoader().load(this.currentViewData.image)
			let sphereMaterial = new THREE.MeshBasicMaterial({
				map: texture,
			})
			this.sphere = new THREE.Mesh(sphereGeometry, sphereMaterial)
			this.scene.add(this.sphere)
		},

		updateTexture() {
			const texture = new THREE.TextureLoader().load(this.currentViewData.image)

			this.sphere.material.map = texture
			this.sphere.material.map.needsUpdate = true
			// this.controls.target = new THREE.Vector3(0, 0, 0)
		},

		create2DRenderer() {
			const threeDBoxDom = this.$refs.threeDBox

			css2DRenderer = new CSS2DRenderer()
			css2DRenderer.setSize(threeDBoxDom.clientWidth, threeDBoxDom.clientHeight)
			css2DRenderer.domElement.id = 'label-renderer'
			css2DRenderer.domElement.style.position = 'absolute'
			css2DRenderer.domElement.style.top = '0px'
			threeDBoxDom.appendChild(css2DRenderer.domElement)
			css2DRendererDom = css2DRenderer.domElement
		},

		create3DRenderer() {
			const threeDBoxDom = this.$refs.threeDBox

			css3DRenderer = new CSS3DRenderer()
			css3DRenderer.setSize(threeDBoxDom.clientWidth, threeDBoxDom.clientHeight)
			css3DRenderer.domElement.id = 'label-renderer-3d'
			css3DRenderer.domElement.style.position = 'absolute'
			css3DRenderer.domElement.style.top = '0px'
			threeDBoxDom.appendChild(css3DRenderer.domElement)
			css3DRendererDom = css3DRenderer.domElement

			this.createLightPoint()
		},

		createLightPoint() {
			const { lightPointList = [] } = this.currentViewData

			lightPointList.forEach(item => {
				const { name, position, lookAt, cameraEndPosition } = item
				const { x, y, z } = position
				let div = document.createElement('div')

				div.className = 'three-label-3d'

				div.addEventListener('mousedown', () => {
					this.currentViewName = name

					new TWEEN.Tween(this.camera)
						.to(
							{
								position: new THREE.Vector3(x, 0, z),
							},
							2000,
						)
						.easing(TWEEN.Easing.Sinusoidal.InOut)
						.onComplete(() => {
							this.updateTexture()
							this.clearLabel()
							this.clearLightPoint()
							this.createLightPoint()
							if (name === 'C1') {
								this.getLabelData()
							} else {
								this.clearCss2DRendererDom()
							}
							if (cameraEndPosition) {
								new TWEEN.Tween(this.camera)
									.to(
										{
											position: new THREE.Vector3(
												cameraEndPosition.x,
												cameraEndPosition.y,
												cameraEndPosition.z,
											),
										},
										0,
									)
									.start()
							}
						})
						.start()
				})

				let lightPointObj = new CSS3DObject(div)

				lightPointObj.position.set(x, y, z)
				if (lookAt) {
					lightPointObj.lookAt(lookAt.x, lookAt.y, lookAt.z)
				} else {
					lightPointObj.lookAt(x, 0, z)
				}

				this.sphere.add(lightPointObj)
				this.lightPointListTmp.push(lightPointObj)
			})
		},

		async getLabelData() {
			processItems = await this.queryProcessItems()
			;(this.currentViewData.tipsList || []).forEach(item => {
				const { position = {}, content = {} } = item
				const { type, list = [] } = content
				const obj = {
					position,
					content: {
						...content,
						list: list.map(item1 => {
							const { code } = item1
							return {
								...item1,
								...this.getLabelDet(code, type),
							}
						}),
					},
				}

				this.initCSS2DObject(obj)
			})
		},

		getLabelDet(code, type) {
			const {
				YQ2200024_0 = [],
				YQ2200024_1 = [],
				YQ2200024_2 = [],
				YQ2200024_3 = [],
				YQ2200024_4 = [],
			} = processItems
			let filterData = null
			let data = {}

			switch (type) {
				case 0:
					filterData = YQ2200024_0.filter(item => item.code === code)
					break
				case 1:
					filterData = YQ2200024_1.filter(item => item.code === code)
					break
				case 2:
					filterData = YQ2200024_2.filter(item => item.code === code)
					break
				case 3:
					filterData = YQ2200024_3.filter(item => item.code === code)
					break
				case 4:
					filterData = YQ2200024_4.filter(item => item.code === code)
					break
			}
			if (filterData) {
				data = filterData[0]
			} else {
				data = {}
			}

			return data
		},

		queryProcessItems() {
			return queryProcessItems({
				stationId: 461,
			}).then(res => {
				const { result = {} } = res
				const { common = {}, eqList = [] } = result
				const { dataList = [] } = common
				let obj = {
					YQ2200024_0: dataList,
				}

				eqList.forEach(item => {
					const { code, dataList = [] } = item

					obj[code] = dataList
				})

				return obj
			})
		},

		// 创建标签
		initCSS2DObject(data) {
			const { position = {}, content = {} } = data
			const { x, y, z } = position
			const { title, list = [] } = content
			const div = document.createElement('div')
			let tmp = ''

			list.forEach(item => {
				const { label = '-', value = '-', unit = '' } = item
				let li = `<div class="three-label-item">
					<div class="label">${label}</div>
					<div class="value">${value}${unit}</div>
				</div>`

				tmp += li
			})

			const str = `<div class="three-label">
        <div class="three-label-inner">
          <div class="three-label-inner-header">${title}</div>
          <div class="three-label-inner-body">
            ${tmp}
          </div>
        </div>
        <div class="three-label-line"></div>
      </div>`

			div.innerHTML = str

			const labelObj = new CSS2DObject(div)

			labelObj.position.set(x, y, z)
			this.sphere.add(labelObj)
			this.labelDataListTmp.push(labelObj)

			const threeLabelInnerDom = div.getElementsByClassName('three-label-inner')[0]
			threeLabelInnerDom.addEventListener('mouseover', this.onMouseoverThreeLabelInnerDom)
			threeLabelInnerDom.addEventListener('mouseleave', this.onMouseleaveThreeLabelInnerDom)
		},

		onMouseoverThreeLabelInnerDom(e) {
			for (let item of e.target.children) {
				if (item.className === 'three-label-inner-body') {
					item.style.display = 'block'
				}
			}
		},

		onMouseleaveThreeLabelInnerDom(e) {
			for (let item of e.target.children) {
				if (item.className === 'three-label-inner-body') {
					item.style.display = 'none'
				}
			}
		},

		clearLabel() {
			this.labelDataListTmp.forEach(item => {
				this.sphere.remove(item)
			})
			this.labelDataListTmp = []
		},

		clearLightPoint() {
			this.lightPointListTmp.forEach(item => {
				this.sphere.remove(item)
			})
			this.lightPointListTmp = []
		},

		clearCss3DRendererDom() {
			const threeDBoxDom = this.$refs.threeDBox

			css3DRendererDom && threeDBoxDom.removeChild(css3DRendererDom)
			css3DRendererDom = null
		},

		clearCss2DRendererDom() {
			const threeDBoxDom = this.$refs.threeDBox

			css2DRendererDom && threeDBoxDom.removeChild(css2DRendererDom)
			css2DRendererDom = null
		},

		handleScreenBtn() {
			this.isFull = !this.isFull

			if (!isFullScreen()) {
				fullScreen()
			} else {
				exitFullScreen()
			}

			this.pumpKey++
			setTimeout(() => {
				this.init()
			}, 200)
		},
	},
}
</script>

<style lang="less" scoped>
.pump-house-lc {
	position: relative;
	width: 100%;
	height: 100%;
	.view-container {
		width: 100%;
		height: 100%;
	}
	.full-screen-btn {
		cursor: pointer;
		position: absolute;
		display: flex;
		align-items: center;
		justify-content: center;
		right: 20px;
		top: 20px;
		width: 40px;
		height: 40px;
		border-radius: 4px;
		background: rgba(0, 0, 0, 0.6);
		z-index: 9;
	}
	/deep/ .three-label {
		cursor: default;
		position: absolute;
		bottom: 0;
		transform: translate(-50%, 0);
		.three-label-inner {
			padding: 5px 10px;
			color: #fff;
			background: url('/images/labelbg01.png') no-repeat;
			background-size: 100% 100%;
			white-space: nowrap;
			.three-label-inner-body {
				display: none;
				font-size: 12px;
				.three-label-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-top: 6px;
					.label {
						min-width: 80px;
					}
					.value {
						text-align: right;
						margin-left: 10px;
						min-width: 50px;
					}
				}
			}
		}
		.three-label-line {
			position: relative;
			top: 0px;
			left: 50%;
			height: 50px;
			margin-left: -3px;

			width: 1px;
			background: rgb(28, 135, 197);
			&::after {
				position: absolute;
				bottom: -3px;
				margin-left: -3px;
				content: '';
				display: inline-block;
				width: 7px;
				height: 7px;
				border-radius: 50%;
				border: 1px solid rgb(28, 135, 197);
				background-color: #fff;
			}
		}
	}

	/deep/ .three-label-3d {
		width: 20px;
		height: 20px;
		background: url('~@/assets/image/light_point.png') no-repeat 0 0;
		background-size: 100% 100%;
		cursor: pointer;
	}
}
</style>
