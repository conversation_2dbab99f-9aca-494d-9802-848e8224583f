<!--
 * @Description: 报表管理
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-03-14 16:32:31
 * @LastEditors: houyan
 * @LastEditTime: 2024-08-08 14:50:29
-->
<template lang="pug">
.setting
	#pie(v-show='isShow')
	no-data(v-show='!isShow')
</template>
<script>
import * as echarts from 'echarts/core'
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { PieChart } from 'echarts/charts'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

echarts.use([TitleComponent, TooltipComponent, LegendComponent, Pie<PERSON>hart, CanvasRenderer, LabelLayout])
import { queryPushTotal } from '@/api/push-manage.js'
import NoData from '@/components/gc-no-data'

let myChart = null
export default {
	components: {
		NoData,
	},
	data() {
		return {
			isShow: false,
		}
	},
	methods: {
		_queryPushTotal() {
			queryPushTotal()
				.then(res => {
					const { result } = res
					if (result && result.length) {
						this.isShow = true
						const seriesData = result.map(item => {
							return {
								value: item.count,
								name: item.channelName,
							}
						})
						this.$nextTick(() => {
							this.initPie(seriesData)
						})
					}
				})
				.catch(error => {
					console.error(error)
				})
		},
		initPie(seriesData) {
			const chartDom = document.getElementById('pie')
			myChart = echarts.init(chartDom)
			const option = {
				title: {
					text: '推送统计',
					left: 'center',
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'vertical',
					left: 'left',
				},
				series: [
					{
						name: '',
						type: 'pie',
						radius: '50%',
						data: seriesData,
						emphasis: {
							itemStyle: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: 'rgba(0, 0, 0, 0.5)',
							},
						},
					},
				],
			}

			option && myChart.setOption(option)
		},
	},
	mounted() {
		this._queryPushTotal()
	},
}
</script>
<style lang="less" scoped>
.setting {
	height: 100%;
	padding: 16px 16px 0 16px;
	#pie {
		height: 100%;
	}
	.no-data {
		background-color: #fff !important;
		color: #706a6a;
	}
}
</style>
