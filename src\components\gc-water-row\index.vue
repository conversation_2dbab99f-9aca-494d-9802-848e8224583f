<!--
 * @Description: flex的容器
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-02-21 10:58:54
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-02-21 10:58:55
-->
<template>
	<div class="water-layout-flex" :style="style">
		<slot></slot>
	</div>
</template>

<script>
export default {
	name: 'water-row',
	components: {},
	props: {
		// row | row-reverse | column | column-reverse;
		direction: {
			type: String,
			default: 'row',
		},
		// nowrap | wrap | wrap-reverse;
		wrap: {
			type: String,
			default: 'nowrap',
		},
		// flex-start | flex-end | center | space-between | space-around;
		justify: {
			type: String,
			default: 'flex-start',
		},
		// flex-start | flex-end | center | baseline | stretch;
		align: {
			type: String,
			default: 'stretch',
		},
	},
	data() {
		return {
			style: {},
		}
	},
	methods: {
		initStyle() {
			this.style = {
				flexDirection: this.direction,
				flexWrap: this.wrap,
				justifyContent: this.justify,
				alignItems: this.align,
			}
		},
	},
	created() {
		this.initStyle()
	},
}
</script>
<style lang="less">
.water-layout-flex {
	display: flex;
}
</style>
