var VUE_PLAT_DOMAIN = 'http://water-plat.test.eslink.net.cn'
var VUE_PLAT_URL = VUE_PLAT_DOMAIN + '/api'
var VUE_PLAT_WS_URL = 'ws://water-plat.test.eslink.net.cn/api/websocket/'
var VUE_PLAT_GD_URL = VUE_PLAT_DOMAIN + '/proxy/gd'
var VUE_PLAT_EG_URL = VUE_PLAT_DOMAIN + '/proxy/eg'
var VUE_PLAT_DC_URL = VUE_PLAT_DOMAIN + '/proxy/dc'
var VUE_PLAT_DD_URL = VUE_PLAT_DOMAIN + '/proxy/dd'
var VUE_PLAT_DMA_URL = VUE_PLAT_DOMAIN + '/proxy/dma'
var VUE_PLAT_DP_URL = VUE_PLAT_DOMAIN + '/proxy/dp'
var VUE_PLAT_ETBC_URL = VUE_PLAT_DOMAIN + '/proxy/swetbc'
var VUE_PLAT_YBDD_URL = VUE_PLAT_DOMAIN + '/proxy/ybdd'

var VUE_EG_URL = 'http://secondsupply-pc.test.eslink.net.cn'
var VUE_GD_URL = 'http://workorder-pc.test.eslink.net.cn'

var VUE_ETBC_URL = 'http://swetbc.test.eslink.net.cn'
var VUE_ETBC_HOME_URL = 'http://swetbc-home-pc.test.eslink.net.cn/#/home'

var VUE_CLOUD_CALL_URL = 'http://cloudcall-pc.test.eslink.net.cn/#/serviceWindow?productType=2'

var VUE_DOMAIN_ALARM_URL = VUE_PLAT_DOMAIN + '/real-time-alarm'

// 通用
var VUE_AMAP_KEY = '95b533dc58b44f3cbae93cd9efff0858'

window._AMapSecurityConfig = {
    securityJsCode: '6171dc6a7993ecde4079b2646d36f5bb',
}