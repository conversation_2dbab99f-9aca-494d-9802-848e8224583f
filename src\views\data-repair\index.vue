<!--
 * @Description: 数据修复
 * @Author: shenxh
 * @Date: 2022-12-26 16:26:28
 * @LastEditors: shenxh
 * @LastEditTime: 2023-01-04 14:42:07
-->

<template lang="pug">
.data-repair
	.header-wrap
		es-header.header(title='数据修复')
	es-search.es-search(
		col='4',
		:show-collapse='false',
		:modules='moduleList',
		@on-search='handleSearchBtn',
		@on-reset='handleResetBtn'
	)
	water-row.table-btn-wrap(justify='space-between', align='center')
		i
		Button(type='primary', @click='handleCreate') 添加

	.data-repair-content
		es-table.data-repair-table(
			:columns='columns',
			:data='tableData',
			:loading='loading',
			border,
			showPage,
			:pageData='pageData',
			@on-page-num-change='handlePageNum',
			@on-page-size-change='handlePageSize'
		)
			template(slot-scope='{ row }', slot='type')
				span {{ getTypeLabel(row.type) }}
			template(slot-scope='{ row }', slot='dataTime')
				span {{ row.dataBeginTime + '~' + row.dateEndTime }}

	//- 创建系统弹窗
	create-system-popup(v-if='showModal', v-model='showModal', :data='currentRow', @submit-form='handleSubForm')
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import CreateSystemPopup from './components/CreateSystemPopup.vue'
import { getStationItemDataRepairTypes, getStationItemDataRepair } from '@/api/other'

export default {
	name: 'data-repair',
	components: {
		WaterRow,
		CreateSystemPopup,
	},
	props: {},
	data() {
		return {
			currentRow: {},
			showModal: false,
			loading: false,
			platformList: [],
			tableData: [],
			moduleList: [
				{
					label: '',
					value: '1',
					isDefault: true,
					keys: ['maintenanceDate', 'type', '', ''],
					model: {
						maintenanceDate: '',
						type: '',
					},
					data: [
						{
							type: 'date_picker',
							key: 'maintenanceDate',
							formItemProps: {
								label: '修复时间',
								prop: 'maintenanceDate',
								labelWidth: 80,
							},
							dataSourceList: [],
							widgetProps: {
								type: 'daterange',
								clearable: true,
								disabled: false,
							},
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
						{
							type: 2,
							key: 'type',
							formItemProps: {
								label: '修复类型',
								prop: 'type',
								labelWidth: 80,
							},
							dataSourceList: [],
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
					],
				},
			],
			columns: [
				// {
				// 	title: '序号',
				// 	type: 'index',
				// 	align: 'center',
				// 	width: 65,
				// },
				{
					title: '修复类型',
					slot: 'type',
				},
				{
					title: '修复目标',
					key: 'targetName',
				},
				{
					title: '数据来源目标',
					key: 'sourceName',
				},
				{
					title: '数据时间段',
					slot: 'dataTime',
				},
				{
					title: '修复人',
					key: 'createUser',
				},
				// {
				// 	title: '申请修复时间',
				// 	key: 'createTime',
				// },
				{
					title: '开始修复时间',
					key: 'createTime',
				},
				{
					title: '结束修复时间',
					key: 'endTime',
				},
				{
					title: '修复状态',
					key: 'states',
				},
				{
					title: '备注',
					key: 'memo',
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 50, 100],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {
		this.getStationItemDataRepairTypes()
	},
	beforeDestroy() {},
	methods: {
		// 搜索按钮
		handleSearchBtn() {
			this.pageData.current = 1

			this.getTableData()
		},

		// 重置按钮
		handleResetBtn() {
			this.pageData.current = 1

			this.getTableData()
		},

		// 添加按钮
		handleCreate() {
			this.currentRow = {}
			this.showModal = true
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum

			this.getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize

			this.getTableData()
		},

		// 弹窗按钮-保存
		handleSubForm(success) {
			if (success) {
				this.$Message.success('修复成功')

				this.getTableData()
			} else {
				this.$Message.error('修复失败')
			}
		},

		// 获取修复类型label
		getTypeLabel(code) {
			let label = ''
			let arr = this.moduleList[0].data[1].dataSourceList.filter(item => item.value === code)

			if (arr.length) {
				label = arr[0].label
			}

			return label
		},

		// 修复类型列表
		getStationItemDataRepairTypes() {
			getStationItemDataRepairTypes().then(res => {
				const { result = {} } = res
				const data = Object.entries(result)
				let arr = data.map(item => {
					return {
						label: item[1],
						value: item[0],
					}
				})

				this.moduleList[0].data[1].dataSourceList = arr

				this.getTableData()
			})
		},

		// 获取表格数据
		getTableData() {
			const formData = this.moduleList[0].model
			const { maintenanceDate = [], type } = formData
			const { current: pageNum, pageSize } = this.pageData
			const beginTime = maintenanceDate[0]
				? this.$moment(maintenanceDate[0].getTime()).format('YYYY-MM-DD HH:mm:ss')
				: ''
			const endTime = maintenanceDate[1]
				? this.$moment(maintenanceDate[1].getTime()).format('YYYY-MM-DD') + ' 23:59:59'
				: ''

			this.loading = true
			getStationItemDataRepair({
				beginTime,
				endTime,
				type,
				pageNum,
				pageSize,
			}).then(res => {
				const data = res.result
				const { list, total } = data

				if (list) {
					this.tableData = list
				}
				this.pageData.total = total
				this.loading = false
			})
		},
	},
}
</script>

<style lang="less" scoped>
.data-repair {
	width: 100%;
	height: 100%;
	padding: 0 16px;
	.es-search {
		padding-top: 4px;
		/deep/ .ivu-input-wrapper {
			overflow: hidden;
		}
		/deep/ .ivu-btn {
			background-color: #57a3f3;
			border-color: #57a3f3;
			color: #fff;
			padding: 0 10px;
			&:last-child {
				color: #515a6e;
				background-color: #fff;
				border-color: #dcdee2;
			}
			.ivu-icon {
				display: none;
			}
			span {
				margin: 0;
			}
		}
	}
	.table-btn-wrap {
		margin: 10px 0;
	}
	.data-repair-content {
		width: 100%;
		height: calc(100vh - 160px);
		.icon-eye {
			margin-left: 5px;
			cursor: pointer;
			color: #41a8ed;
		}
	}
}
</style>
