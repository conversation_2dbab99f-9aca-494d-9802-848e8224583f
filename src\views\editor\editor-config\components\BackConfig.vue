<template lang="pug">
.back-image-config
	.back-image-config-title 画布背景
	water-row.water-margin-top-8(justify='flex-start', align='center')
		ColorPicker(v-model='value.backgroundColor', @on-pick-clear='pickSuccess', @on-pick-success='pickSuccess')
		.image-picker(@click='handleOpenModal')
			Icon(type='ios-image', size='26')
			Icon(type='ios-arrow-down')
	//- water-row.water-margin-top-8(justify="space-between", align="center")
	//- 	.water-margin-right-4.text 画布宽度: {{ canvasWidth }}
	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.text 工艺刷新时间
		Input(v-model='value.refreshTime', style='width: 120px', :min='10')
			template(#append)
				div S
	water-row.water-margin-top-8(justify='space-between', align='center')
		.water-margin-right-4.text app默认旋转
		Select.water-margin-top-8(v-model='value.rotate', @on-change='handleChange("rotate")', style='width: 120px')
			Option(label='0°', :value='0')
			Option(label='90°', :value='90')
			Option(label='180°', :value='180')
	water-row.water-margin-top-8(justify='space-between', align='center')
		.water-margin-right-4.text app缩放
		i-switch(v-model='value.appZoomFlag', @on-change='handleChange("appZoomFlag")')
	water-row.water-margin-top-8(justify='space-between', align='center')
		.water-margin-right-4.text pc缩放
		i-switch(v-model='value.pcZoomFlag', disabled, @on-change='handleChange("pcZoomFlag")')
	.upload-image-modal(v-if='showModal')
		water-row(justify='space-between', align='center')
			//- Upload(
			//- 	multiple,
			//- 	accept="image/gif,image/jpeg,image/jpg,image/png",
			//- 	action="//jsonplaceholder.typicode.com/posts/",
			//- 	style="height: 32px"
			//- )
			Button(icon='ios-cloud-upload-outline', size='small', :disabled='currentIndex === null') 添加本地图片
				input(
					multiple,
					style='position: absolute; left: 36px; opacity: 0; height: 30px; width: 130px',
					type='file',
					@change='modelUpload($event)'
				)
			Button(icon='ios-folder-outline', size='small', @click='handleNewFloder') 新建文件夹
			Icon(type='md-close', size='20', style='cursor: pointer', @click='handleClose')
		.water-margin-top-4
			//- .water-margin-top-8 图片素材
			.content
				.file-list.water-margin-top-8(v-for='(item, index) in fileList', :key='index')
					.file-name(:class='{ active: currentIndex === index }', @click='handleSelected(index, item)')
						.file-name-text
							Icon(
								type='ios-arrow-down',
								size='16',
								:class='{ "active-icon": item.isOpen }',
								@click.stop='handleOpen(item)'
							)
							div(v-if='!item.isEdit') {{ item.name }}
							div(v-else, @click.stop='')
								Input(
									v-model='item.name',
									autofocus,
									:ref='`fileNameRefs${index}`',
									maxlength='32',
									@on-blur='handleEnter(item)',
									@on-enter='handleEnter(item)'
								)
						div(v-if='currentIndex === index && !item.isEdit', @click.prevent='')
							Icon(type='md-create', size='20', @click.stop='handleEdit(item, index)')
							Icon(type='md-trash', size='20', @click.stop='handleDelete(item)')
						//- div(v-if="currentIndex === index && item.isEdit")
						//- 	Icon(type="md-checkmark", size="20", @click.stop="handleEnter(item)")
						//- 	Icon(type="md-close", size="20", @click.stop="handleEnter(item)")
					.file-wrapper(:class='{ hidden: !item.isOpen }')
						.file-image-list
							.image(v-for='(image, k) in item.backgroundImageInfos', :key='k')
								Icon.arrow-down(type='ios-trash', size='20', @click='deleteImage(image)')
								img.file-image.water-margin-top-8(
									:class='{ active: imageActive === index + "_" + k }',
									:src='image.imageUrl',
									@click='pickImage($event.target, image, index, k)'
								)
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import { Input } from '@eslink/esvcp-pc-ui'
import { dirList, createDir, updateDir, deleteDir, deleteImage } from '@/api/editor'
export default {
	name: '',
	components: { WaterRow },
	props: {
		value: {
			type: Object,
			default: function () {
				return {}
			},
		},
		canvasHeight: {
			type: Number,
			default: 0,
		},
		canvasWidth: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			showModal: false,
			currentIndex: null,
			currentRow: {},
			fileList: [],
			imageActive: null,

			// 新建文件夹弹窗输入框对象
			newFileName: '',
		}
	},
	mounted() {
		this.currentIndex = null
		this.dirList()
	},
	methods: {
		// 修改属性
		handleChange() {},
		handleOpenModal() {
			this.showModal = true
		},
		// 选择颜色
		pickSuccess() {
			this.$emit('pickColor', this.value.backgroundColor)
		},
		handleSelected(index, item) {
			this.currentIndex = index
			this.currentRow = item
		},
		//编辑文件夹名
		handleEdit(item, index) {
			this.$set(item, 'isEdit', true)
			setTimeout(() => {
				this.$refs[`fileNameRefs${index}`][0].focus({
					cursor: 'end',
				})
			})
		},
		handleOpen(item) {
			this.$set(item, 'isOpen', !item.isOpen)
		},

		handleClose() {
			this.showModal = false
		},
		pickImage(target, item, index, k) {
			this.imageActive = index + '_' + k
			this.$emit('pickImage', item, target)
		},

		// API ===========
		//提交文件夹
		//新建文件夹
		handleNewFloder() {
			this.$Modal.confirm({
				title: '新建文件夹',
				render: h => {
					return h(Input, {
						props: {
							value: this.newFileName,
							clearable: true,
							autofocus: true,
							placeholder: '请输入文件夹名称',
							maxlength: '32',
						},
						nativeOn: {
							input: e => {
								this.newFileName = e.target.value
							},
						},
					})
				},
				onOk: () => {
					createDir({ name: this.newFileName }).then(res => {
						const { result } = res
						this.$Message.info('文件夹新增成功')
						this.fileList.unshift({
							id: result.id,
							name: this.newFileName,
							isChecked: false,
							isEdit: false,
							isOpen: false,
							imageList: [],
						})
						this.newFileName = ''
					})
				},
				onCancel: () => {
					this.newFileName = ''
				},
			})
		},
		dirList() {
			dirList().then(res => {
				const { result = [] } = res
				this.fileList = result
			})
		},
		// 删除文件
		handleDelete(item) {
			const { id } = item
			if (id) {
				this.$Modal.confirm({
					title: '提示',
					content: '确定要删除这条数据?',
					loading: true,
					onOk: () => {
						this.$Modal.remove()
						deleteDir({ id }).then(() => {
							this.$Message.info('删除成功')
							this.dirList()
						})
					},
				})
			}
		},
		// 删除图片
		deleteImage(item) {
			const { id } = item
			if (id) {
				this.$Modal.confirm({
					title: '提示',
					content: '确定要删除这张图片?',
					loading: true,
					onOk: () => {
						this.$Modal.remove()
						deleteImage({ id }).then(() => {
							this.$Message.info('删除成功')
							this.dirList()
						})
					},
				})
			}
		},
		handleEnter(item) {
			const { id, name } = item

			if (id) {
				updateDir({ name, id }).then(() => {
					this.$Message.info('修改成功')
					this.$set(item, 'isEdit', false)
				})
			}
		},
		modelUpload(event) {
			let file = event.target.files
			const { id } = this.currentRow
			let data = new FormData()
			data.append('dirId', id)
			for (let key in file) {
				if (key < file.length) {
					data.append('files', file[key])
				}
			}
			this.$axios.post('/backgroundImage/image/upload', data).then(() => {
				this.$Message.info('上传成功')
				this.dirList()
			})
		},
	},
	beforeDestroy() {
		this.showModal = false
	},
}
</script>
<style lang="less" scoped>
.back-image-config {
	position: relative;
	padding: 0 8px;
	&-title {
		color: #000;
		font-weight: bold;
	}
	.text {
		font-size: 14px;
	}
}
.content {
	height: 450px;
	overflow: auto;
}
.image-picker {
	border: 1px solid #d1d1de;
	border-radius: 4px;
	color: #535568;
	height: 32px;
	// line-height: 32px;
	padding: 0 8px;
	margin-left: 8px;
	cursor: pointer;
	display: flex;
	align-items: center;
}
::v-deep {
	.ivu-input-number {
		width: 56px;
		margin-top: 0;
	}
	.content {
		.ivu-input {
			background: transparent;
			height: 28px;
		}
	}
	.ivu-color-picker {
		.ivu-input {
			border: 1px solid #d1d1de;
			border-radius: 4px;
			height: 32px;
		}
	}
}
.upload-image-modal {
	position: fixed;
	top: 53px;
	right: 285px;
	width: 300px;
	height: 500px;
	background-color: #fff;
	border: 1px solid #e8e8e8;
	padding: 8px;
	z-index: 3;
	.file-list {
		.file-wrapper {
			max-height: 160px;
			overflow-y: auto;
			&.hidden {
				max-height: 0;
			}
		}
	}
	.file-name {
		display: flex;
		justify-content: space-between;
		align-items: center;
		cursor: pointer;
		&.active {
			background-color: #e8e8e8;
		}
		&-text {
			flex: 1;
			display: flex;
			align-items: center;
			height: 32px;
			font-size: 16px;
			line-height: 32px;
			i {
				transform: rotate(-90deg);
			}
			.active-icon {
				transform: rotate(0);
			}
		}
	}
	.file-image-list {
		display: flex;
		justify-content: space-around;
		align-items: flex-start;
		flex-wrap: wrap;
		.image {
			position: relative;
		}
		.ivu-icon-ios-trash {
			position: absolute;
			top: 8px;
			right: 2px;
			color: #409eff;
			cursor: pointer;
		}
		.file-image {
			width: 120px;
			height: 40px;
			border: 1px solid #333;
			cursor: pointer;
		}
		.active {
			border: 2px solid rgb(11, 112, 196);
		}
	}
}
</style>
<style>
.ivu-modal-confirm-head-icon {
	display: none;
}
.ivu-modal-confirm-head-title {
	margin-left: 0;
	margin-bottom: 10px;
}
</style>
