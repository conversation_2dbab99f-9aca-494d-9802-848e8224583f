<template lang="pug">
es-modal.es-modal(
	:transfer='false',
	:is-direct-close-modal='false',
	width='500',
	v-model='showModal',
	:title='formData && formData.id ? "编辑" : "添加"',
	@on-cancel='handleClose',
	@on-visible-change='changeModal'
)
	template(slot='body')
		.popup-content
			Form.form(ref='form', :model='formData', :rules='formRules', :label-width='80')
				FormItem(label='图例名称', prop='name')
					Input(v-model='formData.name', placeholder='请输入名称', maxlength='16')
				FormItem(label='图例', prop='mapUrl')
					Button(icon='ios-cloud-upload-outline') 添加图例
						input(
							style='position: absolute; left: 0; top: 0; cursor: pointer; font-size: 0; opacity: 0; height: 30px; width: 105px',
							type='file',
							@change='modelUpload($event)'
						)
				.demo-upload-list(v-if='formData.mapUrl')
					template
						img(:src='formData.mapUrl')
				div(style='width: 58px; height: 58px; line-height: 58px')
					Icon(type='camera', size='20')
	template(slot='footer')
		Button(@click='handleClose') 关闭
		Button(type='primary', @click='handleSubForm') 保存
</template>

<script>
import { addMapConfig, updateMapConfig } from '@/api/other'

export default {
	name: 'create-control-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {},
	props: {
		showModal: Boolean,
		data: Object,
	},
	data() {
		return {
			formData: {
				name: '',
				mapUrl: '',
			},
			formRules: {
				name: [
					{
						required: true,
						message: '请输入',
						trigger: 'change',
					},
				],
				mapUrl: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
					},
				],
			},
		}
	},
	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				this.formData = { ...this.data }
			} else {
				this.$refs.form.resetFields()
			}
		},
		// 上传图片
		modelUpload(event) {
			let file = event.target.files[0]
			if (!file) {
				return
			}
			const { type } = file
			if (!['image/jpeg', 'image/png', 'image/svg+xml'].includes(type)) {
				this.$Message.error('请选择正确的图例')
				return
			}
			let data = new FormData()

			data.append('files', file)
			this.$axios
				.post('/backgroundImage/image/upload', data)
				.then(res => {
					if (res.responseCode === '100000') {
						this.$set(this.formData, 'mapUrl', res.result[0].imageUrl)
						this.$Message.info('上传成功')
						this.$refs.form.validateField('mapUrl')
					} else {
						this.$Message.error(res.result)
					}
				})
				.catch(error => {
					this.$Message.error(`服务器异常：${error}`)
				})
		},
		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this._submitForm()
				}
			})
		},
		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},
		// 添加及编辑图例
		_submitForm() {
			const { id, name, mapUrl } = this.formData
			if (id) {
				updateMapConfig({
					id,
					name,
					mapUrl,
				}).then(() => {
					this.$Message.success('操作成功')

					this.handleClose()
					this.$emit('submit-form', this.formData)
				})
			} else {
				addMapConfig({
					name,
					mapUrl,
				}).then(() => {
					this.$Message.success('操作成功')

					this.handleClose()
					this.$emit('submit-form', this.formData)
				})
			}
		},
	},
}
</script>

<style lang="less" scoped>
.es-modal {
	.popup-content {
		width: 100%;
		padding: 0 40px;
		.form {
			width: 100%;
			/deep/ .ivu-form-item {
				margin-bottom: 20px;
			}
		}
	}
}
.demo-upload-list {
	display: inline-block;
	width: 60px;
	height: 60px;
	text-align: center;
	line-height: 60px;
	border: 1px solid transparent;
	border-radius: 4px;
	overflow: hidden;
	background: #fff;
	position: relative;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
	margin-left: 80px;
}
.demo-upload-list img {
	width: 100%;
	height: 100%;
}
</style>
