<!--
 * @Description: 原水-产水年度
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-31 11:00:24
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-24 17:18:48
-->
<template lang="pug">
.raw-fill 
	water-row(justify='flex-end', align='center')
		.raw-fill-title 年份:
		Select.raw-fill-select(v-model='yearId', placeholder='请选择', @on-change='changeYear')
			Option(v-for='(item, index) in yearList', :key='index', :disabled='item.disabled', :value='item.value') {{ item.label }}
		Button.water-margin-left-16(v-if='state === "fill"', type='primary', @click='handleSave()', :loading='buttonLoading') 提交
		Button.water-margin-left-16(v-if='state === "record"', type='primary', @click='handleQuery()') 查询
		Button.water-margin-left-16(v-if='state === "record"', type='primary', @click='handleExport()') 导出
	#qrcodeDowm(style='display: none')
	WaterTable.raw-fill-table(border, :columns='getColumns()', :data='tableData', :loading='loading')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { yearList } from '@/utils/enums.js'
import { EventBus } from '@/utils/eventBus.js'
import { exportFile } from '@/utils/function.js'
import { queryStatisticsData, insertStatisticsData } from '@/api/water-screen-data.js'
export default {
	components: { WaterTable, WaterRow },
	props: ['state'],
	data() {
		return {
			yearId: new Date().getFullYear() + '',
			buttonLoading: false,
			loading: false,
			tableData: [],
			yearList,
		}
	},
	mounted() {
		this.handleQuery()
		this.yearList.forEach(y => {
			if (y.value > this.yearId) {
				y.disabled = true
			}
		})
		EventBus.$on('fresh-raw-record', () => {
			this.state === 'record' && this.handleQuery()
		})
	},
	methods: {
		changeYear() {
			this.handleQuery()
		},
		handleQuery() {
			queryStatisticsData({
				date: this.yearId,
				type: 1,
			})
				.then(res => {
					const { result = [] } = res
					this.tableData = result.map(item => {
						return {
							...item,
							editable: this.state === 'fill',
						}
					})
				})
				.catch(() => {
					this.tableData = []
				})
		},
		//提交
		handleSave() {
			try {
				this.buttonLoading = true
				const list = []
				this.tableData.forEach(item => {
					const { stationName = '', waterSupplyNumPlan = '', sourceWaterNumPlan = '', stationId = '' } = item
					list.push({
						stationName,
						stationId,
						waterSupplyNumPlan,
						sourceWaterNumPlan,
					})
				})
				const params = {
					date: this.yearId,
					type: 1,
					list,
				}
				insertStatisticsData(params)
					.then(() => {
						this.$Message.success('提交成功!')
						this.buttonLoading = false
						this.handleQuery()
						EventBus.$emit('fresh-raw-record')
					})
					.catch(() => {
						this.buttonLoading = false
					})
			} catch {
				this.buttonLoading = false
			}
		},
		handleExport() {
			const baseUrl = `${location.origin}/api`
			let url = baseUrl + '/waterPlat/fillData/selectPowerConsumptionStatisticsExport?type=1&date=' + this.yearId
			exportFile(url)
		},
		getButtonDisable() {
			const year = new Date().getFullYear()
			if (Number(this.yearId) > year) {
				return true
			}
		},
		getColumns() {
			const columns = [
				{
					title: '名称',
					key: 'stationName',
					minWidth: 100,
					align: 'center',
				},
				{
					title: '原水指标（万m³）',
					key: 'sourceWaterNumPlan',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { sourceWaterNumPlan, editable } = params.row
						const disabled = this.getButtonDisable()
						return editable
							? h('Input', {
									props: {
										value: sourceWaterNumPlan,
										maxlength: 18,
										disabled,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'sourceWaterNumPlan')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', sourceWaterNumPlan)
					},
				},
				{
					title: '产水指标（万m³）',
					key: 'waterSupplyNumPlan',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { waterSupplyNumPlan, editable } = params.row
						const disabled = this.getButtonDisable()
						return editable
							? h('Input', {
									props: {
										value: waterSupplyNumPlan,
										maxlength: 18,
										disabled,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'waterSupplyNumPlan')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', waterSupplyNumPlan)
					},
				},
			]
			return columns
		},
		//正则
		valueChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			e.target.value = e.target.value.replace(/^(([0-9]{15}\d))(.\d{1,2})?$/, '')
		},
		//输入值
		handleInputValue(index, value, key) {
			this.tableData[index][key] = value
			// const item = this.tableData[index]
			// this.tableData.splice(index, 1, {
			// 	...item,
			// 	[key]: value,
			// })
		},
	},
}
</script>
<style lang="less" scoped>
.raw-fill {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-title {
		margin-right: 4px;
	}
	&-select {
		width: 240px;
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
}
</style>
