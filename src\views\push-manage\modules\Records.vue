<!--
 * @Description: 报表管理
 * @Version: 
 * @Autor: <PERSON><PERSON><PERSON>
 * @Date: 2024-03-14 16:32:31
 * @LastEditors: hmt
 * @LastEditTime: 2025-01-13 10:39:21
-->
<template lang="pug">
.setting
	water-row(justify='space-between', align='center')
		water-row(justify='flex-start', align='center')
			.query-title 推送时间:
			DatePicker(
				v-model='dateRange',
				:clearable='false',
				type='datetimerange',
				format='yyyy-MM-dd HH:mm:ss',
				placeholder='请选择',
				style='width: 350px'
			)
			Button.water-margin-left-8(type='primary', @click='_queryPushPage') 查询
	water-row
		Tabs(v-model='curTabs', @on-click='_queryPushPage')
			TabPane(v-for='item in tabList', :key='item.code', :label='item.name', :name='item.code')

	WaterTable.table-container.mt10(border, :columns='columns', :data='tableData', :loading='loading')
	Page(
		:total='pageTotal',
		show-elevator,
		show-sizer,
		@on-page-size-change='handleChangeSize',
		@on-change='handleChangePage'
	)
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import { queryPushPage, alarmNotifyInfoTypes } from '@/api/push-manage.js'
import WaterTable from '@/components/gc-water-table'
export default {
	components: {
		WaterRow,
		WaterTable,
	},
	data() {
		return {
			dateRange: [
				this.$moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
				this.$moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
			],
			columns: [
				{
					title: '站点名称',
					key: 'nodeName',
					width: 180,
					align: 'center',
				},
				{
					title: '推送渠道名称',
					key: 'channelName',
					width: 180,
					align: 'center',
				},
				{
					title: '推送信息',
					key: 'detail',
					align: 'center',
				},
				{
					title: '错误信息',
					key: 'error',
					width: 220,
					align: 'center',
				},
				{
					title: '推送时间',
					align: 'center',
					width: 180,
					key: 'createTime',
				},
				{
					title: '用户名称',
					align: 'center',
					width: 100,
					key: 'userName',
				},
				{
					title: '推送对象',
					align: 'center',
					width: 100,
					key: 'pushObject',
				},
				{
					title: '状态',
					key: 'status',
					align: 'center',
					width: 100,
					render: (h, params) => {
						return h(
							'Button',
							{
								style: {
									cursor: 'default',
									color:
										params.row.status == 'FAIL'
											? '#ca7cfc'
											: params.row.status == 'SUCCESS'
											? '#19be6b'
											: '',
									border:
										params.row.status == 'FAIL'
											? '1px solid #ca7cfc'
											: params.row.status == 'SUCCESS'
											? '1px solid #19be6b'
											: '',
								},
							},

							params.row.status == 'FAIL'
								? '失败'
								: params.row.status == 'SUCCESS'
								? '成功'
								: params.row.status == 'SEND'
								? '发送中'
								: '',
						)
					},
				},
			],
			tabList: [],
			curTabs: '',
			tableData: [],
			loading: false,
			pageSize: 10,
			pageNum: 1,
			pageTotal: 0,
		}
	},
	methods: {
		// 获取推送类型
		getPushList() {
			return new Promise(resolve => {
				alarmNotifyInfoTypes().then(res => {
					if (res.responseCode == '100000') {
						this.tabList = res.result
						this.tabList.unshift({ name: '全部', code: '' })
						resolve()
					}
				})
			})
		},
		_queryPushPage() {
			const beginTime = this.$moment(this.dateRange[0]).format('YYYY-MM-DD HH:mm:ss')
			const endTime = this.$moment(this.dateRange[1]).format('YYYY-MM-DD HH:mm:ss')
			console.log('传参', this.curTabs)
			const params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				beginTime,
				endTime,
				channelCode: this.curTabs == 0 ? '' : this.curTabs,
			}
			this.loading = true

			queryPushPage(params)
				.then(res => {
					const { result } = res
					const { list, total } = result

					this.loading = false
					this.tableData = list
					this.pageTotal = total
				})
				.catch(error => {
					console.error(error)
					this.loading = false
				})
		},
		handleChangePage(val) {
			this.pageNum = val
			this._queryPushPage()
		},
		handleChangeSize(val) {
			this.pageSize = val
			this._queryPushPage()
		},
	},
	async mounted() {
		await this.getPushList()
		this._queryPushPage()
	},
}
</script>
<style lang="less" scoped>
.setting {
	height: 100%;
	padding: 16px 16px 0 16px;
	display: flex;
	flex-direction: column;
	.query-title {
		white-space: nowrap;
		margin-right: 4px;
	}
	.table-container {
		flex: 1;
	}
	::v-deep {
		.mr10 {
			margin-right: 10px;
		}
	}
}
</style>
