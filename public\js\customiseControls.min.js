"use strict";(function(t){var e=t.fabric||(t.fabric={}),r="1.6.0",o=function(){return"undefined"!=typeof G_vmlCanvasManager},i=e.util.degreesToRadians,s={mt:0,tr:1,mr:2,br:3,mb:4,bl:5,ml:6,tl:7};r.localeCompare(t.fabric.version)>-1&&console.warn("this extension might not be fully compatible with your version of fabric.js ("+t.fabric.version+").Consider using the latest compatible build of fabric.js (> "+r+")"),e.util.object.extend(e.Object.prototype,{useCustomIcons:!1,cornerBackgroundColor:"transparent",cornerShape:"",cornerPadding:0,customiseCornerIcons:function(t,e){var r,o;for(r in t)t.hasOwnProperty(r)&&(o={},void 0!==t[r].cornerShape&&(this.cornerShape=t[r].cornerShape),void 0!==t[r].cornerBackgroundColor&&(this.cornerBackgroundColor=t[r].cornerBackgroundColor),void 0!==t[r].borderColor&&(this.borderColor=t[r].borderColor),void 0!==t[r].cornerSize&&(this.cornerSize=t[r].cornerSize),void 0!==t[r].cornerPadding&&(this.cornerPadding=t[r].cornerPadding),void 0===t[r].icon&&"settings"!==Object.keys(t)[0]||(this.useCustomIcons=!0,void 0!==t[r].settings&&(o.settings=t[r].settings),void 0!==t[r].icon&&(o.icon=t[r].icon,this.loadIcon(r,o,function(){e&&"function"==typeof e&&e()}))))},loadIcon:function(t,r,o){var i=this,s=new Image;s.onload=function(){i[t+"Icon"]=this,r.settings&&(i[t+"Settings"]=r.settings),o&&"function"==typeof o&&o()},s.onerror=function(){e.warn(this.src+" icon is not an image")},(r.icon.match(/^http[s]?:\/\//)||"//"===r.icon.substring(0,2))&&(s.crossOrigin="Anonymous"),s.src=r.icon},customizeCornerIcons:function(t){this.customiseCornerIcons(t)},drawControls:function(t){if(!this.hasControls)return this;var e,r=this._calculateCurrentDimensions(),o=r.x,i=r.y,s=this.cornerSize,n=-(o+s)/2,c=-(i+s)/2;return this.useCustomIcons?e="drawImage":(t.lineWidth=1,t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1,t.strokeStyle=t.fillStyle=this.cornerColor,this.transparentCorners||(t.strokeStyle=this.cornerStrokeColor),e=this.transparentCorners?"stroke":"fill"),t.save(),this._setLineDash(t,this.cornerDashArray,null),this._drawControl("tl",t,e,n,c,this.tlIcon,this.tlSettings),this._drawControl("tr",t,e,n+o,c,this.trIcon,this.trSettings),this._drawControl("bl",t,e,n,c+i,this.blIcon,this.blSettings),this._drawControl("br",t,e,n+o,c+i,this.brIcon,this.brSettings),this.get("lockUniScaling")||(this._drawControl("mt",t,e,n+o/2,c,this.mtIcon,this.mtSettings),this._drawControl("mb",t,e,n+o/2,c+i,this.mbIcon,this.mbSettings),this._drawControl("mr",t,e,n+o,c+i/2,this.mrIcon,this.mrSettings),this._drawControl("ml",t,e,n,c+i/2,this.mlIcon,this.mlSettings)),this.hasRotatingPoint&&this._drawControl("mtr",t,e,n+o/2,c-this.rotatingPointOffset,this.mtrIcon,this.mtrSettings),t.restore(),this},_drawControl:function(t,e,r,i,s,n,c){if(this.isControlVisible(t)){var a=this.cornerSize,h=this.cornerStrokeColor||"transparent",u=this.cornerBackgroundColor||"black",l=this.cornerShape||"rect",d="number"==typeof this.cornerPadding?this.cornerPadding:10;if(c&&(c.cornerSize&&(i=i+a/2-c.cornerSize/2,s=s+a/2-c.cornerSize/2,a=c.cornerSize),l=c.cornerShape||l,u=c.cornerBackgroundColor||u,d="number"==typeof c.cornerPadding?c.cornerPadding:d,h=c.cornerStrokeColor||h),this.useCustomIcons)if(l){switch(e.globalAlpha=1,e.fillStyle=u,e.lineWidth=1,e.strokeStyle=h,l){case"rect":break;case"circle":}void 0!==n&&e[r](n,i+d/2,s+d/2,a-d,a-d)}else void 0!==n&&e[r](n,i,s,a,a);else o()||this.transparentCorners||e.clearRect(i,s,a,a),e[r+"Rect"](i,s,a,a),!this.transparentCorners&&h&&e.strokeRect(i,s,a,a)}}}),e.util.object.extend(e.Canvas.prototype,{overwriteActions:!1,fixedCursors:!1,customiseControls:function(t){var e;for(e in t)t.hasOwnProperty(e)&&(void 0!==t[e].action&&(this.overwriteActions=!0,this.setCustomAction(e,t[e].action)),void 0!==t[e].cursor&&(this.fixedCursors=!0,this.setCustomCursor(e,t[e].cursor)))},setCustomAction:function(t,e){this[t+"Action"]=e},setCustomCursor:function(t,e){this[t+"cursorIcon"]=e},customizeControls:function(t){this.customiseControls(t)},_getActionFromCorner:function(t,e,r){if(!e)return"drag";if(e)if(this[e+"Action"]&&this.overwriteActions)switch(e){case"mtr":return this[e+"Action"]||"rotate";case"ml":case"mr":return r[this.altActionKey]?r[this.altActionKey]?"skewY":"scaleX":this[e+"Action"];case"mt":case"mb":return r[this.altActionKey]?r[this.altActionKey]?"skewY":"scaleY":this[e+"Action"];default:return this[e+"Action"]||"scale"}else switch(e){case"mtr":return"rotate";case"ml":case"mr":return r[this.altActionKey]?"skewY":"scaleX";case"mt":case"mb":return r[this.altActionKey]?"skewX":"scaleY";default:return"scale"}return!1},_setupCurrentTransform:function(t,e){if(e){var r=this.getPointer(t),o=e._findTargetCorner(this.getPointer(t,!0)),s=this._getActionFromCorner(e,o,t),n=this._getOriginFromCorner(e,o);"function"==typeof s&&(s.call(this,t,e),s="void"),this._currentTransform={target:e,action:s,corner:o,scaleX:e.scaleX,scaleY:e.scaleY,skewX:e.skewX,skewY:e.skewY,offsetX:r.x-e.left,offsetY:r.y-e.top,originX:n.x,originY:n.y,ex:r.x,ey:r.y,lastX:r.x,lastY:r.y,left:e.left,top:e.top,theta:i(e.angle),width:e.width*e.scaleX,mouseXSign:1,mouseYSign:1,shiftKey:t.shiftKey,altKey:t[this.centeredKey]},this._currentTransform.original={left:e.left,top:e.top,scaleX:e.scaleX,scaleY:e.scaleY,skewX:e.skewX,skewY:e.skewY,originX:n.x,originY:n.y},"remove"===s&&this._removeAction(t,e),"moveUp"===s&&this._moveLayerUpAction(t,e),"moveDown"===s&&this._moveLayerDownAction(t,e),"object"==typeof s&&"rotateByDegrees"===Object.keys(s)[0]&&this._rotateByDegrees(t,e,s.rotateByDegrees),this._resetCurrentTransform()}},_removeAction:function(t,e){var r=this;this.getActiveGroup()&&"undefined"!==this.getActiveGroup()?(this.getActiveGroup().forEachObject(function(t){t.off(),t.remove()}),this.discardActiveGroup(),setTimeout(function(){r.deactivateAll()},0)):(e.off(),e.remove(),setTimeout(function(){r.deactivateAll()},0))},_moveLayerUpAction:function(t,e){this.getActiveGroup()&&"undefined"!==this.getActiveGroup()?this.getActiveGroup().forEachObject(function(t){t.bringForward()}):e.bringForward()},_moveLayerDownAction:function(t,e){this.getActiveGroup()&&"undefined"!==this.getActiveGroup()?this.getActiveGroup().forEachObject(function(t){t.sendBackwards()}):e.sendBackwards()},_rotateByDegrees:function(t,e,r){var o=parseInt(e.getAngle())+r,i=!1;"center"===e.originX&&"center"===e.originY||!e.centeredRotation||(this._setOriginToCenter(e),i=!0),o=o>360?o-360:o,this.getActiveGroup()&&"undefined"!==this.getActiveGroup()?this.getActiveGroup().forEachObject(function(t){t.setAngle(o).setCoords()}):e.setAngle(o).setCoords(),i&&this._setCenterToOrigin(e),this.renderAll()},_setCornerCursor:function(t,e,r){var o=/\.(?:jpe?g|png|gif|jpg|jpeg|svg)$/;if(this.fixedCursors&&this[t+"cursorIcon"])this[t+"cursorIcon"].match(o)?this.setCursor("url("+this[t+"cursorIcon"]+"), auto"):"resize"===this[t+"cursorIcon"]?this.setCursor(this._getRotatedCornerCursor(t,e,r)):this.setCursor(this[t+"cursorIcon"]);else if(t in s)this.setCursor(this._getRotatedCornerCursor(t,e,r));else{if("mtr"!==t||!e.hasRotatingPoint)return this.setCursor(this.defaultCursor),!1;this.setCursor(this.rotationCursor)}return!1}}),"undefined"!=typeof exports&&(module.exports=this)})(window);