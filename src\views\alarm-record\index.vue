<!--
 * @Description: 告警记录
 * @Autor: Fengjialing
 * @Date: 2025-04-17 16:57:10
 * @LastEditors: Fengjialing
 * @LastEditTime: 2025-04-17 10:36:00
-->
<template>
	<div class="alarm-record-container">
		<div class="container-header">
			<GcSearch @search="handleSearch" @reset="handleReset" ref="searchRef"></GcSearch>
		</div>
		<div class="table-container">
			<Table border highlight-row :columns="columns" :data="tableData" @on-row-click="handleSelect" height="800">
				<template slot-scope="{ row }" slot="option">
					<div class="options-container">
						<Button type="text" :style="{ color: '#3AA7D8' }" size="small" @click="handleShowDetail(row)">
							详情
						</Button>
						<Button
							type="text"
							:style="{ color: '#3AA7D8' }"
							:disabled="row.disposeStatus == '10'"
							size="small"
							@click="handleShowDialog(row)"
						>
							处理登记
						</Button>
					</div>
				</template>
			</Table>
			<div class="page-wrap">
				<Page
					:total="pageParams.total"
					:current="pageParams.pageNum"
					:page-size="pageParams.pageSize"
					size="small"
					show-sizer
					show-total
					@on-page-size-change="onPageSizeChange"
					@on-change="changePage"
				/>
			</div>
		</div>
		<RegistrationDialog
			v-model="showDialog"
			title="告警处理登记"
			@update="handleUpdate"
			:id="alarmId"
		></RegistrationDialog>
	</div>
</template>
<script>
import RegistrationDialog from './dialog/index.vue'
import GcSearch from './gc-search/index.vue'
import { alarmRecordMixin } from './mixin.js'

export default {
  components: { RegistrationDialog, GcSearch },
  mixins: [alarmRecordMixin],
  data() {
    return {
      pageParams: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
      },
      columns: [
        {
          title: '告警设备',
          key: 'nodeCode',
          align: 'center',
        },
        {
          title: '设备类型',
          key: 'nodeType',
          align: 'center',
        },
        {
          title: '告警类型',
          key: 'alarmTypeName',
          align: 'center',
        },
        {
          title: '告警等级',
          key: 'alarmLevelName',
          align: 'center',
        },
        {
          title: '告警值',
          key: 'alarmValue',
          align: 'center',
        },
        {
          title: '设定值',
          key: 'alarmLimit',
          align: 'center',
        },
        {
          title: '开始时间',
          key: 'beginTime',
          align: 'center',
        },
        {
          title: '最后报警时间',
          key: 'lastActiveTime',
          align: 'center',
        },
        {
          title: '处理状态',
          key: 'disposeStatusDesc',
          align: 'center',
        },
        {
          title: '操作',
          slot: 'option',
          align: 'center',
          width: '160px',
        },
      ],
    }
  },
}
</script>
<style lang="less" scoped>
.alarm-record-container {
	height: 100%;
	padding: 10px;
	.container-header {
		height: 50px;
	}
	.table-container {
		height: calc(100% - 50px);
		width: 100%;
		.options-container {
			display: flex;
		}
	}
}
</style>