<template lang="pug">
.warn-list
	es-search.es-search(
		col='4',
		:show-collapse='false',
		:modules='moduleList',
		@on-search='handleSearchBtn',
		@on-reset='handleResetBtn'
	)

	Button(@click='handleCreate()', size='small', type='primary', marginLeft='160px', style='margin-left: 15px') 新增
	Button(
		@click='handleUpdate()',
		size='small',
		type='primary',
		marginLeft='160px',
		:disabled='selectedRows.length !== 1',
		style='margin-left: 15px'
	) 编辑
	Button(@click='handleBatchClearBtn()', size='small', marginLeft='160px', style='margin-left: 15px') 批量删除

	.warn-list-content
		es-table.water-table.warn-list-table(
			:columns='columns',
			:data='tableData',
			:loading='loading',
			border,
			showPage,
			:pageData='pageData',
			@on-selection-change='handleSelectChange',
			@on-page-num-change='handlePageNum',
			@on-page-size-change='handlePageSize'
		)
			template(slot-scope='{ row }', slot='sysName')
				span {{ getSysName(row.type) }}
	//- 弹窗
	EsConfirmModal(
		v-model='showModal',
		title='提示',
		content='是否删除选中项',
		@on-ok='handleModalSub',
		@on-cancel='deleteShow = false'
	)
		//- 创建系统弹窗
	CreateItemPopup(:show.sync='modalShow', @initList='_getTableData', ref='itemPopup')
</template>
<script>
import { EsConfirmModal } from '@eslink/esvcp-pc-ui'
import { querySysList } from '@/api/common.js'
import { deleteByBatch, queryPage } from '@/api/base-item.js'
import CreateItemPopup from '@/views/base-item/item-list/components/create-item-popup'
export default {
	components: {
		CreateItemPopup,
		EsConfirmModal,
	},
	data() {
		return {
			handleVal: '',
			tabData: {},
			tabList: [],
			formDataFormat: {},
			isSearch: false,
			rowData: {},
			showModal: false,
			modalShow: false,
			loading: false,
			tableData: [],
			selectedRows: [],
			alarmTypes: [],
			sysCodes: [],
			moduleList: [
				{
					label: '',
					value: '1',
					isDefault: true,
					keys: ['baseItemCode', 'type', 'baseItemName'],
					model: {
						baseItemCode: '',
						type: '',
						baseItemName: '',
					},
					data: [
						{
							type: 1,
							key: 'baseItemName',
							formItemProps: {
								label: '数据项名称',
								prop: 'baseItemName',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
						},
						{
							type: 1,
							key: 'baseItemCode',
							formItemProps: {
								label: '数据项编号',
								prop: 'baseItemCode',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
						},
						{
							type: 2,
							key: 'type',
							formItemProps: {
								label: '所属模块',
								prop: 'type',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
					],
				},
			],
			columns: [
				{
					type: 'selection',

					align: 'center',
				},
				{
					title: '编号',
					type: 'index',
					align: 'center',
				},
				{
					title: '数据项名称',
					align: 'center',
					key: 'baseItemName',
				},
				{
					title: '数据项编号',
					align: 'center',
					key: 'baseItemCode',
				},

				{
					title: '所属模块',
					slot: 'sysName',
					align: 'center',
				},
				{
					title: '单位',
					align: 'center',
					key: 'unit',
				},
				{
					title: '有效位数',
					align: 'center',
					key: 'precision',
				},
				{
					title: '创建时间',
					key: 'createTime',
					align: 'center',
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 30, 40, 50],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	computed: {},
	created() {
		querySysList().then(res => {
			this.sysCodes = res.result
			const data = res.result
			if (data && data.length) {
				this.moduleList[0].data[2].dataSourceList = data.map(item => {
					return {
						label: item.name,
						value: item.code,
					}
				})
			}
		})
		this._getTableData()
	},
	mounted() {
		this._getTableData()
	},
	methods: {
		getSysName(sysCode) {
			let name = ''
			for (let i = 0; i < this.sysCodes.length; i++) {
				if (this.sysCodes[i].code === sysCode) {
					name = this.sysCodes[i].name
				}
			}

			return name
		},
		handleCreate() {
			this.modalShow = true
		},
		handleUpdate() {
			const data = JSON.parse(JSON.stringify(this.selectedRows[0]))
			this.$refs.itemPopup.setData(data)
			this.modalShow = true
		},
		// 搜索按钮
		handleSearchBtn() {
			// this.isSearch = true
			this._getTableData()
		},

		// 重置按钮
		handleResetBtn() {
			this.pageData.current = 1

			this._getTableData()
		},

		// 已选行
		handleSelectChange(selectedRows) {
			this.selectedRows = selectedRows
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum
			// this.isSearch = true
			this._getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize
			// this.isSearch = true
			this._getTableData()
		},

		// 批量消缺按钮
		handleBatchClearBtn() {
			if (!this.selectedRows.length) {
				this.$Message.warning('请勾选表格数据')
				return
			}

			this.handleVal = ''
			this.showModal = true
		},

		// 弹窗提交按钮
		handleModalSub() {
			const idList = this.selectedRows.map(item => {
				return item.id
			})

			this._deleteByBatch(idList)
		},

		// 批量删除
		_deleteByBatch(idList) {
			deleteByBatch(idList).then(res => {
				console.log(res)
				this.$Message.success('操作成功')

				this.showModal = false

				this._getTableData()
			})
		},

		// 报警查询
		_getTableData() {
			const formData = this.moduleList[0].model
			formData.pageSize = this.pageData.pageSize
			formData.pageNum = this.pageData.current
			this.loading = true
			queryPage(formData).then(res => {
				const { list: data, total } = res.result

				if (data) {
					this.tableData = data
				}
				this.pageData.total = total
				this.loading = false
			})
		},
	},
}
</script>
<style scoped lang="less">
.warn-list {
	width: 100%;
	height: 100%;
	.es-search {
		/deep/ .ivu-input-wrapper {
			overflow: hidden;
		}
	}
	&-content {
		padding-top: 8px;
		height: calc(100vh - 130px);
		.status-btn {
			cursor: default;
			&.warning {
				color: #ca7cfc;
				border: 1px solid #ca7cfc;
			}
			&.success {
				color: #19be6b;
				border: 1px solid #19be6b;
			}
			&.primary {
				color: #3aa7d8;
				border: 1px solid #3aa7d8;
			}
		}
	}
	&-table {
		width: 100%;
	}
	&-form-item {
		width: fit-content;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-right: 16px;
		.title {
			margin-right: 4px;
		}
		.select {
			width: 140px;
		}
	}
	.data-info {
		display: flex;
		align-items: center;
		margin-right: 20px;
		.group {
			display: flex;
			align-items: center;
			color: #535567;
			font-size: 14px;
			margin-left: 10px;
			.value {
				&.active {
					color: #ca7cfc;
				}
			}
		}
	}
	.void {
		border: 1px solid #3aa7d8;
		color: #3aa7d8;
		background-color: #fff;
	}
}
</style>
