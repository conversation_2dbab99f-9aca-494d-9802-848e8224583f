<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-30 16:35:42
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-24 17:18:28
-->
<template lang="pug">
.fill-list
	water-row(justify='flex-start', align='center')
		.fill-list-form-title 月份:
		DatePicker.water-margin-right-16(
			:editable='false',
			:clearable='false',
			v-model='date',
			format='yyyy-MM',
			type='month',
			:options='options',
			style='width: 215px',
			@on-change='handleQuery()',
			placement='bottom-end'
		)
	WaterTable.fill-list-table(border, :columns='columns', :data='tableData', :loading='loading')
	.fill-list-footer.water-margin-top-16
		Button(type='primary', icon='ios-add-circle-outline', @click='handleAddClick()') 新增数据项
		Button.water-margin-left-16(type='primary', @click='handleSave()', :loading='buttonLoading') 提交
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { queryEgFillDataRecord, queryEgSelectArea, insertEgFillDataRecord } from '@/api/water-screen-data.js'
import { EventBus } from '@/utils/eventBus.js'
export default {
	components: {
		WaterTable,
		WaterRow,
	},
	data() {
		return {
			date: new Date(`${new Date().getFullYear()}-${new Date().getMonth() + 1}`),
			tableData: [],
			columns: [],
			loading: false,
			buttonLoading: false,
			itemIds: [],
			editArr: [],
			options: {
				disabledDate(date) {
					return date.getTime() > new Date().getTime()
				},
			},
		}
	},
	mounted() {
		this.queryEgSelectArea()
		this.handleQuery()
	},
	methods: {
		queryEgSelectArea() {
			queryEgSelectArea().then(res => {
				const { result = [] } = res
				const itemList = result.map(item => {
					return {
						label: item.name,
						value: item.id + '',
					}
				})
				this.getColumns(itemList)
			})
		},
		handleQuery() {
			queryEgFillDataRecord({
				date: this.$moment(this.date).format('YYYY-MM'),
			}).then(res => {
				const { result = [] } = res
				const hasItemIds = []
				this.tableData = result.map(item => {
					hasItemIds.push(item.stationId + '')
					return {
						...item,
						stationId: item.stationId + '',
						editable: true,
					}
				})
				this.itemIds = [].concat(hasItemIds)
			})
		},
		//保存数据项
		handleSave() {
			try {
				this.buttonLoading = true
				const list = []
				this.tableData.forEach(item => {
					const {
						stationId = '',
						stationName = '',
						supplyCommunityNum = '',
						supplyPopulationNum = '',
						supplyUserNum = '',
						waterQualityRate = '',
						integrityRate = '',
					} = item
					if (stationId) {
						list.push({
							stationId,
							stationName,
							supplyCommunityNum,
							supplyPopulationNum,
							supplyUserNum,
							waterQualityRate,
							integrityRate,
						})
					}
				})
				if (!list.length) {
					this.buttonLoading = false
					return false
				}
				const params = {
					date: this.$moment(this.date).format('YYYY-MM'),
					list,
				}
				insertEgFillDataRecord(params)
					.then(() => {
						this.$Message.success('提交成功!')
						this.buttonLoading = false
						this.handleQuery()
						EventBus.$emit('fresh-eg-record')
					})
					.catch(() => {
						this.buttonLoading = false
					})
			} catch {
				this.buttonLoading = false
			}
		},
		//增加数据项
		handleAddClick() {
			const key = this.tableData.length + 1
			this.editArr.push(key)
			this.tableData.push({
				key,
				stationId: '',
				waterQualityRate: '',
				supplyCommunityNum: '',
				supplyUserNum: '',
				supplyPopulationNum: '',
				integrityRate: '',
				editable: true,
			})
		},
		getColumns(itemList) {
			const columns = [
				{
					title: '区域',
					key: 'stationId',
					align: 'center',
					minWidth: 150,
					render: (h, params) => {
						const { stationId, editable, stationName } = params.row
						return editable
							? h(
									'Select',
									{
										props: {
											value: stationId,
											'label-in-value': true,
											clearable: true,
											placement: this.tableData.length > 9 ? 'top' : 'bottom',
										},
										on: {
											'on-change': e => {
												e && this.handleSelectstationName(e, params.index, itemList)
											},
										},
									},
									itemList.map(item => {
										return h('Option', {
											props: {
												value: item.value,
												label: item.label,
												disabled: this.itemIds.includes(item.value),
											},
										})
									}),
							  )
							: h('span', stationName)
					},
				},
				{
					title: '服务小区数（个）',
					key: 'supplyCommunityNum',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { supplyCommunityNum, editable } = params.row
						return editable
							? h('Input', {
									props: {
										value: supplyCommunityNum,
										maxlength: 18,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'supplyCommunityNum')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', supplyCommunityNum)
					},
				},
				{
					title: '供水户数（户）',
					key: 'supplyUserNum',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { supplyUserNum, editable } = params.row
						return editable
							? h('Input', {
									props: {
										value: supplyUserNum,
										maxlength: 18,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'supplyUserNum')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', supplyUserNum)
					},
				},
				{
					title: '服务人口（人）',
					key: 'supplyPopulationNum',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { supplyPopulationNum, editable } = params.row
						return editable
							? h('Input', {
									props: {
										value: supplyPopulationNum,
										maxlength: 18,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'supplyPopulationNum')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', supplyPopulationNum)
					},
				},
				{
					title: '水质合格率',
					key: 'waterQualityRate',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { waterQualityRate, editable } = params.row
						return editable
							? h(
									'div',
									{
										style: {
											display: 'flex',
											justifyContent: 'flex-start',
											alignItems: 'center',
										},
									},
									[
										h('Input', {
											props: {
												value: waterQualityRate,
												maxlength: 6,
											},
											on: {
												'on-change': e => {
													this.valueRateChange(e)
													const value = e.target.value
													this.handleInputValue(params.index, value, 'waterQualityRate')
												},
												'on-keyup': e => {
													this.valueRateChange(e)
												},
											},
										}),
										h('span', { style: { marginLeft: '4px' } }, '%'),
									],
							  )
							: h('span', waterQualityRate + '%')
					},
				},
				{
					title: '设备完好率',
					key: 'integrityRate',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { integrityRate, editable } = params.row
						return editable
							? h(
									'div',
									{
										style: {
											display: 'flex',
											justifyContent: 'flex-start',
											alignItems: 'center',
										},
									},
									[
										h('Input', {
											props: {
												value: integrityRate,
												maxlength: 6,
											},
											on: {
												'on-change': e => {
													this.valueRateChange(e)
													const value = e.target.value
													this.handleInputValue(params.index, value, 'integrityRate')
												},
												'on-keyup': e => {
													this.valueRateChange(e)
												},
											},
										}),
										h('span', { style: { marginLeft: '4px' } }, '%'),
									],
							  )
							: h('span', integrityRate)
					},
				},
			]
			this.columns = columns
		},
		//输入数据项名称
		handleSelectstationName(e, rowIndex) {
			if (!this.itemIds.includes(e.value)) {
				this.itemIds.push(e.value)
			}

			this.handleInputValue(rowIndex, e.value, 'stationId')
			this.handleInputValue(rowIndex, e.label, 'stationName')
		},
		//正则
		valueChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// // 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d{0}).*$/, '$1$2')
			e.target.value = e.target.value.replace(/^(([1-9]{6}\d))?$/, '')
		},
		valueRateChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			e.target.value = e.target.value.replace(/^(([0-9]{3}\d))(.\d{1,2})?$/, '')
			if (e.target.value > 100) {
				e.target.value = 100
			}
		},
		//输入值
		handleInputValue(index, value, key) {
			const item = this.tableData[index]
			this.tableData.splice(index, 1, {
				...item,
				[key]: value,
			})
			// this.tableData[index][key] = value
		},
	},
}
</script>
<style scoped lang="less">
.fill-list {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-form {
		margin-right: 24px;
		&-title {
			margin-right: 4px;
		}
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
}
</style>
