<template lang="pug">
.collect-road-config
	.header-wrap
		es-header.header(title='短信配置')

	.table-wrap
		es-table(:columns='columns', :data='tableData', :loading='loading', border, :showPage='false')
			template(slot-scope='{ row }', slot='content')
				div(v-if='!row.ifEdit && row.type !== 3') {{ tableDataVal[row.key] }}
				//- Tag(
				//- 	v-if="!row.ifEdit && row.type == 3",
				//- 	color="green",
				//- 	v-for="item in tableDataVal.smsSystems",
				//- 	:key="item.id") {{ item.name }}
				Input(v-if='row.ifEdit && row.type == 1', v-model='tableDataVal[row.key]', placeholder='请输入')
				Input(type='number', v-if='row.ifEdit && row.type == 2', v-model='tableDataVal[row.key]')
				Select(:disabled='!row.ifEdit', v-if='row.type == 3', v-model='tableDataVal.smsSystemsVal', multiple)
					Option(v-for='(item, index) in sysList', :key='index', :value='item.id') {{ item.name }}

			template(slot-scope='{ row }', slot='action')
				Button.mar-r5(type='primary', @click='goEditFn(row)', size='small', v-if='!row.ifEdit') 编辑
				Button.mar-r5(v-if='row.ifEdit', type='primary', @click='saveSub(row)', size='small') 保存
				Button(v-if='row.ifEdit', @click='cancelEdit(row)', size='small') 取消
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import { smsConfigInfo, insertOrUpdate } from '@/api/setting.js'
import { querySysList } from '@/api/common.js'

export default {
	name: 'collect-road-config',
	components: {
		WaterRow,
	},
	props: {},
	data() {
		return {
			ifEditIng: false,
			popupType: 0,
			showModal: false,
			loading: false,
			currentRow: {},
			tableData: [
				{ param: 'ip', key: 'ip', ifEdit: false, type: 1 },
				{ param: '端口', key: 'port', ifEdit: false, type: 1 },
				{ param: '版本', key: 'version', ifEdit: false, type: 1 },
				{ param: '用户名', key: 'username', ifEdit: false, type: 1 },
				{ param: '密钥', key: 'password', ifEdit: false, type: 1 },
				{ param: '签名', key: 'autograph', ifEdit: false, type: 1 },
				{ param: '短信模版标题', key: 'title', ifEdit: false, type: 1 },
				{
					param: '短信模版内容',
					key: 'content',
					ifEdit: false,
					type: 1,
				},
				{
					param: '每日短信额定数量',
					key: 'dayLimit',
					ifEdit: false,
					type: 2,
				},
				{
					param: '应用开放',
					key: 'smsSystemsVal',
					ifEdit: false,
					type: 3,
				},
			],
			originTableDataVal: {},
			tableDataVal: {
				ip: '',
				port: '',
				version: '',
				username: '',
				password: '',
				autograph: '',
				title: '',
				content: '',
				dayLimit: '',
				smsSystemsVal: [],
			},
			columns: [
				{
					title: '参数',
					key: 'param',
				},
				{
					title: '参数内容',
					slot: 'content',
				},

				{
					title: '操作',
					slot: 'action',
					align: 'center',
				},
			],
			sysList: [],
		}
	},
	computed: {},
	watch: {},
	created() {
		this.getSysList()
		this.getTableData()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 获取表格数据
		getTableData() {
			this.loading = true
			smsConfigInfo().then(res => {
				console.log('配置数据', res)
				if (res.result) {
					res.result.smsSystemsVal = res.result.smsSystems.map(
						ele => ele.sysCode + (ele.subSysCode ? ele.subSysCode : ''),
					)
					this.originTableDataVal = JSON.parse(JSON.stringify(res.result))
					this.tableDataVal = res.result
				}

				this.loading = false
			})
		},
		// 获取平台列表
		getSysList() {
			return new Promise(resolve => {
				querySysList({ subSystem: true }).then(res => {
					this.sysList = res.result.map(ele => {
						return {
							id: ele.code + (ele.subCode ? ele.subCode : ''),
							name: ele.name,
							sysCode: ele.code,
							subSysCode: ele.subCode ? ele.subCode : '',
						}
					})
					resolve()
				})
			})
		},
		// 去编辑
		goEditFn(row) {
			console.log('编辑前', this.ifEditIng)
			if (!this.ifEditIng) {
				row.ifEdit = true
				this.ifEditIng = true
			} else {
				this.$Message.info('请先完成正在编辑项!')
			}
			// this.$nextTick(() => {
			// 	console.log('长度', this.tableData)
			// 	let len = this.tableData.filter(ele => !ele.ifEdit)
			// 	if (len > 0) {
			// 	} else {
			// 		row.ifEdit = true
			// 	}
			// })
		},

		// 取消编辑按钮
		cancelEdit(row) {
			this.ifEditIng = false
			row.ifEdit = false
			this.tableDataVal[row.key] = this.originTableDataVal[row.key]
		},
		// 保存编辑
		saveSub(row) {
			if (this.tableDataVal[row.key] == '' || this.tableDataVal[row.key]?.length == 0) {
				this.$Message.info('编辑项不能为空!')
				return
			}
			if (!this.tableDataVal[row.key]) {
				this.$Message.info('编辑项不能为空!')
				return
			}

			let platform = this.sysList
				.filter(ele => this.tableDataVal.smsSystemsVal.indexOf(ele.id) > -1)
				.map(ol => {
					console.log('0l', ol)
					return {
						name: ol.name,
						sysCode: ol.sysCode,
						subSysCode: ol.subSysCode,
					}
				})

			let params = {
				...this.tableDataVal,
				smsSystems: platform,
			}
			insertOrUpdate(params).then(res => {
				if (res.responseCode == '100000') {
					this.$Message.success('保存成功')
					this.getTableData()
					row.ifEdit = false
					this.ifEditIng = false
				}
			})
		},
		// 搜索按钮
		handleSearchBtn(params) {
			this.form = params

			this.getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize

			this.getTableData()
		},

		// 行-编辑
		handleRowUpd(row) {
			this.currentRow = row
			this.popupType = 1
			this.showModal = true
		},

		getText(str = '') {
			return str.replace(/\n/g, '<br>')
		},
	},
}
</script>

<style lang="less" scoped>
.collect-road-config {
	padding: 0 16px;
	.mar-r5 {
		margin-right: 5px;
	}

	.table-btn-wrap {
		margin-bottom: 8px;
	}
	.table-wrap {
		width: 100%;
		height: calc(100vh - 150px);
	}
}
</style>
