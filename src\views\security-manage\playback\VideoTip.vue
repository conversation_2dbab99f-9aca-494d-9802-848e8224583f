<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-04-08 14:43:06
 * @LastEditors: shenxh
 * @LastEditTime: 2022-04-13 17:13:39
-->

<template lang="pug">
.video-tip
	.tip-content
		.tit 视频插件加载失败
		p 若视频未正常打开，请点击此处
			a.click-btn(
				referrerpolicy='no-referrer',
				href='https://eslink-iot.oss-cn-beijing.aliyuncs.com/20220413093005247_VideoWebPlugin.exe',
				download='VideoWebPlugin.exe',
				target='_blank'
			) 下载
			span 并安装，并重启浏览器（推荐使用Chrome、IE11及以上版本）
		p 若已经安装过插件，请启动程序，并刷新当前页面。
</template>

<style lang="less" scoped>
.video-tip {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;
	color: #000;
	background-color: #fff;
	font-size: 16px;
	.tip-content {
		flex: 1;
		// height: 100%;
		line-height: 24px;
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: flex-start;
		padding: 40px 0;
		margin-top: -100px;
		.tit {
			font-size: 20px;
			margin-bottom: 30px;
		}
		.click-btn {
			color: #409eff;
			cursor: pointer;
			pointer-events: initial;
		}
	}
}
</style>
