/**
 * @description: 进入全屏
 * @param {*} ele
 * @return {*}
 */
export function fullScreen(ele = document.body) {
	if (ele.requestFullscreen) {
		ele.requestFullscreen()
	} else if (ele.mozRequestFullScreen) {
		ele.mozRequestFullScreen()
	} else if (ele.webkitRequestFullscreen) {
		ele.webkitRequestFullscreen()
	} else if (ele.msRequestFullscreen) {
		ele.msRequestFullscreen()
	}
}

/**
 * @description: 退出全屏
 * @return {*}
 */
export function exitFullScreen() {
	if (document.exitFullScreen) {
		document.exitFullScreen()
	} else if (document.mozCancelFullScreen) {
		document.mozCancelFullScreen()
	} else if (document.webkitExitFullscreen) {
		document.webkitExitFullscreen()
	} else if (document.msExitFullscreen) {
		document.msExitFullscreen()
	}
}

/**
 * @description: 当前是否全屏
 * @return {*}
 */
export function isFullScreen() {
	return !!(
		document.fullscreen ||
		document.mozFullScreen ||
		document.webkitIsFullScreen ||
		document.webkitFullScreen ||
		document.msFullScreen
	)
}

/**
 * @description: 生成唯一标识
 * @return {String}
 */
export const uuid = () => {
	// 标准的UUID格式为：xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx (8-4-4-4-12)
	return 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		let r = (Math.random() * 16) | 0,
			v = c == 'x' ? r : (r & 0x3) | 0x8
		return v.toString(16)
	})
}

//  判断是否为空
export function isBlank(value) {
	return (
		['', null, undefined].includes(value) ||
		value.toString().trim() === '' ||
		value.toString().toLocaleLowerCase().trim() === 'null'
	)
}

// 判断是否是字符串
export function isString(value) {
	return typeof value === 'string' || value instanceof String
}