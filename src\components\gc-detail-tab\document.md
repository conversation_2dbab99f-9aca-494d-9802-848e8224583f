# gcCloudTab 简单使用教学

<gc-detail-tab :tabList="tabList"></gc-detail-tab>

## tabLIst 示例如下

## list 内的 component 的参数需要引入组件,name 的值需要与 component 的命名保持一致

# import basicInfo from "./detailPages/basicInfo";

# import orgStruct from "./detailPages/orgStruct";

# import rightAllocation from "./detailPages/rightAllocation";

# import meterType from "./detailPages/meterType";

# ...

##

    tabList: [
      {
        name: "basicInfo",
        label: "基本信息",
        component: basicInfo,
        needTag: true,  //---------------------------------可以给tab新增tag标签显示，控制tag的显示与隐藏
        tagNum: 2,  //---------------------------------可以给tab新增tag标签显示，tag内部显示的内容
        data: {
          tenantID: "",
        },
      },
      {
        name: "orgStruct",
        label: "组织结构分配",
        component: orgStruct,
        data: {
          text: "啦啦啦",
        },
      },
      {
        name: "rightAllocation",
        label: "权限分配",
        component: rightAllocation,
        data: {
          text: "啦啦啦",
        },
      },
      {
        name: "meterType",
        label: "表类型开通",
        component: meterType,
        data: {
          text: "啦啦啦",
        },
      },
      {
        name: "typeOfDTU",
        label: "DTU类型开通",
        component: typeOfDTU,
        data: {
          text: "啦啦啦",
        },
      },
      {
        name: "operateRecord",
        label: "操作记录",
        component: operateRecord,
        data: {
          text: "啦啦啦",
        },
      },
      {
        name: "msgConfig",
        label: "消息业务配置",
        component: msgConfig,
        data: {
          text: "啦啦啦",
        },
      },
    ],

## 新增 defaultActiveName 属性，可以设置页面初始默认的 tab 页，入参为希望展示的 tab 页的 name

### 上面 list 里面可以看到一个 data 传参，如果需要给某个 tab 页面的组件传参就写在这里面，data 必须是一个对象，在自定义组件里面 props 接收，接收时必须要使用下面的声明，因为组件封装的时候用的这个名字，多担待！！！

props: {
tabData: {
type: Object,
default: () => {},
},
},

#### 以上就是简单使用啦，有什么疑问随时找我~
