<!--
 * @Description: 报警方案
 * @Author: fengjialing
 * @Date: 2025-04-15 14:08:13
 * @LastEditors: fengjialing
 * @LastEditTime: 2025-04-15 14:08:13
-->

<template lang="pug">
.alarm-scheme
	.header
		Button(v-if='$route.query.hideBack !== "1"', @click='handleBack') 返回
	.alarm-scheme-wrap
		Spin(fix, v-if='loading') 加载中。。
		.alarm-scheme-left(v-if='configType == "common"')
			//- .info-left(v-if='configType == "special"')
			//- 	.site-info-con
			//- 		.site-info-con-tit
			//- 			i.iconfont.icon-a-Property1zhengchang
			//- 			span 站点信息
			//- 		.site-info-con-inner
			//- 			.site-info-con-inner-row(v-for='(item, index) in siteList', :key='index') {{ item[0] }}：{{ item[1] || '-' }}
			//- 	.site-info-remarks *具体详细信息由子系统提供
			.tree-left
				.acheme-left-header
					Input(style='width: 200px; margin-right: 10px', clearable, v-model='deviceTypeName', placeholder='请输入名称')
					Button(type='primary', @click='handleSearch') 查询
				Table(
					:show-header='false',
					style='width: 270px',
					:border='false',
					height='800',
					:columns='columns',
					:data='treeData',
					@on-selection-change='handleSelect'
				)
			//- AreaTree(@change-select='handleChangeSelect', multiple)
		.form-wrap(:style='{ width: configType == "special" ? "100%" : "calc(100% - 270px)" }')
			//- Button.header-button(@click='handleEdit()') 编辑
			Form.form.resetForm(ref='form', :model='formVal', :rules='formRules', :label-width='80')
				.form-module
					.form-module-tit
						span 基本信息
					.form-module-con
						Row
							Col(span='10')
								FormItem(label='方案名称', prop='name')
									Input(v-model='formVal.name', placeholder='输入方案名称', :disabled='isReadonly')
							Col(span='10', offset='4')
								FormItem(label='方案编号', prop='code')
									Input(v-model='formVal.code', placeholder='输入方案编号', :disabled='isReadonly')
						Row
							Col(span='24')
								FormItem(label='备注', prop='memo')
									Input(v-model='formVal.memo', placeholder='输入备注内容', :disabled='isReadonly')
				.form-module
					.form-module-tit
						span 报警配置
					.form-module-con(:key='bjpzKey')
						Row.pad-r60(v-for='(item, index) in formVal.ruleList', :key='"ruleList" + index')
							Col.col-mr(span='4')
								FormItem(label='报警类型', :prop='`ruleList.${index}.alarmTypeCode`', :rules='formRules.alarmTypeCode')
									Select(v-model='item.alarmTypeCode', transfer, filterable, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in alarmTypes', :key='item.code', :value='item.code') {{ item.name }}
							Col.col-mr(span='4')
								FormItem(label='报警规则', :prop='`ruleList.${index}.ruleTypeCode`', :rules='formRules.ruleTypeCode')
									Select(
										v-model='item.ruleTypeCode',
										transfer,
										placeholder='请选择',
										:disabled='isReadonly',
										@on-change='changeRules(item, index)'
									)
										Option(v-for='item in ruleTypes', :key='item.code', :value='item.code') {{ item.name }}
							Col.col-mr(v-if='item.ruleTypeCode === "SAME_PERIOD_TIMING"', span='2')
								FormItem(label='', :label-width='0', :prop='`ruleList.${index}.samePeriod`', :rules='formRules.samePeriod')
									Select(v-model='item.samePeriod', transfer, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in samePeriodList1', :key='item.code', :value='item.code') {{ item.name }}
							Col.col-mr(v-if='item.ruleTypeCode === "LEAKAGE"', span='4')
								FormItem(label='报警方案', :prop='`ruleList.${index}.leakageModel`', :rules='formRules.leakageModel')
									Select(v-model='item.leakageModel', transfer, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in leakageModelList', :key='item.code', :value='item.code') {{ item.name }}
							Col.col-mr(v-if='["NIGHT_FLOW_THRESHOLD", "AVERAGE"].includes(item.ruleTypeCode)', span='4')
								FormItem(label='报警方案', :prop='`ruleList.${index}.nightModel`', :rules='formRules.nightModel')
									Select(v-model='item.nightModel', transfer, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in nightModelList', :key='item.code', :value='item.code') {{ item.name }}
							Col.col-mr(span='3', v-if='item.ruleTypeCode === "SAME_PERIOD"')
								FormItem(label='', :label-width='0', :rules='formRules.samePeriod', :prop='`ruleList.${index}.samePeriod`')
									Select(v-model='item.samePeriod', transfer, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in samePeriodList', :key='item.value', :value='item.value') {{ item.lable }}
							Col.col-mr(span='4')
								FormItem(label='报警等级', :prop='`ruleList.${index}.alarmLevelId`', :rules='formRules.alarmLevelId')
									Select(v-model='item.alarmLevelId', transfer, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in levelTypes', :key='item.id', :value='item.id') {{ item.name }}

							Col.Col.col-mr.ps-center(
								span='4',
								style='margin-right: 0',
								v-if='["AVERAGE", "SAME_PERIOD"].includes(item.ruleTypeCode)'
							)
								FormItem(
									label='下限',
									:label-width='60',
									:prop='`ruleList.${index}.lowerLimit`',
									:rules='formRules.downLimit'
								)
									Input(
										ps-center,
										v-model='item.lowerLimit',
										type='number',
										placeholder='请输入',
										:disabled='isReadonly',
										@on-keyup='change($event.target.value, "lowerLimit", index)'
									)

								.rate %
							Col.Col.col-mr.ps-center(
								span='4',
								style='margin-right: 0',
								v-if='["AVERAGE", "SAME_PERIOD"].includes(item.ruleTypeCode)'
							)
								FormItem(label='上限', :label-width='60', :prop='`ruleList.${index}.upperLimit`', :rules='formRules.upLimit')
									Input(
										v-model='item.upperLimit',
										type='number',
										placeholder='请输入',
										:disabled='isReadonly',
										@on-keyup='change($event.target.value, "upperLimit", index)'
									)
								.rate %
							Col.col-mr(
								span='4',
								v-if='item.ruleTypeCode === "RANGE_LIMIT" || (item.ruleTypeCode === "LEAKAGE" && item.leakageModel === "LIMIT")'
							)
								FormItem(label='报警下限', :prop='`ruleList.${index}.lowerLimit`', :rules='formRules.lowerLimit')
									Input(
										v-model='item.lowerLimit',
										type='number',
										placeholder='请输入',
										:disabled='isReadonly',
										@on-keyup='change($event.target.value, "lowerLimit", index, 3)'
									)

							Col.col-mr(
								span='4',
								v-if='item.ruleTypeCode === "RANGE_LIMIT" || (item.ruleTypeCode === "LEAKAGE" && item.leakageModel === "LIMIT")'
							)
								FormItem(label='报警上限', :prop='`ruleList.${index}.upperLimit`', :rules='formRules.upperLimit')
									Input(
										v-model='item.upperLimit',
										type='number',
										placeholder='请输入',
										:disabled='isReadonly',
										@on-keyup='change($event.target.value, "upperLimit", index, 3)'
									)
							//- 新增 断线时间
							Col.Col.timereset.col-mr(span='5', v-if='item.ruleTypeCode === "OFF_LINE"')
								FormItem(label='断线时间', :rules='formRules.offLineTime', :prop='`ruleList.${index}.offLineTime`')
									Input.mar-l10(v-model='item.offLineTime', type='number', placeholder='请输入', :disabled='isReadonly')
									.mintue 分钟

							//- 模式
							Col.Col.timereset.col-mr(span='4', v-if='item.ruleTypeCode === "MUTATION"')
								FormItem(label='模式', :label-width='60', :rules='formRules.model', :prop='`ruleList.${index}.model`')
									Select.mar-l10(v-model='item.model', transfer, filterable, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in modelsList', :key='item.value', :value='item.value') {{ item.name }}

							//- 阙值
							Col.Col.timereset.col-mr(span='4', v-if='item.ruleTypeCode === "MUTATION"')
								FormItem(label='阈值', :label-width='60', :prop='`ruleList.${index}.threshold`', :rules='formRules.threshold')
									Input(
										v-model='item.threshold',
										type='number',
										placeholder='请输入',
										:disabled='isReadonly',
										@on-keyup='change($event.target.value, "threshold", index)'
									)
							//- 趋势
							Col.Col.timereset.col-mr(
								span='4',
								v-if='item.ruleTypeCode === "NIGHT_FLOW_THRESHOLD" && item.nightModel === "TRENDS"'
							)
								FormItem(label='趋势', :label-width='60', :rules='formRules.trend', :prop='`ruleList.${index}.trend`')
									Select.mar-l10(v-model='item.trend', transfer, filterable, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in trendList', :key='item.value', :value='item.value') {{ item.name }}
							//- 计算天数
							Col.Col.timereset.col-mr(v-if='item.ruleTypeCode === "MUTATION"', span='4')
								FormItem(label='判断范围', :rules='formRules.passTimeDay', :prop='`ruleList.${index}.passTime`')
									Input(v-model='item.passTime', type='number', placeholder='请输入', :disabled='isReadonly')
									.mintue 小时
							Col.Col.timereset.col-mr(
								span='4',
								v-if='(item.ruleTypeCode === "NIGHT_FLOW_THRESHOLD" && item.nightModel === "TRENDS") || item.ruleTypeCode === "AVERAGE"'
							)
								FormItem(:label='"计算天数"', :rules='formRules.passTime', :prop='`ruleList.${index}.passTime`')
									Select.mar-l10(v-model='item.passTime', transfer, filterable, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in daysList', :key='item.value', :value='item.value') {{ item.name }}
									.mintue 天
							//-匹配次数
							Col.Col.timereset.col-mr(
								span='4',
								v-if='item.ruleTypeCode === "NIGHT_FLOW_THRESHOLD" && item.nightModel === "TRENDS"'
							)
								FormItem(label='匹配次数', :rules='formRules.matchCount', :prop='`ruleList.${index}.matchCount`')
									Select.mar-l10(v-model='item.matchCount', transfer, filterable, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in getDaysList(item.passTime)', :key='item.value', :value='item.value') {{ item.name }}
							Col.col-mr(
								span='12',
								v-if='item.ruleTypeCode === "NIGHT_FLOW_THRESHOLD" && (item.nightModel === "RANGE" || item.nightModel === "LEAKAGE_CONTROL")'
							)
								.form-item-wrap
									.xx-form-item
										FormItem(
											label='夜间流量区间',
											:label-width='110',
											:prop='`ruleList.${index}.lowerLimit`',
											:rules='formRules.lowerLimit'
										)
											Input(v-model='item.lowerLimit', type='number', placeholder='请输入', :disabled='isReadonly')
												span(slot='prepend') (不含)
												span(slot='append') m³
									span.tag ~
									FormItem(label='', :label-width='0', :prop='`ruleList.${index}.upperLimit`', :rules='formRules.upperLimit')
										Input(v-model='item.upperLimit', type='number', placeholder='请输入', :disabled='isReadonly')
											span(slot='prepend') (含)
											span(slot='append') m³
							Col.col-mr(span='12', v-if='item.ruleTypeCode === "LEAKAGE" && item.leakageModel === "CHANGE_RATE"')
								.form-item-wrap
									.xx-form-item
										FormItem(label='供水差量', :prop='`ruleList.${index}.lowerLimit`', :rules='formRules.lowerLimit')
											Input(v-model='item.lowerLimit', type='number', placeholder='请输入', :disabled='isReadonly')
												span(slot='prepend') (不含)
												span(slot='append') m³
									span.tag ~
									FormItem(label='', :label-width='0', :prop='`ruleList.${index}.upperLimit`', :rules='formRules.upperLimit')
										Input(v-model='item.upperLimit', type='number', placeholder='请输入', :disabled='isReadonly')
											span(slot='prepend') (含)
											span(slot='append') m³
							Col.col-mr(span='12', v-if='item.ruleTypeCode === "SAME_PERIOD_TIMING"')
								.form-item-wrap
									.xx-form-item
										FormItem(
											label='水量',
											:label-width='60',
											:prop='`ruleList.${index}.lowerLimit`',
											:rules='formRules.lowerLimit'
										)
											Input(v-model='item.lowerLimit', type='number', placeholder='请输入', :disabled='isReadonly')
												span(slot='prepend') (不含)
												span(slot='append') m³
									span.tag ~
									FormItem(label='', :label-width='0', :prop='`ruleList.${index}.upperLimit`', :rules='formRules.upperLimit')
										Input(v-model='item.upperLimit', type='number', placeholder='请输入', :disabled='isReadonly')
											span(slot='prepend') (含)
											span(slot='append') m³
							Col.col-mr(span='5', v-if='item.ruleTypeCode === "SAME_PERIOD_TIMING"')
								FormItem(
									label='|变化率|',
									:label-width='75',
									:prop='`ruleList.${index}.threshold`',
									:rules='formRules.threshold'
								)
									Input(key='kkls', v-model='item.threshold', type='number', placeholder='请输入', :disabled='isReadonly')
										span(slot='prepend') ＞
							Col.col-mr(span='5', v-if='item.ruleTypeCode === "LEAKAGE" && item.leakageModel === "CHANGE_RATE"')
								FormItem(
									label='|供水差率变化|',
									:label-width='120',
									:prop='`ruleList.${index}.threshold`',
									:rules='formRules.threshold'
								)
									Input(key='kkls', v-model='item.threshold', type='number', placeholder='请输入', :disabled='isReadonly')
										span(slot='prepend') ＞
							Col.col-mr(span='6', v-if='item.ruleTypeCode === "NIGHT_FLOW_THRESHOLD" && item.nightModel === "RANGE"')
								FormItem(
									label='同/环比变化',
									:label-width='100',
									:prop='`ruleList.${index}.threshold`',
									:rules='formRules.threshold'
								)
									Input(key='thbbh', v-model='item.threshold', type='number', placeholder='请输入', :disabled='isReadonly')
										span(slot='prepend') ＞
										span(slot='append') %
							Col.col-mr(
								span='4',
								v-if='item.ruleTypeCode === "NIGHT_FLOW_THRESHOLD" && item.nightModel === "LEAKAGE_CONTROL"'
							)
								FormItem(label='可控漏损', :prop='`ruleList.${index}.threshold`', :rules='formRules.threshold')
									Input(key='kkls', v-model='item.threshold', type='number', placeholder='请输入', :disabled='isReadonly')
										span(slot='prepend') ＞
							Col.col-mr(span='4', v-if='item.nightModel === "LIMIT"')
								FormItem(label='报警上限', :rules='formRules.threshold', :prop='`ruleList.${index}.threshold`')
									Input(v-model='item.threshold', type='number', placeholder='请输入', :disabled='isReadonly')
							Col.col-mr(v-if='item.ruleTypeCode === "SUSPENDED_ANIMATION"', span='4')
								FormItem(label='触发次数', :rules='formRules.triggerNum', :prop='`ruleList.${index}.triggerNum`')
									Select(v-model='item.triggerNum', transfer, placeholder='请选择', :disabled='isReadonly')
										Option(v-for='item in triggerNumList', :key='item.id', :value='item.id') {{ item.name }}
							Col.col-mr(v-if='item.ruleTypeCode === "SUSPENDED_ANIMATION" && item.triggerNum === "custom"', span='4')
								FormItem(
									:label-width='0',
									:rules='formRules.triggerNumCustom',
									:prop='`ruleList.${index}.triggerNumCustom`'
								)
									Input(v-model='item.triggerNumCustom', type='number', placeholder='请输入触发次数', :disabled='isReadonly')
							Col.Col.col-mr(span='3', v-if='["STATUS", "RANGE_LIMIT", "SAME_PERIOD"].includes(item.ruleTypeCode)')
								FormItem(label='高级配置', :prop='`ruleList.${index}.superConfig`', :rules='formRules.superConfig')
									<i-switch :disabled="isReadonly" v-model="item.superConfig"  />
							Col.Col.col-mr(
								span='4',
								v-if='item.superConfig && ["STATUS", "RANGE_LIMIT", "SAME_PERIOD"].includes(item.ruleTypeCode)'
							)
								FormItem(label='持续时长', :prop='`ruleList.${index}.delayTimeSel`', :rules='formRules.delayTimeSel')
									Select(
										v-model='item.delayTimeSel',
										transfer,
										placeholder='请选择',
										:disabled='isReadonly',
										@on-change='changeTimeFn(item, index)'
									)
										Option(v-for='item in delayTimes', :key='item.value', :value='item.value') {{ item.lable }}
							Col.col-mr.timereset(span='3', v-if='item.superConfig && item.delayTimeSel === "00"')
								FormItem(label='', :label-width='0', :rules='formRules.delayTime', :prop='`ruleList.${index}.delayTime`')
									Input.mar-l10(v-model='item.delayTime', type='number', placeholder='请输入', :disabled='isReadonly')

									.mintue 分钟

							Col.rulebtn(span='2')
								.btn-wrap
									Button.btn.btn-add(
										v-show='!isReadonly',
										type='primary',
										shape='circle',
										icon='md-add',
										@click='handleAddAlarmConf(item, index, "ruleList")'
									)
									Button.btn.btn-subtract(
										v-show='!isReadonly && formVal.ruleList && formVal.ruleList.length > 1',
										type='primary',
										shape='circle',
										icon='md-remove',
										@click='handleDelAlarmConf(item, index, "ruleList")'
									)
				.form-module
					.form-module-tit
						span 生效时间配置
					.form-module-con
						Row
							Col(span='24')
								FormItem(label='生效日期', prop='effectiveDateType')
									RadioGroup.radio-group(v-model='formVal.effectiveDateType')
										Radio.col-mr(label='ALL', :disabled='isReadonly') 所有日期
										Radio.col-mr(label='HOLIDAY', :disabled='isReadonly') 智能节假日
										Radio.col-mr(label='WORKINGDAY', :disabled='isReadonly') 智能工作日
										Radio.col-mr(label='CUSTOM', :disabled='isReadonly') 自定义日期
						.date-wrap(v-if='formVal.effectiveDateType === "CUSTOM"')
							Row(v-for='(item, index) in formVal.dateList', :key='"dateList" + index')
								Col(span='10')
									FormItem(label='日期范围', :prop='`dateList.${index}.dateRange`', :rules='formRules.dateRange')
										DatePicker(
											:editable='false',
											v-model='item.dateRange',
											type='daterange',
											transfer,
											placement='bottom-end',
											separator='   至   ',
											placeholder='请选择日期范围',
											:disabled='isReadonly'
										)
								Col(span='2')
									.btn-wrap
										Button.btn.btn-add(
											v-show='!isReadonly',
											type='primary',
											shape='circle',
											icon='md-add',
											@click='handleAddAlarmConf(item, index, "dateList")'
										)
										Button.btn.btn-subtract(
											v-show='!isReadonly && formVal.dateList && formVal.dateList.length > 1',
											type='primary',
											shape='circle',
											icon='md-remove',
											@click='handleDelAlarmConf(item, index, "dateList")'
										)
						Row(v-for='(item, index) in formVal.timeList', :key='"timeList" + index')
							Col(span='10')
								FormItem(
									label='定义生效时段',
									:prop='`timeList.${index}.timeRange`',
									:rules='formRules.timeRange',
									:label-width='110'
								)
									TimePicker(
										v-model='item.timeRange',
										type='timerange',
										transfer,
										:clearable='false',
										placement='bottom-end',
										separator='   至   ',
										placeholder='请选择时间范围',
										:disabled='isReadonly'
									)
							Col(span='2')
								.btn-wrap
									Button.btn.btn-add(
										v-show='!isReadonly',
										type='primary',
										shape='circle',
										icon='md-add',
										@click='handleAddAlarmConf(item, index, "timeList")'
									)
									Button.btn.btn-subtract(
										v-show='!isReadonly && formVal.timeList && formVal.timeList.length > 1',
										type='primary',
										shape='circle',
										icon='md-remove',
										@click='handleDelAlarmConf(item, index, "timeList")'
									)

				.form-module
					.form-module-tit
						span 通知配置
					.form-module-con
						Row
							Col(span='24')
								FormItem(label='告警展示', prop='stationNotifyFlag', :label-width='110')
									RadioGroup.radio-group(v-model='formVal.stationNotifyFlag')
										Radio.col-mr(label='1', :disabled='isReadonly') 弹窗展示
										Radio.col-mr(label='0', :disabled='isReadonly') 不弹窗
										Radio.col-mr(label='2', :disabled='isReadonly') 弹窗+声音
						Row
							Col(span='24')
							FormItem(label='通知方式', prop='noticeMethod', :label-width='95')
								CheckboxGroup.checkbox-group(v-model='formVal.noticeMethod')
									Checkbox.col-mr(label='notifySmsFlag', :disabled='isReadonly') 短信
									Checkbox.col-mr(label='notifyMailFlag', :disabled='isReadonly') 邮件
									Checkbox.col-mr(label='notifyWeixinFlag', :disabled='isReadonly') 微信
									Checkbox.col-mr(label='notifyAppFlag', :disabled='isReadonly') APP
									Checkbox.col-mr(label='notifyLocalFlag', :disabled='isReadonly') 声光联动
						Row
							Col(span='24')
							FormItem(label='通知人员', prop='noticeType', :label-width='95')
								RadioGroup.checkbox-group(v-model='noticeType', @on-change='handleNoticeWayChange')
									Radio.col-mr(label='person', :disabled='isReadonly') 人员
									Radio.col-mr(label='role', :disabled='isReadonly') 角色
									Radio.col-mr(label='organization', :disabled='isReadonly') 组织
									Radio.col-mr(label='customizeUrl', :disabled='isReadonly') 自定义URL
						Row
							Col(span='10')
								FormItem(label='人员配置', :prop='formVal.noticePerson', :label-width='95')
									.card-container 
										|
										|
										|
										|
										AlarmPersonSelect(
											ref='AlarmPersonSelectRef',
											v-if='["person"].includes(noticeType)',
											:disabled='isReadonly',
											:selected-value='formVal.notifyUserIdsArr',
											:selected-label='formVal.notifyUserNamesArr',
											@change-tree-check-box='changeTreeCheckBox'
										)
										AlarmRoleSelect(
											v-if='["role"].includes(noticeType)',
											ref='AlarmRoleSelectRef',
											:disabled='isReadonly',
											:selected-value='formVal.notifyRoleIds',
											@change-role-select='handleRoleSelect'
										)
										AlarmOrgSelect(
											ref='AlarmOrgSelectRef',
											v-if='["organization"].includes(noticeType)',
											:disabled='isReadonly',
											:selected-value='formVal.notifyOrgIds',
											@change-tree-check-box='changeTreeCheckBox'
										)
										template(v-else)
											div <Input v-model="formVal.inputUrl" placeholder="输入URL链接" @on-change="handleChangeUrlInput"/>
						Row
							Col(span='10')
								FormItem(label='日通知次数', prop='notifyTimesDay', :label-width='95')
									Input(v-model='formVal.notifyTimesDay', type='number', placeholder='输入通知次数', :disabled='isReadonly')
							Col(span='10', offset='4')
								FormItem(label='重复次数', prop='notifyTimesMax')
									Input(v-model='formVal.notifyTimesMax', type='number', placeholder='输入重复次数', :disabled='isReadonly')
						Row
							Col(span='24')
								FormItem(label='通知模版', prop='notifyTemplate', :label-width='95')
									Row(:gutter='16')
										Col(span='20')
											Input(
												v-model='formVal.notifyTemplate',
												placeholder='输入模版内容',
												:disabled='isReadonly',
												type='textarea',
												:rows='7',
												:maxlength='200',
												show-word-limit
											)
										Col(span='4')
											.template-container
												.template-container-item(
													v-for='item in noticeTemplateList',
													:key='item.id',
													:class='{ disabled: isReadonly }',
													@click='handleJointTemplate(item)'
												) {{ item.attributeName }}
						Row(v-show='!isReadonly', type='flex', justify='center')
							Col
								FormItem
									Button(type='primary', @click='handleSubmit("form")') 保存
									//- Button(type='primary', @click='handleSubmit("form")', :loading='saveLoading') 保存
									//- Poptip(confirm, title="是否确认重置表单?", @on-ok="handleReset('form')")
									//- 	Button(style="margin-left: 8px") 重置
</template>	
<script>
import AlarmPersonSelect from '../components/AlarmPersonSelect.vue' //人员选择
import AlarmRoleSelect from '../components/AlarmRoleSelect.vue' //角色选择
import AlarmOrgSelect from '../components/AlarmOrgSelect.vue' //组织选择
// import AreaTree from '../components/AreaTree.vue'
import WaterRow from '@/components/gc-water-row'
import {
	querySysCommonAlarmTypeDTO,
	// querySysSpecialAlarmTypeDTO, //设备创建接口(旧接口)
	querySysSpecialAlarmTypeNewDTO, // 设备类型创建接口(新)
	queryAlarmConfigRuleType,
	queryAlarmLevelList,
	queryNotificationTemplate,
	// querySysNodeInfo,
} from '@/api/common.js'
import { queryTemplateByName } from '@/api/base-item'
// import { getSchemeInfo } from '@/api/alarm-config.js'
import { getSchemeInfo, saveOrUpdate } from '@/api/alarm-config.js'

export default {
	name: 'alarm-scheme',
	components: {
		WaterRow,
		// AreaTree,
		AlarmPersonSelect,
		AlarmRoleSelect,
		AlarmOrgSelect,
	},
	props: {},
	data() {
		const numTimes = (rule, value, callback) => {
			const maxNum = 999999

			if (value > maxNum) {
				callback(new Error(`不能大于${maxNum}`))
			} else if (value < 0) {
				callback(new Error('不能为负数'))
			} else {
				callback()
			}
		}
		const validateDateRange = (rule, value, callback) => {
			if (value && value[0] && value[1]) {
				callback()
			} else {
				callback(new Error('请选择'))
			}
		}
		const validateMultiple = (rule, value, callback) => {
			if (value) {
				callback()
			} else {
				callback(new Error('请选择'))
			}
		}
		const downLimitHS = (rule, value, callback) => {
			if (value >= 0) {
				callback(new Error('不得大于或等于0'))
			} else {
				if (value) {
					callback()
				} else {
					callback(new Error('不得为空'))
				}
			}
		}
		const upLimitHS = (rule, value, callback) => {
			if (value <= 0) {
				callback(new Error('不得小于或等于0'))
			} else {
				if (value) {
					callback()
				} else {
					callback(new Error('不得为空'))
				}
			}
		}
		const yZLimitHS = (rule, value, callback) => {
			if (value <= 0) {
				callback(new Error('不得小于或等于0'))
			} else {
				if (value) {
					callback()
				} else {
					callback(new Error('不得为空'))
				}
			}
		}
		const zsLimit = (rule, value, callback) => {
			const reg = /^[1-9]\d*$/
			if (reg.test(value) && value) {
				callback()
			} else {
				callback(new Error('请输入正整数'))
			}
		}
		const passHourLimit = (rule, value, callback) => {
			if (value <= 0) {
				callback(new Error('不得小于或等于0'))
			} else if (value > 24) {
				callback(new Error('不得大于24'))
			} else {
				if (value) {
					callback()
				} else {
					callback(new Error('不得为空'))
				}
			}
		}
		const validateTriggerNumCustom = (rule, value, callback) => {
			const num = Number(value)

			if (isNaN(num)) {
				callback(new Error('请输入有效数字'))
			} else {
				if (num < 10 || num > 1000) {
					callback(new Error('次数需在 10~1000 之间'))
				} else {
					callback()
				}
			}
		}

		return {
			noticeTemplateList: [], //通知模板
			deviceTypeName: '', //左侧设备类型树名称
			saveLoading: false,
			loading: false,
			dmaTreeType: [
				{ title: '区域配置', key: 'AREA' },
				{ title: '站点配置', key: 'STATION' },
			],
			deepLoopArr: [],
			deepLoopStationCount: 0,
			deepLoopStationArr: [],
			commonPartArr: [],
			// commonPartArrStr: [],
			treeTypeKey: 'AREA',
			bjpzKey: 0,
			treeData: [],
			name: '',
			type: '',
			selectedId: null,
			columns: [
				{
					type: 'selection',
					width: 30,
					align: 'center',
				},
				{ key: 'name' },
			],
			pageType: this.$route.query.type, // 新增: create 修改: update 查看: read
			isReadonly: this.$route.query.type === 'read',
			configType: this.$route.query.configType, // 通用: common 专用: special
			treeType: 1,
			siteList: {},
			selectedNodes: [],
			formVal: {
				ruleList: [
					{
						lowerLimit: '',
						upperLimit: '',
						alarmTypeCode: '',
						ruleTypeCode: '',
						samePeriod: '',
						alarmLevelId: '',
						offLineTime: '',
						passTime: '',
						threshold: '',
						model: '',
						matchCount: '',
						trend: '',
						triggerNum: '',
						triggerNumCustom: '',
					},
				],
				dateList: [
					{
						dateRange: [],
					},
				],
				timeList: [
					{
						timeRange: ['00:00:00', '23:59:59'],
					},
				],
				notifyRoleIds: [], //角色id树
				notifyCustomUrl: [], //自定义Url
				notifyUserIdsArr: [],
				notifyUserNamesArr: [],
				notifyOrgIds: [], //组织树
				effectiveDateType: 'ALL',
				stationNotifyFlag: '1',
				notifyTimesDay: '1',
				notifyTimesMax: '1',
				noticePerson: 'person',
				inputUrl: '',
			},
			noticeType: 'person',
			notifyConfigRules: [], //人员配置动态校验规则
			originFormVal: {},
			alarmTypes: [],
			ruleTypes: [],
			samePeriodList1: [
				{ name: '昨日', code: 'YESTERDAY' },
				{ name: '上月', code: 'LAST_MONTH' },
				{ name: '去年', code: 'LAST_YEAR' },
			],
			nightModelList: [
				{ name: '变化幅度', code: 'RANGE' },
				{ name: '变化趋势', code: 'TRENDS' },
				{ name: '限值', code: 'LIMIT' },
				{ name: '可控漏损', code: 'LEAKAGE_CONTROL' },
			],
			leakageModelList: [
				{ name: '限值', code: 'LIMIT' },
				{ name: '变化率', code: 'CHANGE_RATE' },
			],
			levelTypes: [],
			triggerNumList: [
				{ name: '10', id: '10' },
				{ name: '24', id: '24' },
				{ name: '60', id: '60' },
				{ name: '自定义', id: 'custom' },
			],
			samePeriodList: [
				{
					lable: '昨日同期',
					value: 'YESTERDAY',
				},
				// {
				// 	lable: '上月同期',
				// 	value: 'LAST_MONTH',
				// },
				// {
				// 	lable: '去年同期',
				// 	value: 'LAST_YEAR',
				// },
			],
			delayTimes: [
				{
					lable: '30分钟',
					value: '30',
				},
				{
					lable: '1小时',
					value: '60',
				},
				{
					lable: '6小时',
					value: '180',
				},
				{
					lable: '自定义',
					value: '00',
				},
			],
			daysList: [
				{ name: '2', value: '2' },
				{ name: '3', value: '3' },
				{ name: '4', value: '4' },
				{ name: '5', value: '5' },
				{ name: '6', value: '6' },
				{ name: '7', value: '7' },
			],
			trendList: [
				{ name: '上升', value: 'UP' },
				{ name: '下降', value: 'DOWN' },
			],
			modelsList: [
				{ name: '变化百分比大于', value: 'PERCENTAGE_GREATER' },
				{ name: '变化百分比小于', value: 'PERCENTAGE_LESS' },
				{ name: '绝对值大于', value: 'ABS_GREATER' },
				{ name: '绝对值小于', value: 'ABS_LESS' },
			],
			formRules: {
				name: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
				code: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
				alarmTypeCode: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
					},
				],
				ruleTypeCode: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
					},
				],
				lowerLimit: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
				upperLimit: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
				downLimit: [
					{
						required: true,
						message: '请输入',
					},
					{
						trigger: 'blur',
						validator: downLimitHS,
					},
				],
				upLimit: [
					{
						required: true,
						message: '请输入',
					},
					{
						trigger: 'blur',
						validator: upLimitHS,
					},
				],
				passTime: [
					{
						required: true,
						message: '请输入',
					},
					{
						trigger: 'blur',
						validator: zsLimit,
					},
				],
				passTimeDay: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
					{
						trigger: 'blur',
						validator: passHourLimit,
					},
				],
				threshold: [
					{
						required: true,
						message: '请输入',
					},
					{
						trigger: 'blur',
						validator: yZLimitHS,
					},
				],

				alarmLevelId: [
					{
						required: true,
						type: 'number',
						message: '请选择',
						trigger: 'change',
					},
				],
				superConfig: [
					{
						required: false,
						// type: 'number',
						// message: '请选择',
						// trigger: 'change',
					},
				],
				samePeriod: [
					{
						required: true,
						// type: 'number',
						message: '请选择',
						trigger: 'change',
					},
				],
				nightModel: [
					{
						required: true,
						// type: 'number',
						message: '请选择',
						trigger: 'change',
					},
				],
				leakageModel: [
					{
						required: true,
						// type: 'number',
						message: '请选择',
						trigger: 'change',
					},
				],
				delayTime: [
					{
						required: true,
						message: '请输入',
						type: 'number',
						transform: val => Number(val),
						trigger: 'blur',
					},
				],
				model: [
					{
						required: true,
						message: '请输入',
						trigger: 'change',
					},
				],
				trend: [
					{
						required: true,
						message: '请输入',
						trigger: 'change',
					},
				],
				matchCount: [
					{
						required: true,
						message: '请输入',
						trigger: 'change',
					},
				],
				triggerNum: [
					{
						required: true,
						message: '请输入',
						trigger: 'change',
					},
				],
				triggerNumCustom: [
					{
						required: true,
						message: '请输入',
						trigger: 'change',
					},
					{ validator: validateTriggerNumCustom, trigger: 'change' },
				],
				delayTimeSel: [
					{
						required: true,

						message: '请输入',
						trigger: 'blur',
					},
				],
				effectiveDateType: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
					},
				],
				stationNotifyFlag: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
					},
				],
				dateRange: [
					{ required: true, message: '请选择' },
					{ validator: validateDateRange, trigger: 'change' },
				],
				timeRange: [
					// { required: true, message: '请选择' },
					// { validator: validateDateRange, trigger: 'change' },
				],

				notifyUserIdsArr: [
					{ required: true, message: '请选择' },
					{ validator: validateMultiple, trigger: 'change' },
				],
				noticeMethod: [
					{
						required: false,
						type: 'array',
						message: '请选择',
						trigger: 'change',
					},
				],
				// noticePerson: [
				// 	{
				// 		required: false,
				// 		type: 'string',
				// 		message: '请选择',
				// 		trigger: 'change',
				// 	},
				// ],
				notifyTimesDay: [
					{
						required: true,
						// type: 'number',
						message: '请输入',
					},
					{ validator: numTimes, trigger: 'blur' },
				],
				notifyTimesMax: [
					{
						required: true,
						// type: 'number',
						message: '请输入',
					},
					{ validator: numTimes, trigger: 'blur' },
				],
				ownership: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
						type: 'array',
					},
				],
			},
		}
	},
	computed: {
		bottomBtnDisabled() {
			let disabled = false

			if (this.configType == 'common' && (!this.selectedNodes || !this.selectedNodes.length)) {
				disabled = true
			}

			return disabled
		},
		sysCode() {
			return this.$route.query.sysCode
		},
	},
	watch: {
		treeTypeKey: {
			handler() {
				this.$nextTick(() => {
					if (this.configType != 'special') {
						this._querySysCommonAlarmTypeDTO()
						this.$refs.tree._querySysConfigTree()
					}
				})
			},
		},
	},

	created() {
		if (this.$route.query.sysCode == 'dma' && this.configType == 'common') {
			this.treeTypeKey = this.$route.query.stationType || 'AREA'
		}

		this._queryAlarmConfigRuleType()
		this._queryAlarmLevelList()
		this._setFormData()
		// 创建报警方案
		if (this.pageType === 'create') {
			// this._querySysNodeInfo()
			if (this.configType === 'common') {
				this._querySysCommonAlarmTypeDTO()
				this.handleSearch()
			} else {
				this._querySysSpecialAlarmTypeDTO()
			}
		} else {
			// this.handleSearch()
			this._getSchemeInfo()
		}
		this.updateNotifyConfigRules()
	},
	mounted() {
		this.getNotificationTemplate()
	},
	beforeDestroy() {},
	methods: {
		getNotificationTemplate() {
			// this.noticeTemplateList = [
			// 	{ id: 1, attributeName: 'Item 1', attributeCode: '[]' },
			// 	{ id: 1, attributeName: 'Item 2', attributeCode: '--' },
			// 	{ id: 1, attributeName: 'Item 3', attributeCode: '{}' },
			// 	{ id: 1, attributeName: 'Item 4', attributeCode: '==' },
			// 	{ id: 1, attributeName: 'Item 5', attributeCode: '>' },
			// 	{ id: 1, attributeName: 'Item 6', attributeCode: '<' },
			// 	{ id: 1, attributeName: 'Item 7', attributeCode: '+' },
			// ]
			queryNotificationTemplate().then(res => {
				const { result } = res
				this.noticeTemplateList = result && result.length > 0 ? result : []
			})
		},
		// 根据点击的模板拼接字符串
		handleJointTemplate(item) {
			this.formVal.notifyTemplate = this.formVal.notifyTemplate + item?.attributeCode
		},
		updateNotifyConfigRules() {
			let noticePerson = this.formVal?.noticePerson
			this.notifyConfigRules = [
				{
					validator: (rule, value, callback) => {
						const hasSelection =
							(noticePerson === 'person' && this.formVal.notifyUserIdsArr.length > 0) ||
							(noticePerson === 'role' && this.formVal.notifyRoleArr.length > 0) ||
							(noticePerson === 'organization' && this.formVal.notifyOrgIds.length > 0) ||
							(noticePerson === 'customizeUrl' && this.formVal.notifyCustomUrl.trim() !== '')
						if (hasSelection) {
							callback()
						} else {
							callback(new Error('请选择'))
						}
					},
					trigger: 'change',
				},
			]
		},

		change(val, key, index, decimalDigits = 2) {
			val = val.replace(/(^\s*)|(\s*$)/g, '')
			if (!val) {
				val = ''
				return
			}

			if (decimalDigits === 2) {
				val = val.replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			}
			if (decimalDigits === 3) {
				val = val.replace(/^(-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3')
			}

			this.$set(this.formVal.ruleList[index], key, val)
		},

		changeRules(item, index) {
			let oroginObj = this.$common.deepCopy(this.originFormVal)
			if (oroginObj.ruleList && oroginObj.ruleList[index]) {
				if (item.ruleTypeCode == oroginObj.ruleList[index].ruleTypeCode) {
					for (let key in oroginObj.ruleList[index]) {
						this.formVal.ruleList[index][key] = oroginObj.ruleList[index][key]
					}
				} else {
					this.$set(this.formVal.ruleList[index], 'superConfig', false)
					this.$set(this.formVal.ruleList[index], 'samePeriod', '')
					this.$set(this.formVal.ruleList[index], 'lowerLimit', '')
					this.$set(this.formVal.ruleList[index], 'upperLimit', '')
					this.$set(this.formVal.ruleList[index], 'delayTimeSel', '')
					this.$set(this.formVal.ruleList[index], 'delayTime', '')
				}
			}

			this.bjpzKey++
		},
		changeTimeFn(item) {
			if (item.delayTimeSel === '00') {
				item.delayTime = ''
			}
		},
		// 修改报警规则
		changeRulesFn() {},
		// 提交表单
		handleSubmit(name) {
			// handleSubmit() {
			// this._saveOrUpdate()
			// let params = JSON.parse(JSON.stringify(this.formVal))
			// console.log('保存报警配置params', params)
			this.$refs[name].validate(valid => {
				if (valid) {
					this._saveOrUpdate()
				} else {
					this.$Message.warning('表单输入格式不正确')
				}
			})
		},

		// 重置表单
		handleReset(name) {
			this.$refs[name].resetFields()
		},

		// 增加报警配置
		handleAddAlarmConf(data = {}, idx, prop) {
			const { alarmLevelId, alarmTypeCode, ruleTypeCode } = data
			let obj = {}
			if (prop === 'ruleList') {
				obj = {
					alarmLevelId,
					alarmTypeCode,
					ruleTypeCode,
				}
			}
			if (prop === 'dateList') {
				obj = {
					dateRange: [],
				}
			}
			if (prop === 'timeList') {
				obj = {
					timeRange: ['00:00:00', '23:59:59'],
				}
			}

			this.formVal[prop].splice(idx + 1, 0, obj)
		},

		// 删除报警配置
		handleDelAlarmConf(data, idx, prop) {
			this.formVal[prop].splice(idx, 1)
		},

		// 初始化表单
		initForm() {
			const form = {
				ruleList: [{}],
				dateList: [
					{
						dateRange: [],
					},
				],
				timeList: [
					{
						timeRange: [],
					},
				],
			}

			this.formVal = form
		},

		// 级联选择格式化
		cascaderFormat(label) {
			if (label) {
				return label[label.length - 1]
			}
		},

		// 选择部门
		changeDepartment(data) {
			if (data && data.length) {
				this.formVal.notifyUserIds = []
				this._getUsers(data[data.length - 1])
			}
		},
		// 设置表单数据
		_setFormData() {
			if (this.$route.query.configType == 'special') {
				const selectedNodes = sessionStorage.getItem('alarm-scheme-selected-tree')
				if (selectedNodes) {
					const selectedNodesFmt = JSON.parse(selectedNodes)
					this.selectedNodes = selectedNodesFmt
				}
			}
		},
		// 根据报警类型 code 获取选中的整个对象
		_getAlarmTypeObj(code) {
			let obj = {}

			this.alarmTypes.forEach(item => {
				if (item.code == code) {
					obj = item
				}
			})

			return obj
		},

		// 查询报警方案配置
		_getSchemeInfo() {
			this.loading = true
			getSchemeInfo({ id: this.$route.query.id }).then(res => {
				this.loading = false
				let data = Object.assign({}, res.result)
				let noticeMethod = []
				// 日期处理
				if (data.effectiveDateStr) {
					data.dateList = data.effectiveDateStr.split(',').map(item => {
						const dateRange = item.split('~')

						return {
							dateRange,
						}
					})
				} else {
					data.dateList = [
						{
							dateRange: [],
						},
					]
				}

				// 时间处理
				if (data.effectiveTimeStr) {
					data.timeList = data.effectiveTimeStr.split(',').map(item => {
						const timeRange = item.split('~')

						return {
							timeRange,
						}
					})
				}

				// 上下限处理
				if (data.ruleList && data.ruleList.length) {
					data.ruleList.forEach(item => {
						const ruleDetail = JSON.parse(item.ruleDetail)

						if (ruleDetail) {
							item.lowerLimit = ruleDetail.lowerLimit
							item.upperLimit = ruleDetail.upperLimit
							item.superConfig = ruleDetail.superConfig
							item.delayTime = ruleDetail.delayTime
							item.delayTimeSel = ruleDetail.delayTimeSel
							item.nightModel = ruleDetail.nightModel
							item.leakageModel = ruleDetail.leakageModel
							item.samePeriod = ruleDetail.samePeriod
							item.offLineTime = ruleDetail.offLineTime //新增的
							item.passTime = ruleDetail.passTime
							item.threshold = ruleDetail.threshold
							item.model = ruleDetail.model
							item.matchCount = ruleDetail.matchCount
							item.trend = ruleDetail.trend
							item.triggerNum = ruleDetail.triggerNumCustom ? 'custom' : ruleDetail.triggerNum
							item.triggerNumCustom = ruleDetail.triggerNumCustom
						}
					})
				}

				// 人员配置处理
				// data.notifyUserIds = '82789,82792'
				// data.notifyRoleIds = '5555,5556'
				// data.notifyOrgIds = '7538,7540,7542'
				if (data.notifyUserIds) {
					this.$set(this, 'noticeType', 'person')
					data.notifyUserIdsArr = data.notifyUserIds.split(',').map(item => {
						return Number(item)
					})
				} else {
					data.notifyUserIdsArr = []
				}
				if (data.notifyUserNames) {
					data.notifyUserNamesArr = data.notifyUserNames.split(',')
				} else {
					data.notifyUserNamesArr = []
				}
				// 角色配置处理
				if (data.notifyRoleIds) {
					this.$set(this, 'noticeType', 'role')
					data.notifyRoleIds = data.notifyRoleIds?.split(',')
				}
				// 组织配置处理
				if (data.notifyOrgIds) {
					this.$set(this, 'noticeType', 'organization')
					data.notifyOrgIds = data.notifyOrgIds?.split(',')
				}
				// // 自定义url配置处理
				// if (data.notifyRoleIds) {
				// this.formVal.noticeType = 'customizeUrl'
				// 	data.notifyRoleIds
				// }
				// 自定义url方式处理
				if (data.notifySmsFlag == 1) {
					noticeMethod.push('notifySmsFlag')
				}
				if (data.notifyMailFlag == 1) {
					noticeMethod.push('notifyMailFlag')
				}
				if (data.notifyWeixinFlag == 1) {
					noticeMethod.push('notifyWeixinFlag')
				}
				if (data.notifyAppFlag == 1) {
					noticeMethod.push('notifyAppFlag')
				}
				if (data.notifyLocalFlag == 1) {
					noticeMethod.push('notifyLocalFlag')
				}
				data.noticeMethod = noticeMethod

				if (this.configType !== 'special') {
					this.selectedNodes = data.refList
					this.handleSearch()
				}

				// DMA情况，编辑情况下区域配置和站点配置不能进行切换

				// 处理其他
				data.notifyTimesDay = String(data.notifyTimesDay)
				data.notifyTimesMax = String(data.notifyTimesMax)
				data.stationNotifyFlag = String(data.stationNotifyFlag)

				this.formVal = data
				// this._querySysNodeInfo()
				if (this.configType === 'common') {
					this._querySysCommonAlarmTypeDTO()
				} else {
					this._querySysSpecialAlarmTypeDTO()
				}
				this.originFormVal = this.$common.deepCopy(this.formVal)
			})
		},

		// 保存更新报警方案
		_saveOrUpdate() {
			let params = JSON.parse(JSON.stringify(this.formVal))
			let alarmTypeCodeTmp = []
			let msg = ''
			let canCreat = true
			// const personConfigList = this.$refs.AlarmConfigSelectRef.getPersonConfigData()
			params.ruleList.forEach((item, index) => {
				const {
					alarmTypeCode, // 报警类型
					ruleTypeCode, // 报警规则
					samePeriod, // 报警方案 (同期报警(用量))
					leakageModel, // 报警方案 (管网漏损报警)
					nightModel, // 报警方案 (夜间流量报警/多日均值报警)
					alarmLevelId, // 报警等级
					upperLimit,
					lowerLimit,
				} = item
				const name = alarmTypeCode + '_' + alarmLevelId
				let canRepeatCreat = false

				// http://zentao.eslink.cc/zentao/story-view-15089.html
				// 夜间流量
				if (alarmTypeCode === 'GSL_NIGHT_FQ') {
					// 夜间流量报警
					if (ruleTypeCode === 'NIGHT_FLOW_THRESHOLD') {
						// 变化幅度
						if (['RANGE'].includes(nightModel)) {
							canRepeatCreat = true
						}
					}
					// 可控漏损流量
				} else if (alarmTypeCode === 'KKLS') {
					// 夜间流量报警
					if (ruleTypeCode === 'NIGHT_FLOW_THRESHOLD') {
						// 可控漏损
						if (['LEAKAGE_CONTROL'].includes(nightModel)) {
							canRepeatCreat = true
						}
					}
					// 累计流量
				} else if (alarmTypeCode === 'LJLL') {
					// 同期报警(用量)
					if (ruleTypeCode === 'SAME_PERIOD_TIMING') {
						// 昨日, 上月, 去年
						if (['YESTERDAY', 'LAST_MONTH', 'LAST_YEAR'].includes(samePeriod)) {
							canRepeatCreat = true
						}
					}
					// 供水差率
				} else if (alarmTypeCode === 'GSCL') {
					// 管网漏损报警
					if (ruleTypeCode === 'LEAKAGE') {
						// 变化率
						if (['CHANGE_RATE'].includes(leakageModel)) {
							canRepeatCreat = true
						}
					}
				}

				if (alarmTypeCodeTmp.includes(name)) {
					if (!canRepeatCreat) {
						msg = '报警类型与报警等级相同的记录只允许创建一条'
						canCreat = false
					}
				} else {
					alarmTypeCodeTmp.push(name)
				}
				if (Number(lowerLimit) > Number(upperLimit)) {
					msg = `第${index + 1}条报警配置下限值应小于上限值`
					canCreat = false
				}
			})

			let timeListTmp = []

			if (params.timeList && params.timeList.length) {
				params.timeList.forEach(item => {
					const str = item.timeRange[0] + '-' + item.timeRange[1]

					if (timeListTmp.includes(str)) {
						msg = '日期或时段存在完全重复的配置，请检查'
						canCreat = false
					} else {
						timeListTmp.push(str)
					}
				})
			}

			if (!canCreat) {
				this.$Message.warning(msg)
				return
			}

			// 日期处理
			if (params.dateList && params.dateList.length) {
				let effectiveDateStr = ''

				params.dateList.forEach((item, index) => {
					let comma = index === 0 ? '' : ','

					effectiveDateStr +=
						comma +
						this.$moment(item.dateRange[0]).format('YYYY-MM-DD') +
						'~' +
						this.$moment(item.dateRange[1]).format('YYYY-MM-DD')
				})
				params.effectiveDateStr = effectiveDateStr
				params.dateList = undefined
				if (params.effectiveDateType !== 'CUSTOM') {
					params.effectiveDateStr = undefined
				}
			}

			// 时间处理
			if (params.timeList && params.timeList.length) {
				let effectiveTimeStr = ''

				params.timeList.forEach((item, index) => {
					let comma = index === 0 ? '' : ','

					effectiveTimeStr += comma + item.timeRange[0] + '~' + item.timeRange[1]
				})
				params.effectiveTimeStr = effectiveTimeStr
				params.timeList = undefined
			}

			if (params.ruleList && params.ruleList.length) {
				params.ruleList.forEach(item => {
					// 上下限处理
					let detime = ''
					if (item.superConfig) {
						if (item.delayTimeSel == '00') {
							if (item.delayTime == '') {
								this.$Message.warning('请填写相关自定义时长')
							} else {
								detime = item.delayTime
							}
						} else {
							detime = item.delayTimeSel
						}
					}

					// 触发次数处理
					if (item.triggerNum === 'custom') {
						item.triggerNum = item.triggerNumCustom
					} else {
						item.triggerNumCustom = ''
					}

					let JSONruleDetail = JSON.stringify({
						lowerLimit: item.lowerLimit,
						upperLimit: item.upperLimit,
						superConfig: item.superConfig,
						delayTime: detime,
						delayTimeSel: item.delayTimeSel,
						nightModel: item.nightModel,
						leakageModel: item.leakageModel,
						samePeriod: item.samePeriod,
						offLineTime: item.offLineTime, //新增的
						passTime: item.passTime,
						threshold: item.threshold,
						model: item.model,
						matchCount: item.matchCount,
						trend: item.trend,
						triggerNum: item.triggerNum,
						triggerNumCustom: item.triggerNumCustom,
					})

					item.ruleDetail = JSONruleDetail
					item.lowerLimit = item.upperLimit = undefined

					// 报警类型处理
					let alarmObj = this._getAlarmTypeObj(item.alarmTypeCode)

					item.alarmTypeName = alarmObj.name
					item.alarmTypeId = alarmObj.id
				})
			}
			// 人员配置处理
			if (params.notifyUserIdsArr && this.noticeType === 'person') {
				params.notifyUserIds = params.notifyUserIdsArr.toString()
				params.notifyUserIdsArr = undefined
				params.notifyRoleIds = undefined
				params.notifyCustomUrl = undefined
				params.notifyOrgIds = undefined
			}
			// 角色树处理
			if (params.notifyRoleIds && this.noticeType === 'role') {
				params.notifyRoleIds = params.notifyRoleIds?.join(',')
				params.notifyUserIds = undefined
				params.notifyUserIdsArr = undefined
				params.notifyUserNames = undefined
				params.notifyUserNamesArr = undefined
				params.notifyOrgIds = undefined
			}
			// 组织树处理
			if (params.notifyOrgIds && this.noticeType === 'organization') {
				params.notifyOrgIds = params.notifyOrgIds?.join(',')
				params.notifyUserIds = undefined
				params.notifyUserIdsArr = undefined
				params.notifyUserNames = undefined
				params.notifyUserNamesArr = undefined
				params.notifyRoleIds = undefined
			}
			// 自定义Url模板
			if (params.notifyCustomUrl && this.noticeType === 'customizeUrl') {
				params.notifyUserIds = undefined
				params.notifyUserIdsArr = undefined
				params.notifyUserNames = undefined
				params.notifyUserNamesArr = undefined
				params.notifyOrgIds = undefined
			}
			console.log('params.notifyRoleIds', params.notifyRoleIds)
			console.log('params.notifyOrgIds', params.notifyOrgIds)
			// 通知方式处理
			if (params.noticeMethod && params.noticeMethod.length) {
				params.noticeMethod.forEach(item => {
					params[item] = 1
				})
				params.noticeMethod = undefined
			}
			// 左侧树形图
			if (this.selectedNodes && this.selectedNodes.length) {
				params.refList = []
				this.commonPartArr = []
				this.loopData(this.selectedNodes)
				params.refList = this.commonPartArr
			}
			// // 其他参数
			// DMA页面时保存修改需要增加stationType参数
			if (this.$route.query.sysCode == 'jz') {
				params.stationType = 'DEVICE_TYPE'
			}
			params.configType = this.configType === 'common' ? 3 : 4
			params.sysCode = this.$route.query.sysCode || 'dc'
			params.subSysCode = this.$route.query.subSysCode
			this.saveLoading = true
			saveOrUpdate(params)
				.then(() => {
					this.$Message.success('保存成功')
					this.deepLoopArr = []
					this.deepLoopStationCount = 0
					this.deepLoopStationArr = []
					this.saveLoading = false
					this.handleBack()
				})
				.catch(() => {
					this.saveLoading = false
				})
		},
		loopData(list) {
			list.forEach(ele => {
				const { id, deviceTypeCode, name, type } = ele
				this.commonPartArr.push({
					alarmConfigId: this.$route.query.id,
					nodeId: id, // 树的节点id
					nodeName: name,
					nodeType: deviceTypeCode,
					sysCode: type,
					nodeCode: 'device_type_jz',
				})
			})
		},
		deepLoopCommon(list) {
			list.forEach(ele => {
				const { id, nodeId, oldId, nodeCode, title, nodeType, link, checked, nodeName } = ele

				this.deepLoopArr.push({
					alarmConfigId: this.$route.query.id,
					id: String(id).includes('$_$') ? undefined : id, // 数据保存后回显的id
					nodeCode,
					nodeId: nodeId || oldId, // 树的节点id
					nodeName: title || nodeName,
					nodeType,
					link,
					checked,
					sysCode: this.$route.query.sysCode || 'dc',
				})

				if (ele.nodeType != 'AREA' && ele.checked) {
					this.deepLoopStationCount++
					this.deepLoopStationArr.push({
						alarmConfigId: this.$route.query.id,
						id: String(id).includes('$_$') ? undefined : id, // 数据保存后回显的id
						nodeCode,
						nodeId: nodeId || oldId, // 树的节点id
						nodeName: title || nodeName,
						nodeType,
						sysCode: this.$route.query.sysCode || 'dc',
					})
				}
			})
		},
		// 返回按钮
		handleBack() {
			const { sysCode, subSysCode, isCommon, stationType, nodeType = '' } = this.$route.query
			let path = 'device-alarm-config-home/special-use-alarm?'
			if (this.configType === 'common') {
				path = 'device-alarm-config-home/common-alarm?'
			}
			if (sysCode) {
				path += `&sysCode=${sysCode}`
			}
			if (subSysCode) {
				path += `&subSysCode=${subSysCode}`
			}
			if (isCommon) {
				path += `&isCommon=${isCommon}`
			}
			if (stationType) {
				path += `&stationType=${sysCode === 'gasdd' ? (nodeType ? nodeType : stationType) : stationType}`
			}

			this.$router.push(path)
		},
		// 查询系统-报警类型（通用）
		_querySysCommonAlarmTypeDTO() {
			querySysCommonAlarmTypeDTO({
				sysCode: 'jz',
			}).then(res => {
				const data = res.result

				if (data && data.length) {
					this.alarmTypes = data
				}
			})
		},
		// 修改树类型
		changeTreeType(item) {
			let count = 0
			this.formVal.ruleList.forEach(ele => {
				if (ele.alarmTypeCode) {
					count++
				}
			})

			if (count > 0) {
				this.$Modal.confirm({
					title: '提示',
					content: '您有未保存的报警规则，请确认继续切换?',
					loading: true,
					onOk: () => {
						this.$Modal.remove()
						this.treeTypeKey = item.key
						this.selectedNodes = []
					},
				})
			} else {
				this.treeTypeKey = item.key
				this.selectedNodes = []
			}
		},
		// 查询系统-报警类型（专用）
		_querySysSpecialAlarmTypeDTO() {
			let data = {}

			if (this.pageType === 'create') {
				data = this.$route.query
			} else {
				// if (this.selectedNodes && this.selectedNodes.length) {
				// 	data = this.selectedNodes[0]
				// }
				data = this.$route.query
			}
			// const { nodeCode, nodeType, nodeId } = data
			const { nodeType } = data
			// 单个设备对应报警类型查询,if-else判断设备还是设备类型查询报警类型,暂时没有设备查询，先不写

			// querySysSpecialAlarmTypeDTO({
			// 	sysCode:
			// 		this.$route.query.sysCode == 'dma'
			// 			? this.$route.query.stationType == 'AREA'
			// 				? 'dma'
			// 				: 'dd'
			// 			: this.$route.query.sysCode || 'dc',
			// 	nodeCode: '',
			// 	nodeType,
			// 	nodeId,
			// }).then(res => {
			// 	const data = res.result

			// 	if (data && data.length) {
			// 		this.alarmTypes = data
			// 	}
			// })
			// 设备类型对应报警类型查询
			querySysSpecialAlarmTypeNewDTO({
				sysCode: 'jz',
				nodeType,
			}).then(res => {
				const data = res.result
				if (data && data.length) {
					this.alarmTypes = data
				}
			})
		},
		// 查询报警配置【规则类型】
		_queryAlarmConfigRuleType() {
			queryAlarmConfigRuleType().then(res => {
				const data = res.result

				if (data && data.length) {
					this.ruleTypes = data
				}
			})
		},

		// 查询【报警等级列表】
		_queryAlarmLevelList() {
			queryAlarmLevelList().then(res => {
				const data = res.result

				if (data && data.length) {
					this.levelTypes = data
				}
			})
		},

		// 查询系统【报警来源信息】专用设备配置设备详情信息查询
		// _querySysNodeInfo() {
		// 	if (this.configType !== 'special') return
		// 	let params = {}

		// 	if (this.pageType === 'create') {
		// 		const { sysCode, nodeId, nodeType } = this.$route.query

		// 		params = {
		// 			sysCode: sysCode || 'dc',
		// 			nodeId,
		// 			nodeType,
		// 		}
		// 	} else {
		// 		if (this.formVal.refList && this.formVal.refList.length) {
		// 			const { sysCode, nodeId, nodeType } = this.formVal.refList[0]

		// 			params = {
		// 				sysCode: sysCode || 'dc',
		// 				nodeId,
		// 				nodeType,
		// 			}
		// 		}
		// 	}

		// 	if (!params.sysCode || !params.nodeId || !params.nodeType) return

		// 	querySysNodeInfo(params).then(res => {
		// 		const data = res.result

		// 		if (data) {
		// 			this.siteList = Object.entries(data)
		// 		} else {
		// 			this.siteList = []
		// 		}
		// 	})
		// 	this.siteList = Object.entries({
		// 		设备名称: '金狮啤酒厂',
		// 		设备编码: 'Stn051',
		// 		设备类型: '流量计',
		// 		所属分区: '梧田',
		// 		安装位置: '浙江省温州市鹿城区南汇街道惠民路地铁站3号口',
		// 	})
		// },
		getDaysList(number) {
			let arr = []
			if (number) {
				for (let i = 1; i < number; i++) {
					arr.push({
						name: i + '',
						value: i + '',
					})
				}
			} else {
				arr = [
					{ name: '2', value: '2' },
					{ name: '3', value: '3' },
					{ name: '4', value: '4' },
					{ name: '5', value: '5' },
					{ name: '6', value: '6' },
				]
			}
			return arr
		},
		changeConfigType(str) {
			this.configType = str
			if (str === 'special') {
				this.$router.push('/special-alarm-config')
			} else {
				this.$router.push('/commom-alarm-config')
			}
		},
		// 左侧树
		handleChangeSelect(selectList) {
			this.selectedNodes = selectList
		},
		// 角色配置树改变
		handleRoleSelect(selectList) {
			this.formVal.notifyRoleIds = selectList?.map(item => item?.value)
			// this.$refs.form.validateField('notifyUserIds') // 手动触发校验
		},
		// 组织树选择
		changeTreeCheckBox(checkedList) {
			this.formVal.notifyOrgIds = checkedList?.map(item => item?.id)
			// this.$refs.form.validateField('notifyUserIdsArr') // 手动触发校验
		},
		// 自定义url改变
		handleChangeUrlInput(val) {
			this.formVal.notifyCustomUrl = val
			// this.$refs.form.validateField('notifyUserIdsArr') // 手动触发校验
		},
		// 查询左侧模板树
		handleSearch() {
			let params = { name: this.deviceTypeName }
			queryTemplateByName(params).then(res => {
				this.treeData = res.result
				if (this.treeData && this.treeData.length && this.treeData.length > 0) {
					this.treeData[0]._highlight = true
					const treeIdArr = this.selectedNodes?.map(item => item.nodeId)
					this.selectedNodes = this.treeData.filter(item => treeIdArr.includes(String(item?.id)))
					this.treeData = this.treeData.map(item => {
						const { id, deviceTypeCode, name, type } = item
						const isChecked = this.selectedNodes?.findIndex(obj => obj?.id == item?.id)
						return {
							name: name,
							id: id, // 树的节点id
							deviceTypeCode: deviceTypeCode,
							type: type,
							_checked: isChecked !== -1,
							_disabled: this.isReadonly,
						}
					})
					// this.selectedNodes = [this.treeData[0]]
				}
			})
		},
		// 左侧模板树选择
		handleSelect(selection) {
			this.selectedNodes = selection
		},
		handleNoticeWayChange() {
			if (this.noticeType === 'person') {
				this.$nextTick(() => {
					this.$refs.AlarmPersonSelectRef.clearSelectData()
					this.$refs.AlarmPersonSelectRef.updateUserList()
				})
				this.treeType = 1
			}
			if (this.noticeType === 'organization') {
				this.$nextTick(() => {
					this.$refs.AlarmPersonSelectRef.clearSelectData()
					this.$refs.AlarmPersonSelectRef.updateUserList()
				})
				this.treeType = 2
			}
			if (['role', 'customizeUrl'].includes(this.formVal.noticePerson)) {
				this.$nextTick(() => {
					this.$refs.AlarmConfigSelectRef?.updateTree(this.formVal.noticePerson)
				})
			}
		},
	},
}
</script>
<style scoped>
.resetForm .form-module .form-module-con .ivu-row .ps-center {
	display: flex !important;
}
.resetForm .form-module .form-module-con .ivu-row .timereset .ivu-form-item /deep/ .ivu-form-item-content {
	display: flex !important;
	align-items: center;
}
.resetForm .form-module .form-module-con .ivu-row {
	display: flex !important;
	flex-wrap: wrap;
}
</style>	
<style lang="less" scoped>
.header-btn-container {
	padding: 12px;
	/deep/ .ivu-btn {
		width: 200px;
	}
	::v-deep {
		.ivu-radio-wrapper-checked {
			background: #1192e8;
			color: #ffffff;
		}
	}
}
.header {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 5px;
}
.pad-r60 {
	padding-right: 60px;
}
.mar-l10 {
	margin-left: 10px;
}
.mintue {
	white-space: nowrap;
	margin-left: 4px;
}
.rate {
	height: 30px;
	line-height: 30px;
	margin-left: 10px;
}
.card-container {
	border: 2px solid #dadae2;
	width: 650px;
	height: 355px;
	overflow: hidden;
}
.alarm-scheme {
	padding: 0 16px;
	overflow: hidden;
	height: 100%;
	.alarm-scheme-wrap {
		position: relative;
		display: flex;
		height: calc(100vh - 40px);
		.alarm-scheme-left {
			flex-shrink: 0;
			width: 270px;
			height: 94%;
			padding-right: 16px;
			border-right: 1px solid #dadae2;
			display: flex;
			flex-direction: column;
			.acheme-left-header {
				display: flex;
				// margin-bottom: 10px;
			}
			.disable-tab {
				opacity: 0.5;
			}
			.tree-type-wrap {
				display: flex;
				margin-top: 8px;
				border: 1px solid #b4c8fd;
				padding-bottom: 2px;
				border-radius: 4px;

				.tab-item {
					width: 114px;
					cursor: pointer;
					text-align: center;
					// line-height: 36px;
					color: #5f627d;
					font-weight: 400;
					font-size: 14px;
					background: #fff;
					border: none;
				}
				.cur-tab {
					color: #282c42;
					font-weight: 700;
					font-size: 14px;
					background: #b4c8fd;
				}
			}
			.info-left {
				padding: 8px 0;
				.site-info-con {
					font-size: 16px;
					padding-bottom: 16px;
					border-bottom: 1px solid #dadae2;
					color: #535567;
					.site-info-con-tit {
						display: flex;
						align-items: center;
						.icon-a-Property1zhengchang {
							font-size: 13px;
							margin-right: 5px;
							color: #3aa7d8;
						}
					}
					.site-info-con-inner {
						line-height: 1.5;
						.site-info-con-inner-row {
							margin-top: 16px;
						}
					}
				}
				.site-info-remarks {
					padding-top: 16px;
					font-size: 16px;
					line-height: 1.5;
				}
			}
		}
		.form-wrap {
			flex-grow: 1;
			height: 100%;
			overflow: auto;
			.form {
				width: 100%;
				flex-grow: 1;
				.form-module {
					.form-module-tit {
						display: flex;
						align-items: center;
						justify-content: left;
						height: 40px;
						border-bottom: 2px solid #dadae2;
						margin-left: 12px;
						font-size: 18px;
						font-weight: 600;
					}
					.form-module-con {
						padding: 25px 80px;
						/deep/ .ivu-row {
							position: relative;

							.ivu-form-item {
								margin-bottom: 20px;
							}
						}
						.btn-wrap {
							display: flex;
							align-items: center;
							width: 82px;
							height: 30px;
							padding-left: 10px;
							.btn {
								display: flex;
								align-items: center;
								justify-content: center;
								width: 20px;
								height: 20px;
								&.btn-add {
									background: #3aa7d8;
									border-color: #3aa7d8;
									/deep/ .ivu-icon {
										line-height: 0.3 !important;
									}
								}
								&.btn-subtract {
									margin-left: 12px;
									background: #ec5151;
									border-color: #ec5151;
									/deep/ .ivu-icon {
										line-height: 0.3 !important;
									}
								}
							}
						}
						.rulebtn {
							position: absolute;
							right: -50px;
							bottom: 20px;
						}
						.line {
							border: 1px dashed #dadae2;
						}
						.col-mr {
							margin-right: 15px;
						}
						/deep/ .checkbox-group {
							.ivu-checkbox-wrapper {
								span {
									margin-right: 5px;
								}
							}
						}
					}
				}
			}
		}
	}
}
.form-item-wrap {
	display: flex;
	flex-grow: 1;
	.xx-form-item {
		min-width: 250px;
	}
	.tag {
		margin: 6px 10px;
	}
}
.header-button {
	float: right;
}
.template-container {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-gap: 10px;
	max-height: 136px;
	overflow-y: auto;
	width: 220px;
	// border: 1px solid #ccc;
	padding: 5px;
	.template-container-item {
		height: 36px;
		width: 92px;
		line-height: 36px;
		text-align: center;
		border: 1px solid #abbdf6;
		border-radius: 8px;
		box-sizing: border-box;
		cursor: pointer;
	}
	.disabled {
		cursor: not-allowed;
		color: #ccc;
		background-color: #eee;
	}
}
</style>