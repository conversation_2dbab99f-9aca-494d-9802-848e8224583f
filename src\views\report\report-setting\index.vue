<!--
 * @Description: 报表管理
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-03-14 16:32:31
 * @LastEditors: houyan
 * @LastEditTime: 2024-03-15 15:26:02
-->
<template lang="pug">
.report-setting
	.report-setting-content
		Tabs(v-model='tabValue', :animated='false')
			TabPane(label='报表配置', name='setting')
				setting(v-if='tabValue === "setting"')
			TabPane(label='权限配置', name='permiss')
				permiss(v-if='tabValue === "permiss"')
</template>
<script>
import Setting from './modules/setting/index.vue'
import Permiss from './modules/permiss/Permisses.vue'
export default {
	name: 'data-fill-management',
	components: {
		Setting,
		Permiss,
	},
	data() {
		return {
			tabValue: 'setting',
		}
	},
}
</script>
<style lang="less" scoped>
.report-setting {
	height: 100%;
	padding: 16px;
	background-color: rgb(242 242 242) !important;
	&-content {
		height: 100%;
		padding: 16px;
		background: #fff;
	}
	::v-deep {
		.ivu-tabs {
			display: flex;
			flex-direction: column;
			height: 100%;
			.ivu-tabs-content {
				flex: 1;
			}
			.ivu-tabs-tabpane {
				height: 100%;
			}
		}
	}
}
</style>
