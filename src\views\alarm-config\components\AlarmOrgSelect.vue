<!--
 * @Description: 人员配置选择框
 * @Author: shenxh
 * @Date: 2022-03-23 10:15:05
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2024-08-13 13:55:45
-->

<template>
	<div>
		<Tree
			:data="ownershipList"
			@on-select-change="changeTree"
			@on-check-change="changeTreeCheckBox"
			:show-checkbox="true"
		></Tree>
	</div>
</template>

<script>
// import MultifunctionalSelectNew from '@/components/gc-multifunctional-select-new'
import { getDepartment } from '@/api/common.js'

export default {
	name: 'alarm-org-select',
	components: {
		// MultifunctionalSelectNew,
	},
	props: {
		// 已选择的value数组
		selectedValue: {
			type: Array,
			default: () => [],
		},
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			searchVal: '',
			tableLoading: false,
			currentNode: {},
			ownershipList: [],
			usersList: [],
			total: 0,
			page: {
				pageSize: 20,
				pageNum: 1,
			},
			firstTree: '',
		}
	},
	computed: {},
	watch: {},
	created() {
		this._getDepartment()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 选中树形图节点
		changeTree(selectedNodes, currentNode) {
			this.page.pageNum = 1
			this.currentNode = currentNode
		},
		// 选中树形图节点勾选框
		changeTreeCheckBox(selectedNodes) {
			this.$emit('change-tree-check-box', selectedNodes)
		},

		// 部门组织树
		_getDepartment() {
			getDepartment().then(res => {
				const data = res.result
				if (data) {
					this.firstTree = data[0].id
					this.currentNode = data[0]
					this.ownershipList = this._filterUsers(data)
				}
			})
		},

		// 过滤
		_filterUsers(list) {
			if (!list || !list.length) return []
			return list.map(item => {
				const isChecked = this.selectedValue.includes(String(item?.id))
				return {
					...item,
					title: item.name,
					expand: true,
					checked: isChecked,
					selected: item.id === this.firstTree,
					disabled: this.disabled,
					children: this._filterUsers(item.nodes),
				}
			})
		},
		clearSelectData() {
			this.$refs.multifunctionalSelect.clearSelectValue()
		},
	},
}
</script>

<style lang="less" scoped></style>
