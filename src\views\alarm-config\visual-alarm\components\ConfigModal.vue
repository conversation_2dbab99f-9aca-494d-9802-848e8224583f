<template lang="pug">
es-modal.es-modal(
	:transfer='false',
	:is-direct-close-modal='false',
	width='1400',
	v-model='showModal',
	title='有效规制筛选',
	@on-cancel='handleClose',
	@on-visible-change='changeModal'
)
	template(slot='body')
		Spin(v-show='saveLoading', fix)
		Row(:gutter='16')
			Col(:span='11')
				.search-box
					.label 规则类型:
					Select.w100(v-model='toBeChoseQuery.alarmConfigType', filterable, remote, clearable)
						Option(value='1') 通用规则
						Option(value='2') 专用规则
					.label.water-margin-left-8 报警等级:
					Select.w100.water-margin-left-8(v-model='toBeChoseQuery.alarmLevelId', clearable, transfer)
						Option(value='1') 低级报警
						Option(value='2') 普通报警
						Option(value='3') 紧急报警
					.label.water-margin-left-8 方案名称:
					Input.w100(v-model='toBeChoseQuery.name', clearable)
					Button.water-margin-left-8(type='primary', @click='getUnbindAlarmRules(1)') 查询
				.h500
					Spin(v-show='toBeChoseLoading', fix)
					es-table.water-table(
						v-if='showModal',
						ref='toBeChoseTable',
						stripe,
						:columns='columns',
						:data='toBeChoseTableData',
						border,
						showPage,
						:pageData='toBeChosePageData',
						@on-selection-change='handleToBeChoseSelectChange',
						@on-page-num-change='handlePageNum',
						@on-page-size-change='handlePageSize'
					)
						template(slot-scope='{ row }', slot='configType')
							span {{ row.configType === 1 ? '通用规则' : '专用规则' }}
						template(slot-scope='{ row }', slot='abstract')
							span {{ row.ruleList[0].alarmAbstract || '--' }}
						template(slot-scope='{ row }', slot='status')
							span {{ row.status === 0 ? '禁用' : '启用' }}
						template(slot-scope='{ row }', slot='effectivePoints')
							span {{ row.refList ? row.refList.length : '--' }}
			Col.flex-box(:span='2')
				.flag-text 待选择
				.btns-box.h500
					Button(
						:disabled='toBeChoseSelected.length === 0 || saveLoading',
						shape='circle',
						icon='ios-arrow-forward',
						type='primary',
						@click='handleSave("insert")'
					)
					Button(
						:disabled='chosedSelected.length === 0 || saveLoading',
						shape='circle',
						icon='ios-arrow-back',
						type='primary',
						@click='handleSave("delete")'
					)
				.flag-text 已选择
			Col(:span='11')
				.search-box
					.label 规则类型:
					Select.w100(v-model='chosedQuery.alarmConfigType', filterable, remote, clearable)
						Option(value='1') 通用规则
						Option(value='2') 专用规则
					.label.water-margin-left-8 报警等级:
					Select.w100.water-margin-left-8(v-model='chosedQuery.alarmLevelId', clearable, transfer)
						Option(value='1') 低级报警
						Option(value='2') 普通报警
						Option(value='3') 紧急报警
					.label.water-margin-left-8 方案名称:
					Input.w100(v-model='chosedQuery.name', clearable)
					Button.water-margin-left-8(type='primary', @click='getBindedAlarmRulers') 查询
				.h500
					Spin(v-show='chosedLoading', fix)
					es-table.water-table(
						v-if='showModal',
						ref='chosedTable',
						stripe,
						:columns='columns',
						:data='chosedTableData',
						border,
						@on-selection-change='handleChosedSelectChange'
					)
						template(slot-scope='{ row }', slot='configType')
							span {{ row.configType === 1 ? '通用规则' : '专用规则' }}
						template(slot-scope='{ row }', slot='abstract')
							span {{ row.ruleList[0].alarmAbstract || '--' }}
						template(slot-scope='{ row }', slot='status')
							span {{ row.status === 0 ? '禁用' : '启用' }}
						template(slot-scope='{ row }', slot='effectivePoints')
							span {{ (row.refList && row.refList.length) || '--' }}
	template(slot='footer')
		Button(@click='handleClose') 取消
		//- Button(type="primary", :loading="saveLoading", @click="handleSave") 保存
</template>

<script>
import { getSystemAlarmFilter, getAlarmFilter, bindAlarmRulers } from '@/api/alarm-config'

export default {
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	props: {
		showModal: Boolean,
		code: String,
	},
	data() {
		return {
			columns: [
				{
					type: 'selection',
					width: 50,
					align: 'center',
				},
				{
					title: '规则类型',
					// 1通用2专用
					slot: 'configType',
					align: 'center',
				},
				{
					title: '方案名称',
					key: 'name',
					align: 'center',
				},
				{
					title: '报警摘要',
					slot: 'abstract',
					align: 'center',
				},
				{
					title: '是否启用',
					// 0禁用1启用
					slot: 'status',
					align: 'center',
				},
				{
					title: '生效点数',
					// refList.length
					slot: 'effectivePoints',
					align: 'center',
				},
			],
			toBeChoseLoading: false,
			toBeChoseQuery: {
				alarmConfigType: '',
				alarmLevelId: '',
				name: '',
			},
			// 待选择表格数据
			toBeChoseTableData: [],
			toBeChosePageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 30, 40, 50],
				total: 0,
				transfer: true,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
			// 待选择表格选中数据
			toBeChoseSelected: [],

			chosedQuery: {
				alarmConfigType: '',
				alarmLevelId: '',
				name: '',
			},
			// 已选择表格数据
			chosedLoading: false,
			chosedTableData: [],
			// 已选择表格选中数据
			chosedSelected: [],

			// 保存
			saveLoading: false,
		}
	},
	methods: {
		changeModal(value) {
			if (value) {
				// 获取左侧表格
				this.getUnbindAlarmRules()
				// 获取右侧表格
				this.getBindedAlarmRulers()
			} else {
				this.chosedTableData = []
				this.chosedTableData = []
				this.toBeChoseSelected = []
				this.chosedSelected = []
				this.$emit('save')
			}
		},
		// 表格选中事件
		handleToBeChoseSelectChange(rows) {
			this.toBeChoseSelected = rows
		},
		handleChosedSelectChange(rows) {
			this.chosedSelected = rows
		},
		// 获取左侧表格数据
		async getUnbindAlarmRules(page) {
			this.toBeChoseLoading = true
			try {
				const { current: pageNum, pageSize } = this.toBeChosePageData
				const res = await getSystemAlarmFilter({
					...JSON.parse(JSON.stringify(this.toBeChoseQuery)),
					sysCode: this.code,
					pageNum: page ? page : pageNum,
					pageSize,
				})
				const { list = [], total = 0 } = res.result
				this.toBeChoseTableData = list
				this.toBeChosePageData.total = total
			} catch (error) {
				this.toBeChoseTableData = []
			} finally {
				this.toBeChoseLoading = false
			}
		},
		// 获取右侧表格数据
		async getBindedAlarmRulers() {
			this.chosedLoading = true
			try {
				const res = await getAlarmFilter(this.code, JSON.parse(JSON.stringify(this.chosedQuery)))
				const { alarmConfigs = [] } = res.result
				this.chosedTableData = alarmConfigs
			} catch (error) {
				this.chosedTableData = []
			} finally {
				this.chosedLoading = false
			}
		},
		// 按钮-保存
		async handleSave(operateType) {
			this.saveLoading = true
			let ids = []
			if (operateType === 'insert') {
				ids = this.toBeChoseSelected.map(item => item.id)
			} else if (operateType === 'delete') {
				ids = this.chosedSelected.map(item => item.id)
			}
			try {
				await bindAlarmRulers({
					operateType,
					code: this.code,
					alarmConfigIds: ids,
				})
				this.chosedSelected = []
				this.toBeChoseSelected = []
				// 获取左侧表格
				this.getUnbindAlarmRules()
				// 获取右侧表格
				this.getBindedAlarmRulers()
			} catch (error) {
				this.$Message.error('操作失败')
			} finally {
				this.saveLoading = false
			}
		},
		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.toBeChosePageData.current = pageNum
			this.getUnbindAlarmRules()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.toBeChosePageData.pageSize = pageSize
			this.getUnbindAlarmRules()
		},
	},
}
</script>

<style lang="less" scoped>
.h500 {
	position: relative;
	height: 500px;
	overflow: hidden;
}
.btns-box {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	.ivu-btn {
		& + .ivu-btn {
			margin-top: 10px;
		}
	}
}
.flex-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	.flag-text {
		color: #333;
		font-size: 16px;
		font-weight: 600;
		writing-mode: vertical-lr;
		letter-spacing: 4px;
	}
}
.w100 {
	width: 100%;
}
.search-box {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	.label {
		flex-shrink: 0;
		width: 60px;
	}
}
</style>
