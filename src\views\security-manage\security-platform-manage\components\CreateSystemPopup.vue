<!--
 * @Description: 创建系统弹窗
 * @Author: shenxh
 * @Date: 2022-04-01 15:54:51
 * @LastEditors: shenxh
 * @LastEditTime: 2023-06-13 11:13:31
-->

<template lang="pug">
.create-system-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='600',
		v-model='showModal',
		:title='formData && formData.id ? "编辑" : "添加"',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :label-width='80')
					FormItem(label='平台名称', prop='name')
						Input(v-model='formData.name', placeholder='请输入平台名称')
					FormItem(label='平台类型', prop='type')
						Select(v-model='formData.type', transfer, filterable, placeholder='请选择')
							Option(v-for='item in platformList', :key='item.type', :value='item.type') {{ item.name }}
					FormItem(label='平台地址', prop='address')
						Input(v-model='formData.address', placeholder='请输入平台地址')
					FormItem(label='账号', prop='appkey')
						Input(v-model='formData.appkey', placeholder='请输入账号')
					FormItem(label='密码', prop='secret')
						Input(v-model='formData.secret', type='password', placeholder='请输入密码')
					FormItem(label='备注', prop='memo')
						Input(v-model='formData.memo', type='textarea', maxlength='100', show-word-limit, placeholder='请输入备注')
		template(slot='footer')
			Button(@click='handleClose') 关闭
			Button(type='primary', :loading='testBtnLoading', @click='handleTest') 测试
			Button(type='primary', @click='handleSubForm') 保存
</template>

<script>
import { updSecurityPlatform, checkData, getPlatDic } from '@/api/security-manage'

export default {
	name: 'create-system-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {},
	props: {
		showModal: Boolean,
		data: Object,
	},
	data() {
		const validateName = (rule, value, callback) => {
			const myRule = /^[(\u2E80-\u9FFF)A-Za-z]{2,}$/

			if (myRule.test(value)) {
				callback()
			} else {
				callback(new Error('仅支持中文和字母，长度不少于2个字符'))
			}
		}
		const validateAddress = (rule, value, callback) => {
			// const myRule = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
			const myRule = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)((:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5]))?)$/

			if (myRule.test(value)) {
				callback()
			} else {
				callback(new Error('请输入正确地址，如：***************'))
			}
		}
		const validateAppkey = (rule, value, callback) => {
			const myRule = /^[A-Za-z0-9_-]{1,32}$/

			if (myRule.test(value)) {
				callback()
			} else {
				callback(new Error('仅支持数字、字母、下划线以及短线，长度为1-32个字符'))
			}
		}
		const validateSecret = (rule, value, callback) => {
			const myRule = /^[A-Za-z0-9_-]{6,}$/

			if (myRule.test(value)) {
				callback()
			} else {
				callback(new Error('仅支持数字、字母、下划线以及短线，长度不少于6个字符'))
			}
		}

		return {
			testBtnLoading: false,
			formData: {},
			platformList: [],
			formRules: {
				name: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
					{ validator: validateName, trigger: 'blur' },
				],
				type: [
					{
						required: true,
						type: 'number',
						message: '请选择',
						trigger: 'change',
					},
				],
				address: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
					{ validator: validateAddress, trigger: 'blur' },
				],
				appkey: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
					{ validator: validateAppkey, trigger: 'blur' },
				],
				secret: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
					{ validator: validateSecret, trigger: 'blur' },
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		this._getPlatDic()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				this.formData = { ...this.data }
			} else {
				this.$refs.form.resetFields()
			}
		},

		// 按钮-测试
		handleTest() {
			// this.$refs.form.validate(valid => {
			// 	if (valid) {
			// 		this._checkData()
			// 	}
			// })
			this._checkData()
		},

		// 按钮-保存
		handleSubForm() {
			// this.$refs.form.validate(valid => {
			// 	if (valid) {
			// 		this._updSecurityPlatform()
			// 	}
			// })
			this._updSecurityPlatform()
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		// 添加及编辑安防平台
		_updSecurityPlatform() {
			const { id, name, type, address, appkey, secret, memo } = this.formData

			updSecurityPlatform({
				id,
				name,
				type,
				address,
				appkey,
				secret,
				memo,
			}).then(res => {
				console.log(res)
				this.$Message.success('操作成功')

				this.handleClose()
				this.$emit('submit-form', this.formData)
			})
		},

		// 连接信息测试
		_checkData() {
			const { type, address, appkey, secret } = this.formData

			this.testBtnLoading = true
			checkData({
				type,
				address,
				appkey,
				secret,
			})
				.then(res => {
					this.testBtnLoading = false
					if (res.result === '0') {
						this.$Message.success('验证成功')
					} else {
						this.$Message.error(res.result || '验证失败')
					}
				})
				.catch(() => {
					this.testBtnLoading = false
				})
		},

		// 获取平台数据
		_getPlatDic() {
			getPlatDic().then(res => {
				const data = res.result

				this.platformList = data || []
			})
		},
	},
}
</script>

<style lang="less" scoped>
.create-system-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			padding: 0 40px;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
