import * as THREE from 'three'
export default {
	//同一个工艺流程图中处理多个相同模型的数据
	handSameModalData({ type, name, arr, arrOrigin }) {
		arr.map((val, index) => {
			let obj = {
				type,
				name: name + index,
			}
			const { position, rotation, difname } = val
			if (position) obj.position = position
			obj.hasGlbRotateSet = true
			if (rotation) {
				obj.rotation = new THREE.Vector3(...rotation)
			}
			if (difname) obj.name = difname
			arrOrigin.push(obj)
		})
		console.log(arrOrigin)
	},
	//设置一个工艺流程图中所有模型数据公共的配置（只限glb）
	dealPositionData({ allArr }) {
		console.log(allArr)
		return allArr.map(val => {
			const { position, type, name } = val
			let pointString = []
			let positionNumber = []
			pointString = position.split(' ')
			positionNumber = [Number(pointString[2]), Number(pointString[0]), Number(pointString[1])]
			// positionNumber = [
			//	 Number(pointString[0]),
			//	 -Number(pointString[2]),
			//	 Number(pointString[1])
			// ];
			if (!val.rotation) {
				val.rotation = new THREE.Vector3(Math.PI / 2, Math.PI / 2, 0)
				// 调整页面的特殊模型的旋转角度
				if (type === 'liuliangji' && name === '进水') {
					val.rotation = new THREE.Vector3(Math.PI / 2, 0, 0)
				}
				if (type === 'liuliangji' || type === 'yaliji') {
					val.scale = new THREE.Vector3(2, 2, 2)
				}
				val.hasGlbRotateSet = true
			}
			val.position = new THREE.Vector3(...positionNumber)
			val.modelType = 'glb'
			return val
		})
	},
	//老合台三维场景（模型数据）
	getObjDataZLYSC() {
		//树
		let treeArr = [
			{ position: '-3717.25 241.619 -3708.28' },
			{ position: '-4239.05 241.619 -3708.28' },
			{ position: '-4708.22 241.619 -4369.34' },
			{ position: '-4708.22 241.619 -4037.41' },
			{ position: '-4708.22 241.619 -3708.28' },
			{ position: '-3273.36 241.619 -3708.28' },
			{ position: '-3273.36 241.619 -4037.41' },
			{ position: '-3273.36 241.619 -4369.34' },
			{ position: '-2576.7 241.619 -4369.34' },
			{ position: '-2576.7 241.619 -4088.32' },
			{ position: '-2576.7 241.619 -3782.01' },
			{ position: '-2222.06 241.619 -3782.01' },
			{ position: '-1853.8 241.619 -3782.01' },
			{ position: '-1514.16 241.619 -3782.01' },
			{ position: '-1514.16 241.619 -4086.97' },
			{ position: '-1514.16 241.619 -4355.92' },
			{ position: '-639.06 248.884 -3086.89' },
			{ position: '-639.06 248.884 -3388.69' },
			{ position: '-639.06 248.884 -2807.65' },
			{ position: '-639.06 248.884 -2500.47' },
			{ position: '-639.06 248.884 -2198.67' },
			{ position: '-639.06 248.884 -1254.2' },
			{ position: '-639.06 248.884 -1556' },
			{ position: '-639.06 248.884 -1863.17' },
			{ position: '-639.06 248.884 -966.512' },
			{ position: '-639.06 248.884 -659.333' },
			{ position: '-639.06 248.884 -357.533' },
			{ position: '-1702.99 256.719 -1424.22' },
			{ position: '-1702.99 256.719 -1721.06' },
			{ position: '-1702.99 256.719 -2021.59' },
			{ position: '-1702.99 256.719 -2331.9' },
			{ position: '-1702.99 256.719 -2624.29' },
			{ position: '-1702.99 256.719 -2924.95' },
			{ position: '-1499.13 255.012 -824.398' },
			{ position: '-1722.4 255.012 -824.398' },
			{ position: '-1966.43 255.012 -824.398' },
			{ position: '-3966.69 255.012 -756.943' },
			{ position: '-3514.27 250.114 1008.32' },
			{ position: '-557.081 250.114 1008.32' },
			{ position: '-557.081 250.114 1244.36' },
			{ position: '-557.081 250.114 1503.24' },
			{ position: '-557.081 250.114 1751.61' },
			{ position: '-3581.59 255.012 -716.518' },
			{ position: '-2932.01 255.012 -756.943' },
			{ position: '-2546.91 255.012 -716.518' },
			{ position: '-270.38 250.114 1759.08' },
			{ position: '-270.38 250.114 1498.08' },
			{ position: '-270.38 250.114 1222.8' },
			{ position: '-270.38 250.114 1004.23' },
			{ position: '-270.38 250.114 673.659' },
			{ position: '-270.38 250.114 60.2463' },
		]
		//太阳能
		let solarArr = [
			{ position: '-2475.56 198.323 -1589.72' },
			{ position: '-2302.78 198.323 -1792.34' },
			{ position: '-2128.21 198.323 -1996.76' },
			{ position: '-2141.72 198.323 -1544.5' },
			{ position: '-1976.11 198.323 -1742.61' },
			{ position: '-2839.52 198.323 -1588.91' },
			{ position: '-2666.74 198.323 -1791.53' },
			{ position: '-2492.17 198.323 -1995.95' },
			{ position: '-2313.96 198.323 -2205.52' },
			{ position: '-2134.86 198.323 -2415.83' },
			{ position: '-3244.37 198.323 -1587.37' },
			{ position: '-3071.59 198.323 -1789.99' },
			{ position: '-2897.02 198.323 -1994.41' },
			{ position: '-2718.81 198.323 -2203.98' },
			{ position: '-2539.71 198.323 -2414.29' },
			{ position: '-2355.03 198.323 -2640.74' },
			{ position: '-2161.08 198.323 -2861.26' },
			{ position: '-3731.31 198.323 -1562.4' },
			{ position: '-3556.74 198.323 -1766.82' },
			{ position: '-3378.53 198.323 -1976.39' },
			{ position: '-3199.43 198.323 -2186.7' },
			{ position: '-3014.74 198.323 -2413.15' },
			{ position: '-2820.79 198.323 -2633.67' },
			{ position: '-2631.18 198.323 -2861.57' },
			{ position: '-3827.96 198.323 -1964.51' },
			{ position: '-3648.86 198.323 -2174.82' },
			{ position: '-3464.17 198.323 -2401.27' },
			{ position: '-3270.22 198.323 -2621.79' },
			{ position: '-3080.61 198.323 -2849.69' },
			{ position: '-4019.54 198.323 -1733.35' },
			{ position: '-4203.39 198.323 -1508.14' },
			{ position: '-4196.37 198.323 -2055.85' },
			{ position: '-4011.69 198.323 -2282.3' },
			{ position: '-3817.74 198.323 -2502.82' },
			{ position: '-3628.13 198.323 -2730.72' },
			{ position: '-4295.35 198.323 -2406.94' },
			{ position: '-4101.39 198.323 -2627.47' },
			{ position: '-3911.79 198.323 -2855.36' },
		]
		//栅栏
		let lanArr = [
			{
				position: '-797.931 117.776 1866.05',
				rotation: [Math.PI / 2, 0, 0],
			},
			{
				position: '-621.466 117.776 1866.05',
				rotation: [Math.PI / 2, 0, 0],
			},
			{
				position: '-444.844 117.776 1866.05',
				rotation: [Math.PI / 2, 0, 0],
			},
			{ position: '-345.439 117.776 1788.79' },
			{ position: '-345.439 117.776 1612.14' },
			{ position: '-345.439 117.776 1258.93' },
			{ position: '-345.439 117.776 1435.58' },
			{ position: '-345.439 117.776 1082.44' },
			{ position: '-345.439 117.776 905.786' },
			{ position: '519.315 209.102 3818.15' },
			{ position: '519.315 209.102 3994.33' },
			{ position: '519.315 209.102 3641.93' },
			{ position: '519.315 209.102 3465.75' },
			{ position: '519.315 209.102 3289.13' },
			{ position: '519.315 209.102 2759.75' },
			{ position: '519.315 209.102 2936.37' },
			{ position: '519.315 209.102 3112.55' },
			{ position: '519.315 209.102 2583.31' },
			{ position: '519.315 209.102 2407.12' },
			{ position: '519.315 209.102 2230.5' },
			{ position: '519.315 209.102 1701.33' },
			{ position: '519.315 209.102 1877.94' },
			{ position: '519.315 209.102 2054.13' },
			{ position: '519.315 209.102 1524.91' },
			{ position: '519.315 209.102 1348.72' },
			{ position: '519.315 209.102 1172.11' },
			{
				position: '319.555 155.227 125.381',
				rotation: [Math.PI / 2, 0, 0],
			},
			{ position: '418.619 155.227 47.7441' },
			{ position: '-270.38 250.114 60.2463' },
			{ position: '-270.38 250.114 60.2463' },
			{ position: '-270.38 250.114 60.2463' },
			{ position: '-270.38 250.114 60.2463' },
		]
		let arr = [
			{
				type: 'lawnLHTGTLF',
				name: '地盘',
				position: '-1757.563 84.627 -121.058',
			},
			{
				type: 'lawn1LHTGTLF',
				name: '地盘1',
				position: '-647.201 85.4424 -1850.31',
			},
			{
				type: 'lawn2LHTGTLF',
				name: '地盘2',
				position: '-619.873 81.1307 1337',
			},
			{
				type: 'lawn3LHTGTLF',
				name: '地盘3',
				position: '-3139.35 81.8986 -4205.93',
			},
			{
				type: 'lawn4LHTGTLF',
				name: '地盘4',
				position: '-3113.49 84.6273 2057.58',
			},
			{
				type: 'lawn5LHTGTLF',
				name: '地盘5',
				position: '-1640.41 84.6273 2140.54',
			},
			{
				type: 'lawn6LHTGTLF',
				name: '地盘6',
				position: '-291.229 118.154 1197.38',
			},
			{
				type: 'lawn7LHTGTLF',
				name: '地盘7',
				position: '-236.426 115.364 38.5939',
			},
			{
				type: 'lawn8LHTGTLF',
				name: '地盘8',
				position: '-2756.36 84.6273 -145.379',
			},
			{
				type: 'lawn9LHTGTLF',
				name: '地盘9',
				position: '-1719.94 84.6273 -2185.47',
			},
			{
				type: 'lawn10LHTGTLF',
				name: '地盘10',
				position: '-3791.03 84.6273 -145.379',
			},
			{
				type: 'lawn11LHTGTLF',
				name: '地盘11',
				position: '-2070.28 104.04 -4124.73',
			},
			{
				type: 'lawn12LHTGTLF',
				name: '地盘12',
				position: '-3151.8 97.9901 -2185.56',
			},
			{
				type: 'lawn13LHTGTLF',
				name: '地盘13',
				position: '-4038.79 104.04 -4124.73',
			},
			{
				type: 'roadLHTGTLF',
				name: '道路',
				needLabel: true,
				position: '-2212.188 69.976 -607.855',
			},
			{
				type: 'wallLHTGTLF',
				name: '墙',
				position: '-2258.482 115.364 -619.957',
			},
			{
				type: 'wastewaterreclamationLHTGTLF',
				name: '房子1',
				position: '-1760.556 317.838 -78.021',
			},
			{
				type: 'distributionroom1LHTGTLF',
				name: '屋顶装饰1',
				position: '-4781.12 264.182 -22.2873',
			},
			{
				type: 'distributionroom2LHTGTLF',
				name: '屋顶装饰2',
				position: '-4799.37 264.182 1418.54',
			},
			{
				type: 'distributionroom3LHTGTLF',
				name: '屋顶装饰3',
				position: '-4799.37 264.182 2736.29',
			},
			{
				type: 'distributionroom4LHTGTLF',
				name: '屋顶装饰4',
				position: '-756.276 264.182 -4110.29',
			},
			{
				type: 'officebuildingLHTGTLF',
				name: '房子1',
				position: '-446.881 310.149 2761.43',
			},
			{
				type: 'pumphouseLHTGTLF',
				name: '房子2',
				position: '-3130.59 290.085 -4842.14',
			},
			{
				type: 'reactiontankLHTGTLF',
				name: '房子3',
				position: '-3137.18 448.476 2028.95',
			},
			{
				type: 'recoveryworkshop2LHTGTLF',
				name: '房子4',
				position: '-1659.02 269.667 2507.12',
			},
			{
				type: 'recoveryworkshop1LHTGTLF',
				name: '房子5',
				position: '-1624.37 308.612 1549.82',
			},
			{
				type: 'securityroomLHTGTLF',
				name: '房子6',
				position: '144.744 189.938 -35.4994',
			},
			{
				type: 'sedimentLHTGTLF',
				name: '房子7',
				position: '-2751.19 443.625 -157.803',
			},
			{
				type: 'sedimentLHTGTLF',
				name: '房子8',
				position: '-3806.2 443.625 -157.803',
			},
			{
				type: 'handrail1LHTGTLF',
				name: '围栏1',
				position: '418.073 154.808 -139.776',
			},
			{
				type: 'handrail1LHTGTLF',
				name: '围栏2',
				position: '520.151 209.735 984.874',
			},
		]
		this.handSameModalData({
			type: 'Tree1LHTGTLF',
			name: '树',
			arr: treeArr,
			arrOrigin: arr,
		})
		this.handSameModalData({
			type: 'solarpanelLHTGTLF',
			name: '太阳能',
			arr: solarArr,
			arrOrigin: arr,
		})
		this.handSameModalData({
			type: 'handrailLHTGTLF',
			name: '栅栏',
			arr: lanArr,
			arrOrigin: arr,
		})
		const changArr = this.dealPositionData({ allArr: arr })
		return changArr
	},
	//二台子水厂沙盘
	getObjDataEHZGLB(configType) {
		const arr = [
			{
				name: '建筑',
				type: '20210720194454082_build',
				position: '-11752.8 2264.73 -17323.6',
				needTransparent: true,
			},
			{
				name: '地块',
				type: '20210720194454082_dikuai',
				position: '-19502.1 30 -16311.4',
				needTransparent: true,
			},
			{
				name: '地面',
				type: '20210720194454082_dimian',
				position: '-12514.3 0 -14235',
				needTransparent: true,
			},
			{
				name: '草',
				type: '20210720194454082_grass',
				position: '-13726.7 111.839 -26016.2',
				needTransparent: true,
			},
			{
				name: '墙',
				type: 'guangqiang',
				position: '-3181.959 4000 -12524.203',
				needTransparent: true,
				needBeam: true,
			},
			{
				name: '地面',
				type: 'roadETZGLB',
				position: '-3140.01 42.4804 -12084.2',
			},
			{
				name: '电箱',
				type: 'distribution_boxETZGLB',
				position: '2278.31 372.561 -18322.9',
				// showLabelType: 1,
			},
			{
				name: '元素1',
				type: 'element1ETZGLB',
				position: '2893.1 458.151 -15382.1',
				// showLabelType: 1,
			},
			{
				name: '元素2',
				type: 'element2ETZGLB-processed',
				position: '-4927.05 524.193 -2982.19',
				// showLabelType: 1,
			},
			{
				name: '玻璃',
				type: 'glassETZGLB',
				position: '2568.99 603.263 -11807.2',
				// showLabelType: 1,
			},
			{
				name: '草1',
				type: 'grassland1ETZGLB',
				position: '-11493 86.5049 -12239.3',
				// showLabelType: 1,
			},
			{
				name: '草2',
				type: 'grassland2ETZGLB',
				position: '3767.61 129 -17099.1',
				// showLabelType: 1,
			},
			{
				name: '草3',
				type: 'grassland3ETZGLB',
				position: '3170.44 69.3754 -8480.35',
			},
			{
				name: '草4',
				type: 'grassland4ETZGLB',
				position: '-10338.8 63.9065 -23801.1',
			},
			{
				name: '草5',
				type: 'grassland5ETZGLB',
				position: '-4275.14 135.487 -23815.1',
			},
			{
				name: '草6',
				type: 'grassland6ETZGLB',
				position: '3671.91 57.0135 -23894.1',
			},
			{
				name: '草7',
				type: 'glassland7ETZGLB',
				position: '-4968.58 179.367 -7778.8',
			},
			{
				name: '草8',
				type: 'glassland8ETZGLB',
				position: '-4910.54 166.295 -2972.18',
			},
			{
				name: '草9',
				type: 'glassland9ETZGLB',
				position: '-4378.04 169.661 -15940.5',
			},
			{
				name: 'ground',
				type: 'groundETZGLB',
				position: '3199.66 77.7136 -1946.27',
			},
			{
				name: '灯光',
				type: 'lightETZGLB-processed',
				position: '-397.269 579.935 -10671.6',
			},
			{
				name: '办公室',
				type: 'officebuilding1ETZGLB-processed',
				position: '3274.31 832.031 -3187.3',
			},
			{
				name: '安全房',
				type: 'securityroom1ETZGLB-processed',
				position: '-244.446 348.735 156.062',
			},
			{
				name: '太阳能',
				type: 'solarpanelETZGLB-processed',
				position: '4898.42 502.125 -14927.6',
			},
			{
				name: '太阳能1',
				type: 'solarpanelETZGLB-processed',
				position: '4898.42 502.125 -18340.2',
			},
			{
				name: '太阳能2',
				type: 'solarpanelETZGLB-processed',
				position: '4898.42 502.125 -17195.2',
			},
			{
				name: '清水池',
				nextDiagramId: '111',
				type: 'solarpanelETZGLB-processed',
				position: '4898.42 502.125 -16048.6',
				showLabelType: 1,
				labelContent: [
					{
						monitorData: '0.71',
						monitorTypeName: '余氯：',
						monitorTypeUnit: 'mg/L',
					},
					{
						monitorData: '2.2',
						monitorTypeName: '液位：',
						monitorTypeUnit: 'm',
					},
				],
				labelScale: 30,
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '6443.32 687.362 -13539.6',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '3978.85 687.362 -13539.6',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '1913.21 687.362 -13539.6',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '274.886 687.362 -16376.4',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '274.886 687.362 -15041.5',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '274.886 687.362 -13539.6',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '4617.65 539.217 -5746.83',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '2772.56 539.217 -5746.83',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '1026.58 539.217 -5746.83',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '1026.58 539.217 -11196.1',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '2772.56 539.217 -11196.1',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '4617.65 539.217 -11196.1',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '6311.78 539.217 -11196.1',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '6311.78 539.217 -9841.42',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '6311.78 539.217 -8652.16',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '6311.78 539.217 -7282.7',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				// showLabelType: 1,
				position: '6311.78 539.217 -6183.78',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				// showLabelType: 1,
				position: '-213.206 539.217 -6183.78',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '-213.206 539.217 -7282.7',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '-213.206 539.217 -8652.16',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '-213.206 539.217 -9841.42',
			},
			{
				name: '树木',
				type: 'treeETZGLB-processed',
				position: '-213.206 539.217 -11196.1',
			},
			{
				name: '墙1',
				type: 'wall1ETZGLB-processed',
				position: '-6082.77 192.91 -0.831147',
			},
			{
				name: '墙2',
				type: 'wall2ETZGLB-processed',
				position: '7996.06 306.246 -12327.9',
			},
			{
				name: '墙3',
				type: 'wall3ETZGLB-processed',
				position: '-12155.7 306.246 -12268.2',
			},
			{
				name: '墙4',
				type: 'wall4ETZGLB-processed',
				position: '-3228.4 273.185 -25297.7',
			},
			{
				name: '栅栏1',
				type: 'zhalan1ETZGLB-processed',
				position: '4159.23 252.746 126.891',
			},
			{
				name: '污水排放',
				type: 'liuliangji',
				position: '-6928.809 271.183 -670.031',
				// showLabelType: 1,
				nextDiagramId: '111',
				scaleDefault: 2,
				labelContent: [
					{
						monitorTypeName: '污水浊度：',
						monitorData: '3.254',
						monitorTypeUnit: 'NTU',
						showDataType: 0,
					},
					{
						monitorTypeName: '瞬时流量：',
						monitorData: '560',
						monitorTypeUnit: 'm³/h',
						showDataType: 0,
					},
					{
						monitorTypeName: '今日累计：',
						monitorData: '1457',
						monitorTypeUnit: 'm³',
						showDataType: 0,
					},
				],
				labelScale: 30,
			},
			{
				name: '进水',
				type: 'liuliangji',
				position: '-3879.835 1019.173 -26048.566',
				showLabelType: 1,
				nextDiagramId: '111',
				scaleDefault: 2,
				labelContent: [
					{
						monitorTypeName: '浊度：',
						monitorData: '5.24',
						monitorTypeUnit: 'NTU',
						showDataType: 0,
					},
					{
						monitorTypeName: '瞬时流量：',
						monitorData: '1763',
						monitorTypeUnit: 'm³/h',
						showDataType: 0,
					},
					{
						monitorTypeName: '今日累计：',
						monitorData: '3.43',
						monitorTypeUnit: '万m³',
						showDataType: 0,
					},
				],
				labelScale: 30,
			},
			{
				name: '出水',
				type: 'liuliangji',
				position: '7946.816 798.827 -20868.016',
				showLabelType: 1,
				nextDiagramId: '111',
				scaleDefault: 2,
				labelContent: [
					{
						monitorTypeName: '出水余氯：',
						monitorData: '0.71',
						monitorTypeUnit: 'mg/L',
						showDataType: 0,
					},
					{
						monitorTypeName: '出水浊度：',
						monitorData: '0.08',
						monitorTypeUnit: 'NTU',
						showDataType: 0,
					},
					{
						monitorTypeName: '出水PH值：',
						monitorData: '7.2',
						monitorTypeUnit: '',
						showDataType: 0,
					},
					{
						monitorTypeName: '瞬时流量：',
						monitorData: '883',
						monitorTypeUnit: 'm³/h',
						showDataType: 0,
					},
					{
						monitorTypeName: '出水压力：',
						monitorData: '0.42',
						monitorTypeUnit: 'Mpa',
						showDataType: 0,
						// alarm:true
					},
					{
						monitorTypeName: '今日累计：',
						monitorData: '3.36',
						monitorTypeUnit: '万m³',
						showDataType: 0,
					},
				],
				labelScale: 30,
			},
			{
				name: '压力计',
				type: 'yaliji',
				scaleDefault: 2,
				position: '8861.37 826.706 -20853.77',
			},

			{
				name: '栅栏2',
				type: 'zhalan2ETZGLB-processed',
				position: '3722.14 252.757 126.902',
			},
			{
				name: '斑马线',
				type: '20210720194454082_banmaxian',
				position: '-12839.8 5 -25994.8',
				needTransparent: true,
			},
			{
				name: '房子2',
				type: 'architecture22ETZGLB',
				position: '-4129.23 919.783 -15956.6',
			},
			{
				name: '房子3',
				type: 'architecture4ETZGLB-processed',
				position: '-4853.33 962.022 -7776.68',
				showMonitorType: configType ? 4 : null,
			},
			{
				name: '房子4',
				type: 'architecture5ETZGLB-processed',
				position: '4732.53 680.863 -20791.1',
				showMonitorType: configType ? 4 : null,
			},
			{
				name: '房子5',
				type: 'architecture6ETZGLB-processed',
				position: '848.001 670.788 -19605.1',
				// showLabelType: 1,
			},
			{
				name: '房子6',
				type: 'architecture7ETZGLB',
				position: '3665.96 601.443 -23844.1',
				// showLabelType: 1,
			},
			{
				name: '房子7',
				type: 'architecture8ETZGLB',
				position: '-3577.29 1048.55 -23739.8',
				// showLabelType: 1,
			},
			{
				name: '房子8',
				// type: 'architecture9ETZGLB-processed',
				type: 'architecture9ETZGLB',
				position: '-10622.2 600.526 -23862.2',
				// showLabelType: 1,
			},
			{
				name: '房子8',
				type: 'brandETZGLB',
				position: '-1884.98 153.417 2.33188',
				// showLabelType: 1,
			},
			{
				name: '房子8',
				type: 'dunETZGLB',
				position: '4134.55 95.2116 123.294',
				// showLabelType: 1,
			},
			{
				name: '房子1',
				// type: 'architecture13ETZGLB-processed',
				type: 'architecture13ETZGLB',
				position: '5414.03 1048.44 -611.238',
				showMonitorType: configType ? 4 : null,
			},
		]
		return this.dealPositionData({ allArr: arr })
	},

	//章山水厂沙盘
	getObjDataZSGLB() {
		let arr = [
			{
				name: '地形',
				type: 'dixingZS-processed',
				position: '2155.64 1632.909 -2253.645',
				// showLabelType: 1,
			},
			{
				name: '水箱',
				type: 'shuixiang',
				position: '9085.35 2943.87 -4818.22',
				// showLabelType: 1
			},
			{
				name: '列',
				type: 'column',
				position: '2002.67 3579.05 -4991.65',
				// showLabelType: 1
			},
			{
				name: '消防',
				type: 'xiaofang',
				position: '1568.59 999.379 -1429.07',
			},
			{
				name: '垃圾桶',
				type: 'lajitong',
				position: '-1558.01 1011.69 -852.243',
				// showLabelType: 1
			},
			{
				name: '木',
				type: 'wood',
				position: '-111.557 1247.92 596.24',
				// showLabelType: 1,
				// labelContent:[{
				// 	monitorData: '-111.557 1247.92 596.24',
				// 	monitorTypeName: '浊度：',
				// 	monitorTypeUnit: 'NTU',
				// }],
				// labelScale:20
			},
			{
				name: '基础',
				type: 'base',
				position: '580.356 940.556 -1113.52',
				// showLabelType: 1,
				// labelContent:[{
				// 	monitorData: '580.356 940.556 -1113.52',
				// 	monitorTypeName: '浊度：',
				// 	monitorTypeUnit: 'NTU',
				// }],
				// labelScale:20
			},
			{
				name: '清水池液位',
				type: 'architecture3ZS',
				position: '-3655.59 1819.35 -1796.53',
				showLabelType: 1,
				showMonitorType: 4,
				labelContent: [
					{
						monitorTypeName: '液位：',
						monitorData: '2.83',
						monitorTypeUnit: 'm',
						showDataType: 0,
					},
				],
				labelScale: 10,
			},
			{
				name: '瓦罐',
				type: 'waguan',
				position: '147.728 1040.75 464.748',
				// showLabelType: 1,
			},
			{
				name: '树',
				type: 'treeZS-processed',
				position: '39.796 2515.48 -5621.86',
				// showLabelType: 1,
			},

			{
				name: '树',
				type: 'treeZS-processed',
				position: '-2699.47 1835.74 -7916.43',
				// showLabelType: 1,
			},
			{
				name: '树',
				type: 'treeZS-processed',
				position: '-1757.29 1869.04 -5621.86',
				// showLabelType: 1,
			},
			{
				name: '树',
				type: 'treeZS-processed',
				position: '-5431.32 2178.48 -5123.5',
				// showLabelType: 1,
			},
			{
				name: '树',
				type: 'treeZS-processed',
				position: '-1864.18 1514.79 -1956.44',
				// showLabelType: 1,
			},
			{
				name: '树1',
				type: 'tree2ZS-processed',
				position: '-4652.97 1359.7 -653.834',
				// showLabelType: 1,
			},
			{
				name: '树2',
				type: 'tree2ZS-processed',
				position: '-4652.97 1389.94 469.709',
			},
			{
				name: '树3',
				type: 'tree2ZS-processed',
				position: '-4291.28 1405.96 1065.12',
				showMonitorType: 4,
				labelScale: 1.1,
			},
			{
				name: '树4',
				type: 'tree2ZS-processed',
				position: '-3170 1405.96 1065.12',
			},
			{
				name: '树5',
				type: 'tree2ZS-processed',
				position: '-2064.57 1405.96 1065.12',
			},
			{
				name: '树6',
				type: 'tree2ZS-processed',
				position: '-810.397 1405.96 1065.12',
				// showLabelType: 1,
			},
			{
				name: '树7',
				type: 'tree2ZS-processed',
				position: '159.111 1405.96 1065.12',
				// showLabelType: 1,
			},
			{
				name: '树8',
				type: 'tree2ZS-processed',
				position: '1152.72 1405.96 1065.12',
				// showLabelType: 1,
			},
			{
				name: '树9',
				type: 'tree1',
				position: '2213.22 2943.39 -5473.45',
				// showLabelType: 1,
			},

			{
				name: '房屋',
				type: 'architecture2ZS-processed',
				position: '-323.496 1856.81 -2092.25',
			},
			{
				name: '结构体系1',
				type: 'architecture1ZS-processed',
				position: '3497.67 1856.81 -1462.27',
				// showLabelType: 1,
				showMonitorType: 4,
				labelScale: 0.5,
			},

			// 工艺流程模型
			{
				name: '结构体系1',
				type: 'shebei4ZSGY',
				position: '4996.4 1405.12 -2250.98',
			},
			{
				name: '结构体系1',
				type: 'dizhuan1ZSGY',
				position: '4200.14 790.054 -1480.12',
			},
			{
				name: '结构体系1',
				type: 'dizhuan2ZSGY',
				position: '32.1993 788.715 -2099.54',
			},
			{
				name: '结构体系1',
				type: 'shuixiang3ZSGY',
				position: '4041.3 1451.88 -3620.08',
			},
			{
				name: '产水水质',
				type: 'shuixiang1ZSGY',
				position: '4040.91 1608.33 -3620.47',
				showLabelType: 1,
				labelContent: [
					{
						monitorTypeName: '产水浊度：',
						monitorData: '0.11',
						monitorTypeUnit: 'NTU',
						showDataType: 0,
					},
					{
						monitorTypeName: '产水余氯：',
						monitorData: '0.23',
						monitorTypeUnit: 'mg/L',
						showDataType: 0,
					},
					{
						monitorTypeName: '产水ph值：',
						monitorData: '6.8',
						monitorTypeUnit: '',
						showDataType: 0,
					},
				],
				labelScale: 10,
			},
			{
				name: '结构体系1',
				type: 'guandao7ZSGY',
				position: '3717.8 1244.35 -3620.32',
			},
			{
				name: '膜组参数',
				type: 'guandao6ZSGY',
				position: '-105.031 1232.55 -2167.45',
			},
			{
				name: '膜组参数',
				type: 'guandao5ZSGY',
				position: '-142.908 1445.85 -2626.78',
				showLabelType: 1,
				labelContent: [
					{
						monitorTypeName: '1#膜前液位：',
						monitorData: '1.8',
						monitorTypeUnit: 'm',
						showDataType: 0,
					},
					{
						monitorTypeName: '1#跨膜压差：',
						monitorData: '0.19',
						monitorTypeUnit: 'Mpa',
						showDataType: 0,
					},
				],
				labelScale: 10,
			},
			{
				name: '结构体系1',
				type: 'guandao3ZSGY',
				position: '-535.006 1081.65 -2152.63',
			},
			{
				name: '结构体系1',
				type: 'guandao2ZSGY',
				position: '4636.03 949.635 -20.6398',
			},
			{
				name: '结构体系1',
				type: 'guandao1ZSGY',
				position: '4961.87 1300.37 -1142.46',
			},
			{
				name: '结构体系1',
				type: 'shebei8ZSGY',
				position: '289.115 857.459 -2401.21',
			},
			{
				name: '结构体系1',
				type: 'shebei1ZSGY',
				position: '4830.54 1290.86 -1197.53',
			},
			{
				name: '结构体系1',
				type: 'shebei3ZSGY',
				position: '4733.58 1322.34 -1149.1',
			},
			{
				name: '结构体系1',
				type: 'shebei2ZSGY',
				position: '4866.67 1270.39 -1094.86',
			},
			{
				name: '结构体系1',
				type: 'xiashuidaoZSGY',
				position: '4201 806.427 -1475.62',
			},
			{
				name: '产水出口参数',
				type: 'shuixiang2ZSGY',
				position: '4413.92 972.898 417.224',
				showLabelType: 1,
				labelContent: [
					{
						monitorTypeName: '产水压力：',
						monitorData: '0.36',
						monitorTypeUnit: 'Mpa',
						showDataType: 0,
					},
					{
						monitorTypeName: '产水瞬时流量：',
						monitorData: '389',
						monitorTypeUnit: 'm³/h',
						showDataType: 0,
					},
					{
						monitorTypeName: '累计流量：',
						monitorData: '28950',
						monitorTypeUnit: 'm³',
						showDataType: 0,
					},
				],
				labelScale: 10,
			},
			{
				name: '结构体系1',
				type: 'shuixiang2ZSGY',
				position: '4604.21 972.898 417.224',
			},

			{
				name: '结构体系1',
				type: 'shuixiang2ZSGY',
				position: '4229.6 972.898 417.224',
			},
			{
				name: '结构体系1',
				type: 'shuixiang2ZSGY',
				position: '4042.66 972.898 417.224',
			},
			{
				name: '结构体系1',
				type: 'shuixiangZSGY',
				position: '-1047.1 1008.78 -1325.3',
			},
			{
				name: '结构体系1',
				type: 'beng1ZSGY',
				position: '4962.32 880.505 206.386',
			},
			{
				name: '结构体系1',
				type: 'beng2ZSGY',
				position: '4962.32 865.277 -68.3952',
			},
			{
				name: '结构体系1',
				type: 'beng2ZSGY',
				position: '4962.32 865.277 332.915',
			},
			{
				name: '结构体系1',
				type: 'beng3ZSGY',
				position: '-1039.53 878.293 -1569.42',
			},
			{
				name: '结构体系1',
				type: 'shebei5ZSGY',
				position: '4883 969.556 -311.627',
			},
			{
				name: '结构体系1',
				type: 'shebei6ZSGY',
				position: '3768.96 1609.33 -3630.74',
			},
			{
				name: '结构体系1',
				type: 'shebei7ZSGY',
				position: '3783.18 1461.3 -3620.74',
			},
			{
				name: '结构体系1',
				type: 'shalvguan1ZSGY',
				position: '480.879 1393.96 -2622.77',
			},
			{
				name: '结构体系1',
				type: 'bengZSGY',
				position: '299.752 932.611 -2702.63',
			},
			{
				name: '结构体系1',
				type: 'yiqiZSGY',
				position: '-216.777 1508.96 -2115.33',
			},
			{
				name: '结构体系1',
				type: 'yiqiZSGY',
				position: '-12.9136 1515.77 -2115.33',
			},
			{
				name: '结构体系1',
				type: 'yiqiZSGY',
				position: '-216.777 1355.65 -2113.96',
			},
			{
				name: '结构体系1',
				type: 'yiqiZSGY',
				position: '12.9136 1351.03 -2113.96',
			},
			{
				name: '结构体系1',
				type: 'yiqiZSGY',
				position: '-216.777 1199.3 -2115.33',
			},
			{
				name: '结构体系1',
				type: 'dibanZSGY',
				position: '3916.52 1486.96 -3622.17',
			},
		]
		return this.dealPositionData({ allArr: arr })
	},

	// 模型
	getObjectDataSGWFY() {
		let arr = [
			{
				name: 'wallCNTXGLB',
				type: 'wallCNTXGLB',
				position: '-61.8 61.552 38.053',
			},
			{
				name: 'bashouSGWFY',
				type: 'bashouSGWFY',
				position: '-11.1159 -11.5023 11.0205',
			},
			{
				name: '2#泵',
				type: 'beng1SGWFY',
				position: '5.60822 -15.7908 5.87472',
				showLabelType: 1,
				labelContent: [
					{
						monitorTypeName: '频率：',
						monitorData: '34.8',
						monitorTypeUnit: 'Hz',
						showDataType: 0,
					},
					{
						monitorTypeName: '电流：',
						monitorData: '7.6',
						monitorTypeUnit: 'A',
						showDataType: 0,
					},
					{
						monitorTypeName: '电压：',
						monitorData: '396',
						monitorTypeUnit: 'V',
						showDataType: 0,
					},
				],
				labelScale: 0.5,
			},
			{
				name: '1#泵',
				type: 'bengSGWFY',
				position: '-24.5872 -6.3441 4.71018',
				showLabelType: 1,
				labelContent: [
					{
						monitorTypeName: '频率：',
						monitorData: '34',
						monitorTypeUnit: 'Hz',
						showDataType: 0,
					},
					{
						monitorTypeName: '电流：',
						monitorData: '80',
						monitorTypeUnit: 'A',
						showDataType: 0,
					},
					{
						monitorTypeName: '电压：',
						monitorData: '220',
						monitorTypeUnit: 'V',
						showDataType: 0,
					},
				],
				labelScale: 0.5,
			},
			{
				name: 'bengSGWFY',
				type: 'bengSGWFY',
				position: '-60.4665 -6.3441 4.71018',
			},
			{
				name: 'dimianSGWFY',
				type: 'dimianSGWFY',
				position: '-56.2902 -61.9769 42.3709',
			},
			{
				name: 'dizuoSGWFY',
				type: 'dizuoSGWFY',
				position: '-11.9599 8.9246 -12.4468',
			},
			{
				name: 'guandaoBXGSGWFY',
				type: 'guandaoBXGSGWFY',
				position: '18.4641 40.1433 -54.4543',
			},
			{
				name: 'kongzhiguiSGWFY',
				type: 'kongzhiguiSGWFY',
				position: '-186.469 30.5336 185.338',
			},
			{
				name: '出口流量',
				type: 'liuliangjiSGWFY',
				position: '76.5237 25.0628 41.7808',
				showLabelType: 1,
				labelContent: [
					{
						monitorTypeName: '累计流量：',
						monitorData: '869',
						monitorTypeUnit: 'm³',
						showDataType: 0,
					},
					{
						monitorTypeName: '瞬时流量：',
						monitorData: '32',
						monitorTypeUnit: 'm³/h',
						showDataType: 0,
					},
				],
				labelScale: 0.5,
			},
			{
				name: 'shuixiangSGWFY',
				type: 'shuixiangSGWFY',
				position: '-232.051 90.6312 32.4345',
			},
			{
				name: 'shuixiangSGWFY',
				type: 'shuixiangSGWFY',
				position: '-232.051 31.5269 32.4345',
			},
			{
				name: '出口压力',
				type: 'yalijiSGWFY',
				position: '-45.4043 27.9021 42.552',
				showLabelType: 1,
				labelContent: [
					{
						monitorTypeName: '压力：',
						monitorData: '0.5',
						monitorTypeUnit: 'Mpa',
						showDataType: 0,
					},
				],
				labelScale: 0.6,
			},
			{
				name: '进口压力',
				type: 'yalijiSGWFY',
				position: '109.228 -0.074598 -86.1181',
				showLabelType: 1,
				labelContent: [
					{
						monitorTypeName: '压力：',
						monitorData: '0.23',
						monitorTypeUnit: 'Mpa',
						showDataType: 0,
					},
				],
				labelScale: 0.6,
			},
		]
		return this.dealPositionData({ allArr: arr })
	},

	//高新区水厂
	getObjDataGXQSCglb() {
		let arr = [
			{
				name: 'architecture1',
				type: 'architecture1GXQGLB',
				position: '-428.716 323.977 128.73',
			},
			{
				name: 'architecture2',
				type: 'architecture2GXQGLB',
				position: '2178.74 777.935 3474.31',
			},
			{
				name: 'architecture3',
				type: 'architecture3GXQGLB',
				position: '8398.52 965.202 9947.7',
			},
			{
				name: 'architecture4',
				type: 'architecture4GXQGLB',
				position: '6643.78 557.923 133.502',
			},
			{
				name: 'architecture5',
				type: 'architecture5GXQGLB',
				position: '1465.66 408.281 -2306.07',
			},
			{
				name: 'architecture6',
				type: 'architecture6GXQGLB',
				position: '9152.61 319.661 131.521',
			},
			{
				name: 'grass1',
				type: 'grass1GXQGLB',
				position: '-914.624 88.2401 -1940.15',
			},
			{
				name: 'grass2',
				type: 'grass2GXQGLB',
				position: '6193.09 39.8562 5866.4',
			},
			{
				name: 'grass3',
				type: 'grass3GXQGLB',
				position: '12813.4 38.8783 2938.4',
			},
			{
				name: 'grass4',
				type: 'grass4GXQGLB',
				position: '8122.5 250.611 4189.28',
			},
			{
				name: 'grass5',
				type: 'grass5GXQGLB',
				position: '1587.83 113.104 -2344.73',
			},
			{
				name: 'ground',
				type: 'groundGXQGLB',
				position: '6247.31 0 4389.79',
			},
			{
				name: 'langan',
				type: 'langanGXQGLB',
				position: '6430.48 169.038 4448.54',
			},
			{
				name: 'light',
				type: 'lightGXQGLB',
				position: '7168.74 438.199 2722.74',
			},
			{
				name: 'tree',
				type: 'treeGXQGLB',
				position: '6036.27 648.726 2169.87',
			},
		]
		return this.dealPositionData({ allArr: arr })
	},

	//玉皇阁水厂沙盘
	getObjDataYHGGLB() {
		let arr = [
			{
				name: 'B0YHGGLB',
				type: 'B0YHGGLB',
				position: '6849.08 321.123 20121.6',
			},
			{
				name: 'B1YHGGLB',
				type: 'B1YHGGLB',
				position: '12090.3 251.119 19259.4',
			},
			{
				name: 'B2YHGGLB',
				type: 'B2YHGGLB',
				position: '10377.1 1478.88 7194.16',
			},
			{
				name: 'B3YHGGLB',
				type: 'B3YHGGLB',
				position: '-6100.83 1679.75 1041',
			},
			{
				name: 'B4YHGGLB',
				type: 'B4YHGGLB',
				position: '12071.2 1840.86 -3095.19',
			},
			{
				name: 'B5YHGGLB',
				type: 'B5YHGGLB',
				position: '3854.31 624.722 -4223.68',
			},
			{
				name: 'B6YHGGLB',
				type: 'B6YHGGLB',
				position: '4967.38 688.264 -11768.8',
			},
			{
				name: 'B7YHGGLB',
				type: 'B7YHGGLB',
				position: '9988.6 546.199 -12007.6',
			},
			{
				name: 'B8YHGGLB',
				type: 'B8YHGGLB',
				position: '16478.1 750.831 -12549.5',
			},
			{
				name: 'B9YHGGLB',
				type: 'B9YHGGLB',
				position: '4750.44 1059.6 -14989.3',
			},
			{
				name: 'B10YHGGLB',
				type: 'B10YHGGLB',
				position: '-14741.8 879.994 3624',
			},
			{
				name: 'B11YHGGLB',
				type: 'B11YHGGLB',
				position: '-12728 1155.07 14463',
			},
			{
				name: 'B12YHGGLB',
				type: 'B12YHGGLB',
				position: '-14713.9 862.454 -6260.94',
			},
			{
				name: 'B13YHGGLB',
				type: 'B13YHGGLB',
				position: '-7287.93 841.566 18937.6',
			},
			{
				name: 'B14HGGLB',
				type: 'B14YHGGLB',
				position: '-13188.3 981.304 -15011.5',
			},
			{
				name: 'B15YHGGLB',
				type: 'B15YHGGLB',
				position: '-6639.85 743.282 16827.1',
			},
			{
				name: 'G1YHGGLB',
				type: 'G1YHGGLB',
				position: '-3517.4 0.381366 -713.641',
			},
			{
				name: 'G2YHGGLB',
				type: 'G2YHGGLB',
				position: '10540.5 280.557 18164.8',
			},
			{
				name: 'G3YHGGLB',
				type: 'G3YHGGLB',
				position: '10378.4 39.8247 7674.05',
			},
			{
				name: 'G4YHGGLB',
				type: 'G4YHGGLB',
				position: '10784.6 282.256 -2973.33',
			},
			{
				name: 'G5YHGGLB',
				type: 'G5YHGGLB',
				position: '10900.8 282.256 -13430.8',
			},
			{
				name: 'G6YHGGLB',
				type: 'G6YHGGLB',
				position: '-10070.5 20 -2279.6',
			},
			{
				name: 'lightYHGGLB',
				type: 'lightYHGGLB',
				position: '2889.79 496.198 659.673',
			},
			{
				name: 'roadYHGGLB',
				type: 'roadYHGGLB',
				position: '1571.59 -152.059 1050.02',
			},
			{
				name: 'solar panel',
				type: 'solar panel',
				position: '-3458.92 321.48 -172.481',
			},
			{
				name: 'treeYHGGLB',
				type: 'treeYHGGLB',
				position: '3426.13 986.92 6190.59',
			},
		]
		return this.dealPositionData({ allArr: arr })
	},

	// 状元水厂模型
	getObjDataZYSCGLB() {
		let arr = [
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-109.534 0.619406 63.2152',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-87.9487 0.619406 63.2152',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-64.8864 0.619406 63.2152',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-17.6095 0.619406 63.2152',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '5.45279 0.619406 63.2152',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '52.7777 0.619406 63.2152',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '75.84 0.619406 63.2152',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '101.86 0.619406 63.2152',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-109.534 0.619406 -53.1835',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-64.8864 0.619406 -53.1835',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-39.1944 0.619406 -53.1835',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-17.6095 0.619406 -53.1835',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '31.1928 0.619406 -53.1835',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '52.7777 0.619406 -53.1835',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '101.86 0.619406 -53.1835',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '123.445 0.619406 -53.1835',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-109.534 0.619406 -71.1315',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-87.9487 0.619406 -71.1315',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-64.8864 0.619406 -71.1315',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-17.6095 0.619406 -71.1315',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '5.45279 0.619406 -71.1315',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '52.7777 0.619406 -71.1315',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '75.84 0.619406 -71.1315',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '101.86 0.619406 -71.1315',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '131.364 0.619406 -183.013',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '97.2038 0.619406 -183.013',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '82.0567 0.619406 -183.013',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-2.49714 0.619406 -183.013',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '15.8323 0.619406 -183.013',
			},
			{
				name: 'tree1ZYSCWZ',
				type: 'tree1ZYSCWZ',
				position: '-113.947 0.619406 -183.013',
			},
			{
				name: 'signalZYSCWZ',
				type: 'signalZYSCWZ',
				// showLabelType: 1,
				position: '585.771 26.006 -89.37',
			},
			{
				name: 'logoZYSCWZ',
				type: 'logoZYSCWZ',
				// // showLabelType: 1,
				position: '576.042 2.137 68.846',
			},
			{
				name: 'mlZYSCWZ',
				type: 'mlZYSCWZ',
				// // showLabelType: 1,
				position: '448.897 0.181 69.625',
			},
			{
				name: 'landZYSCWZ',
				type: 'landZYSCWZ',
				position: '119.894 -2.126 48.829',
			},
			{
				name: 'dimianWNZYSCWZ',
				type: 'dimianWNZYSCWZ',
				// // showLabelType: 1,
				position: '-266.407 0.167 44.123',
				// "position": "0.167 -44.123 -266.407"
			},
			{
				name: 'grandZYSCWZ',
				type: 'grandZYSCWZ',
				position: '140.404 -1.061 48.94',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -159.336',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -152.967',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -139.268',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -132.898',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -51.4943',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -45.1248',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -117.91',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -111.541',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -96.1535',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -89.7841',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -74.7512',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -68.3818',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -30.4693',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -24.0999',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -8.4017',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -2.03227',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 12.8504',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 19.2198',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 32.9259',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 39.2954',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -156.321',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -136.137',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -114.538',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -92.5967',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -71.6803',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -48.2873',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -27.0016',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -5.27955',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 15.9512',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 36.4907',
			},
			{
				name: 'gaojiaZYSCWZ',
				type: 'gaojiaZYSCWZ',
				position: '121.716 15.8977 -210.415',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-153.355 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-108.704 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-67.3144 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '71.5593 8.92841 -212.108',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '39.5265 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-28.5671 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '6.37245 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-244.237 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-199.586 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '200.671 7.37485 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '245.323 7.21339 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '286.712 7.62939 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '399.736 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '331.642 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '366.582 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '109.79 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '154.441 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '430.771 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '466.927 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '499.174 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '537.337 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-341.798 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-297.146 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '577.561 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '622.213 8.92841 -210.516',
			},
			{
				name: 'qiao1wlZYSCWZ',
				type: 'qiao1wlZYSCWZ',
				position: '316.578 2.87959 64.9832',
			},
			{
				name: 'qiao1wlZYSCWZ',
				type: 'qiao1wlZYSCWZ',
				position: '316.578 2.87959 77.531',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '312.419 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '308.519 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '304.607 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '292.889 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '296.801 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '300.701 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '288.985 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '285.086 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '285.086 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '288.985 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '300.701 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '296.801 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '292.889 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '304.607 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '308.519 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '312.419 2.87959 77.5294',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-128.564 0.619406 39.0034',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-140.041 0.619406 -83.6487',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-133.241 0.619406 -164.36',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-235.232 0.619406 -117.433',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-275.457 0.619406 -137.926',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-302.141 0.619406 -71.1897',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-274.459 0.642768 86.1916',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '100.065 0.41153 157.454',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '61.9762 0.891909 157.454',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '15.9155 0.888286 148.627',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-66.0226 0.372361 134.535',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-136.318 0.57909 116.821',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '390.427 1.31549 -44.7451',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '421.098 1.31549 -154.246',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '529.919 1.31549 -142.308',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '479.003 1.31549 -97.5669',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '268.299 0.827622 -97.5669',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '258.977 0.599476 -129.639',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '268.957 1.18793 -159.971',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '255.121 0.820643 40.0991',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '243.319 1.31549 136.748',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '243.319 1.31549 96.4559',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '158.387 1.31549 259.854',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '232.49 1.31549 236.366',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '539.94 1.31549 118.531',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '355.979 1.31549 300.254',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '446.711 1.31549 189.536',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '433.654 1.31549 259.654',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '390.331 1.68479 275.063',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '522.483 1.31549 26.1749',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '405.694 1.31549 127.021',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '366.942 1.56248 116.739',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '449.107 1.31549 125.672',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '517.209 1.31549 168.427',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '485.597 1.59486 226.157',
			},
			{
				name: 'taZYSCWZ',
				type: 'taZYSCWZ',
				//
				position: '189.331 12.3726 180.57',
			},
			{
				name: 'parkZYSCWZ',
				type: 'parkZYSCWZ',
				position: '403.499 4.83917 -76.7985',
			},
			{
				name: 'parkZYSCWZ',
				type: 'parkZYSCWZ',
				position: '504.562 4.83917 -71.5291',
			},
			{
				name: 'modelBTMZYSCWZ',
				type: 'modelBTMZYSCWZ',
				needTransparent: true,
				// needBeam: true,
				position: '-97.7295 23.2896 190.379',
			},
			{
				name: 'ltBXGZYSCWZ',
				type: 'ltBXGZYSCWZ',
				// showLabelType: 1,
				position: '43.888 5.677 -61.525',
			},

			{
				name: 'guandaoZYSCWZ',
				type: 'guandaoZYSCWZ',
				position: '296.401 2.60546 125.636',
			},
			{
				name: 'fmjZYSCWZ',
				type: 'fmjZYSCWZ',
				// // showLabelType: 1,
				position: '152.087 5.342 120.45',
			},
			{
				name: 'equipment2BXGZYSCWZ',
				type: 'equipment2BXGZYSCWZ',
				position: '-188.363 12.239 88.667',
			},
			{
				name: 'equipment2BXGZYSCWZ',
				type: 'equipment2BXGZYSCWZ',
				position: '-204.721 12.239 88.667',
			},
			{
				name: 'equipment1BXGZYSCWZ',
				type: 'equipment1BXGZYSCWZ',
				// // showLabelType: 1,
				position: '-193.195 13.037 76.308',
			},
			{
				name: 'equipment1BXGZYSCWZ',
				type: 'equipment1BXGZYSCWZ',
				// // showLabelType: 1,
				position: '-197.405 13.038 76.308',
			},
			// {
			// 	"name": "dmZTSCWZ",
			// 	"type": "dmZTSCWZ",
			// 	"position": "119.894 21.564 48.939"
			// },
			{
				name: 'weilanZYSCWZ-processed',
				type: 'weilanZYSCWZ-processed',
				position: '408.657 2.634 169.834',
			},
			{
				name: 'bengTCZYSCWZ',
				type: 'bengTCZYSCWZ',
				// // showLabelType: 1,
				position: '-191.044 11.047 76.517',
			},
			{
				name: 'bengTCZYSCWZ',
				type: 'bengTCZYSCWZ',
				// // showLabelType: 1,
				position: '-202.298 11.047 76.517',
			},
			{
				name: '回流调节池',
				type: 'architecture14ZYSCWZ',
				showLabelType: 1,
				position: '-20.295 2.223 112.147',
			},
			{
				name: 'architecture13ZYSCWZ',
				type: 'architecture13ZYSCWZ',
				// // showLabelType: 1,
				position: '-244.566 4.06639 103.136',
			},
			{
				name: 'architecture13ZYSCWZ',
				type: 'architecture13ZYSCWZ',
				// // showLabelType: 1,
				position: '26.2673 4.06639 129.8',
			},
			{
				name: '滤池',
				type: 'architecture12ZYSCWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '112.083 7.91647 -28.3548',
				labelPoints: [
					{
						position: '188.083 7.91647 35.3548',
						name: '1#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '188.083 7.91647 15.3548',
						name: '2#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '188.083 7.91647 -6.3548',
						name: '3#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '188.083 7.91647 -27.3548',
						name: '4#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '188.083 7.91647 -48.3548',
						name: '5#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '188.083 7.91647 -71.3548',
						name: '6#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '188.083 7.91647 -93.3548',
						name: '7#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '188.083 7.91647 -115.3548',
						name: '8#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '188.083 7.91647 -137.3548',
						name: '9#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '188.083 7.91647 -157.3548',
						name: '10#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					// 清水池
					{
						position: '90.083 7.91647 57.3548',
						name: '1#清水池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '90.083 7.91647 -82.3548',
						name: '2#清水池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					// 沉淀池
					{
						position: '-30.083 7.91647 8.3548',
						name: '1#沉淀池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '-30.083 7.91647 -132.3548',
						name: '2#沉淀池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					// 进水
					{
						position: '-140.083 7.91647 2.3548',
						name: '1#进水',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '-140.083 7.91647 -132.3548',
						name: '2#进水',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '-149.083 7.91647 58.3548',
						name: '原水总进水',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
				],
			},
			{
				name: 'architecture11ZYSCWZ',
				type: 'architecture11ZYSCWZ',
				// // showLabelType: 1,
				position: '-88.6423 9.33145 105.391',
			},
			{
				name: 'architecture10ZYSCWZ',
				type: 'architecture10ZYSCWZ',
				// // showLabelType: 1,
				position: '-271.053 8.257 -45.944',
			},
			{
				name: '出水泵房',
				type: 'architecture9ZYSCWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '159.885 6.781 138.501',
				labelPoints: [
					{
						position: '159.885 6.781 118.501',
						nextDiagramId: 'go',
						name: '出水泵房',
						labelContent: [],
					},
					{
						position: '89.885 6.781 118.501',
						name: '送水泵房',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '139.885 6.781 88.501',
						name: '吸水井',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
				],
			},
			{
				name: 'architecture8ZYSCWZ',
				type: 'architecture8ZYSCWZ',
				// // showLabelType: 1,
				position: '-223.835 8.19848 -56.0735',
			},
			{
				name: '加药间',
				type: 'architecture7ZYSCWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '-283.594 5.955 -48.664',
			},
			{
				name: 'architecture6ZYSCWZ',
				type: 'architecture6ZYSCWZ',
				// // showLabelType: 1,
				position: '-196.553 -0.41 51.117',
			},
			{
				name: '污泥间',
				type: 'architecture5ZYSCWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '-277.442 9.24024 45.9839',
			},
			{
				name: 'architecture4ZYSCWZ',
				type: 'architecture4ZYSCWZ',
				position: '-219.283 7.5162 85.5646',
			},
			{
				name: 'architecture3ZYSCWZ',
				type: 'architecture3ZYSCWZ',
				position: '-197.621 4.797 86.537',
			},
			{
				name: 'architecture2ZYSCWZ',
				type: 'architecture2ZYSCWZ',
				// // showLabelType: 1,
				position: '387.602 0.345 -100.22',
			},
			{
				name: 'architecture1ZYSCWZ',
				type: 'architecture1ZYSCWZ',
				// // showLabelType: 1,
				position: '467.445 9.388 -4.76',
			},
			{
				name: 'architecture0ZYSCWZ',
				type: 'architecture0ZYSCWZ',
				position: '572.631 5.762 28.164',
			},
			// {
			// 	name: 'wanggeZYSCWZ',
			// 	type: 'wanggeZYSCWZ',
			// 	position: '1.95401 1.3682 -3.32895',
			// },
			// 污泥间设备
			{
				name: 'tzWNZYSCWZ',
				type: 'tzWNZYSCWZ',
				position: '-272.911 1.49334 39.7332',
			},
			{
				name: 'sb13WNZYSCWZ',
				type: 'sb13WNZYSCWZ',
				position: '-281.929 5.29002 45.3434',
			},
			{
				name: 'sb13WNZYSCWZ',
				type: 'sb13WNZYSCWZ',
				position: '-280.483 5.29002 45.3434',
			},
			{
				name: 'sb13WNZYSCWZ',
				type: 'sb13WNZYSCWZ',
				position: '-281.194 5.29002 45.3434',
			},
			{
				name: 'sb12BXGWNZYSCWZ',
				type: 'sb12BXGWNZYSCWZ',
				position: '-263.188 3.41647 41.4326',
			},
			{
				name: 'sb11WNZYSCWZ',
				type: 'sb11WNZYSCWZ',
				position: '-263.188 3.47006 39.2822',
			},
			{
				name: 'sb10WNZYSCWZ',
				type: 'sb10WNZYSCWZ',
				position: '-266.289 1.45618 43.291',
			},
			{
				name: 'sb10WNZYSCWZ',
				type: 'sb10WNZYSCWZ',
				position: '-266.289 1.45618 40.7864',
			},
			{
				name: 'sb10WNZYSCWZ',
				type: 'sb10WNZYSCWZ',
				position: '-266.289 1.45618 38.261',
			},
			{
				name: 'sb9WNZYSCWZ',
				type: 'sb9WNZYSCWZ',
				position: '-281.029 1.45618 44.5444',
			},
			{
				name: 'sb9WNZYSCWZ',
				type: 'sb9WNZYSCWZ',
				position: '-281.029 1.45618 40.9178',
			},
			{
				name: 'sb9WNZYSCWZ',
				type: 'sb9WNZYSCWZ',
				position: '-281.029 1.45618 37.1307',
			},
			{
				name: 'sb8WNZYSCWZ',
				type: 'sb8WNZYSCWZ',
				position: '-274.287 14.2772 42.6545',
			},
			{
				name: 'sb8WNZYSCWZ',
				type: 'sb8WNZYSCWZ',
				position: '-274.287 14.2772 39.8467',
			},
			{
				name: 'sb8WNZYSCWZ',
				type: 'sb8WNZYSCWZ',
				position: '-274.287 14.2772 37.0435',
			},
			{
				name: 'sb7TCWNZYSCWZ-processed',
				type: 'sb7TCWNZYSCWZ-processed',
				position: '-291.643 13.543 40.1178',
			},
			{
				name: 'sb7TCWNZYSCWZ-processed',
				type: 'sb7TCWNZYSCWZ-processed',
				position: '-286.562 13.543 40.1178',
			},
			{
				name: 'sb7TCWNZYSCWZ-processed',
				type: 'sb7TCWNZYSCWZ-processed',
				position: '-281.132 13.543 40.1178',
			},
			{
				name: 'sb6WNZYSCWZ',
				type: 'sb6WNZYSCWZ',
				showLabelType: 1,
				position: '-278.064 3.74151 44.9581',
			},
			{
				name: 'sb6WNZYSCWZ',
				type: 'sb6WNZYSCWZ',
				position: '-271.402 3.74151 44.9581',
			},
			{
				name: 'sb5TCWNZYSCWZ',
				type: 'sb5TCWNZYSCWZ',
				showLabelType: 1,
				position: '-265.301 12.0862 48.1478',
			},
			{
				name: 'sb5TCWNZYSCWZ',
				type: 'sb5TCWNZYSCWZ',
				position: '-265.301 12.0862 38.9729',
			},
			{
				name: 'sb4WNZYSCWZ',
				type: 'sb4WNZYSCWZ',
				position: '-263.836 1.39625 39.7099',
			},
			{
				name: 'sb4WNZYSCWZ',
				type: 'sb4WNZYSCWZ',
				position: '-263.836 1.39625 41.9671',
			},
			{
				name: 'ltWNZYSCWZ',
				type: 'ltWNZYSCWZ',
				position: '-274.9 5.57614 51.3062',
			},
			{
				name: 'lbWNZYSCWZ',
				type: 'lbWNZYSCWZ',
				// needBeam:true,
				position: '-279.686 5.82296 44.0714',
			},
			{
				name: 'gx2WNZYSCWZ1',
				type: 'gx2WNZYSCWZ1',
				position: '-271.279 1.76338 42.1973',
			},
			{
				name: 'gx1WNZYSCWZ',
				type: 'gx1WNZYSCWZ',
				position: '-256.758 1.55031 42.2628',
			},
			{
				name: 'famen2WNZYSCWZ',
				type: 'famen2WNZYSCWZ',
				position: '-278.725 1.74025 44.2743',
			},
			{
				name: 'famen2WNZYSCWZ',
				type: 'famen2WNZYSCWZ',
				position: '-278.232 1.74308 39.0519',
			},
			{
				name: 'famen2WNZYSCWZ',
				type: 'famen2WNZYSCWZ',
				position: '-278.69 1.74308 37.0937',
			},
			{
				name: 'famen1WNZYSCWZ',
				type: 'famen1WNZYSCWZ',
				position: '-257.569 1.55809 44.893',
			},
			{
				name: 'famen1WNZYSCWZ',
				type: 'famen1WNZYSCWZ',
				position: '-256.747 1.54767 43.1659',
			},
			{
				name: 'famen1WNZYSCWZ',
				type: 'famen1WNZYSCWZ',
				position: '-256.747 1.54767 41.2454',
			},
			{
				name: 'famen1WNZYSCWZ',
				type: 'famen1WNZYSCWZ',
				position: '-261.493 1.56737 46.1614',
			},
			{
				name: 'BXGWNZYSCWZ-processed',
				type: 'BXGWNZYSCWZ-processed',
				position: '-276.495 6.42113 44.1571',
			},
			{
				name: 'bengTCWNZYSCWZ',
				type: 'bengTCWNZYSCWZ',
				position: '-271.01 9.46312 39.4769',
			},
			{
				name: 'beng1WNZYSCWZ',
				type: 'beng1WNZYSCWZ',
				position: '-259.614 1.66023 40.3763',
			},
			{
				name: 'beng1WNZYSCWZ',
				type: 'beng1WNZYSCWZ',
				position: '-259.614 1.66023 42.9868',
			},
			{
				name: 'beng1WNZYSCWZ',
				type: 'beng1WNZYSCWZ',
				position: '-259.614 1.66023 45.6809',
			},
			// 加药设备
			{
				name: 'dm1JYZYSCWZ',
				type: 'dm1JYZYSCWZ',
				position: '-209.811 0.503 -66.721',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-222.128 1.13474 -67.6722',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-221.96 1.13474 -59.6411',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-213.588 1.13474 -59.6411',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-213.756 1.13474 -67.6722',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-206.415 1.13474 -67.6722',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-206.246 1.13474 -59.6411',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-198.884 1.13474 -59.6411',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-199.052 1.13474 -67.6722',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-196.745 1.13474 -70.5246',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-196.745 1.13474 -62.4224',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-204.071 1.13474 -62.4224',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-204.071 1.13474 -70.5246',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-211.42 1.13474 -70.5246',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-211.42 1.13474 -62.4224',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-219.053 1.13474 -62.4224',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-219.053 1.13474 -70.5246',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-227.145 1.13474 -70.5246',
			},
			{
				name: 'sb7JYZTSCWZ',
				type: 'sb7JYZTSCWZ',
				position: '-227.145 1.13474 -62.4224',
			},
			{
				name: 'zcJYZYSCWZ',
				type: 'zcJYZYSCWZ',
				position: '-254.368 6.86634 -26.7275',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-224.64 2.95745 -62.4781',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-201.63 2.95745 -62.4781',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-194.297 2.95745 -62.4781',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-216.262 2.95745 -62.4781',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-208.865 2.95745 -62.4781',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-208.865 2.95745 -70.4833',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-216.262 2.95745 -70.4833',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-194.297 2.95745 -70.4833',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-201.63 2.95745 -70.4833',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-224.64 2.95745 -70.4833',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-297.763 2.42007 -19.0072',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-303.894 2.41373 -19.0078',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-309.478 2.41373 -19.0078',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-309.478 2.41373 -27.6216',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-303.894 2.41373 -27.6216',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-297.764 2.41373 -27.6216',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-297.764 2.41373 -35.3088',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-303.894 2.41373 -35.3088',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-309.478 2.41373 -35.3088',
			},
			{
				name: 'sb5JYZYSCWZ',
				type: 'sb5JYZYSCWZ',
				position: '-269.237 6.88707 -28.9874',
			},
			{
				name: 'sb3JYZYSCWZ',
				type: 'sb3JYZYSCWZ',
				position: '-276.739 7.53025 -21.0492',
			},
			{
				name: 'sb3JYZYSCWZ',
				type: 'sb3JYZYSCWZ',
				position: '-276.739 7.53025 -33.2436',
			},
			{
				name: 'sb3JYZYSCWZ',
				type: 'sb3JYZYSCWZ',
				position: '-278.401 1.75163 -25.3845',
			},
			{
				name: 'sb3JYZYSCWZ',
				type: 'sb3JYZYSCWZ',
				position: '-283.431 7.5689 -30.399',
			},
			{
				name: 'sb2JYZYSCWZ',
				type: 'sb2JYZYSCWZ',
				position: '-269.858 2.43816 -26.4427',
			},
			{
				name: 'sb1JYZYSCWZ',
				type: 'sb1JYZYSCWZ',
				position: '-269.858 2.43816 -32.6392',
			},
			{
				name: 'sb1JYZYSCWZ',
				type: 'sb1JYZYSCWZ',
				position: '-269.858 2.43816 -31.1511',
			},
			{
				name: 'sb1JYZYSCWZ',
				type: 'sb1JYZYSCWZ',
				position: '-269.858 2.43816 -29.6718',
			},
			{
				name: 'sb1JYZYSCWZ',
				type: 'sb1JYZYSCWZ',
				position: '-269.858 2.43816 -28.073',
			},
			{
				name: 'sb1JYZYSCWZ',
				type: 'sb1JYZYSCWZ',
				position: '-269.858 2.43816 -24.9763',
			},
			{
				name: 'ltBXGJYZYSCWZ-processed',
				type: 'ltBXGJYZYSCWZ-processed',
				position: '-266.647 7.03029 -27.1587',
			},
			{
				name: 'lt1JYZYSCWZ',
				type: 'lt1JYZYSCWZ',
				position: '-261.888 2.88915 -29.5619',
			},
			{
				name: 'logoJYZYSCWZ',
				type: 'logoJYZYSCWZ',
				position: '-274.921 6.20691 -26.4272',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.493743 -19.958',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.493743 -21.4396',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.493743 -22.9119',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.30414 -32.6651',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.30414 -31.1707',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.30414 -29.6979',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.30414 -28.096',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.30414 -24.9962',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-282.26 8.80703 -22.272',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-282.26 8.80703 -31.565',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-277.453 9.10066 -30.1184',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-277.453 9.10066 -23.8131',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-254.442 8.4608 -33.2914',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-254.442 8.4608 -28.903',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-254.442 8.4608 -24.3485',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-254.442 8.4608 -19.9736',
			},
			{
				name: 'gx7JYZYSCWZ',
				type: 'gx7JYZYSCWZ',
				position: '-306.962 0.143183 -31.8961',
			},
			{
				name: 'gx6JYZYSCWZ',
				type: 'gx6JYZYSCWZ',
				position: '-262.593 1.65238 -23.1785',
			},
			{
				name: 'gx5JYZYSCWZ',
				type: 'gx5JYZYSCWZ',
				position: '-273.749 1.73396 -23.1421',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -24.9784',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -26.4773',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -28.0677',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -29.6942',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -31.1549',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -32.6457',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -34.1871',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -33.3271',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -28.6171',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -27.5333',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -19.5803',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -20.7736',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -24.8677',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -23.6744',
			},
			{
				name: 'gx2JYZYSCWZ',
				type: 'gx2JYZYSCWZ',
				position: '-280.114 6.62846 -27.142',
			},
			{
				name: 'gx1JYZYSCWZ-processed',
				type: 'gx1JYZYSCWZ-processed',
				position: '-260.756 1.73396 -29.1324',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-284.195 6.45456 -23.5961',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-284.195 6.45456 -30.4015',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-277.492 7.5641 -20.5632',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-277.492 7.5641 -33.704',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-278.224 6.31764 -18.6577',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-278.215 6.31764 -35.6257',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-277.947 7.26912 -26.8673',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-278.067 7.28564 -32.1301',
			},
			{
				name: 'dmJYZYSCWZ',
				type: 'dmJYZYSCWZ',
				// // showLabelType: 1,
				position: '-279.137 -0.0774128 -26.942',
			},
			{
				name: 'BXGJYZYSCWZ',
				type: 'BXGJYZYSCWZ',
				position: '-276.065 6.18104 -27.597',
			},
			{
				name: 'build1JYZYSCWZ',
				type: 'build1JYZYSCWZ',
				position: '-269.444 3.70138 -27.0322',
			},
			{
				name: 'beng6JYZYSCWZ-processed',
				type: 'beng6JYZYSCWZ-processed',
				position: '-318.662 -0.11043 -28.7525',
			},
			{
				name: 'beng6JYZYSCWZ-processed',
				type: 'beng6JYZYSCWZ-processed',
				position: '-318.662 -0.11043 -21.9265',
			},
			{
				name: 'beng5JYZYSCWZ',
				type: 'beng5JYZYSCWZ',
				position: '-261.274 1.62304 -28.6144',
			},
			{
				name: 'beng5JYZYSCWZ',
				type: 'beng5JYZYSCWZ',
				position: '-261.274 1.62304 -30.4957',
			},
			{
				name: 'beng5JYZYSCWZ',
				type: 'beng5JYZYSCWZ',
				position: '-261.274 1.62304 -32.325',
			},
			{
				name: 'beng4JYZYSCWZ',
				type: 'beng4JYZYSCWZ',
				position: '-262.148 1.32373 -24.0037',
			},
			{
				name: 'beng4JYZYSCWZ',
				type: 'beng4JYZYSCWZ',
				position: '-262.148 1.32373 -21.5255',
			},
			{
				name: 'beng4JYZYSCWZ',
				type: 'beng4JYZYSCWZ',
				position: '-262.148 1.32373 -22.7642',
			},
			{
				name: 'beng3JYZYSCWZ',
				type: 'beng3JYZYSCWZ',
				position: '-269.128 1.7152 -26.4956',
			},
			{
				name: 'beng2JYZYSCWZ',
				type: 'beng2JYZYSCWZ',
				position: '-269.128 1.7152 -25.0106',
			},
			{
				name: 'beng2JYZYSCWZ',
				type: 'beng2JYZYSCWZ',
				position: '-269.128 1.7152 -28.0938',
			},
			{
				name: 'beng2JYZYSCWZ',
				type: 'beng2JYZYSCWZ',
				position: '-269.128 1.7152 -29.7205',
			},
			{
				name: 'beng2JYZYSCWZ',
				type: 'beng2JYZYSCWZ',
				position: '-269.128 1.7152 -31.1896',
			},
			{
				name: 'beng2JYZYSCWZ',
				type: 'beng2JYZYSCWZ',
				position: '-269.128 1.7152 -32.6445',
			},
			{
				name: 'beng1JYZYSCWZ',
				type: 'beng1JYZYSCWZ',
				position: '-268.717 1.49275 -22.9335',
			},
			{
				name: 'beng1JYZYSCWZ',
				type: 'beng1JYZYSCWZ',
				position: '-268.717 1.49275 -21.4472',
			},
			{
				name: 'beng1JYZYSCWZ',
				type: 'beng1JYZYSCWZ',
				position: '-268.717 1.49275 -19.9829',
			},
			// 出水泵房设备
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '159.118 -4.31924 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '147.315 -4.31924 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '135.944 -4.31924 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '125.308 -4.31924 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '105.67 -4.31924 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '92.0843 -4.31924 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '80.6763 -4.31924 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '69.5402 -4.31924 128.343',
			},
			{
				name: 'sb2CSZYSCWZ',
				type: 'sb2CSZYSCWZ',
				position: '157.153 -4.31924 128.343',
			},
			{
				name: 'sb2CSZYSCWZ',
				type: 'sb2CSZYSCWZ',
				position: '145.35 -4.31924 128.343',
			},
			{
				name: 'sb2CSZYSCWZ',
				type: 'sb2CSZYSCWZ',
				position: '133.979 -4.31924 128.343',
			},
			{
				name: 'sb2CSZYSCWZ',
				type: 'sb2CSZYSCWZ',
				position: '123.344 -4.31924 128.343',
			},
			{
				name: 'sb2CSZYSCWZ',
				type: 'sb2CSZYSCWZ',
				position: '103.705 -4.31924 128.343',
			},
			{
				name: 'sb2CSZYSCWZ',
				type: 'sb2CSZYSCWZ',
				position: '90.1196 -4.31924 128.343',
			},
			{
				name: 'sb2CSZYSCWZ',
				type: 'sb2CSZYSCWZ',
				position: '78.7117 -4.31924 128.343',
			},
			{
				name: 'sb2CSZYSCWZ',
				type: 'sb2CSZYSCWZ',
				position: '67.5756 -4.31924 128.343',
			},
			{
				name: 'ltCSZYSCWZ',
				type: 'ltCSZYSCWZ',
				position: '111.657 -3.492 124.663',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '151.842 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '140.298 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '128.184 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '115.655 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '104.712 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '91.2327 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '68.6223 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '79.7212 -6.93307 126.666',
			},
			{
				name: 'lt1CSZYSCWZ',
				type: 'lt1CSZYSCWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, -Math.PI / 2, 0),
				position: '65.8407 -2.92086 131.246',
			},
			{
				name: 'lt1CSZYSCWZ',
				type: 'lt1CSZYSCWZ',
				position: '163.442 -2.92086 131.246',
			},
			{
				name: 'landCSZYSCWZ',
				type: 'landCSZYSCWZ',
				position: '104.409 -5.77349 129.851',
			},
			{
				name: 'equipmentCSZYSCWZ',
				type: 'equipmentCSZYSCWZ',
				position: '111.318 -3.25519 131.942',
			},
			{
				name: 'BXGCSZYSCWZ',
				type: 'BXGCSZYSCWZ',
				position: '112.645 -2.95673 121.59',
			},
			{
				name: 'beng3CSZYSCWZ-processed',
				type: 'beng3CSZYSCWZ-processed',
				position: '56.1811 -6.47413 120.235',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '93.541 -6.49918 121.812',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '106.456 -6.49918 121.812',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '117.894 -6.49918 121.812',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '129.488 -6.49918 121.812',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '141.665 -6.49918 121.812',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '153.695 -6.49918 121.812',
			},
			{
				name: 'beng1CSZYSCWZ',
				type: 'beng1CSZYSCWZ',
				position: '69.9848 -6.49918 121.812',
			},
			{
				name: 'beng1CSZYSCWZ',
				type: 'beng1CSZYSCWZ',
				position: '81.6941 -6.49918 121.812',
			},
			// 滤池设备
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -62.1537',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -58.2772',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -121.494',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -126.11',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -130.64',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -135.139',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -139.58',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -144.729',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -117.11',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -112.53',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -108.001',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -103.507',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -98.971',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -94.3663',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -90.0091',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -85.4903',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -80.878',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -76.398',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -71.9004',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -67.2304',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -53.9409',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -49.2709',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -44.7733',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -40.2933',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -35.681',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -31.1622',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -26.805',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -22.2003',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -17.664',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -13.17',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -8.64097',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 -4.06132',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 23.5581',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 18.4083',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 13.9675',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 9.46851',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 4.9389',
			},
			{
				name: 'aqckLCZYSCWZ',
				type: 'aqckLCZYSCWZ',
				position: '223.621 4.66462 0.322441',
			},
			{
				name: 'ltLCZYSCWZ',
				type: 'ltLCZYSCWZ',
				position: '225.315 5.001 -51.182',
			},
			{
				name: 'xfZYSCWZ',
				type: 'xfZYSCWZ',
				position: '219.747 4.16371 -46.8983',
			},
			{
				name: 'sb4LCZYSCWZ',
				type: 'sb4LCZYSCWZ',
				position: '228.409 9.00815 -80.7842',
			},
			{
				name: 'sb4LCZYSCWZ',
				type: 'sb4LCZYSCWZ',
				position: '228.409 9.00815 -82.7908',
			},
			{
				name: 'sb3LCZYSCWZ',
				type: 'sb3LCZYSCWZ',
				position: '232.857 8.60164 -78.0475',
			},
			{
				name: 'sb3LCZYSCWZ',
				type: 'sb3LCZYSCWZ',
				position: '232.857 8.60164 -73.8395',
			},
			{
				name: 'sb3LCZYSCWZ',
				type: 'sb3LCZYSCWZ',
				position: '232.857 8.60164 -69.648',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 29.7251',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 7.89757',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -11.8905',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -28.819',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -51.2109',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -74.4656',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -95.0474',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -114.861',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -134.251',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -137.334',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -117.367',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -97.903',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -77.3921',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -53.2988',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -30.9404',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -13.7634',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '217.279 3.40107 28.024',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 5.80926',
			},
			{
				name: 'lc2LCZYSCWZ',
				type: 'lc2LCZYSCWZ',
				position: '217.576 5.94819 -60.4332',
			},
			{
				name: '1',
				type: 'lc1LCZYSCWZ',
				// showLabelType: 1,
				position: '217.576 5.94819 -141.079',
			},
			{
				name: '2',
				type: 'lc1LCZYSCWZ',
				// showLabelType: 1,
				position: '217.576 5.94819 -121.074',
			},
			{
				name: '3',
				type: 'lc1LCZYSCWZ',
				// showLabelType: 1,
				position: '217.576 5.94819 -101.67',
			},
			{
				name: '4',
				type: 'lc1LCZYSCWZ',
				// showLabelType: 1,
				position: '217.576 5.94819 -82.0443',
			},
			{
				name: '5',
				type: 'lc1LCZYSCWZ',
				// showLabelType: 1,
				position: '217.576 5.94819 -34.7277',
			},
			{
				name: '6',
				type: 'lc1LCZYSCWZ',
				// showLabelType: 1,
				position: '217.576 5.94819 -17.437',
			},
			{
				name: '7',
				type: 'lc1LCZYSCWZ',
				// showLabelType: 1,
				position: '217.576 5.94819 2.05688',
			},
			{
				name: '8',
				type: 'lc1LCZYSCWZ',
				// showLabelType: 1,
				position: '217.576 5.94819 22.2758',
			},
			{
				name: '1',
				type: 'gd1LCZYSCWZ',
				// showLabelType: 1,
				position: '216.922 1.83607 5.88702',
			},
			{
				name: '2',
				type: 'gd1LCZYSCWZ',
				// showLabelType: 1,
				position: '216.922 1.83607 -13.633',
			},
			{
				name: '3',
				type: 'gd1LCZYSCWZ',
				// showLabelType: 1,
				position: '216.922 1.83607 -30.8883',
			},
			{
				name: '4',
				type: 'gd1LCZYSCWZ',
				// showLabelType: 1,
				position: '216.922 1.83607 -53.9451',
			},
			{
				name: '5',
				type: 'gd1LCZYSCWZ',
				// showLabelType: 1,
				position: '216.922 1.83607 -78.194',
			},
			{
				name: '6',
				type: 'gd1LCZYSCWZ',
				// showLabelType: 1,
				position: '216.922 1.83607 -97.8239',
			},
			{
				name: '7',
				type: 'gd1LCZYSCWZ',
				// showLabelType: 1,
				position: '216.922 1.83607 -117.236',
			},
			{
				name: '8',
				type: 'gd1LCZYSCWZ',
				// showLabelType: 1,
				position: '216.922 1.83607 -137.214',
			},
			{
				name: 'landLCZYSCWZ',
				type: 'landLCZYSCWZ',
				// // showLabelType: 1,
				position: '227.04 7.231 -60.56',
			},
			{
				name: 'kzg3LCZYSCWZ',
				type: 'kzg3LCZYSCWZ',
				position: '229.629 8.78472 -54.4541',
			},
			{
				name: 'kzg2LCZYSCWZ',
				type: 'kzg2LCZYSCWZ',
				position: '233.883 8.28916 -82.6577',
			},
			{
				name: 'kzg1LCZYSCWZ',
				type: 'kzg1LCZYSCWZ',
				position: '231.29 8.842 -82.767',
			},
			{
				name: 'kzg1LCZYSCWZ',
				type: 'kzg1LCZYSCWZ',
				position: '236.145 8.424 -82.767',
			},
			{
				name: 'gd6LCZYSCWZ-processed',
				type: 'gd6LCZYSCWZ-processed',
				position: '231.969 7.66843 -45.5319',
			},
			{
				name: 'gd5LCZYSCWZ',
				type: 'gd5LCZYSCWZ',
				position: '229.435 8.45185 -69.1253',
			},
			{
				name: 'gd5LCZYSCWZ',
				type: 'gd5LCZYSCWZ',
				position: '229.435 8.50147 -73.4332',
			},
			{
				name: 'gd5LCZYSCWZ',
				type: 'gd5LCZYSCWZ',
				position: '229.435 8.55834 -77.703',
			},
			{
				name: 'gd4LCZYSCWZ',
				type: 'gd4LCZYSCWZ',
				position: '232.016 8.23871 -75.7864',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -137.114',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -117.695',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -97.9339',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -77.252',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -54.1553',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -31.6909',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -14.8744',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 5.05658',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 26.8473',
			},
			{
				name: 'gd2LCZYSCWZ',
				type: 'gd2LCZYSCWZ',
				position: '217.684 2.04106 27.2489',
			},
			{
				name: 'beng2LCZYSCWZ-processed',
				type: 'beng2LCZYSCWZ-processed',
				position: '229.504 5.30963 -44.3934',
			},
			{
				name: 'beng1LCZYSCWZ-processed',
				type: 'beng1LCZYSCWZ-processed',
				position: '232.377 5.97319 -42.702',
			},
			{
				name: 'beng1LCZYSCWZ-processed',
				type: 'beng1LCZYSCWZ-processed',
				position: '235.087 5.97319 -42.702',
			},
		]
		return this.dealPositionData({ allArr: arr })
	},
	getObjDataZYSCGLB1() {
		let arr = [
			{
				name: 'signalZYSCWZ',
				type: 'signalZYSCWZ',
				// showLabelType: 1,
				position: '585.771 26.006 -89.37',
			},
			{
				name: 'logoZYSCWZ',
				type: 'logoZYSCWZ',
				// showLabelType: 1,
				position: '576.042 2.137 68.846',
			},
			{
				name: 'mlZYSCWZ',
				type: 'mlZYSCWZ',
				// showLabelType: 1,
				position: '448.897 0.181 69.625',
			},
			{
				name: 'landZYSCWZ',
				type: 'landZYSCWZ',
				position: '211.296 0.195 48.625',
			},
			{
				name: 'dimianWNZYSCWZ',
				type: 'dimianWNZYSCWZ',
				// showLabelType: 1,
				position: '-266.407 0.167 44.123',
				// "position": "0.167 -44.123 -266.407"
			},
			{
				name: 'grandZYSCWZ',
				type: 'grandZYSCWZ',
				position: '140.404 -1.061 48.94',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -159.336',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -152.967',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -139.268',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -132.898',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -51.4943',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -45.1248',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -117.91',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -111.541',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -96.1535',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -89.7841',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -74.7512',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -68.3818',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -30.4693',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -24.0999',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -8.4017',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 -2.03227',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 12.8504',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 19.2198',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 32.9259',
			},
			{
				name: 'equipment3BXGZYSCWZ',
				type: 'equipment3BXGZYSCWZ',
				position: '175.881 9.06822 39.2954',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -156.321',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -136.137',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -114.538',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -92.5967',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -71.6803',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -48.2873',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -27.0016',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 -5.27955',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 15.9512',
			},
			{
				name: 'equipment4BXGZYSCWZ',
				type: 'equipment4BXGZYSCWZ',
				position: '181.864 9.94841 36.4907',
			},
			{
				name: 'gaojiaZYSCWZ',
				type: 'gaojiaZYSCWZ',
				position: '121.716 15.8977 -210.415',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-153.355 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-108.704 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-67.3144 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '71.5593 8.92841 -212.108',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '39.5265 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-28.5671 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '6.37245 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-244.237 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-199.586 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '200.671 7.37485 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '245.323 7.21339 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '286.712 7.62939 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '399.736 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '331.642 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '366.582 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '109.79 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '154.441 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '430.771 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '466.927 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '499.174 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '537.337 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-341.798 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '-297.146 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '577.561 8.92841 -210.516',
			},
			{
				name: 'dunZYSCWZ',
				type: 'dunZYSCWZ',
				position: '622.213 8.92841 -210.516',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '66.7714 2.4171 143.487',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '74.2929 2.4171 143.487',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '80.2842 2.4171 143.135',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '86.9855 2.4171 144.49',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '94.117 2.4171 143.135',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '100.658 2.29344 143.135',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '143.555 2.29344 143.135',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '137.117 2.29344 145.18',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '109.613 2.29344 144.187',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '116.897 2.29344 142.663',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '122.92 2.29344 142.663',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '129.847 2.29344 142.663',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-181.585 2.29344 21.6115',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-180.903 2.29344 67.1321',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-180.899 2.29344 55.6099',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-151.274 2.29344 35.9484',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-141.617 2.29344 51.5837',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-179.702 2.29344 29.3172',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-187.212 2.29344 102.228',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-253.193 2.29344 72.3586',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-249.108 2.29344 99.8911',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-242.423 2.29344 98.0615',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-251.391 2.29344 29.0224',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-251.391 2.29344 17.0028',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-225.604 2.29344 22.0396',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-217.472 2.29344 28.7249',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-227.798 2.29344 35.7431',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-237.013 2.29344 28.7249',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-189.855 2.29344 -33.5208',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-225.339 2.29344 -3.87854',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-181.585 2.29344 -116.938',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-199.779 2.29344 -126.01',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-189.391 2.29344 -126.689',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-213.777 2.29344 -171.361',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-235.534 2.29344 -171.361',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-227.076 2.29344 -166.326',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-257.028 2.29344 -166.326',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-247.655 2.29344 -143.216',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-269.009 2.29344 -132.002',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-290.408 2.29344 -136.941',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-309.359 2.29344 -125.53',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '463.849 2.83592 153.551',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '473.35 2.83592 164.837',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '490.671 2.83592 180.713',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '493.407 2.83592 174.703',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '499.57 2.83592 177.45',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '475.278 2.83592 215.377',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '480.267 2.83592 220.818',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '483.114 2.83592 213.565',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '526.422 2.83592 199.171',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '518.862 2.83592 196.207',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '511.864 2.83592 219.111',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '371.96 2.83592 249.971',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '382.844 2.83592 258.485',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '479.834 2.83592 -128.381',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '487.299 2.83592 -121.925',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '527.596 2.83592 -101.356',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '534.158 2.83592 -105.542',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '503.396 2.83592 -144.355',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '510.305 2.83592 -141.448',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '449.145 2.83592 -144.618',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '406.857 2.83592 -144.618',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '392.657 2.83592 -150.832',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '491.76 2.83592 -130.346',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '527.596 2.83592 -35.2726',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '534.158 2.83592 -39.4586',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '537.591 2.83592 11.5273',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '544.153 2.83592 7.34126',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '544.153 2.83592 -25.9888',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '434.537 2.83592 -35.2726',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '414.262 2.83592 251.148',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '422.274 2.83592 245.283',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '151.247 1.90253 -0.161414',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '151.247 1.90253 -20.4729',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '151.247 1.90253 36.7555',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '141.304 1.90253 -35.0933',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '139.542 1.90253 -47.3575',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '139.542 1.90253 -90.0459',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '146.73 1.90253 -110.462',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '113.065 1.90253 -71.6699',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '107.583 1.90253 -71.6699',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '101.267 1.90253 -71.6699',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '78.322 1.90253 -71.6699',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '72.8398 1.90253 -71.6699',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '66.524 1.90253 -71.6699',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '55.7629 1.90253 -71.6699',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '50.2806 1.90253 -71.6699',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '43.9648 1.90253 -71.6699',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '9.71839 1.90253 -73.4',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '4.23613 1.90253 -73.4',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-2.07969 1.90253 -73.4',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-34.7642 1.90253 -72.1018',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-40.2465 1.90253 -72.1018',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-46.5623 1.90253 -72.1018',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-88.5623 1.90253 -72.1018',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-94.0445 1.90253 -72.1018',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-100.36 1.90253 -72.1018',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-27.3688 2.4171 130.842',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-21.3775 2.4171 130.49',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-14.6762 2.4171 131.845',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-7.54467 2.4171 130.49',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-33.1698 2.4171 130.842',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '1.38369 2.4171 130.49',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-53.231 2.4171 92.5789',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-47.76 2.4171 95.688',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-47.76 2.4171 89.2835',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '-69.4485 2.4171 89.7656',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '6.36889 2.4171 122.722',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '6.36889 2.4171 114.492',
			},
			{
				name: 'plantZYSCWZ',
				type: 'plantZYSCWZ',
				position: '6.36889 2.4171 108.637',
			},
			{
				name: 'qiao1wlZYSCWZ',
				type: 'qiao1wlZYSCWZ',
				position: '316.578 2.87959 64.9832',
			},
			{
				name: 'qiao1wlZYSCWZ',
				type: 'qiao1wlZYSCWZ',
				position: '316.578 2.87959 77.531',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '312.419 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '308.519 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '304.607 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '292.889 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '296.801 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '300.701 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '288.985 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '285.086 2.87959 64.9816',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '285.086 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '288.985 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '300.701 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '296.801 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '292.889 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '304.607 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '308.519 2.87959 77.5294',
			},
			{
				name: 'qiao2wlZYSCWZ',
				type: 'qiao2wlZYSCWZ',
				position: '312.419 2.87959 77.5294',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-128.564 0.619406 39.0034',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-140.041 0.619406 -83.6487',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-133.241 0.619406 -164.36',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-235.232 0.619406 -117.433',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-275.457 0.619406 -137.926',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-302.141 0.619406 -71.1897',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-274.459 0.642768 86.1916',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '100.065 0.41153 157.454',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '61.9762 0.891909 157.454',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '15.9155 0.888286 148.627',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-66.0226 0.372361 134.535',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '-136.318 0.57909 116.821',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '390.427 1.31549 -44.7451',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '421.098 1.31549 -154.246',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '529.919 1.31549 -142.308',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '479.003 1.31549 -97.5669',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '268.299 0.827622 -97.5669',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '258.977 0.599476 -129.639',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '268.957 1.18793 -159.971',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '255.121 0.820643 40.0991',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '243.319 1.31549 136.748',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '243.319 1.31549 96.4559',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '158.387 1.31549 259.854',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '232.49 1.31549 236.366',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '539.94 1.31549 118.531',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '355.979 1.31549 300.254',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '446.711 1.31549 189.536',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '433.654 1.31549 259.654',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '390.331 1.68479 275.063',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '522.483 1.31549 26.1749',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '405.694 1.31549 127.021',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '366.942 1.56248 116.739',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '449.107 1.31549 125.672',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '517.209 1.31549 168.427',
			},
			{
				name: 'treeZYSCWZ',
				type: 'treeZYSCWZ',
				position: '485.597 1.59486 226.157',
			},
			{
				name: 'taZYSCWZ',
				type: 'taZYSCWZ',
				//
				position: '189.331 12.3726 180.57',
			},
			{
				name: 'parkZYSCWZ',
				type: 'parkZYSCWZ',
				position: '403.499 4.83917 -76.7985',
			},
			{
				name: 'parkZYSCWZ',
				type: 'parkZYSCWZ',
				position: '504.562 4.83917 -71.5291',
			},
			{
				name: 'modelBTMZYSCWZ',
				type: 'modelBTMZYSCWZ',
				needTransparent: true,
				// needBeam: true,
				position: '-97.7295 23.2896 190.379',
			},
			{
				name: 'ltBXGZYSCWZ-processed',
				type: 'ltBXGZYSCWZ-processed',
				position: '43.6337 5.60413 -61.0255',
			},

			{
				name: 'guandaoZYSCWZ',
				type: 'guandaoZYSCWZ',
				position: '296.401 2.60546 125.636',
			},
			{
				name: 'fmjZYSCWZ',
				type: 'fmjZYSCWZ',
				// showLabelType: 1,
				position: '152.087 5.342 120.45',
			},
			{
				name: 'equipment2BXGZYSCWZ',
				type: 'equipment2BXGZYSCWZ',
				position: '-188.363 12.239 88.667',
			},
			{
				name: 'equipment2BXGZYSCWZ',
				type: 'equipment2BXGZYSCWZ',
				position: '-204.721 12.239 88.667',
			},
			{
				name: 'equipment1BXGZYSCWZ',
				type: 'equipment1BXGZYSCWZ',
				// showLabelType: 1,
				position: '-193.195 13.037 76.308',
			},
			{
				name: 'equipment1BXGZYSCWZ',
				type: 'equipment1BXGZYSCWZ',
				// showLabelType: 1,
				position: '-197.405 13.038 76.308',
			},
			// {
			// 	"name": "dmZTSCWZ",
			// 	"type": "dmZTSCWZ",
			// 	"position": "119.894 21.564 48.939"
			// },
			{
				name: 'weilanZYSCWZ-processed',
				type: 'weilanZYSCWZ-processed',
				position: '408.657 2.634 169.834',
			},
			{
				name: 'bengTCZYSCWZ',
				type: 'bengTCZYSCWZ',
				// showLabelType: 1,
				position: '-191.044 11.047 76.517',
			},
			{
				name: 'bengTCZYSCWZ',
				type: 'bengTCZYSCWZ',
				// showLabelType: 1,
				position: '-202.298 11.047 76.517',
			},
			{
				name: 'architecture14ZYSCWZ',
				type: 'architecture14ZYSCWZ',
				showLabelType: 1,
				position: '-20.295 2.223 112.147',
			},
			// {
			// 	"name": "architecture13ZYSCWZ1",
			// 	"type": "architecture13ZYSCWZ",
			// 	showLabelType: 1,
			// 	"position": "-81.1749 3.87187 -185.029"
			// },
			{
				name: 'architecture13ZYSCWZ',
				type: 'architecture13ZYSCWZ',
				// showLabelType: 1,
				position: '-244.566 4.06639 103.136',
			},
			{
				name: 'architecture13ZYSCWZ',
				type: 'architecture13ZYSCWZ',
				// showLabelType: 1,
				position: '26.2673 4.06639 129.8',
			},
			{
				name: '滤池',
				type: 'architecture12ZYSCWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '112.083 7.91647 -28.3548',
			},
			{
				name: 'architecture11ZYSCWZ',
				type: 'architecture11ZYSCWZ',
				// showLabelType: 1,
				position: '-88.6423 9.33145 105.391',
			},
			{
				name: 'architecture10ZYSCWZ',
				type: 'architecture10ZYSCWZ',
				// showLabelType: 1,
				position: '-271.053 8.257 -45.944',
			},
			{
				name: '出水泵房',
				type: 'architecture9ZYSCWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '159.885 6.781 138.501',
			},
			{
				name: 'architecture8ZYSCWZ',
				type: 'architecture8ZYSCWZ',
				// showLabelType: 1,
				position: '-223.835 8.19848 -56.0735',
			},
			{
				name: '加药间',
				type: 'architecture7ZYSCWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '-283.594 5.955 -48.664',
			},
			{
				name: 'architecture6ZYSCWZ',
				type: 'architecture6ZYSCWZ',
				// showLabelType: 1,
				position: '-196.553 -0.41 51.117',
			},
			{
				name: '污泥间',
				type: 'architecture5ZYSCWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '-277.442 9.24024 45.9839',
			},
			{
				name: 'architecture4ZYSCWZ',
				type: 'architecture4ZYSCWZ',
				position: '-219.283 7.5162 85.5646',
			},
			{
				name: 'architecture3ZYSCWZ',
				type: 'architecture3ZYSCWZ',
				position: '-197.621 4.797 86.537',
			},
			{
				name: 'architecture2ZYSCWZ',
				type: 'architecture2ZYSCWZ',
				// showLabelType: 1,
				position: '387.602 0.345 -100.22',
			},
			{
				name: 'architecture1ZYSCWZ',
				type: 'architecture1ZYSCWZ',
				showLabelType: 1,
				position: '467.445 9.388 -4.76',
			},
			{
				name: 'architecture0ZYSCWZ',
				type: 'architecture0ZYSCWZ',
				position: '572.631 5.762 28.164',
			},
			// {
			// 	name: 'wanggeZYSCWZ',
			// 	type: 'wanggeZYSCWZ',
			// 	position: '1.95401 1.3682 -3.32895',
			// },
			// 污泥间设备
			{
				name: 'tzWNZYSCWZ',
				type: 'tzWNZYSCWZ',
				position: '-272.911 1.49334 39.7332',
			},
			{
				name: 'sb13WNZYSCWZ',
				type: 'sb13WNZYSCWZ',
				position: '-281.929 5.29002 45.3434',
			},
			{
				name: 'sb13WNZYSCWZ',
				type: 'sb13WNZYSCWZ',
				position: '-280.483 5.29002 45.3434',
			},
			{
				name: 'sb13WNZYSCWZ',
				type: 'sb13WNZYSCWZ',
				position: '-281.194 5.29002 45.3434',
			},
			{
				name: 'sb12BXGWNZYSCWZ',
				type: 'sb12BXGWNZYSCWZ',
				position: '-263.188 3.41647 41.4326',
			},
			{
				name: 'sb11WNZYSCWZ',
				type: 'sb11WNZYSCWZ',
				position: '-263.188 3.47006 39.2822',
			},
			{
				name: 'sb10WNZYSCWZ',
				type: 'sb10WNZYSCWZ',
				position: '-266.289 1.45618 43.291',
			},
			{
				name: 'sb10WNZYSCWZ',
				type: 'sb10WNZYSCWZ',
				position: '-266.289 1.45618 40.7864',
			},
			{
				name: 'sb10WNZYSCWZ',
				type: 'sb10WNZYSCWZ',
				position: '-266.289 1.45618 38.261',
			},
			{
				name: 'sb9WNZYSCWZ',
				type: 'sb9WNZYSCWZ',
				position: '-281.029 1.45618 44.5444',
			},
			{
				name: 'sb9WNZYSCWZ',
				type: 'sb9WNZYSCWZ',
				position: '-281.029 1.45618 40.9178',
			},
			{
				name: 'sb9WNZYSCWZ',
				type: 'sb9WNZYSCWZ',
				position: '-281.029 1.45618 37.1307',
			},
			{
				name: 'sb8WNZYSCWZ',
				type: 'sb8WNZYSCWZ',
				position: '-274.287 14.2772 42.6545',
			},
			{
				name: 'sb8WNZYSCWZ',
				type: 'sb8WNZYSCWZ',
				position: '-274.287 14.2772 39.8467',
			},
			{
				name: 'sb8WNZYSCWZ',
				type: 'sb8WNZYSCWZ',
				position: '-274.287 14.2772 37.0435',
			},
			{
				name: 'sb7TCWNZYSCWZ-processed',
				type: 'sb7TCWNZYSCWZ-processed',
				position: '-291.643 13.543 40.1178',
			},
			{
				name: 'sb7TCWNZYSCWZ-processed',
				type: 'sb7TCWNZYSCWZ-processed',
				position: '-286.562 13.543 40.1178',
			},
			{
				name: 'sb7TCWNZYSCWZ-processed',
				type: 'sb7TCWNZYSCWZ-processed',
				position: '-281.132 13.543 40.1178',
			},
			{
				name: 'sb6WNZYSCWZ',
				type: 'sb6WNZYSCWZ',
				position: '-278.064 3.74151 44.9581',
			},
			{
				name: 'sb6WNZYSCWZ',
				type: 'sb6WNZYSCWZ',
				position: '-271.402 3.74151 44.9581',
			},
			{
				name: 'sb5TCWNZYSCWZ',
				type: 'sb5TCWNZYSCWZ',
				position: '-265.301 12.0862 48.1478',
			},
			{
				name: 'sb5TCWNZYSCWZ',
				type: 'sb5TCWNZYSCWZ',
				position: '-265.301 12.0862 38.9729',
			},
			{
				name: 'sb4WNZYSCWZ',
				type: 'sb4WNZYSCWZ',
				position: '-263.836 1.39625 39.7099',
			},
			{
				name: 'sb4WNZYSCWZ',
				type: 'sb4WNZYSCWZ',
				position: '-263.836 1.39625 41.9671',
			},
			{
				name: 'ltWNZYSCWZ',
				type: 'ltWNZYSCWZ',
				position: '-274.9 5.57614 51.3062',
			},
			{
				name: 'lbWNZYSCWZ',
				type: 'lbWNZYSCWZ',
				// needBeam:true,
				position: '-279.686 5.82296 44.0714',
			},
			{
				name: 'gx2WNZYSCWZ1',
				type: 'gx2WNZYSCWZ1',
				position: '-271.279 1.76338 42.1973',
			},
			{
				name: 'gx1WNZYSCWZ',
				type: 'gx1WNZYSCWZ',
				position: '-256.758 1.55031 42.2628',
			},
			{
				name: 'famen2WNZYSCWZ',
				type: 'famen2WNZYSCWZ',
				position: '-278.725 1.74025 44.2743',
			},
			{
				name: 'famen2WNZYSCWZ',
				type: 'famen2WNZYSCWZ',
				position: '-278.232 1.74308 39.0519',
			},
			{
				name: 'famen2WNZYSCWZ',
				type: 'famen2WNZYSCWZ',
				position: '-278.69 1.74308 37.0937',
			},
			{
				name: 'famen1WNZYSCWZ',
				type: 'famen1WNZYSCWZ',
				position: '-257.569 1.55809 44.893',
			},
			{
				name: 'famen1WNZYSCWZ',
				type: 'famen1WNZYSCWZ',
				position: '-256.747 1.54767 43.1659',
			},
			{
				name: 'famen1WNZYSCWZ',
				type: 'famen1WNZYSCWZ',
				position: '-256.747 1.54767 41.2454',
			},
			{
				name: 'famen1WNZYSCWZ',
				type: 'famen1WNZYSCWZ',
				position: '-261.493 1.56737 46.1614',
			},
			{
				name: 'BXGWNZYSCWZ-processed',
				type: 'BXGWNZYSCWZ-processed',
				position: '-276.495 6.42113 44.1571',
			},
			{
				name: 'bengTCWNZYSCWZ',
				type: 'bengTCWNZYSCWZ',
				position: '-271.01 9.46312 39.4769',
			},
			{
				name: 'beng1WNZYSCWZ',
				type: 'beng1WNZYSCWZ',
				position: '-259.614 1.66023 40.3763',
			},
			{
				name: 'beng1WNZYSCWZ',
				type: 'beng1WNZYSCWZ',
				position: '-259.614 1.66023 42.9868',
			},
			{
				name: 'beng1WNZYSCWZ',
				type: 'beng1WNZYSCWZ',
				position: '-259.614 1.66023 45.6809',
			},
			// 加药设备
			{
				name: 'zcJYZYSCWZ',
				type: 'zcJYZYSCWZ',
				position: '-254.368 6.86634 -26.7275',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-297.764 2.41373 -19.0078',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-303.894 2.41373 -19.0078',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-309.478 2.41373 -19.0078',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-309.478 2.41373 -27.6216',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-303.894 2.41373 -27.6216',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-297.764 2.41373 -27.6216',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-297.764 2.41373 -35.3088',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-303.894 2.41373 -35.3088',
			},
			{
				name: 'tongJYZYSCWZ',
				type: 'tongJYZYSCWZ',
				position: '-309.478 2.41373 -35.3088',
			},
			{
				name: 'sb5JYZYSCWZ',
				type: 'sb5JYZYSCWZ',
				position: '-269.237 6.88707 -28.9874',
			},
			{
				name: 'sb3JYZYSCWZ',
				type: 'sb3JYZYSCWZ',
				position: '-276.739 7.53025 -21.0492',
			},
			{
				name: 'sb3JYZYSCWZ',
				type: 'sb3JYZYSCWZ',
				position: '-276.739 7.53025 -33.2436',
			},
			{
				name: 'sb3JYZYSCWZ',
				type: 'sb3JYZYSCWZ',
				position: '-278.401 1.75163 -25.3845',
			},
			{
				name: 'sb3JYZYSCWZ',
				type: 'sb3JYZYSCWZ',
				position: '-283.431 7.5689 -30.399',
			},
			{
				name: 'sb2JYZYSCWZ',
				type: 'sb2JYZYSCWZ',
				position: '-269.858 2.43816 -26.4427',
			},
			{
				name: 'sb1JYZYSCWZ',
				type: 'sb1JYZYSCWZ',
				position: '-269.858 2.43816 -32.6392',
			},
			{
				name: 'sb1JYZYSCWZ',
				type: 'sb1JYZYSCWZ',
				position: '-269.858 2.43816 -31.1511',
			},
			{
				name: 'sb1JYZYSCWZ',
				type: 'sb1JYZYSCWZ',
				position: '-269.858 2.43816 -29.6718',
			},
			{
				name: 'sb1JYZYSCWZ',
				type: 'sb1JYZYSCWZ',
				position: '-269.858 2.43816 -28.073',
			},
			{
				name: 'sb1JYZYSCWZ',
				type: 'sb1JYZYSCWZ',
				position: '-269.858 2.43816 -24.9763',
			},
			{
				name: 'ltBXGJYZYSCWZ-processed',
				type: 'ltBXGJYZYSCWZ-processed',
				position: '-266.647 7.03029 -27.1587',
			},
			{
				name: 'lt1JYZYSCWZ',
				type: 'lt1JYZYSCWZ',
				position: '-261.888 2.88915 -29.5619',
			},
			{
				name: 'logoJYZYSCWZ',
				type: 'logoJYZYSCWZ',
				position: '-274.921 6.20691 -26.4272',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.493743 -19.958',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.493743 -21.4396',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.493743 -22.9119',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.30414 -32.6651',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.30414 -31.1707',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.30414 -29.6979',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.30414 -28.096',
			},
			{
				name: 'kgJYZYSCWZ',
				type: 'kgJYZYSCWZ',
				position: '-271.086 0.30414 -24.9962',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-282.26 8.80703 -22.272',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-282.26 8.80703 -31.565',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-277.453 9.10066 -30.1184',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-277.453 9.10066 -23.8131',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-254.442 8.4608 -33.2914',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-254.442 8.4608 -28.903',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-254.442 8.4608 -24.3485',
			},
			{
				name: 'jbjJYZYSCWZ',
				type: 'jbjJYZYSCWZ',
				position: '-254.442 8.4608 -19.9736',
			},
			{
				name: 'gx7JYZYSCWZ',
				type: 'gx7JYZYSCWZ',
				position: '-306.962 0.143183 -31.8961',
			},
			{
				name: 'gx6JYZYSCWZ',
				type: 'gx6JYZYSCWZ',
				position: '-262.593 1.65238 -23.1785',
			},
			{
				name: 'gx5JYZYSCWZ',
				type: 'gx5JYZYSCWZ',
				position: '-273.749 1.73396 -23.1421',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -24.9784',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -26.4773',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -28.0677',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -29.6942',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -31.1549',
			},
			{
				name: 'gx4JYZYSCWZ',
				type: 'gx4JYZYSCWZ',
				position: '-274.939 4.54989 -32.6457',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -34.1871',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -33.3271',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -28.6171',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -27.5333',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -19.5803',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -20.7736',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -24.8677',
			},
			{
				name: 'gx3JYZYSCWZ',
				type: 'gx3JYZYSCWZ',
				position: '-252.208 7.56873 -23.6744',
			},
			{
				name: 'gx2JYZYSCWZ',
				type: 'gx2JYZYSCWZ',
				position: '-280.114 6.62846 -27.142',
			},
			{
				name: 'gx1JYZYSCWZ-processed',
				type: 'gx1JYZYSCWZ-processed',
				position: '-260.756 1.73396 -29.1324',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-284.195 6.45456 -23.5961',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-284.195 6.45456 -30.4015',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-277.492 7.5641 -20.5632',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-277.492 7.5641 -33.704',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-278.224 6.31764 -18.6577',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-278.215 6.31764 -35.6257',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-277.947 7.26912 -26.8673',
			},
			{
				name: 'fmJYZYSCWZ',
				type: 'fmJYZYSCWZ',
				position: '-278.067 7.28564 -32.1301',
			},
			{
				name: 'dmJYZYSCWZ',
				type: 'dmJYZYSCWZ',
				// showLabelType: 1,
				position: '-279.137 -0.0774128 -26.942',
			},
			{
				name: 'BXGJYZYSCWZ',
				type: 'BXGJYZYSCWZ',
				position: '-276.065 6.18104 -27.597',
			},
			{
				name: 'build1JYZYSCWZ',
				type: 'build1JYZYSCWZ',
				position: '-269.444 3.70138 -27.0322',
			},
			{
				name: 'beng6JYZYSCWZ-processed',
				type: 'beng6JYZYSCWZ-processed',
				position: '-318.662 -0.11043 -28.7525',
			},
			{
				name: 'beng6JYZYSCWZ-processed',
				type: 'beng6JYZYSCWZ-processed',
				position: '-318.662 -0.11043 -21.9265',
			},
			{
				name: 'beng5JYZYSCWZ',
				type: 'beng5JYZYSCWZ',
				position: '-261.274 1.62304 -28.6144',
			},
			{
				name: 'beng5JYZYSCWZ',
				type: 'beng5JYZYSCWZ',
				position: '-261.274 1.62304 -30.4957',
			},
			{
				name: 'beng5JYZYSCWZ',
				type: 'beng5JYZYSCWZ',
				position: '-261.274 1.62304 -32.325',
			},
			{
				name: 'beng4JYZYSCWZ',
				type: 'beng4JYZYSCWZ',
				position: '-262.148 1.32373 -24.0037',
			},
			{
				name: 'beng4JYZYSCWZ',
				type: 'beng4JYZYSCWZ',
				position: '-262.148 1.32373 -21.5255',
			},
			{
				name: 'beng4JYZYSCWZ',
				type: 'beng4JYZYSCWZ',
				position: '-262.148 1.32373 -22.7642',
			},
			{
				name: 'beng3JYZYSCWZ',
				type: 'beng3JYZYSCWZ',
				position: '-269.128 1.7152 -26.4956',
			},
			{
				name: 'beng2JYZYSCWZ',
				type: 'beng2JYZYSCWZ',
				position: '-269.128 1.7152 -25.0106',
			},
			{
				name: 'beng2JYZYSCWZ',
				type: 'beng2JYZYSCWZ',
				position: '-269.128 1.7152 -28.0938',
			},
			{
				name: 'beng2JYZYSCWZ',
				type: 'beng2JYZYSCWZ',
				position: '-269.128 1.7152 -29.7205',
			},
			{
				name: 'beng2JYZYSCWZ',
				type: 'beng2JYZYSCWZ',
				position: '-269.128 1.7152 -31.1896',
			},
			{
				name: 'beng2JYZYSCWZ',
				type: 'beng2JYZYSCWZ',
				position: '-269.128 1.7152 -32.6445',
			},
			{
				name: 'beng1JYZYSCWZ',
				type: 'beng1JYZYSCWZ',
				position: '-268.717 1.49275 -22.9335',
			},
			{
				name: 'beng1JYZYSCWZ',
				type: 'beng1JYZYSCWZ',
				position: '-268.717 1.49275 -21.4472',
			},
			{
				name: 'beng1JYZYSCWZ',
				type: 'beng1JYZYSCWZ',
				position: '-268.717 1.49275 -19.9829',
			},
			// 出水泵房设备
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '67.6391 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '68.8912 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '156.893 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '158.145 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '146.243 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '144.99 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '122.061 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '123.313 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '105.356 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '104.104 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '91.3893 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '92.6415 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '80.7243 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '79.4721 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '62.0387 -4.32426 126.507',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '63.2909 -4.32426 126.507',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '134.466 -4.31092 128.343',
			},
			{
				name: 'sb1CSZYSCWZ',
				type: 'sb1CSZYSCWZ',
				position: '133.214 -4.31092 128.343',
			},
			{
				name: 'ltCSZYSCWZ-processed',
				type: 'ltCSZYSCWZ-processed',
				position: '111.657 -3.492 124.663',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '151.842 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '140.298 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '128.184 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '115.655 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '104.712 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '91.2327 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '68.6223 -6.93307 126.666',
			},
			{
				name: 'lt2CSZYSCWZ',
				type: 'lt2CSZYSCWZ',
				position: '79.7212 -6.93307 126.666',
			},
			{
				name: 'lt1CSZYSCWZ',
				type: 'lt1CSZYSCWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, -Math.PI / 2, 0),
				position: '65.8407 -2.92086 131.246',
			},
			{
				name: 'lt1CSZYSCWZ',
				type: 'lt1CSZYSCWZ',
				position: '163.442 -2.92086 131.246',
			},
			{
				name: 'landCSZYSCWZ',
				type: 'landCSZYSCWZ',
				position: '104.409 -5.77349 129.851',
			},
			{
				name: 'equipmentCSZYSCWZ',
				type: 'equipmentCSZYSCWZ',
				position: '111.318 -3.25519 131.942',
			},
			{
				name: 'BXGCSZYSCWZ',
				type: 'BXGCSZYSCWZ',
				position: '112.645 -2.95673 121.59',
			},
			{
				name: 'beng3CSZYSCWZ-processed',
				type: 'beng3CSZYSCWZ-processed',
				position: '56.1811 -6.47413 120.235',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '93.541 -6.49918 121.812',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '106.456 -6.49918 121.812',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '117.894 -6.49918 121.812',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '129.488 -6.49918 121.812',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '141.665 -6.49918 121.812',
			},
			{
				name: 'beng2CSZYSCWZ',
				type: 'beng2CSZYSCWZ',
				position: '153.695 -6.49918 121.812',
			},
			{
				name: 'beng1CSZYSCWZ-processed',
				type: 'beng1CSZYSCWZ-processed',
				position: '69.9848 -6.49918 121.812',
			},
			{
				name: 'beng1CSZYSCWZ-processed',
				type: 'beng1CSZYSCWZ-processed',
				position: '81.6941 -6.49918 121.812',
			},
			// 滤池设备
			{
				name: 'ltLCZYSCWZ',
				type: 'ltLCZYSCWZ',
				position: '225.315 5.001 -51.182',
			},
			{
				name: 'xfZYSCWZ',
				type: 'xfZYSCWZ',
				position: '219.747 4.16371 -46.8983',
			},
			{
				name: 'sb4LCZYSCWZ',
				type: 'sb4LCZYSCWZ',
				position: '228.409 9.00815 -80.7842',
			},
			{
				name: 'sb4LCZYSCWZ',
				type: 'sb4LCZYSCWZ',
				position: '228.409 9.00815 -82.7908',
			},
			{
				name: 'sb3LCZYSCWZ',
				type: 'sb3LCZYSCWZ',
				position: '232.857 8.60164 -78.0475',
			},
			{
				name: 'sb3LCZYSCWZ',
				type: 'sb3LCZYSCWZ',
				position: '232.857 8.60164 -73.8395',
			},
			{
				name: 'sb3LCZYSCWZ',
				type: 'sb3LCZYSCWZ',
				position: '232.857 8.60164 -69.648',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 29.7251',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 7.89757',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -11.8905',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -28.819',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -51.2109',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -74.4656',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -95.0474',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -114.861',
			},
			{
				name: 'sb2LCZYSCWZ',
				type: 'sb2LCZYSCWZ',
				position: '216.757 3.56078 -134.251',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -137.334',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -117.367',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -97.903',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -77.3921',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -53.2988',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -30.9404',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 -13.7634',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '217.279 3.40107 28.024',
			},
			{
				name: 'sb1LCZYSCWZ',
				type: 'sb1LCZYSCWZ',
				position: '216.957 3.40107 5.80926',
			},
			{
				name: 'lc2LCZYSCWZ',
				type: 'lc2LCZYSCWZ',
				position: '217.576 5.94819 -60.4332',
			},
			{
				name: 'lc1LCZYSCWZ',
				type: 'lc1LCZYSCWZ',
				position: '217.576 5.94819 -140.079',
			},
			{
				name: 'lc1LCZYSCWZ',
				type: 'lc1LCZYSCWZ',
				position: '217.576 5.94819 -120.074',
			},
			{
				name: 'lc1LCZYSCWZ',
				type: 'lc1LCZYSCWZ',
				position: '217.576 5.94819 -100.67',
			},
			{
				name: 'lc1LCZYSCWZ',
				type: 'lc1LCZYSCWZ',
				position: '217.576 5.94819 -80.9443',
			},
			{
				name: 'lc1LCZYSCWZ',
				type: 'lc1LCZYSCWZ',
				position: '217.576 5.94819 -34.7977',
			},
			{
				name: 'lc1LCZYSCWZ',
				type: 'lc1LCZYSCWZ',
				position: '217.576 5.94819 -17.537',
			},
			{
				name: 'lc1LCZYSCWZ',
				type: 'lc1LCZYSCWZ',
				position: '217.576 5.94819 2.01688',
			},
			{
				name: 'lc1LCZYSCWZ',
				type: 'lc1LCZYSCWZ',
				position: '217.576 5.94819 22.2758',
			},
			{
				name: 'landLCZYSCWZ',
				type: 'landLCZYSCWZ',
				// showLabelType: 1,
				position: '227.04 7.231 -60.56',
			},
			{
				name: 'kzg3LCZYSCWZ',
				type: 'kzg3LCZYSCWZ',
				position: '229.629 8.78472 -54.4541',
			},
			{
				name: 'kzg2LCZYSCWZ',
				type: 'kzg2LCZYSCWZ',
				position: '233.883 8.28916 -82.6577',
			},
			{
				name: 'kzg1LCZYSCWZ',
				type: 'kzg1LCZYSCWZ',
				position: '231.29 8.842 -82.767',
			},
			{
				name: 'kzg1LCZYSCWZ',
				type: 'kzg1LCZYSCWZ',
				position: '236.145 8.424 -82.767',
			},
			{
				name: 'gd6LCZYSCWZ-processed',
				type: 'gd6LCZYSCWZ-processed',
				position: '231.969 7.66843 -45.5319',
			},
			{
				name: 'gd5LCZYSCWZ',
				type: 'gd5LCZYSCWZ',
				position: '229.435 8.45185 -69.1253',
			},
			{
				name: 'gd5LCZYSCWZ',
				type: 'gd5LCZYSCWZ',
				position: '229.435 8.50147 -73.4332',
			},
			{
				name: 'gd5LCZYSCWZ',
				type: 'gd5LCZYSCWZ',
				position: '229.435 8.55834 -77.703',
			},
			{
				name: 'gd4LCZYSCWZ',
				type: 'gd4LCZYSCWZ',
				position: '232.016 8.23871 -75.7864',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -137.114',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -117.695',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -97.9339',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -77.252',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -54.1553',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -31.6909',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 -14.8744',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 5.05658',
			},
			{
				name: 'gd3LCZYSCWZ',
				type: 'gd3LCZYSCWZ',
				position: '216.066 7.89114 26.8473',
			},
			{
				name: 'gd2LCZYSCWZ',
				type: 'gd2LCZYSCWZ',
				position: '217.684 2.04106 27.3689',
			},
			{
				name: 'gd1LCZYSCWZ',
				type: 'gd1LCZYSCWZ',
				position: '216.922 1.83607 5.88702',
			},
			{
				name: 'gd1LCZYSCWZ',
				type: 'gd1LCZYSCWZ',
				position: '216.922 1.83607 -13.633',
			},
			{
				name: 'gd1LCZYSCWZ',
				type: 'gd1LCZYSCWZ',
				position: '216.922 1.83607 -30.8883',
			},
			{
				name: 'gd1LCZYSCWZ',
				type: 'gd1LCZYSCWZ',
				position: '216.922 1.83607 -53.1451',
			},
			{
				name: 'gd1LCZYSCWZ',
				type: 'gd1LCZYSCWZ',
				position: '216.922 1.83607 -77.194',
			},
			{
				name: 'gd1LCZYSCWZ',
				type: 'gd1LCZYSCWZ',
				position: '216.922 1.83607 -97.7239',
			},
			{
				name: 'gd1LCZYSCWZ',
				type: 'gd1LCZYSCWZ',
				position: '216.922 1.83607 -117.236',
			},
			{
				name: 'gd1LCZYSCWZ',
				type: 'gd1LCZYSCWZ',
				position: '216.922 1.83607 -137.214',
			},
			{
				name: 'beng2LCZYSCWZ-processed',
				type: 'beng2LCZYSCWZ-processed',
				position: '229.504 5.30963 -44.3934',
			},
			{
				name: 'beng1LCZYSCWZ-processed',
				type: 'beng1LCZYSCWZ-processed',
				position: '232.377 5.97319 -42.702',
			},
			{
				name: 'beng1LCZYSCWZ-processed',
				type: 'beng1LCZYSCWZ-processed',
				position: '235.087 5.97319 -42.702',
			},
		]
		return this.dealPositionData({ allArr: arr })
	},
	// 石鼓山水厂模型
	getObjDataSGSSCGLB() {
		let arr = [
			// 以上 加药间新增模型
			{
				name: 'wgSGSWZ',
				type: 'wgSGSWZ',
				position: '-49554.3 -80543.7 47688.6',
			},
			{
				name: 'landSGSWZ',
				type: 'landSGSWZ',
				position: '-49554.3 -80543.7 47688.6',
			},
			{
				name: 'dmSGSWZ',
				type: 'dmSGSWZ',
				position: '0 0 -0',
			},
			// 文件6
			{
				name: '污泥间',
				type: 'bulid3SGSWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '-9403.5 318.515 -44193.6',
			},
			{
				name: '出水泵房',
				type: 'bulid2SGSWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '77118.1 1288.32 48335.7',
			},
			{
				name: 'bulid1SGSWZ',
				type: 'bulid1SGSWZ',
				position: '171614 6214.72 -4371.69',
			},
			{
				name: 'build11SGSWZ',
				type: 'build11SGSWZ',
				position: '-39007.3 1252.18 41246.4',
			},
			{
				name: 'build10SGSWZ',
				type: 'build10SGSWZ',
				position: '-145161 232.147 35995.8',
			},
			{
				name: '沉淀池',
				type: 'build9SGSWZ',
				showLabelType: 1,
				position: '8148.16 1342.31 -2181.04',
				labelPoints: [
					{
						position: '-103148.16 8342.31 18181.04',
						name: '进水',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '1148.16 1342.31 12181.04',
						name: '清水池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
				],
			},
			{
				name: 'build8SGSWZ',
				type: 'build8SGSWZ',
				position: '27176.4 2823.09 -56494.3',
			},
			{
				name: 'build7SGSWZ',
				type: 'build7SGSWZ',
				position: '108119 2019.94 -42889.2',
			},
			{
				name: '加药间',
				type: 'Build6SGSWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '-80963.9 1245.19 -45102.3',
			},
			{
				name: '滤池',
				type: 'build5SGSWZ',
				showLabelType: 1,
				nextDiagramId: 'go',
				position: '69298 2482.9 7698.2',
				labelPoints: [
					{
						position: '59298 2482.9 -3000',
						name: '1#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '69298 2482.9 -3000',
						name: '2#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '77298 2482.9 -3000',
						name: '3#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '59298 2482.9 20698.2',
						name: '4#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '69298 2482.9 20698.2',
						name: '5#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
					{
						position: '77298 2482.9 20698.2',
						name: '6#滤池',
						labelContent: [
							{
								text: '进口压力：3.45Mpa',
							},
							{
								text: '瞬时流量：32m³/h',
							},
							{
								text: '瞬时流量：232m³',
							},
						],
					},
				],
			},
			{
				name: 'build4SGSWZ',
				type: 'build4SGSWZ',
				position: '222086 0 20844.1',
			},
			{
				name: 'BMXSGSWZ',
				type: 'BMXSGSWZ',
				position: '44484.6 60.8137 -3191.48',
			},
			{
				name: 'wd1SGSWZ',
				type: 'wd1SGSWZ',
				position: '194632.218 6123.888 -9801.713',
			},
			{
				name: 'FS4SGSWZ',
				type: 'FS4SGSWZ',
				position: '84216.389 3016.767 -10274.589',
			},
			{
				name: 'langan1SGSWZ',
				type: 'langan1SGSWZ',
				position: '84985.453 2425.724 -4166.782',
			},
			{
				name: 'langan2SGSWZ',
				type: 'langan2SGSWZ',
				position: '54713.122 2392.894 7716.39',
			},
			{
				name: 'lg2SGSWZ',
				type: 'lg2SGSWZ',
				position: '2541.796 -854.333 3003.528',
			},
			{
				name: 'lt10SGSWZ',
				type: 'lt10SGSWZ',
				position: '-38875.91 -2911.706 -9000.251',
			},
			{
				name: 'wl7SGSWZ',
				type: 'wl7SGSWZ',
				position: '-212284 1930.11 -11386.2',
			},
			{
				name: 'wl6SGSWZ',
				type: 'wl6SGSWZ',
				position: '-119108 1930.11 -55974.7',
			},
			{
				name: 'wl5SGSWZ',
				type: 'wl5SGSWZ',
				position: '-174825 1930.11 52160.2',
			},
			{
				name: 'wl4SGSWZ',
				type: 'wl4SGSWZ',
				position: '70773 1930.11 -62744.3',
			},
			{
				name: 'wl3SGSWZ',
				type: 'wl3SGSWZ',
				position: '188408 1947.31 -40691.2',
			},
			{
				name: 'wl2SGSWZ',
				type: 'wl2SGSWZ',
				position: '200511 1930.11 51506.6',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				// hasGlbRotateSet: true,
				// rotation: new THREE.Vector3(Math.PI / 2, -Math.PI, 0),
				position: '54514.1 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				position: '179761 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '174866 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '169971 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '165076 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '160181 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '155286 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '150391 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '145496 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '140601 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '135706 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '130811 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '125916 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '121021 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '116126 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '111231 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '106336 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '101441 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '96545.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '91650.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '86755.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '81860.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '76965.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '72070.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '67175.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '62280.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '57385.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '52490.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '47595.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '42700.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '37805.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '32910.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '28015.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '23120.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '18225.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '13330.9 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '8435.95 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '3540.95 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-1354.05 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-6249.05 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-11144.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-16039.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-20934.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-25829.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-30724.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-35619.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-40514.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-45409.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-50304.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-55199.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-60094.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-64989.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-69884.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-74779.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-79674.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-84569.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-89464.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-94359.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-99254.1 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-104149 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-109044 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-113939 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-118834 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-123729 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-128624 2.41231e-14 67996.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '49618 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '44723 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '39828 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '34933 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '30038 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '25143 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '20248 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '15353 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '10458 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '5563.02 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '668.023 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-4226.98 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-9121.98 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-14017 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-18912 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-23807 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-28702 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-33597 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-38492 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-43387 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-48282 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-53177 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-58072 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-62967 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-67862 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-72757 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-77652 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-82547 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-87442 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-92337 2.41231e-14 -67847.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '144994 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '140099 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '135204 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '130309 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '125414 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '120519 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '115624 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '110729 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '105834 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '100939 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '96044.1 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '91149.1 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '86254.1 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '154809 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '149914 2.41231e-14 -57144.4',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '218837 48.8012 32689.7',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '218773 2.41231e-14 27791.8',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, -Math.PI, 0),
				position: '219577 2.41231e-14 -2023.25',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, -Math.PI, 0),
				position: '219577 2.41231e-14 -6918.65',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, -Math.PI, 0),
				position: '219577 2.41231e-14 -11809.1',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, -Math.PI, 0),
				position: '219577 2.41231e-14 -16704.5',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, -Math.PI, 0),
				position: '219577 2.41231e-14 -21595.8',
			},
			{
				name: '2',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-218454 2.41231e-14 34202',
			},
			{
				name: '1',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-218454 2.41231e-14 29303',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-218454 2.41231e-14 24404',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-218454 2.41231e-14 19505',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-218454 2.41231e-14 14606',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-218454 2.41231e-14 9707.02',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-218454 2.41231e-14 4808.02',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',

				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-218454 2.41231e-14 -90.978',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-218454 2.41231e-14 -4989.98',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-204039 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-199147 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-194249 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-189358 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-184457 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-179566 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-174668 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-169776 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-164867 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-159976 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-155077 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-150186 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-145273 2.41231e-14 -15248.9',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-143042 2.41231e-14 -17912.2',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-143042 2.41231e-14 -22812.1',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-143042 2.41231e-14 -27717.6',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-143042 2.41231e-14 -32617.6',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-143042 2.41231e-14 -37505.6',
			},
			{
				name: 'wl1SGSWZ',
				type: 'wl1SGSWZ',
				position: '-143042 2.41231e-14 -42405.5',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '205723 154.65 14529.3',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '205723 154.65 -20533.5',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '156418 154.65 25568.4',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '163682 154.65 11387.1',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '163682 154.65 -37097.9',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '135881 154.65 -19860.1',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '137032 154.65 -2493.51',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '163682 154.65 50765.2',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '152418 154.65 43081.7',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '122175 154.65 39978.4',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '113545 154.65 1006.13',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '121044 154.65 15654.3',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '103790 154.65 -7936.1',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '87615.1 154.65 -44462.8',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '95925.5 154.65 -36633.9',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '121681 154.65 -39907.5',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '117545 154.65 -35516.3',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '19613.1 154.65 -31625.2',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '7635.83 154.65 -51531',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-29624.9 154.65 -53751.2',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '18562 154.65 21974',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-18390.2 154.65 22668.6',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-119420 154.65 23841.6',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-110859 154.65 49538',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-166758 154.65 26446.2',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-175388 154.65 1006.13',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-151136 154.65 11099.2',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-185143 154.65 5484.51',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-136839 154.65 4718.71',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-140830 154.65 9142.39',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-156513 154.65 -562.603',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-137527 154.65 -18365.6',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-134784 154.65 -26951.6',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-138607 154.65 -39158.9',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '59423 154.65 -20662.5',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '77405.7 154.65 -20168.4',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '89611.2 154.65 -10212.7',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '49009.1 154.65 12039.3',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '51419 154.65 42271.4',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '48582.2 154.65 -3744.99',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-5239.24 154.65 66039.7',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '18826.9 154.65 66039.7',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-84317.3 154.65 66039.7',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-50680.7 154.65 66039.7',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '90309.3 154.65 20146.9',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '68269.7 154.65 -19051.5',
			},
			{
				name: 'tree2SGSWZ',
				type: 'tree2SGSWZ',
				position: '-44173.2 154.65 -31625.2',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '202170 154.65 3846.56',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '203175 154.65 25498',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '184343 154.65 27767.9',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '184938 154.65 -30055.4',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '140406 154.65 -44913.7',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '175891 154.65 25498',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '166267 154.65 27767.9',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '148859 154.65 21467.8',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '140406 154.65 27767.9',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '148859 154.65 58503.9',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '140327 154.65 47969.8',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '171807 154.65 63581.5',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '116382 154.65 47969.8',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '107752 154.65 28028',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '118908 154.65 -47682.5',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '63617.2 154.65 -49055.4',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '10396.6 154.65 -60054.7',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '10396.6 154.65 20310.1',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-3865.09 154.65 22688',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-29246.6 154.65 19959.6',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-41209 154.65 22644.7',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-57306 154.65 21291.3',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '33929.8 154.65 29291',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '23673.1 154.65 18206.3',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-120770 154.65 -11769.7',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-109688 154.65 10454.1',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-118141 154.65 53246.7',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-109890 154.65 34911.6',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-117074 154.65 42268',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-64647.6 154.65 24088.7',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-189536 154.65 13336.9',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-194189 154.65 23233.4',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '51805 154.65 -16791.8',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-21128.5 154.65 65997.3',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-113591 154.65 65997.3',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '-91428.5 154.65 65997.3',
			},
			{
				name: 'tree1SGSWZ',
				type: 'tree1SGSWZ',
				position: '22536.2 154.65 -19574.6',
			},
			{
				name: 'otherSGSWZ',
				type: 'otherSGSWZ',
				position: '-7494.38 503.099 8465.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '26443.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '25137.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '23831.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '22525.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '21219.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '19913.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '18607.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '17301.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '15995.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '14689.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '13383.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '12077.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '10771.4 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '9465.36 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '8159.36 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '6853.36 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '5547.36 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '4241.36 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '2935.36 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '1629.36 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '323.358 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-982.642 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-2288.64 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-3594.64 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-4900.64 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-6206.64 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-7512.64 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-8818.64 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-10124.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-11430.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-12736.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-14042.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-15348.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-16654.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-17960.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-19266.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-20572.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-21878.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-23184.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-24490.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-25796.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-27102.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-28408.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-29714.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-31020.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-32326.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-33632.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-34938.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-36244.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-37550.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-38856.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-40162.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-41468.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-42774.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-44080.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-45386.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-46692.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-47998.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-49304.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-50610.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-51916.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-53222.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-54528.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-55834.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-57140.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-58446.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-59752.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-61058.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-62364.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-63670.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-64976.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-66282.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-67588.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-68894.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-70200.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-71506.6 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '27758.9 3120.94 -14221.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '28412.5 3120.94 -13567.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27089.1 3117.14 402.605',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27089.1 3117.14 1710.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27089.1 3117.14 3018.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27089.1 3117.14 4326.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27089.1 3117.14 5634.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27089.1 3117.14 6942.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27089.1 3117.14 8250.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27089.1 3117.14 9558.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '26443.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '25137.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '23831.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '22525.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '21219.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '19913.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '18607.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '17301.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '15995.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '14689.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '13383.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '12077.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '10771.4 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '9465.36 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '8159.36 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '6853.36 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '5547.36 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '4241.36 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '2935.36 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '1629.36 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '323.358 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-982.642 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-2288.64 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-3594.64 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-4900.64 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-6206.64 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-7512.64 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-8818.64 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-10124.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-11430.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-12736.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-14042.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-15348.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-16654.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-17960.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-19266.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-20572.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-21878.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-23184.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-24490.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-25796.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-27102.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-28408.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-29714.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-31020.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-32326.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-33632.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-34938.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-36244.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-37550.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-38856.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-40162.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-41468.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-42774.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-44080.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-45386.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-46692.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-47998.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-49304.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-50610.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-51916.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-53222.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-54528.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-55834.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-57140.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-58446.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-59752.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-61058.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-62364.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-63670.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-64976.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-66282.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-67588.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-68894.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-70200.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-71506.6 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '27750.5 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '29062.3 3120.94 12459.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '26443.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '25137.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '23831.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '22525.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '21219.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '19913.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '18607.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '17301.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '15995.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '14689.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '13383.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '12077.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '10771.4 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '9465.36 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '8159.36 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '6853.36 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '5547.36 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '4241.36 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '2935.36 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '1629.36 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '323.358 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-982.642 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-2288.64 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-3594.64 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-4900.64 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-6206.64 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-7512.64 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-8818.64 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-10124.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-11430.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-12736.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-14042.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-15348.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-16654.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-17960.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-19266.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-20572.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-21878.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-23184.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-24490.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-25796.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-27102.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-28408.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-29714.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-31020.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-32326.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-33632.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-34938.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-36244.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-37550.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-38856.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-40162.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-41468.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-42774.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-44080.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-45386.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-46692.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-47998.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-49304.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-50610.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-51916.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-53222.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-54528.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-55834.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-57140.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-58446.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-59752.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-61058.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-62364.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-63670.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-64976.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-66282.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-67588.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-68894.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-70200.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-71506.6 3120.94 -12111.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '26443.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '25137.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '23831.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '22525.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '21219.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '19913.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '18607.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '17301.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '15995.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '14689.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '13383.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '12077.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '10771.4 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '9465.36 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '8159.36 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '6853.36 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '5547.36 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '4241.36 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '2935.36 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '1629.36 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '323.358 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-982.642 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-2288.64 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-3594.64 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-4900.64 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-6206.64 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-7512.64 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-8818.64 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-10124.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-11430.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-12736.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-14042.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-15348.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-16654.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-17960.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-19266.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-20572.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-21878.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-23184.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-24490.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-25796.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-27102.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-28408.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-29714.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-31020.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-32326.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-33632.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-34938.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-36244.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-37550.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-38856.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-40162.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-41468.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-42774.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-44080.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-45386.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-46692.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-47998.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-49304.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-50610.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-51916.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-53222.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-54528.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-55834.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-57140.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-58446.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-59752.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-61058.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-62364.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-63670.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-64976.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-66282.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-67588.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-68894.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-70200.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-71506.6 3120.94 10220.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '26443.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '25137.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '23831.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '22525.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '21219.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '19913.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '18607.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '17301.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '15995.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '14689.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '13383.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '12077.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '10771.4 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '9465.36 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '8159.36 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '6853.36 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '5547.36 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '4241.36 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '2935.36 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '1629.36 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '323.358 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-982.642 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-2288.64 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-3594.64 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-4900.64 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-6206.64 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-7512.64 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-8818.64 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-10124.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-11430.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-12736.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-14042.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-15348.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-16654.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-17960.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-19266.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-20572.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-21878.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-23184.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-24490.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-25796.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-27102.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-28408.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-29714.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-31020.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-32326.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-33632.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-34938.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-36244.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-37550.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-38856.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-40162.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-41468.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-42774.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-44080.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-45386.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-46692.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-47998.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-49304.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-50610.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-51916.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-53222.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-54528.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-55834.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-57140.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-58446.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-59752.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-61058.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-62364.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-63670.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-64976.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-66282.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-67588.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-68894.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-70200.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-71506.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-75224.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-76530.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-77836.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-79142.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-80448.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-81754.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-83060.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-84366.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-85672.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-86978.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-88284.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-89590.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-90896.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-92202.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-93508.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-94814.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-96120.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-97426.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-98732.6 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-100039 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-101345 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-102651 3120.94 -250.509',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-75224.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-76530.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-77836.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-79142.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-80448.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-81754.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-83060.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-84366.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-85672.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-86978.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-88284.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-89590.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-90896.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-92202.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-93508.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-94814.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-96120.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-97426.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-98732.6 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-100039 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-101345 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-102651 3120.94 11909.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-74138.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-75444.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-76750.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-78056.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-79362.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-80668.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-81974.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-83280.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-84586.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-85892.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-87198.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-88504.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-89810.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-91116.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-92422.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-93728.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-95034.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-96340.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-97646.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-98952.8 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-100259 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-75224.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-76530.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-77836.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-79142.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-80448.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-81754.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-83060.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-84366.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-85672.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-86978.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-88284.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-89590.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-90896.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-92202.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-93508.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-94814.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-96120.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-97426.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-98732.6 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-100039 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-101345 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-102651 3120.94 -13771.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-72816.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-74122.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-75428.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-76734.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-78040.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-79346.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-80652.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-81958.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-83264.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-84570.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-85876.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-87182.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-88488.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-89794.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-91100.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-92406.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-93712.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-95018.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-96324.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-97630.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-98936.5 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-100243 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-72826.7 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72157.2 3120.94 13114.4',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-104187 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-105493 3120.94 13776.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 13120.9',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 11814.1',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 10503.4',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 9196.69',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 7888.91',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 6582.16',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 5271.46',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 3964.71',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-106804 3120.94 3318.47',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-108120 3120.94 3318.47',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 -5778.31',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 -7085.05',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 -8395.75',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 -9702.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 -11010.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 -12317',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 -13627.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-106142 3120.94 -14934.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72157.2 3120.94 -14878.4',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-101562 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-102868 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-104174 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-105502 3120.94 -15526.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-106142 3120.94 -4463.05',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-106806 3120.94 -3815.74',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-108118 3120.94 -3815.74',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-108789 3120.94 -3163.77',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-108789 3120.94 -1851.44',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-108789 3120.94 -548.211',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-108789 3120.94 768.016',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-109434 3120.94 3318.47',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-110739 3120.94 3318.47',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '26443.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '25137.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '23831.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '22525.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '21219.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '19913.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '18607.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '17301.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '15995.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '14689.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '13383.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '12077.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '10771.4 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '9465.36 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '8159.36 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '6853.36 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '5547.36 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '4241.36 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '2935.36 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '1629.36 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '323.358 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-982.642 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-2288.64 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-3594.64 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-4900.64 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-6206.64 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-7512.64 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-8818.64 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-10124.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-11430.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-12736.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-14042.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-15348.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-16654.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-17960.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-19266.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-20572.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-21878.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-23184.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-24490.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-25796.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-27102.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-28408.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-29714.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-31020.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-32326.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-33632.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-34938.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-36244.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-37550.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-38856.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-40162.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-41468.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-42774.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-44080.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-45386.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-46692.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-47998.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-49304.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-50610.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-51916.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-53222.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-54528.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-55834.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-57140.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-58446.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-59752.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-61058.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-62364.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-63670.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-64976.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-66282.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-67588.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-68894.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-70200.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-71506.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-75224.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-76530.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-77836.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-79142.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-80448.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-81754.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-83060.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-84366.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-85672.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-86978.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-88284.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-89590.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-90896.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-92202.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-93508.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-94814.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-96120.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-97426.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-98732.6 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-100039 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-101345 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-102651 3120.94 -1606.26',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 -11419.9',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 -10111.9',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 -8803.89',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 -7495.89',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 -6187.89',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 -4879.89',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 -3571.89',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 -2263.89',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 -2411.65',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 -3721.83',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 -5036.11',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 -6346.28',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 -7660.65',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 -8970.82',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 -10285.1',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 -11595.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 -12908.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 11095.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 9785.59',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 8471.31',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 7161.14',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 5846.77',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 4536.6',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 3222.32',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 1912.15',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-74534 3117.14 598.737',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 11095.8',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 9785.59',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 8471.31',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 7161.14',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 5846.77',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 4536.6',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 3222.32',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 1912.15',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 598.737',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 -2461.58',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 -3771.75',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 -5086.04',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 -6396.21',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 -7710.57',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 -9020.75',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 -10335',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 -11645.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-103397 3117.14 -12958.6',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27097 3117.14 -11456.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27097 3117.14 -10148.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27097 3117.14 -8840.47',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27097 3117.14 -7532.47',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27097 3117.14 -6224.47',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27097 3117.14 -4916.47',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27097 3117.14 -3608.47',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '27097 3117.14 -2300.47',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '28415.6 3117.14 -12259.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '28415.6 3117.14 -10951.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '28415.6 3117.14 -9643.15',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '28415.6 3117.14 -8335.15',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '28415.6 3117.14 -7027.15',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '28415.6 3117.14 -5719.15',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '28415.6 3117.14 -4411.15',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '29071.4 3117.14 -3760.36',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '30379.9 3117.14 -3760.36',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '29706.6 3117.14 6574.69',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '29706.6 3117.14 7882.69',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '29706.6 3117.14 9190.69',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '29706.6 3117.14 10498.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '29706.6 3117.14 11806.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '30364.4 3117.14 5925.02',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '31672.3 3117.14 5925.02',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '32983.7 3117.14 5925.02',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '33635.7 3117.14 5271.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '33635.7 3117.14 3960.5',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '33635.7 3117.14 2655.71',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '33635.7 3117.14 1346.65',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '31029.8 3117.14 -3105.37',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '31029.8 3117.14 -1797.37',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '31029.8 3117.14 -480.222',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 402.605',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 1710.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 3018.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 4326.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 5634.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 6942.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 8250.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-72158.5 3117.14 9558.61',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-104774 4826.05 17028.1',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-103467 4826.05 17028.1',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-99527.4 4826.05 17028.1',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-100842 4826.05 17028.1',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-104774 4826.05 24872.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-103467 4826.05 24872.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-99527.4 4826.05 24872.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-100842 4826.05 24872.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-105435 4826.05 17682.9',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-105435 4826.05 18987.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-105435 4826.05 20292.9',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-105435 4826.05 21597.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-105435 4826.05 22907.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-105435 4826.05 24212',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-98868.2 4826.05 17682.9',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-98868.2 4826.05 18987.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-98868.2 4826.05 20292.9',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-98868.2 4826.05 21597.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-98868.2 4826.05 22907.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-98868.2 4826.05 24212',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-102143 4826.05 24872.7',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-100233 4826.05 23887',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-101543 4826.05 23887',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-102857 4826.05 23887',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-100233 4826.05 17732.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-101543 4826.05 17732.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-102857 4826.05 17732.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-104159 4826.05 23887',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				position: '-104159 4826.05 17732.3',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-99563.6 4826.05 18385.6',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-99563.6 4826.05 19700.9',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-99563.6 4826.05 21014',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-99563.6 4826.05 22324.2',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-104810 4826.05 18385.6',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-104810 4826.05 19700.9',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-104810 4826.05 21014',
			},
			{
				name: 'lt9SGSWZ',
				type: 'lt9SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '-104810 4826.05 22324.2',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-213774 4091.43 22760.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-213774 4091.43 -866.208',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-201566 4091.43 -10784.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-180666 4091.43 -10784.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-160340 4091.43 -10784.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-137819 4091.43 -10784.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-123470 4091.43 33957.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-123470 4091.43 9174.65',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-108599 4091.43 -20409.4',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-77051.3 4091.43 -20409.4',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-51322.4 4091.43 -20409.4',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-23903.3 4091.43 -20409.4',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '9251.64 4091.43 -20409.4',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '144142 4091.43 38039.4',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '165203 4091.43 38039.4',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '134880 4091.43 18282.2',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '134880 4091.43 -13439.1',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '134880 4091.43 -43132.5',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '124248 4091.43 48505.2',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '124248 4091.43 27259.7',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '124248 4091.43 7318.74',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '124248 4091.43 -16235.7',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '46840.8 4091.43 -16014.1',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '46840.8 4091.43 2087.84',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '46840.8 4091.43 25172.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '46840.8 4091.43 48726.7',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '62271.8 4091.43 -20278.6',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '83062.8 4091.43 -20278.6',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '73261.5 4091.43 -29248.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '52470.5 4091.43 -29248.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '116322 4091.43 -29248.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '95531.1 4091.43 -29248.3',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '121908 4091.43 65098.8',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '94835.1 4091.43 65098.8',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '67762.1 4091.43 65098.8',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '40689.1 4091.43 65098.8',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '13616.1 4091.43 65098.8',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-13456.9 4091.43 65098.8',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-40529.9 4091.43 65098.8',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-67602.9 4091.43 65098.8',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-94675.9 4091.43 65098.8',
			},
			{
				name: 'lightSGSWZ',
				type: 'lightSGSWZ',
				position: '-121749 4091.43 65098.8',
			},
			{
				name: 'light1SGSWZ',
				type: 'light1SGSWZ',
				position: '-176899 4091.43 48674.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37120 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '36182.1 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '32424.6 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '33362.5 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '35238 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '34300.1 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '30549.4 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '31487.3 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '29611.8 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28673.9 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28604.2 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '29542.2 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '31417.6 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '30479.7 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '34230.4 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '35168.3 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '33292.9 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '32355 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '36112.4 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37050.3 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37050.3 2842.45 -54390.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '36112.4 2842.45 -54390.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '32355 2842.45 -54390.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '33292.9 2842.45 -54390.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '35168.3 2842.45 -54390.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '34230.4 2842.45 -54390.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '30479.7 2842.45 -54390.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '31417.6 2842.45 -54390.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '29542.2 2842.45 -54390.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28604.2 2842.45 -54390.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28604.2 2842.45 -55428.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '29542.2 2842.45 -55428.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '31417.6 2842.45 -55428.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '30479.7 2842.45 -55428.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '34230.4 2842.45 -55428.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '35168.3 2842.45 -55428.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '33292.9 2842.45 -55428.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '32355 2842.45 -55428.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '36112.4 2842.45 -55428.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37050.3 2842.45 -55428.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26524.3 2842.45 -55411',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '25586.4 2842.45 -55411',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '21828.9 2842.45 -55411',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '22766.8 2842.45 -55411',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '24642.3 2842.45 -55411',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '23704.4 2842.45 -55411',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '19953.7 2842.45 -55411',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '20891.6 2842.45 -55411',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '19016.1 2842.45 -55411',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '18078.2 2842.45 -55411',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17951 2842.45 -54434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '18888.9 2842.45 -54434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '20764.3 2842.45 -54434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '19826.4 2842.45 -54434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '23577.2 2842.45 -54434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '24515.1 2842.45 -54434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '22639.6 2842.45 -54434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '21701.7 2842.45 -54434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '25459.1 2842.45 -54434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26397 2842.45 -54434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26397 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '25459.1 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '21701.7 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '22639.6 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '24515.1 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '23577.2 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '19826.4 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '20764.3 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '18888.9 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17951 2842.45 -39305.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17951 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '18888.9 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '20764.3 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '19826.4 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '23577.2 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '24515.1 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '22639.6 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '21701.7 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '25459.1 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26397 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28203.5 2842.45 -38092.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -38092.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -40717.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -39779.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -42603.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -41664.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -44487.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -43549',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -46373.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -45434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -48264.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -47326.3',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -50150.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -49211.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -52034.7',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -51096.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -53920.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -52981.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -40717.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -39779.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -42603.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -41664.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -44487.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -43549',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -46373.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -45434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -48264.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -47326.3',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -50150.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -49211.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -52034.7',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -51096.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -53920.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28140.6 2842.45 -52981.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -40717.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -39779.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -42603.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -41664.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -44487.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -43549',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -46373.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -45434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -48264.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -47326.3',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -50150.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -49211.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -52034.7',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -51096.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -53920.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17467.6 2842.45 -52981.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -40717.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -39779.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -42603.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -41664.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -44487.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -43549',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -46373.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -45434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -48264.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -47326.3',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -50150.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -49211.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -52034.7',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -51096.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -53920.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37515 2842.45 -52981.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '31947.3 2842.45 -35744.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28671.6 2842.45 -36186.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '29609.9 2842.45 -36186.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '30552.7 2842.45 -36186.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '31495.6 2842.45 -36186.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -40717.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -42603.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -41664.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -44487.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -43549',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -46373.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -45434.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -48264.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -47326.3',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -50150.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -49211.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -52034.7',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -51096.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -53920.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -52981.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -54862.3',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -56747.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -55809.3',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37537.5 2842.45 -55928',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37537.5 2842.45 -57813.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37537.5 2842.45 -56874.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -58628.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '38270.2 2842.45 -57690',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37050.3 2842.45 -58292.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '36112.4 2842.45 -58292.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '32355 2842.45 -58292.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '33292.9 2842.45 -58292.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '35168.3 2842.45 -58292.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '34230.4 2842.45 -58292.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '30479.7 2842.45 -58292.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '31417.6 2842.45 -58292.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '29542.2 2842.45 -58292.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28604.2 2842.45 -58292.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '33088.3 2837.67 -61924.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '34026.2 2837.67 -61924.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '35901.7 2837.67 -61924.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '34963.7 2837.67 -61924.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '32150.9 2837.67 -61924.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '31215.3 2837.67 -61924.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '23710 2842.45 -58237.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '24647.9 2842.45 -58237.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26523.4 2842.45 -58237.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '25585.4 2842.45 -58237.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28137.7 2842.45 -57825',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28137.7 2842.45 -56879.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28137.7 2842.45 -55927.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26997.1 2842.45 -57775.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26997.1 2842.45 -56829.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26997.1 2842.45 -55878.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17622.8 2842.45 -57775.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17622.8 2842.45 -56829.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17622.8 2842.45 -55878.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '19962.1 2842.45 -58237.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '20900 2842.45 -58237.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '22775.4 2842.45 -58237.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '21837.5 2842.45 -58237.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '18090.7 2842.45 -58237.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '19028.6 2842.45 -58237.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '22126.6 2842.45 -61520',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '23064.6 2842.45 -61520',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '24940 2842.45 -61520',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '24002.1 2842.45 -61520',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '20251.4 2842.45 -61520',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '21189.3 2842.45 -61520',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '19313.8 2842.45 -61520',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '37802.8 2842.45 -59098.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '36864 2842.45 -59098.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '36368.9 2842.45 -59562',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '36368.9 2842.45 -60499.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '36368.9 2842.45 -61442.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '30277 2837.67 -61924.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '29340.3 2837.67 -61924.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28878 2842.45 -59562',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28878 2842.45 -60499.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28878 2842.45 -61442.8',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '25413.2 2842.45 -61052.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '18828.7 2842.45 -61052.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28406.2 2842.45 -59094.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '27468.8 2842.45 -59094.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26538.7 2842.45 -59094.2',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '18347.3 2842.45 -59160.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16924.8 2842.45 -59161.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -58692.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -57753.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -56816.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -55878',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -54939.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -54000.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -53063.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -52124.6',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -51191.3',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -50252.3',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -49315.3',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -48376.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -47431.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -46492.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -45555.4',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -44616.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -43682.9',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -42744',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -41807',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -40868',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -39929',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '16454.4 2842.45 -38990.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '17011.1 2842.45 -38562.1',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '30530.3 2842.45 -35271.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '31478.9 2842.45 -35271.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '28646.3 2842.45 -35271.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '29594.9 2842.45 -35271.5',
			},
			{
				name: 'lg1SGSWZ',
				type: 'lg1SGSWZ',
				position: '26879.2 2842.45 -35727.9',
			},
			{
				name: 'grass2SGSWZ',
				type: 'grass2SGSWZ',
				position: '3156.57 176.888 315.487',
			},
			{
				name: 'grass1ZYSCWZ',
				type: 'grass1ZYSCWZ',
				position: '13221.4 256.291 -2643.15',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '80907.3 3016.77 -12143.1',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '76935.3 3016.77 -12092.5',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '71268.6 3016.77 -12092.5',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '67341.9 3016.77 -12092.5',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '61744.2 3016.77 -12162.4',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '57831.9 3016.77 -12194.4',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '84441.8 3016.77 -13580.3',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '54967.7 3016.77 -13580.3',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '80844.4 3016.77 27447.1',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '76935.3 3016.77 27447.1',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '71268.6 3016.77 27447.1',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '67341.9 3016.77 27447.1',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '61693.1 3016.77 27447.1',
			},
			{
				name: 'FS3SGSWZ',
				type: 'FS3SGSWZ',
				position: '57778.5 3016.77 27447.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 1932.95',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 623.996',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 -684.96',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 -1993.92',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 -3302.87',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 -4611.83',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 3243.07',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 -5926.47',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 -7235.43',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 -8544.39',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 -9853.34',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '82870.3 3006.77 -11166.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 1932.95',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 623.996',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 -684.96',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 -1993.92',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 -3302.87',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 -4611.83',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 3243.07',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 -5926.47',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 -7235.43',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 -8544.39',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 -9853.34',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '75021.1 3006.77 -11166.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 1932.95',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 623.996',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 -684.96',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 -1993.92',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 -3302.87',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 -4611.83',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 3243.07',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 -5926.47',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 -7235.43',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 -8544.39',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 -9853.34',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '73284.7 3006.77 -11166.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 1958.2',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 649.243',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 -659.714',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 -1968.67',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 -3277.63',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 -4586.58',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 3268.32',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 -5901.23',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 -7210.18',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 -8519.14',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 -9828.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '65364.8 3006.77 -11141.2',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 1932.95',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 623.996',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 -684.96',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 -1993.92',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 -3302.87',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 -4611.83',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 3243.07',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 -5926.47',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 -7235.43',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 -8544.39',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 -9853.34',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '63703.4 3006.77 -11166.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 1869.43',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 560.47',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 -748.487',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 -2057.44',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 -3366.4',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 -4675.36',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 3179.55',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 -5990',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 -7298.96',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 -8607.91',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 -9916.87',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '55871.5 3006.77 -11230',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84207 3006.77 -9.3494',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84207 3006.77 -1318.31',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84207 3006.77 -2627.26',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84207 3006.77 -3936.22',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84207 3006.77 -5245.18',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84207 3006.77 1299.69',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84207 3006.77 2611.16',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84207 3006.77 -6550.49',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84765.2 3006.77 -8998.28',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84765.2 3006.77 -10307.2',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84765.2 3006.77 -11616.2',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, 0, 0),
				position: '84765.2 3006.77 -12925.2',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '83448.1 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '82140 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '80826.4 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '79518.4 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '78209.9 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '76901.9 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '75591.3 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '74283.3 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '72969.7 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '71661.6 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '70353.1 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '69045.1 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '67734.1 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '66426.1 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '65112.5 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '63804.5 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '62495.9 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '61187.9 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '59878.3 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '58570.2 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '57256.6 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '55948.6 3006.77 -13578',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 -7649.38',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 -6336.26',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 -5022.46',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 -3709.34',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 -2397.57',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 -1084.46',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 229.352',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 2848.91',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 -11590.3',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 -10277.2',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 -8963.4',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 -12898.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54664.5 3006.77 4154.33',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '63017.5 3006.77 3899.14',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '61708.5 3006.77 3899.14',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '60399.6 3006.77 3899.14',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '59090.6 3006.77 3899.14',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '57781.7 3006.77 3899.14',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '56472.7 3006.77 3899.14',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '72584.5 3006.77 3911.49',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '71275.6 3006.77 3911.49',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '69966.6 3006.77 3911.49',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '68657.7 3006.77 3911.49',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '67348.7 3006.77 3911.49',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '66039.8 3006.77 3911.49',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '82210.3 3006.77 3905.06',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '80901.3 3006.77 3905.06',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '79592.4 3006.77 3905.06',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '78283.4 3006.77 3905.06',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '76974.5 3006.77 3905.06',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '75665.5 3006.77 3905.06',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 13375.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 14684.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 15993.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 17302.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 18611.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 19920.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 12065.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 21235.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 22544.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 23853',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 25162',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '55818.9 3006.77 26475.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 13375.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 14684.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 15993.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 17302.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 18611.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 19920.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 12065.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 21235.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 22544.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 23853',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 25162',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '63668 3006.77 26475.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 13375.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 14684.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 15993.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 17302.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 18611.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 19920.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 12065.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 21235.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 22544.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 23853',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 25162',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '65404.4 3006.77 26475.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 13375.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 14684.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 15993.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 17302.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 18611.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 19920.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 12065.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 21235.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 22544.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 23853',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 25162',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '73237.5 3006.77 26475.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 13375.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 14684.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 15993.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 17302.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 18611.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 19920.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 12065.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 21235.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 22544.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 23853',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 25162',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '74985.7 3006.77 26475.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 13375.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 14684.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 15993.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 17302.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 18611.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 19920.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 12065.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 21235.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 22544.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 23853',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 25162',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '82784.4 3006.77 26475.1',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 15147.9',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 16456.8',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 17765.8',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 19074.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 20383.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 12528.8',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 11225.4',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 21689',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 22997.9',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 24306.9',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 25615.9',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 26924.8',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '54583.2 3006.77 28233.8',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '55241.1 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '56549.1 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '57862.7 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '59170.7 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '60479.3 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '61787.3 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '63097.8 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '64405.9 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '65719.5 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '67027.5 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '68336 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '69644 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '70955 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '72263.1 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '73576.6 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '74884.7 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '76193.2 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '77501.2 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '78810.9 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '80118.9 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '81432.5 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '82740.5 3006.77 28886.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '84187.4 3006.77 22555.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '84187.4 3006.77 21242.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '84187.4 3006.77 19928.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '84187.4 3006.77 18615.5',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '84187.4 3006.77 17303.8',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '84187.4 3006.77 15990.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '84187.4 3006.77 14676.8',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '84187.4 3006.77 13363.7',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '84187.4 3006.77 12057.3',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '75536.1 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '76845.1 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '78154 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '79463 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '80772 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '82080.9 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '66039.8 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '67348.7 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '68657.7 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '69966.6 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '71275.6 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '72584.5 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '56478.8 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '57787.8 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '59096.8 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '60405.7 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '61714.7 3006.77 11403.6',
			},
			{
				name: 'FS2SGSWZ',
				type: 'FS2SGSWZ',
				position: '63023.6 3006.77 11403.6',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '82211 3006.77 -11823.9',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '81234.2 3006.77 -11823.9',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '76307.6 3006.77 -11823.6',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '75330.8 3006.77 -11823.6',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '72599.6 3006.77 -11824',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '71622.8 3006.77 -11824',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '66652.5 3006.77 -11803.4',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '65675.7 3006.77 -11803.4',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '63036.9 3006.77 -11832',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '62060.1 3006.77 -11832',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '57179.5 3006.77 -11887.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '56202.7 3006.77 -11887.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '80236.6 3006.77 -12462.9',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '79259.8 3006.77 -12462.9',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '78279.1 3006.77 -12462.9',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '77302.3 3006.77 -12462.9',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '70601.1 3006.77 -12407.4',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '69624.3 3006.77 -12407.4',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '68643.6 3006.77 -12407.4',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '67666.8 3006.77 -12407.4',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '61085.3 3006.77 -12514.4',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '60108.5 3006.77 -12514.4',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '59127.8 3006.77 -12514.4',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '58151 3006.77 -12514.4',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '56477.1 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '57453.9 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '62348.6 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '63325.4 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '66045.8 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '67022.6 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '71918.8 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '72895.6 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '75633.9 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '76610.7 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '81496.6 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '82473.4 3006.77 27128',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '58435.1 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '59411.9 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '60392.6 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '61369.4 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '68005.7 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '68982.5 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '69963.3 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '70940.1 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '77590.3 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '78567.1 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '79547.8 3006.77 27769.2',
			},
			{
				name: 'FS1SGSWZ',
				type: 'FS1SGSWZ',
				position: '80524.6 3006.77 27769.2',
			},
			{
				name: 'ds2SGSWZ',
				type: 'ds2SGSWZ',
				position: '35568.7 2557.41 -21689.6',
			},
			{
				name: 'djSGSWZ',
				type: 'djSGSWZ',
				position: '119628 1525.62 28830.2',
			},

			// 文件2
			{
				name: '0FBFSGS',
				type: '0FBFSGS',
				position: '82049 -1802.5 44961.4',
			},
			{
				name: '1FBFSGS',
				type: '1FBFSGS',
				position: '82049 1669.52 44961.4',
			},
			{
				name: '2FBFSGS',
				type: '2FBFSGS',
				position: '83103.7 441.738 41053.9',
			},
			{
				name: 'xiaofang1BFSGS',
				type: 'xiaofang1BFSGS',
				position: '59087.3 -3705.01 44714.4',
			},
			{
				name: 'xiaofang1BFSGS',
				type: 'xiaofang1BFSGS',
				position: '62853.1 -3705.01 44714.4',
			},
			{
				name: 'xiaofang1BFSGS',
				type: 'xiaofang1BFSGS',
				position: '66710.1 -3705.01 44714.4',
			},
			{
				name: 'xfBFSGS',
				type: 'xfBFSGS',
				position: '70171.7 -1325.26 45061.6',
			},
			{
				name: 'wolun1BFSGS',
				type: 'wolun1BFSGS',
				position: '59266.6 -2916.39 44679.8',
			},
			{
				name: 'wolun1BFSGS',
				type: 'wolun1BFSGS',
				position: '63032.5 -2916.39 44679.8',
			},
			{
				name: 'wolun1BFSGS',
				type: 'wolun1BFSGS',
				position: '66889.5 -2916.39 44679.8',
			},
			{
				name: 'tag3BFSGS',
				type: 'tag3BFSGS',
				position: '83040.3 3372.29 41544.5',
			},
			{
				name: 'tag2BFSGS',
				type: 'tag2BFSGS',
				position: '82940.3 3304.22 42340.4',
			},
			{
				name: 'tag1BFSGS',
				type: 'tag1BFSGS',
				position: '100236 2555.17 44768.7',
			},
			{
				name: 'sb5BFSGS',
				type: 'sb5BFSGS',
				position: '75814.8 787.663 41446.4',
			},
			{
				name: 'sb4BFSGS',
				type: 'sb4BFSGS',
				position: '82911.1 1388.31 43213.5',
			},
			{
				name: 'sb3BFSGS',
				type: 'sb3BFSGS',
				position: '70295.8 1347.3 49676.7',
			},
			{
				name: 'sb2BFSGS',
				type: 'sb2BFSGS',
				position: '79728.5 1647.25 42247.9',
			},
			{
				name: 'sb1BFSGS',
				type: 'sb1BFSGS',
				position: '88031.6 2352.08 49255.9',
			},
			{
				name: 'LTBFSGS',
				type: 'LTBFSGS',
				position: '62515.1 -1506.97 44955',
			},
			{
				name: 'kzg4BFSGS',
				type: 'kzg4BFSGS',
				position: '76671.1 1481.21 49075.8',
			},
			{
				name: 'kzg4BFSGS',
				type: 'kzg4BFSGS',
				position: '76671.1 1481.21 46125.8',
			},
			{
				name: 'kzg4BFSGS',
				type: 'kzg4BFSGS',
				position: '76671.1 1481.21 43200.367',
			},
			{
				name: 'kzg3BFSGS',
				type: 'kzg3BFSGS',
				position: '64214.6 -539.669 41585.4',
			},
			{
				name: 'kzg3BFSGS',
				type: 'kzg3BFSGS',
				position: '60226.5 -539.669 41585.4',
			},
			{
				name: 'kzg2BFSGS',
				type: 'kzg2BFSGS',
				position: '100265 1594.48 41000.5',
			},
			{
				name: 'kzg2BFSGS',
				type: 'kzg2BFSGS',
				position: '100265 1594.48 44644.2',
			},
			{
				name: 'kzg1BFSGS',
				type: 'kzg1BFSGS',
				position: '100230 2024.38 48678.5',
			},
			{
				name: 'guandao1BFSGS',
				type: 'guandao1BFSGS',
				position: '59786.4 -2895.35 44692.9',
			},
			{
				name: 'guandao1BFSGS',
				type: 'guandao1BFSGS',
				position: '63552.3 -2895.35 44692.9',
			},
			{
				name: 'guandao1BFSGS',
				type: 'guandao1BFSGS',
				position: '67409.3 -2895.35 44692.9',
			},
			{
				name: 'guanBFSGS',
				type: 'guanBFSGS',
				position: '83819.5 1546.85 42369.1',
			},
			{
				name: 'gd3BFSGS',
				type: 'gd3BFSGS',
				position: '79019.3 1473.13 45721.2',
			},
			{
				name: 'gd2BFSGS',
				type: 'gd2BFSGS',
				position: '83068.3 1436.29 41938.7',
			},
			{
				name: 'gd1BFSGS',
				type: 'gd1BFSGS',
				position: '59318.8 -102.389 46409.1',
			},
			{
				name: 'beng1BFSGS',
				type: 'beng1BFSGS',
				position: '58292.9 -3333.42 44600.6',
			},
			{
				name: 'beng1BFSGS',
				type: 'beng1BFSGS',
				position: '62058.8 -3333.42 44600.6',
			},
			{
				name: 'beng1BFSGS',
				type: 'beng1BFSGS',
				position: '65915.7 -3333.42 44600.6',
			},
			// 文件三
			{
				name: 'xiaofangJYJSGS',
				type: 'xiaofangJYJSGS',
				position: '-61129.8 883.767 -50368.3',
			},
			{
				name: 'tagJYJSGS',
				type: 'tagJYJSGS',
				position: '-55967.9 2926.09 -50552.8',
			},
			{
				name: 'sb3JYJSGS',
				type: 'sb3JYJSGS',
				position: '-55365.5 983.465 -54065.1',
			},
			{
				name: 'sb2JYJSGS',
				type: 'sb2JYJSGS',
				position: '-68540.9 1899.43 -50907.2',
			},
			{
				name: 'sb2JYJSGS',
				type: 'sb2JYJSGS',
				position: '-65736.1 1899.43 -50907.2',
			},
			{
				name: 'sb4JYJSGS',
				type: 'sb4JYJSGS',
				position: '-62668.7 1366.26 -53312.7',
			},
			{
				name: 'sb1JYJSGS2',
				type: 'sb1JYJSGS',
				position: '-45852.4 2088 -46949',
			},
			{
				name: 'sb1JYJSGS3',
				type: 'sb1JYJSGS',
				position: '-45285.7 2088 -46949',
			},
			{
				name: 'sb1JYJSGS4',
				type: 'sb1JYJSGS',
				position: '-44732.1 2088 -46949',
			},
			{
				name: 'sb1JYJSGS5',
				type: 'sb1JYJSGS',
				position: '-44161.7 2088 -46949',
			},
			{
				name: 'sb1JYJSGS6',
				type: 'sb1JYJSGS',
				position: '-43651.5 2088 -46949',
			},
			{
				name: 'kzg3JYJSGS',
				type: 'kzg3JYJSGS',
				position: '-55096 1613.09 -44562.8',
			},
			{
				name: 'kzg2JYJSGS',
				type: 'kzg2JYJSGS',
				position: '-70497.5 2123.97 -44917.1',
			},
			{
				name: 'kzg1JYJSGS',
				type: 'kzg1JYJSGS',
				position: '-67340.7 1975.64 -46624.4',
			},
			{
				name: 'kzg1JYJSGS',
				type: 'kzg1JYJSGS',
				position: '-65155.7 1975.64 -46624.4',
			},
			// {
			// 	"name": "kzg1JYJSGS",
			// 	"type": "kzg1JYJSGS",
			// 	"position": "749.189 538.737 263.871"
			// },
			{
				name: 'hxgJYJSGS',
				type: 'hxgJYJSGS',
				position: '-56169.3 763.736 -49522.7',
			},
			{
				name: 'guan6JYJSGS',
				type: 'guan6JYJSGS',
				position: '-51646.2 2841.31 -50501',
			},
			{
				name: 'guan5JYJSGS',
				type: 'guan5JYJSGS',
				position: '-47772.5 2841.31 -50501',
			},
			{
				name: 'guan4JYJSGS',
				type: 'guan4JYJSGS',
				position: '-43899.1 2841.31 -50501',
			},
			{
				name: 'guan3JYJSGS',
				type: 'guan3JYJSGS',
				position: '-43899.1 2841.31 -54163.7',
			},
			{
				name: 'guan2JYJSGS',
				type: 'guan2JYJSGS',
				position: '-47772.5 2841.31 -54163.7',
			},
			{
				name: 'guan6JYJSGS',
				type: 'guan6JYJSGS',
				position: '-51646.2 2841.31 -54163.7',
			},
			{
				name: 'gd5JYJSGS',
				type: 'gd5JYJSGS',
				position: '-44913.2 773.661 -47949.1',
			},
			{
				name: 'gd4JYJSGS',
				type: 'gd4JYJSGS',
				position: '-45789.6 2605.45 -47130.7',
			},
			{
				name: 'gd4JYJSGS',
				type: 'gd4JYJSGS',
				position: '-45190.1 2608.31 -47130.7',
			},
			{
				name: 'gd4JYJSGS',
				type: 'gd4JYJSGS',
				position: '-44622.9 2606.8 -47130.7',
			},
			{
				name: 'gd4JYJSGS',
				type: 'gd4JYJSGS',
				position: '-44070.8 2608.7 -47130.7',
			},
			{
				name: 'gd4JYJSGS',
				type: 'gd4JYJSGS',
				position: '-43469.8 2642.93 -47130.7',
			},
			{
				name: 'gd3JYJSGS',
				type: 'gd3JYJSGS',
				position: '-43596 2602.91 -46915.8',
			},
			{
				name: 'gd3JYJSGS',
				type: 'gd3JYJSGS',
				position: '-43979.4 2610.11 -46915.8',
			},
			{
				name: 'gd3JYJSGS',
				type: 'gd3JYJSGS',
				position: '-44506.8 2610.11 -46915.8',
			},
			{
				name: 'gd3JYJSGS',
				type: 'gd3JYJSGS',
				position: '-45087.4 2610.11 -46915.8',
			},
			{
				name: 'gd3JYJSGS',
				type: 'gd3JYJSGS',
				position: '-45651.8 2610.11 -46915.8',
			},
			{
				name: 'gd3JYJSGS',
				type: 'gd3JYJSGS',
				position: '-46224.8 2642.93 -46915.8',
			},
			{
				name: 'gd2JYJSGS',
				type: 'gd2JYJSGS',
				position: '-44274.9 1287.1 -47079.1',
			},
			{
				name: 'gd1JYJSGS',
				type: 'gd1JYJSGS',
				position: '-53159.9 2680.08 -52998.9',
			},
			{
				name: 'djJYJSGS',
				type: 'djJYJSGS',
				position: '-56172.9 752.219 -50389.3',
			},
			{
				name: 'BXGJYJSGS',
				type: 'BXGJYJSGS',
				position: '-51643 4178.74 -54027.8',
			},
			{
				name: 'beng1JYJSGS',
				type: 'beng1JYJSGS',
				position: '-44176 1629.97 -46539.7',
			},
			{
				name: 'beng1JYJSGS',
				type: 'beng1JYJSGS',
				position: '-44731.7 1629.97 -46539.7',
			},
			{
				name: 'beng1JYJSGS',
				type: 'beng1JYJSGS',
				position: '-45276.6 1629.97 -46539.7',
			},
			{
				name: 'beng1JYJSGS',
				type: 'beng1JYJSGS',
				position: '-45868 1629.97 -46539.7',
			},
			{
				name: 'beng1JYJSGS',
				type: 'beng1JYJSGS',
				position: '-43648.4 1629.97 -46539.7',
			},
			{
				name: 'aqckJYJSGS',
				type: 'aqckJYJSGS',
				position: '-51449.3 550.528 -45081.3',
			},
			// 文件四
			{
				name: 'zcLCSGS',
				type: 'zcLCSGS',
				position: '70242.6 3220.38 7696.76',
			},
			{
				name: 'winLCSGS',
				type: 'winLCSGS',
				position: '69874.5 4531.19 7700.88',
			},
			{
				name: 'windowLCSGS',
				type: 'windowLCSGS',
				position: '66192.8 2485.33 7688.08',
			},
			{
				name: 'wallLCSGS',
				type: 'wallLCSGS',
				position: '69298 2482.9 7698.2',
			},
			{
				name: 'sb2LCSGS',
				type: 'sb2LCSGS',
				position: '79672.1 2994.64 9260.97',
			},
			{
				name: 'sb2LCSGS',
				type: 'sb2LCSGS',
				position: '70153.8 2994.64 9260.97',
			},
			{
				name: 'sb2LCSGS',
				type: 'sb2LCSGS',
				position: '60794 2994.64 9260.97',
			},
			{
				name: 'sb2LCSGS',
				type: 'sb2LCSGS',
				position: '60786.2 2994.64 6108.23',
			},
			{
				name: 'sb2LCSGS',
				type: 'sb2LCSGS',
				position: '79688.8 2994.64 6108.23',
			},
			{
				name: 'sb2LCSGS',
				type: 'sb2LCSGS',
				position: '70292.5 2994.64 6108.23',
			},
			{
				name: 'sb1LCSGS',
				type: 'sb1LCSGS',
				position: '64535.4 1507.62 9262.23',
			},
			{
				name: 'sb1LCSGS',
				type: 'sb1LCSGS',
				position: '74125.5 1533.99 9262.23',
			},
			{
				name: 'sb1LCSGS',
				type: 'sb1LCSGS',
				position: '81974.7 1622.27 9262.23',
			},
			{
				name: 'sb1LCSGS',
				type: 'sb1LCSGS',
				position: '64262.7 1493.28 6305.69',
			},
			{
				name: 'sb1LCSGS',
				type: 'sb1LCSGS',
				position: '73780.8 1528.89 6305.69',
			},
			{
				name: 'sb1LCSGS',
				type: 'sb1LCSGS',
				position: '81645.7 1622.27 6305.69',
			},
			{
				name: 'lt4LCSGS',
				type: 'lt4LCSGS',
				position: '68413.5 1713.841 7691.121',
			},
			{
				name: 'lt3LCSGS',
				type: 'lt3LCSGS',
				position: '69658.062 2322.319 8735.822',
			},
			// 			{
			// 				"name": "lt2LCSGS1",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "70669.9 2093.2 7669.89"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS2",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "1679.79 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS3",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "879.79 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS4",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "79.79 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS5",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-720.21 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS6",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-1520.21 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS7",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-2320.21 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS8",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-3120.21 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS9",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "6504.08 -29.9609 -992.566"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS10",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "7304.08 -29.9609 -992.566"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS11",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "8104.08 -29.9609 -992.566"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS12",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "8904.08 -29.9609 -992.566"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS13",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "9704.08 -29.9609 -992.566"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS14",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "10504.1 -29.9609 -992.566"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS15",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "11304.1 -29.9609 -992.566"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS16",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "6504.08 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS17",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "7304.08 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS18",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "8104.08 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS19",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "8904.08 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS20",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "9704.08 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS21",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "10504.1 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS22",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "11304.1 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS23",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-7913.62 -29.9609 -1004.26"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS24",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-8713.62 -29.9609 -1004.26"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS25",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-9513.62 -29.9609 -1004.26"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS26",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-10313.6 -29.9609 -1004.26"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS27",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-11113.6 -29.9609 -1004.26"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS28",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-11913.6 -29.9609 -1004.26"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS29",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-12713.6 -29.9609 -1004.26"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS30",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "3445.9 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS31",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "3411.4 -29.9609 -1005.32"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS32",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-6188.72 -29.9609 -1005.44"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS33",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-6154.23 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS34",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-7913.62 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS35",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-8713.62 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS36",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-9513.62 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS37",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-10313.6 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS38",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-11113.6 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS39",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-11913.6 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS40",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-12713.6 -29.9609 1045.1"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS41",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "1679.79 -29.9609 -1002.61"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS42",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "879.79 -29.9609 -1002.61"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS43",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "79.79 -29.9609 -1002.61"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS44",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-720.21 -29.9609 -1002.61"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS45",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-1520.21 -29.9609 -1002.61"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS46",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-2320.21 -29.9609 -1002.61"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS47",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "-3120.21 -29.9609 -1002.61"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS48",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "11767.4 459.4 -841.744"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS49",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "11767.4 459.4 -1639.56"
			// 			},
			// 			{
			// 				"name": "lt2LCSGS50",
			// 				"type": "lt2LCSGS",
			// showLabelType: 1,
			// 				"position": "11767.4 459.4 1450.94"
			// 			},
			{
				name: 'lt2LCSGS51',
				type: 'lt2LCSGS',
				position: '72349.7 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS52',
				type: 'lt2LCSGS',
				position: '71549.7 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS53',
				type: 'lt2LCSGS',
				position: '70749.7 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS54',
				type: 'lt2LCSGS',
				position: '69949.7 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS55',
				type: 'lt2LCSGS',
				position: '69149.7 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS56',
				type: 'lt2LCSGS',
				position: '68349.7 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS57',
				type: 'lt2LCSGS',
				position: '67549.7 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS58',
				type: 'lt2LCSGS',
				position: '77174 2063.24 6677.32',
			},
			{
				name: 'lt2LCSGS59',
				type: 'lt2LCSGS',
				position: '77974 2063.24 6677.32',
			},
			{
				name: 'lt2LCSGS60',
				type: 'lt2LCSGS',
				position: '78774 2063.24 6677.32',
			},
			{
				name: 'lt2LCSGS62',
				type: 'lt2LCSGS',
				position: '79574 2063.24 6677.32',
			},
			{
				name: 'lt2LCSGS63',
				type: 'lt2LCSGS',
				position: '80374 2063.24 6677.32',
			},
			{
				name: 'lt2LCSGS64',
				type: 'lt2LCSGS',
				position: '81174 2063.24 6677.32',
			},
			{
				name: 'lt2LCSGS65',
				type: 'lt2LCSGS',
				position: '81974 2063.24 6677.32',
			},
			{
				name: 'lt2LCSGS66',
				type: 'lt2LCSGS',
				position: '77174 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS67',
				type: 'lt2LCSGS',
				position: '77974 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS68',
				type: 'lt2LCSGS',
				position: '78774 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS69',
				type: 'lt2LCSGS',
				position: '79574 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS70',
				type: 'lt2LCSGS',
				position: '80374 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS71',
				type: 'lt2LCSGS',
				position: '81174 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS72',
				type: 'lt2LCSGS',
				position: '81974 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS73',
				type: 'lt2LCSGS',
				position: '62756.3 2063.24 6665.63',
			},
			{
				name: 'lt2LCSGS74',
				type: 'lt2LCSGS',
				position: '61956.3 2063.24 6665.63',
			},
			{
				name: 'lt2LCSGS75',
				type: 'lt2LCSGS',
				position: '61156.3 2063.24 6665.63',
			},
			{
				name: 'lt2LCSGS76',
				type: 'lt2LCSGS',
				position: '60356.3 2063.24 6665.63',
			},
			{
				name: 'lt2LCSGS77',
				type: 'lt2LCSGS',
				position: '59556.3 2063.24 6665.63',
			},
			{
				name: 'lt2LCSGS78',
				type: 'lt2LCSGS',
				position: '58756.3 2063.24 6665.63',
			},
			{
				name: 'lt2LCSGS79',
				type: 'lt2LCSGS',
				position: '57956.3 2063.24 6665.63',
			},
			{
				name: 'lt2LCSGS80',
				type: 'lt2LCSGS',
				position: '74115.8 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS81',
				type: 'lt2LCSGS',
				position: '74081.3 2063.24 6664.57',
			},
			{
				name: 'lt2LCSGS82',
				type: 'lt2LCSGS',
				position: '64481.2 2063.24 6664.45',
			},
			{
				name: 'lt2LCSGS83',
				type: 'lt2LCSGS',
				position: '64515.7 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS84',
				type: 'lt2LCSGS',
				position: '62756.3 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS85',
				type: 'lt2LCSGS',
				position: '61956.3 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS86',
				type: 'lt2LCSGS',
				position: '61156.3 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS87',
				type: 'lt2LCSGS',
				position: '60356.3 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS88',
				type: 'lt2LCSGS',
				position: '59556.3 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS89',
				type: 'lt2LCSGS',
				position: '58756.3 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS90',
				type: 'lt2LCSGS',
				position: '57956.3 2063.24 8714.98',
			},
			{
				name: 'lt2LCSGS91',
				type: 'lt2LCSGS',
				position: '72349.7 2063.24 6667.28',
			},
			{
				name: 'lt2LCSGS92',
				type: 'lt2LCSGS',
				position: '71549.7 2063.24 6667.28',
			},
			{
				name: 'lt2LCSGS93',
				type: 'lt2LCSGS',
				position: '70749.7 2063.24 6667.28',
			},
			{
				name: 'lt2LCSGS94',
				type: 'lt2LCSGS',
				position: '69949.7 2063.24 6667.28',
			},
			{
				name: 'lt2LCSGS95',
				type: 'lt2LCSGS',
				position: '69149.7 2063.24 6667.28',
			},
			{
				name: 'lt2LCSGS96',
				type: 'lt2LCSGS',
				position: '68349.7 2063.24 6667.28',
			},
			{
				name: 'lt2LCSGS97',
				type: 'lt2LCSGS',
				position: '67549.7 2063.24 6667.28',
			},
			{
				name: 'lt2LCSGS98',
				type: 'lt2LCSGS',
				position: '82437.3 2552.6 6828.14',
			},
			{
				name: 'lt2LCSGS99',
				type: 'lt2LCSGS',
				position: '82437.3 2552.6 6030.33',
			},
			{
				name: 'lt2LCSGS100',
				type: 'lt2LCSGS',
				position: '82437.3 2552.6 9120.83',
			},
			{
				name: 'landLCSGS',
				type: 'landLCSGS',
				position: '69991 1269.11 7698.2',
			},
			{
				name: 'kzg2LCSGS',
				type: 'kzg2LCSGS',
				position: '83488.8 3281.09 9445.02',
			},
			{
				name: 'kzg1LCSGS',
				type: 'kzg1LCSGS',
				position: '83526.4 3236.51 8894.2',
			},
			{
				name: 'guandao4LCSGS',
				type: 'guandao4LCSGS',
				position: '69805.7 2768.76 7693.62',
			},
			{
				name: 'guandao3LCSGS',
				type: 'guandao3LCSGS',
				position: '64722.6 1095.91 9332.89',
			},
			{
				name: 'guandao2LCSGS',
				type: 'guandao2LCSGS',
				position: '82165 1246.11 9400.57',
			},
			{
				name: 'guandao1LCSGS',
				type: 'guandao1LCSGS',
				position: '62620.3 1599.22 10150.5',
			},
			{
				name: 'falan1LCSGS',
				type: 'falan1LCSGS',
				position: '79659.8 2651.44 9687.08',
			},
			// 文件五 TODO 还剩一个1.glb不明确
			{
				name: 'sb3WNSGS',
				type: 'sb3WNSGS',
				position: '-10099.4 3011.69 -35966.7',
			},
			{
				name: 'sb2WNSGS',
				type: 'sb2WNSGS',
				position: '-5827.47 924.924 -46917.6',
			},
			{
				name: 'sb1WNSGS',
				type: 'sb1WNSGS',
				position: '-12349.7 4883.73 -39237',
			},
			{
				name: 'sb1WNSGS',
				type: 'sb1WNSGS',
				position: '-8984.99 4883.73 -39237',
			},
			{
				name: 'lt3WNSGS1',
				type: 'lt3WNSGS',
				position: '-16037 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS2',
				type: 'lt3WNSGS',
				position: '-14252.8 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS3',
				type: 'lt3WNSGS',
				position: '-15147.3 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS4',
				type: 'lt3WNSGS',
				position: '-13361 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS5',
				type: 'lt3WNSGS',
				position: '-12466.5 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS6',
				type: 'lt3WNSGS',
				position: '-7094.79 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS7',
				type: 'lt3WNSGS',
				position: '-7989.26 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS8',
				type: 'lt3WNSGS',
				position: '-9775.55 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS9',
				type: 'lt3WNSGS',
				position: '-8881.08 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS11',
				type: 'lt3WNSGS',
				position: '-10665.3 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS12',
				type: 'lt3WNSGS',
				position: '-11559.8 4139.74 -43368.4',
			},
			{
				name: 'lt3WNSGS14',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-6651.19 4139.74 -38455.2',
			},
			{
				name: 'lt3WNSGS16',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-6651.19 4139.74 -39349.7',
			},
			{
				name: 'lt3WNSGS15',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-6651.19 4139.74 -41133.9',
			},
			{
				name: 'lt3WNSGS17',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-6651.19 4139.74 -40239.4',
			},
			{
				name: 'lt3WNSGS18',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-6651.19 4139.74 -42025.7',
			},
			{
				name: 'lt3WNSGS19',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-6651.19 4139.74 -42920.2',
			},
			{
				name: 'lt3WNSGS21',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-3784.21 4139.74 -37555.4',
			},
			{
				name: 'lt3WNSGS22',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-3784.21 4139.74 -36660.9',
			},
			{
				name: 'lt3WNSGS23',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-3784.21 4139.74 -34874.6',
			},
			{
				name: 'lt3WNSGS24',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-3784.21 4139.74 -35769.1',
			},
			{
				name: 'lt3WNSGS25',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-3784.21 4139.74 -33984.9',
			},
			{
				name: 'lt3WNSGS26',
				type: 'lt3WNSGS',
				hasGlbRotateSet: true,
				rotation: new THREE.Vector3(Math.PI / 2, Math.PI, 0),
				position: '-3784.21 4139.74 -33090.4',
			},
			{
				name: 'lt2WNSGS',
				type: 'lt2WNSGS',
				position: '-10355.7 2544.09 -40687.4',
			},
			{
				name: 'beng1WNSGS',
				type: 'beng1WNSGS',
				position: '-13782.4 1170.58 -46044.4',
			},
			{
				name: 'beng1WNSGS',
				type: 'beng1WNSGS',
				position: '-15108.9 1170.58 -46044.4',
			},
			{
				name: '2FWNSGS',
				type: '2FWNSGS',
				position: '-10306.6 3635.34 -38120.5',
			},
			{
				name: '1FWNSGS',
				type: '1FWNSGS',
				position: '-7903.5 444.263 -40168.6',
			},
		]
		return this.dealPositionData({ allArr: arr })
	},
	// 石鼓山管道模型
	getObjDataSGSGDSCGLB() {
		let arr = [
			{
				name: 'wgSGSWZ',
				type: 'wgSGSWZ',
				position: '-49554.3 -80543.7 47688.6',
			},
			{
				name: 'landSGSWZ',
				type: 'landSGSWZ',
				position: '-49554.3 -80543.7 47688.6',
			},
			{
				name: 'bulid3-whiteSGSWZ',
				type: 'bulid3-whiteSGSWZ',
				position: '-9403.5 318.515 -44193.6',
			},
			{
				name: 'bulid2-whiteSGSWZ',
				type: 'bulid2-whiteSGSWZ',
				position: '77118.1 1288.32 48335.7',
			},
			{
				name: 'bulid1-whiteSGSWZ',
				type: 'bulid1-whiteSGSWZ',
				position: '171614 6214.72 -4371.69',
			},
			{
				name: 'build10-whiteSGSWZ',
				type: 'build10-whiteSGSWZ',
				position: '-145161 232.147 35995.8',
			},
			{
				name: 'build9-whiteSGSWZ',
				type: 'build9-whiteSGSWZ',
				position: '8148.16 1342.31 -2181.04',
			},
			{
				name: 'build8-whiteSGSWZ',
				type: 'build8-whiteSGSWZ',
				position: '27176.4 2823.09 -56494.3',
			},
			{
				name: 'build7-whiteSGSWZ',
				type: 'build7-whiteSGSWZ',
				position: '108119 2019.94 -42889.2',
			},
			{
				name: 'Build6-whiteSGSWZ',
				type: 'Build6-whiteSGSWZ',
				position: '-80963.9 1245.19 -45102.3',
			},
			{
				name: 'build5-whiteSGSWZ',
				type: 'build5-whiteSGSWZ',
				position: '69298 2482.9 7698.2',
			},
			{
				name: 'build4-whiteSGSWZ',
				type: 'build4-whiteSGSWZ',
				position: '222086 0 20844.1',
			},
			{
				name: '10BIM',
				type: '10BIM',
				position: '78207.1 560.136 22494',
			},
			{
				name: '11BIM',
				type: '11BIM',
				position: '58545.3 1889.65 12364.5',
			},
			{
				name: '1BIM',
				type: '1BIM',
				position: '-35368.8 -65087.4 103453',
			},
			{
				name: '2BIM',
				type: '2BIM',
				position: '53670.2 992.976 26002.7',
			},
			{
				name: '3BIM',
				type: '3BIM',
				position: '48475.9 664.225 18690.7',
			},
			{
				name: '4BIM',
				type: '4BIM',
				position: '45711.7 -83.8522 -6274.42',
			},
			{
				name: '5BIM',
				type: '5BIM',
				position: '13507.5 467.627 -23181.4',
			},
			{
				name: '6BIM',
				type: '6BIM',
				position: '-53185.6 376.811 -32036.9',
			},
			{
				name: '7BIM',
				type: '7BIM',
				position: '-39271 234.962 -10925.8',
			},
			{
				name: '8BIM',
				type: '8BIM',
				position: '32543.8 -65420.3 85992.4',
			},
			{
				name: '9BIM',
				type: '9BIM',
				position: '33740 -66156 81261.2',
			},
		]
		return this.dealPositionData({ allArr: arr })
	},
	/**
	 * 以下为管线和相机数据
	 * */

	// 石鼓山 （位置）
	getGuandaoDataSGSSCglb() {
		const startPosition = new THREE.Vector3(3690.27230961493194, 6750.2620128344506, 2250.67240086368264)

		const endPosition = new THREE.Vector3(207762.74018307112, 160703.58211298837, 100531.44306837667)
		const endTarget = new THREE.Vector3(10000, 50000, 0)
		const startTarget = new THREE.Vector3(-4001.24, -2062.8, 362.091)
		return {
			// points,
			endPosition,
			endTarget,
			startPosition,
			startTarget,
			// guanDaoRadius,
			processFlowDiagramName: '石鼓山水厂',
		}
	},

	//老合台三维场景（相机起始位置）
	getPositionLHTSWMX() {
		const endPosition = new THREE.Vector3(5785.143735107162, 4069.4392351071624, 6468.274735107162)
		const endTarget = new THREE.Vector3(-423.9050000000002, -2139.6095, 259.226)
		const startPosition = new THREE.Vector3(12244.388743637803, 10528.684243637803, 12927.519743637804)
		const startTarget = new THREE.Vector3(-423.9050000000002, -2139.6095, 259.226)
		return { endPosition, endTarget, startPosition, startTarget }
	},
	//CBD泵站工艺（相机起始位置）
	getPositionCBDBZ() {
		const endPosition = new THREE.Vector3(431.1637, 480.2046, 600.0119)
		const endTarget = new THREE.Vector3(-62.4064, -13.3655, 106.4418)
		const startPosition = new THREE.Vector3(106.4418, 2128.7417, 2248.549)
		const startTarget = new THREE.Vector3(-62.4064, -13.3655, 106.4418)
		return { endPosition, endTarget, startPosition, startTarget }
	},
	//汝城加药间（相机起始位置）
	getGuandaoDataJYJglb() {
		const endPosition = new THREE.Vector3(109.80274025368307, 114.93694025368308, 207.83359025368307)
		const endTarget = new THREE.Vector3(-24.6093, -19.4751, 73.42155)
		const startPosition = new THREE.Vector3(434.23075828747335, 439.36495828747337, 532.2616082874733)
		const startTarget = new THREE.Vector3(-24.6093, -19.4751, 73.42155)
		return {
			endPosition,
			endTarget,
			startPosition,
			startTarget,
		}
	},
	//汝城泵房（相机起始位置）
	getGuandaoDataRCBFglb() {
		const endPosition = new THREE.Vector3(512.7311026415366, 650.1748026415365, 671.5575026415365)
		const endTarget = new THREE.Vector3(-20.802699999999987, 116.64099999999999, 138.0237)
		const startPosition = new THREE.Vector3(2204.993767768713, 2342.4374677687133, 2363.8201677687134)
		const startTarget = new THREE.Vector3(-20.802699999999987, 116.64099999999999, 138.0237)
		return {
			endPosition,
			endTarget,
			startPosition,
			startTarget,
		}
	},
	//二台子水厂沙盘 （相机起始位置）
	getGuandaoDataEHTglb() {
		const guanDaoRadius = {
			pointsRadius: 75,
		}
		let points = []

		points.push([
			this.getRealVector3('-5100.19 148.139 -1173.54'),
			this.getRealVector3('-5128.32 148.139 -671.302'),
			this.getRealVector3('-10426.8 148.139 -671.302'),
		])
		points.push([
			this.getRealVector3('-2319.91 148.139 -6250.53'),
			this.getRealVector3('-2319.91 148.139 -5885.36'),
			this.getRealVector3('-5100.19 148.139 -5885.36'),
			this.getRealVector3('-5100.19 148.139 -4911.71'),
		])
		points.push([
			this.getRealVector3('-4677.38 188.325 -10880.7'),
			this.getRealVector3('-4642.64 188.325 -10643.7'),
			this.getRealVector3('-1167.21 188.325 -10643.7'),
			this.getRealVector3('-1167.21 188.325 -15253.1'),
			this.getRealVector3('-218.575 188.325 -15253.1'),
		])
		points.push([
			this.getRealVector3('-6087.46 506.249 -15948.9'),
			this.getRealVector3('-7813.78 506.249 -15948.9'),
			this.getRealVector3('-7813.78 506.249 -7973.82'),
			this.getRealVector3('-7295.68 506.249 -7956.01'),
		])
		points.push([
			this.getRealVector3('-10758.4 649.192 -23211.9'),
			this.getRealVector3('-10758.4 649.192 -21277.8'),
			this.getRealVector3('-4610.56 649.192 -21261.9'),
		])
		points.push([
			this.getRealVector3('-3915.12 649.192 -23211.9'),
			this.getRealVector3('-3915.12 649.192 -22299.8'),
			this.getRealVector3('-4610.56 649.192 -22299.8'),
			this.getRealVector3('-4610.56 649.192 -20766.8'),
			this.getRealVector3('-4610.56 649.192 -21261.9'),
		])
		points.push([
			this.getRealVector3('-3873.47 901.189 -26907.2'),
			this.getRealVector3('-3873.47 901.189 -24909.2'),
		])

		points.push([
			this.getRealVector3('7777.711 663.901 -20873.366'),
			this.getRealVector3('10119.917 663.901 -20873.366'),
		])
		// const endPosition = new THREE.Vector3(
		//	 6777.590154881038,
		//	 17269.144654881038,
		//	 19894.47985488104
		// );
		// const endTarget = new THREE.Vector3(
		//	 -12571.3745,
		//	 -2079.82,
		//	 545.5151999999999
		// );
		const endPosition = new THREE.Vector3(
			// 6777.590154881038,
			// 17269.144654881038,
			// 29004.47985488104 // 设置是否面向自己
			10777.590154881038,
			17269.144654881038,
			19894.47985488104,
		)
		const endTarget = new THREE.Vector3(
			// -9571.3745,
			// 6079.82,
			// 545.5151999999999
			-12571.3745,
			179.82,
			545.5151999999999,
		)
		const startPosition = new THREE.Vector3(26722.24271466532, 37213.79721466532, 39839.13241466532)
		const startTarget = new THREE.Vector3(-12571.3745, -2079.82, 545.5151999999999)
		return {
			points,
			endPosition,
			endTarget,
			startPosition,
			startTarget,
			guanDaoRadius,
			processFlowDiagramName: '水务水厂测试1',
		}
	},
	//状元 （相机起始位置）
	getGuandaoDataZYSCglb() {
		const startPosition = new THREE.Vector3(3690.27230961493194, 6750.2620128344506, 2250.67240086368264)
		const endPosition = new THREE.Vector3(540.3792439993382, 427.4858803912922, 232.3268517799147)
		const endTarget = new THREE.Vector3(-200, 200, -100)
		const startTarget = new THREE.Vector3(-4001.24, -2062.8, 362.091)
		return {
			// points,
			endPosition,
			endTarget,
			startPosition,
			startTarget,
			// guanDaoRadius,
			processFlowDiagramName: '状元水厂',
		}
	},
	//苍南天信
	getGuandaoDataCNTXglb() {
		const endPosition = new THREE.Vector3(
			344.7590154881038,
			391.9144654881038,
			402.447985488104,
			// 6777.590154881038,
			// 17269.144654881038,
			// 19894.47985488104
		)
		const endTarget = new THREE.Vector3(-136.758, 29.651, 112.6)
		const startPosition = new THREE.Vector3(26722.24271466532, 37213.79721466532, 39839.13241466532)
		const startTarget = new THREE.Vector3(311.51873293109793, 409.57008689244503, 458.716)
		return {
			endPosition,
			endTarget,
			startPosition,
			startTarget,
			processFlowDiagramName: '苍南天信',
		}
	},
	getGuandaoDataSGWFYglb() {
		const endPosition = new THREE.Vector3(344.7590154881038, 391.9144654881038, 402.447985488104)
		const endTarget = new THREE.Vector3(0, 0, 0)
		const startPosition = new THREE.Vector3(26722.24271466532, 37213.79721466532, 39839.13241466532)
		const startTarget = new THREE.Vector3(311.51873293109793, 409.57008689244503, 458.716)
		return {
			endPosition,
			endTarget,
			startPosition,
			startTarget,
			processFlowDiagramName: '苍南天信',
			// factor:2
		}
	},
	//章山水厂沙盘 （相机起始位置）
	getGuandaoDataZSglb() {
		const endPosition = new THREE.Vector3(
			// 6777.590154881038,
			// 17269.144654881038,
			// 29004.47985488104 // 设置是否面向自己
			11777.590154881038,
			269.144654881038,
			6894.47985488104,
		)
		const endTarget = new THREE.Vector3(
			// -9571.3745,
			// 6079.82,
			// 545.5151999999999
			-3620.47,
			4040.91,
			1608.33,
		)
		const startPosition = new THREE.Vector3(26722.24271466532, 37213.79721466532, 39839.13241466532)
		const startTarget = new THREE.Vector3(-12571.3745, -2079.82, 545.5151999999999)
		return {
			endPosition,
			endTarget,
			startPosition,
			startTarget,
			processFlowDiagramName: '章山',
			factor: 2,
		}
	},
	//章山水厂沙盘 （相机起始位置）
	getGuandaoDataZSALARMglb() {
		const endPosition = new THREE.Vector3(
			// 6777.590154881038,
			// 17269.144654881038,
			// 29004.47985488104 // 设置是否面向自己
			11777.590154881038,
			269.144654881038,
			6894.47985488104,
		)
		const endTarget = new THREE.Vector3(
			// -9571.3745,
			// 6079.82,
			// 545.5151999999999
			// -3620.47,
			// 4040.91,
			// 1608.33
			-5621.86,
			39.796,
			2515.48,
		)
		const startPosition = new THREE.Vector3(26722.24271466532, 37213.79721466532, 39839.13241466532)
		const startTarget = new THREE.Vector3(-12571.3745, -2079.82, 545.5151999999999)
		return {
			endPosition,
			endTarget,
			startPosition,
			startTarget,
			processFlowDiagramName: '章山',
			factor: 2,
		}
	},

	getRealVector3(str) {
		let pointString = str.split(' ')
		return new THREE.Vector3(Number(pointString[2]), Number(pointString[0]), Number(pointString[1]))
	},
	//高新区水厂
	getPositionGXQSC() {
		const endPosition = new THREE.Vector3(14776.632112600699, 17692.319112600697, 12225.532112600698)
		const endTarget = new THREE.Vector3(3033.7010000000005, 5949.388, 482.601)
		const startPosition = new THREE.Vector3(17692.319112600697, 8709.230112600697, 12225.532112600698)
		const startTarget = new THREE.Vector3(949.388, -3033.7010000000005, 482.601)
		return {
			endPosition,
			endTarget,
			startPosition,
			startTarget,
		}
	},
	//玉皇阁水厂沙盘
	getPositionYHGGLB() {
		const endPosition = new THREE.Vector3(30573.875054838405, 27150.675054838404, 30482.364987838402)
		const endTarget = new THREE.Vector3(868.1499999999996, -2555.0499999999993, 776.6399329999999)
		const startPosition = new THREE.Vector3(62821.94829403007, 59398.74829403007, 62730.43822703007)
		const startTarget = new THREE.Vector3(868.1499999999996, -2555.0499999999993, 776.6399329999999)
		return {
			endPosition,
			endTarget,
			startPosition,
			startTarget,
		}
	},
	// 组装管道数据
	getGuandaoData({ processFlowDiagramName = '保定荣投水厂' }) {
		const startPosition = new THREE.Vector3(-24834.215461582557, -20166.460103801546, 13912.120846877859)
		const endPosition = new THREE.Vector3(-7540.573572273142, 4551.8673764003015, 3928.3764955086176)
		new THREE.Vector3(4001.24, -2062.8, 362.091)
		const startTarget = new THREE.Vector3(-4001.24, -2062.8, 362.091)
		const endTarget = new THREE.Vector3(0, 0, 0)
		return {
			// points,
			endPosition,
			endTarget,
			startPosition,
			startTarget,
			// guanDaoRadius,
			processFlowDiagramName: processFlowDiagramName,
		}
	},
}
