<!--
 * @Description: 人员、组织树选择器
 * @Author: fengjialing
 * @Date: 2025-04-15 19:18:01
 * @LastEditors: fengjialing
 * @LastEditTime: 2025-04-15 19:18:01
-->

<template>
	<div class="personnel-config-select">
		<div>
			<WaterTree
				:search="false"
				ref="WaterTreeRef"
				:treeData="roleList"
				@change-select="handleChangeSelect"
			></WaterTree>
		</div>
	</div>
</template>

<script>
import WaterTree from '@/components/gc-multiple-tree'
import { apiGetRole } from '@/api/base-item.js'
export default {
	name: 'personnel-config-select',
	components: {
		WaterTree,
	},
	props: {
		selectedValue: {
			type: Array,
			default: () => [],
		},
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			currentNode: {},
			ownershipList: [],
			usersList: [],
			roleList: [],
			selectList: [],
			urlList: '',
			firstTree: '',
			type: 'person',
			treeData: [],
		}
	},
	computed: {},
	created() {
		this._getRole()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 选中树形图节点
		changeTree(selectedNodes, currentNode) {
			this.page.pageNum = 1
			this.currentNode = currentNode

			this._getUsers()
		},
		// 过滤
		_filterUsers(list) {
			if (!list || !list.length) return []

			return list.map(item => {
				return {
					...item,
					title: item.name,
					expand: true,
					selected: item.id === this.firstTree,
					children: this._filterUsers(item.nodes),
				}
			})
		},
		clearSelectData() {
			this.$refs.multifunctionalSelect.clearSelectValue()
		},
		_getRole() {
			// const params = {}
			apiGetRole().then(res => {
				this.roleList = res?.result?.map(item => {
					const isChecked = this.selectedValue.includes(String(item.roleId))
					return {
						title: item.name,
						value: item.roleId,
						checked: isChecked,
						disabled: this.disabled,
					}
				})
			})
		},
		// 更新树
		updateTree(showType) {
			if (showType === this.type || ['role'].includes(showType)) {
				this._getRole()
			}
			this.$refs?.WaterTreeRef?.clearSearchValue()
			this.type = showType
		},
		handleChangeSelect(data) {
			this.selectList = data
			this.$emit('change-role-select', data)
		},
	},
}
</script>

<style lang="less" scoped>
.personnel-config-select {
	height: 100%;
}
</style>
