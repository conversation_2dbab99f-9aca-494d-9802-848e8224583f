<template>
	<!--数据绑定-->
	<Modal :value="show" :mask-closable="false" width="650" @on-cancel="handleCancel" title="模型属性配置">
		<Form ref="tab3EditInfo" :model="tab3EditInfo" :rules="ruleValidate" :label-width="110">
			<Row>
				<Col span="11">
					<Form-item label="模型名称" prop="modelName">
						<Input clearable :disabled="editModel" v-model="tab3EditInfo.modelName" />
					</Form-item>
				</Col>
				<Col span="2" style="text-align: center"></Col>
				<Col span="11">
					<Form-item label="模型URL(勿动)" prop="mail">
						<Input clearable disabled v-model="tab3EditInfo.modelUrl" />
					</Form-item>
				</Col>
			</Row>
			<Row>
				<Col span="11">
					<Form-item label="缩放比例" prop="modelName">
						<Input clearable disabled v-model="tab3EditInfo.labelScale" />
					</Form-item>
				</Col>
				<Col span="2" style="text-align: center"></Col>
				<Col span="11">
					<Form-item label="渲染类型" prop="mail">
						<Select
							v-model="tab3EditInfo.renderType"
							clearable
							transfer
							placeholder=""
							:disabled="glbEditModel ? glbEditModel : editModel"
						>
							<Option v-for="item in tab3EditType" :value="item.value" :key="item.value">
								{{ item.label }}
							</Option>
						</Select>
					</Form-item>
				</Col>
			</Row>
			<Row>
				<Col span="11">
					<Form-item label="标签展示" prop="modelName">
						<Select transfer v-model="tab3EditInfo.showLabelType" :disabled="editModel">
							<Option v-for="item in showLabelTypeList" :value="item.value" :key="item.value">
								{{ item.label }}
							</Option>
						</Select>
					</Form-item>
				</Col>
				<Col span="2" style="text-align: center"></Col>
				<!-- <Col span="11">
					<Form-item label="点击变色" prop="mail">
						<Input
							clearable
							:disabled="glbEditModel ? glbEditModel : editModel"
							v-model="tab3EditInfo.clickColor"
							style="width: 103px"
						/>
						<ColorPicker
							v-model="tab3EditInfo.clickColor"
							:disabled="glbEditModel ? glbEditModel : editModel"
						/>
					</Form-item>
				</Col> -->
			</Row>
			<!-- <Row>
				<Col span="11">
					<Form-item label="模型透明度" prop="modelName">
						<Input
							clearable
							:disabled="glbEditModel ? glbEditModel : editModel"
							v-model="tab3EditInfo.clickOpacity"
							style="width: 160px"
						/>
					</Form-item>
				</Col>
				<Col span="2" style="text-align: center"></Col>
				<Col span="11">
					<Form-item label="模型点击跳转工艺流程" prop="mail">
						<Input
							v-model="tab3EditInfo.nextDiagramId"
							:disabled="editModel"
						/>
						<Select
							v-model="tab3EditInfo.nextDiagramId"
							clearable
							transfer
							:disabled="editModel"
							placeholder=""
						>
							<Option
								v-for="(item, i) in tab3EditTo"
								:value="item.processId"
								:key="i"
							>
								{{ item.stationProcessName }}
							</Option>
						</Select>
					</Form-item>
				</Col>
			</Row> -->
			<Row>
				<Col span="11">
					<Form-item label="是否是管道模型" prop="modelName">
						<Select v-model="tab3EditInfo.isPipe" clearable transfer :disabled="editModel" placeholder="">
							<Option v-for="(item, i) in pipeOption" :value="item.value" :key="i">
								{{ item.label }}
							</Option>
						</Select>
					</Form-item>
				</Col>
				<Col span="2" style="text-align: center"></Col>
				<Col span="11" v-show="tab3EditInfo.isPipe == 1">
					<Form-item label="管道名称" prop="mail">
						<Input
							v-model="tab3EditInfo.pipeName"
							:disabled="editModel"
							placeholder="请输入管道名称"
						></Input>
					</Form-item>
				</Col>
			</Row>
			<Row v-show="tab3EditInfo.isPipe == 1">
				<Col span="11">
					<Form-item label="管径" prop="modelName">
						<Input
							v-model="tab3EditInfo.pipeDiameter"
							:disabled="editModel"
							placeholder="请输入管径"
						></Input>
					</Form-item>
				</Col>
				<Col span="2" style="text-align: center"></Col>
				<Col span="11">
					<Form-item label="管材" prop="mail">
						<Input
							v-model="tab3EditInfo.pipeMaterial"
							:disabled="editModel"
							placeholder="请输入管材"
						></Input>
					</Form-item>
				</Col>
			</Row>
			<Row>
				<Col span="11" v-show="tab3EditInfo.isPipe == 1">
					<Form-item label="管龄" prop="modelName">
						<Input
							v-model="tab3EditInfo.pipeAge"
							style="width: 160px"
							:disabled="editModel"
							placeholder="请输入管龄"
						></Input>
					</Form-item>
				</Col>
				<Col span="2" style="text-align: center"></Col>
				<Col span="11" v-show="tab3EditInfo.showLabelType === 6">
					<Form-item label="视频标识" prop="mail">
						<Input :disabled="editModel" clearable v-model="tab3EditInfo.cameraCode" />
					</Form-item>
				</Col>
			</Row>
			<Row>
				<Col span="11">
					<Form-item label="Y" prop="positionY">
						<Input
							v-model="tab3EditInfo.positionY"
							style="width: 160px"
							disabled
							placeholder="请输入管龄"
						></Input>
					</Form-item>
				</Col>
				<Col span="2" style="text-align: center"></Col>
				<Col span="11">
					<Form-item label="Z" prop="positionZ">
						<Input
							v-model="tab3EditInfo.positionZ"
							style="width: 160px"
							disabled
							placeholder="请输入管龄"
						></Input>
					</Form-item>
				</Col>
			</Row>
			<Row>
				<Col span="11">
					<Form-item label="X" prop="positionX">
						<Input disabled clearable v-model="tab3EditInfo.positionX" />
					</Form-item>
				</Col>
				<Col span="2" style="text-align: center"></Col>
				<Col span="11">
					<Form-item label="原始坐标" prop="originalPosition">
						<Input disabled clearable v-model="tab3EditInfo.originalPosition" />
					</Form-item>
				</Col>
			</Row>

			<!-- <Form-item>
				<div slot="btn" v-if="editModel">
					<Button type="primary" @click="editModel = false">
						编辑
					</Button>
				</div>
				<div slot="btn" v-else>
					<Button @click="modelThreeQx">取消</Button>
					<Button type="primary" @click="tab3EditFun">保存</Button>
				</div>
			</Form-item> -->
		</Form>
		<div slot="footer">
			<div slot="btn" v-if="editModel">
				<Button type="primary" @click="editModel = false">编辑</Button>
			</div>
			<div slot="btn" v-else>
				<!-- <Button @click="modelThreeQx">取消</Button> -->
				<Button type="primary" @click="tab3EditFun">保存</Button>
			</div>
		</div>
	</Modal>
</template>

<script>
import { getStationOtherDiagram, labelSiteSaveModel } from '@/api/setting'

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '新增',
		},
	},
	computed: {
		stationListLength() {
			return this.pointList.length
		},
	},
	mounted() {},
	data() {
		return {
			tab3EditInfo: {
				modelId: '',
				modelName: '',
				modelUrl: '',
				labelScale: '',
				stationId: '',
				renderType: '',
				showLabelType: '',
				clickColor: '',
				clickOpacity: '',
				nextDiagramId: '',
				cameraCode: '',
				isPipe: '',
				positionZ: '',
				positionX: '',
				positionY: '',
			},
			ruleValidate: {},
			editModel: true,
			glbEditModel: false,
			tab3EditType: [
				{
					value: 'edges',
					label: 'edges',
				},
				{
					value: 'normal',
					label: 'normal',
				},
			],
			pipeOption: [
				{
					value: '1',
					label: '是',
				},
				{
					value: '0',
					label: '否',
				},
			],
			showLabelTypeList: [
				{
					value: 0,
					label: '都不展示',
				},
				{
					value: 1,
					label: '默认展示',
				},
				{
					value: 2,
					label: '点击展示',
				},
				{
					value: 5,
					label: '展示风叶',
				},
				{
					value: 6,
					label: '展示摄像头',
				},
			],
			tab3EditTo: [],
			property: {},
			currentRow: {},
		}
	},
	methods: {
		init(e) {
			this.currentRow = e
			this.glbEditModel = e.modelType == 'glb' ? true : false
			let property = JSON.parse(e.property)
			this.property = property
			this.tab3QxInfo = JSON.stringify(e)
			this.editModel = true
			this.tab3EditInfo.originalPosition = ''
			this.tab3EditInfo.modelId = e.modelId
			this.tab3EditInfo.modelName = e.modelName
			this.tab3EditInfo.modelUrl = property.modelUrl
			this.tab3EditInfo.labelScale = property.labelScale
			this.tab3EditInfo.stationId = e.stationId
			this.tab3EditInfo.renderType = e.renderType
			this.tab3EditInfo.showLabelType = property.showLabelType - 0
			this.tab3EditInfo.isPipe = property.isPipe
			this.tab3EditInfo.clickColor = property.clickColor || ''
			this.tab3EditInfo.clickOpacity = property.clickOpacity
			this.tab3EditInfo.pipeDiameter = property.pipeDiameter
			this.tab3EditInfo.pipeName = property.pipeName
			this.tab3EditInfo.pipeMaterial = property.pipeMaterial
			this.tab3EditInfo.pipeAge = property.pipeAge
			this.tab3EditInfo.nextDiagramId = property.nextDiagramId
			this.tab3EditInfo.cameraCode = property.cameraCode
			this.tab3EditInfo.positionZ = property.positionZ
			this.tab3EditInfo.positionX = property.positionX
			this.tab3EditInfo.positionY = property.positionY
			this.tab3EditInfo.applicationName = e.applicationName

			// await this.getProcessList(e);
			// this.$refs.modalConf.modal = true
			console.log(this.tab3EditTo)
			console.log('1111', this.tab3EditInfo.nextDiagramId)
			console.log(this.tab3EditTo)
			this.getStationOtherDiagram(e)
		},
		viewModel(row) {
			this.$refs.previewRef.viewModel(row)
		},
		// 取消设置
		modelThreeQx() {
			console.log(this.tab3EditInfo)
			this.editModel = true
			this.tab3EditInfo = JSON.parse(this.tab3QxInfo)
			this.tab3EditInfo.nextDiagramId = this.tab3EditInfo.id
			if (!this.tab3EditInfo.clickColor) this.tab3EditInfo.clickColor = ''
		},

		getStationOtherDiagram(row) {
			// debugger
			// console.log(row)
			const { applicationName, diagramId, stationId } = row
			getStationOtherDiagram({
				applicationName: applicationName,
				diagramId: diagramId,
				stationId: stationId,
			}).then(res => {
				// console.log(res)
				this.tab3EditTo = res.result
			})
		},

		// 获取折线的点集合
		getPoints(line) {
			let arr = []
			line.points.forEach(item => {
				arr.push(item.x + ',' + item.y)
			})
			return arr.join(' ')
		},

		tab3EditFun() {
			let {
				cameraCode,
				modelId,
				modelName,
				modelUrl,
				labelScale,
				stationId,
				renderType,
				showLabelType,
				isPipe,
				clickColor,
				clickOpacity,
				pipeDiameter,
				pipeName,
				pipeMaterial,
				pipeAge,
				applicationName,
				nextDiagramId,
				positionX,
				positionY,
				positionZ,
			} = this.tab3EditInfo
			// debugger
			if (labelScale && !/^(\d|[1-9]\d+)(\.\d+)?$/.test(labelScale)) {
				this.$Message.warning('缩放比例必须为大于0的数')
				return
			}
			if (modelName === '') {
				this.$Message.warning('模型名称不能为空！')
				return
			}
			if (clickOpacity && (!/^(\d|[1-9]\d+)(\.\d+)?$/.test(clickOpacity) || clickOpacity > 1)) {
				this.$Message.warning('模型透明度必须大于0小于1')
				return
			}

			let paramPro = {
				...this.property,
				modelUrl,
				labelScale,
				renderType,
				showLabelType,
				isPipe,
				clickOpacity,
				modelId,
				pipeName,
				pipeDiameter,
				pipeMaterial,
				pipeAge,
				clickColor,
				cameraCode,
				nextDiagramId,
				positionX: positionX - 0,
				positionY: positionY - 0,
				positionZ: positionZ - 0,
				processType: this.tabsName === 'name3' ? 2 : 1,
			}

			// let pointString = ''
			// if (this.tab3EditInfo.originalPosition) {
			// 	pointString = this.tab3EditInfo.originalPosition.split(' ')
			// 	paramPro.positionX = pointString && Number(pointString[2])
			// 	paramPro.positionY = pointString && Number(pointString[0])
			// 	paramPro.positionZ = pointString && Number(pointString[1])
			// }

			let params = {
				applicationName,
				modelId,
				stationId,
				modelName,
				property: JSON.stringify(paramPro),
			}
			this.tab3EditTo.forEach(i => {
				if (i.processId === this.tab3EditInfo.nextDiagramId) {
					params.nextDiagramId = i.processId
					if (this.tabsName === 'name4') {
						params.stationId = i.stationId
					}
				}
			})
			labelSiteSaveModel(params).then(() => {
				// this.$emit(
				// 	'successCallBack',
				// 	'dataLabelConfig',
				// 	this.currentRow,
				// )
				// debugger
				this.$emit('successCallBack', 'editorMeaasg', this.tab3EditInfo)
				this.$emit('update:show', false)
				this.$Message.success('保存成功！')
			})
			// this.$axios
			// 	.post(
			// 		'/api/iwater/label/site/saveModel',
			// 		this.qs.stringify(params),
			// 	)
			// 	.then(res => {

			// 	})
		},

		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},
	},
}
</script>
<style lang="less" scoped>
.flex {
	display: flex;
	justify-content: space-around;
}
</style>
