/*
 * @Descripttion:
 * @version:
 * @Author: heliping
 * @Date: 2022-03-03 14:22:29
 * @LastEditors: shenxh
 * @LastEditTime: 2024-09-18 11:25:43
 */
const path = require('path')
// const TerserPlugin = require('terser-webpack-plugin')
const CompressionWebpackPlugin = require('compression-webpack-plugin')
// 定义压缩文件类型
// const productionGzipExtensions = ['js', 'css']
const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i

function resolve(dir) {
	return path.join(__dirname, dir)
}

module.exports = {
	outputDir: process.env.OUTPUT_DIR,
	// chainWebpack: config => {
	// 	// 删除 prefetch 插件
	// 	config.plugins.delete('prefetch')
	// },
	// 去除打包映射
	productionSourceMap: false,
	css: {
		extract: false,
		loaderOptions: {
			scss: {
				additionalData(content, loaderContext) {
					const { resourcePath, rootContext } = loaderContext
					const relativePath = path.relative(rootContext, resourcePath)
					if (relativePath.replace(/\\/g, '/') !== 'src/styles/variables/variables.scss')
						return '@import "~@/styles/variables/variables.scss";' + content
					return content
				},
			},
		},
	},
	transpileDependencies: ['@eslink/esvcp-pc-ui/packages'], // 解析jsx
	devServer: {
		proxy: {
			'/alarm': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api', // etbc新测试环境
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				// target: 'http://10.8.37.131:8081', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/role': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/waterPlat': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				// target: 'http://10.8.37.131:8081',
				changeOrigin: true,
			},
			'/sms': {
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/monitor': {
				target: 'http://gccpiot.jize.cn:32080/', // 鸡泽测试环境
				// target: 'http://10.8.37.131:8081',
				changeOrigin: true,
			},
			'/device': {
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				// target: 'http://10.8.37.131:8081',
				changeOrigin: true,
			},
			'/transRule': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				// target: 'http://10.8.37.78:8081',
				changeOrigin: true,
			},
			'/processFlowChart': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				// target: 'http://10.20.9.168:8080',
				changeOrigin: true,
			},
			'/backgroundImage': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				// target: 'http://10.20.9.168:8080',
				changeOrigin: true,
			},
			'/influxDb': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/stationItemDataFix': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/security/PlatDic': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/security/platform': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/security/area': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/security/devicebinded': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/security/thirdplat': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				// target: 'http://10.20.9.170:8080',
				changeOrigin: true,
			},
			'/iwater': {
				target: 'http://yq-water-digital-pc.k8s-qa.eslink.net.cn/api',
				changeOrigin: true,
			},
			'/ssy/screen': {
				target: 'http://secondsupply-pc.kaifa.eslink.net.cn/api',
				changeOrigin: true,
			},
			'/screen': {
				target: 'http://water-plat.test.eslink.net.cn/proxy/dp',
				changeOrigin: true,
			},
			// '/screen/common': {
			// 	target: 'http://water-plat.test.eslink.net.cn/api',
			// 	target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
			// target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
			// 	changeOrigin: true,
			// },
			'/villageWater': {
				target: 'http://water-plat.test.eslink.net.cn/proxy/dc',
				changeOrigin: true,
			},
			'/pos': {
				// target: 'http://water-digital.hw-qa.eslink.net.cn',
				target: 'http://water-plat.test.eslink.net.cn/proxy/dd',
				changeOrigin: true,
			},
			'/workorder': {
				// target: 'http://water-digital.hw-qa.eslink.net.cn',
				target: 'http://water-plat.test.eslink.net.cn/proxy/gd',
				changeOrigin: true,
			},
			'/datasource': {
				// target: 'http://water-digital.hw-qa.eslink.net.cn',
				target: 'http://water-plat.test.eslink.net.cn/proxy/gd',
				changeOrigin: true,
			},
			'/ssy': {
				target: 'http://water-plat.test.eslink.net.cn/proxy/eg',
				changeOrigin: true,
			},
			'/sysConfig': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/stationItemDataRepair': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/processAuth': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/smart': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/log': {
				target: 'http://swetbc-system-pc.kaifa.eslink.net.cn',
				changeOrigin: true,
			},
			'/topic': {
				// target: 'http://water-plat.test.eslink.net.cn/api',
				// target: 'http://water-plat.test-new.eslink.net.cn:32080/api',
				target: 'http://water-plat-pc.jize.cn:32080/api', // 鸡泽测试环境
				changeOrigin: true,
			},
			'/dma': {
				target: 'http://water-plat.test.eslink.net.cn/proxy/dma',
				changeOrigin: true,
			},
		},
		host: 'localhost', // can be overwritten by process.env.HOST
		port: 3000,
		disableHostCheck: true,
	},
	configureWebpack: {
		plugins: [
			new CompressionWebpackPlugin({
				filename: '[path][base].gz', //[file] 会被替换成原始资源。[path] 会被替换成原始资源的路径， [query] 会被替换成查询字符串
				algorithm: 'gzip', //压缩成gzip
				//所有匹配该正则的资源都会被处理。默认值是全部资源。
				test: productionGzipExtensions,
				threshold: 10240, //只有大小大于该值的资源会被处理。单位是 bytes。默认值是 0。
				minRatio: 0.8, //只有压缩率小于这个值的资源才会被处理。默认值是 0.8。
				// deleteOriginalAssets: true,
			}),
		],
		resolve: {
			alias: {
				'@': resolve('src'),
				package: resolve('package'),
				plugin: resolve('src/plugin'),
				config: resolve('src/config'),
			},
		},
		// optimization: {
		// 	// 确保不压缩代码
		// 	// minimize: false,
		// 	// minimizer: [
		// 	// 	new TerserPlugin({
		// 	// 		terserOptions: {
		// 	// 			ecma: undefined,
		// 	// 			warnings: false,
		// 	// 			parse: {},
		// 	// 			compress: {
		// 	// 				// drop_console: true,
		// 	// 				drop_debugger: true,
		// 	// 				// pure_funcs: ['console.log'], // 移除console
		// 	// 			},
		// 	// 		},
		// 	// 	}),
		// 	// ],
		// 	splitChunks: {
		// 		chunks: 'all',
		// 		cacheGroups: {
		// 		  vendor: {
		// 			test: /[\\/]node_modules[\\/]/,
		// 			name: 'vendors',
		// 			chunks: 'all',
		// 			enforce: true,
		// 		  },
		// 		},
		// 	}
		// },
		//...
		optimization: {
			splitChunks: {
				chunks: 'all',
				minSize: 20000,
				minChunks: 1,
				maxAsyncRequests: 30,
				maxInitialRequests: 30,
				automaticNameDelimiter: '~',
				cacheGroups: {
					defaultVendors: {
						test: /[\\/]node_modules[\\/]/,
						priority: -10,
						reuseExistingChunk: true,
					},
					default: {
						minChunks: 2,
						priority: -20,
						reuseExistingChunk: true,
					},
				},
			},
		},
	},
}
