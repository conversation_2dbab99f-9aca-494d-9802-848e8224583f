<template>
	<div class="basic-info">
		<div class="left">
			<div class="top">
				<div class="title">基本属性</div>
				<Row :gutter="20">
					<Col :span="8">
						<div class="info-item">
							<div class="label" style="width: 100px">设备类型名称：</div>
							<div class="value" style="width: calc(100% - 110px)">
								<gc-custom-tooltip refName="deviceTypeName" :content="tabData.deviceTypeName"></gc-custom-tooltip>
							</div>
						</div>
					</Col>
					<Col :span="8">
						<div class="info-item">
							<div class="label">设备编号：</div>
							<div class="value">
								<gc-custom-tooltip refName="deviceCode" :content="tabData.deviceCode"></gc-custom-tooltip>
							</div>
						</div>
					</Col>
					<Col :span="8">
						<div class="info-item">
							<div class="label">设备名称：</div>
							<div class="value">
								<gc-custom-tooltip refName="deviceName" :content="tabData.deviceName"></gc-custom-tooltip>
							</div>
						</div>
					</Col>
				</Row>
			</div>
			<div class="bottom">
				<div class="title">安装属性</div>
				<Row :gutter="20">
					<Col :span="8">
						<div class="info-item">
							<div class="label">安装日期：</div>
							<div class="value">
								{{ tabData.installationDate || '--' }}
							</div>
						</div>
					</Col>
					<Col :span="8">
						<div class="info-item">
							<div class="label">客户名称：</div>
							<div class="value">
								<gc-custom-tooltip refName="userName" :content="tabData.userName"></gc-custom-tooltip>
							</div>
						</div>
					</Col>
					<Col :span="8">
						<div class="info-item">
							<div class="label">联系电话：</div>
							<div class="value">
								<gc-custom-tooltip refName="contactPhone" :content="tabData.contactPhone"></gc-custom-tooltip>
							</div>
						</div>
					</Col>
					<Col :span="24">
						<div class="info-item">
							<div class="label">安装地址：</div>
							<div class="value">
								{{ tabData.address || '--' }}
							</div>
						</div>
					</Col>
					<Col :span="24">
						<div class="info-item">
							<div class="label">设备位置：</div>
							<div class="value">
								{{
									'经度' +
									(tabData.longitude || '--') +
									'度，纬度' +
									(tabData.latitude || '--') +
									'度'
								}}
							</div>
						</div>
					</Col>
				</Row>
			</div>
		</div>
	</div>
</template>

<script>
import { isBlank } from '@/utils/util.js'
import GcCustomTooltip from '@/components/gc-custom-tooltip/index.vue'

export default {
	name: 'basicInfo',
	components: { GcCustomTooltip },
	props: {
		tabData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			refreshTime: null,
		}
	},
	computed: {
	},
	methods: {
		isBlank,
	},
}
</script>
<style lang="scss" scoped>
.basic-info {
	display: flex;
	justify-content: space-between;
	background-color: #fff;
	height: 100%;
	overflow: auto;
	.left {
		flex: 1;
		min-width: 889px;
		display: flex;
		flex-direction: column;
		padding-right: 40px;
		padding-left: 24px;
		.top {
			padding: 34px 0 40px;
			border-bottom: 1px dashed #999999;
			::v-deep [class*='Col-'] {
				height: 44px;
			}
		}
		.bottom {
			padding-top: 43px;
		}
	}
	.right {
		flex: 1;
		max-width: 790px;
		padding-top: 30px;
		.card {
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
		}
	}
	.title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 16px;
		font-family: SourceHanSansCN-Medium, SourceHanSansCN;
		font-weight: 600;
		color: #333333;
		padding-right: 40px;
		.refresh-time {
			font-size: 14px;
			font-family: SourceHanSansCN-Regular, SourceHanSansCN;
			font-weight: 400;
			color: #999999;
		}
	}
	.info-item {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		width: 100%;
		margin-top: 20px;
		.label {
			font-size: 14px;
			font-family: SourceHanSansCN-Regular, SourceHanSansCN;
			font-weight: 400;
			color: #666666;
			flex-shrink: 0;
		}
		.value {
			flex: 1;
			width: 0;
			font-size: 14px;
			font-family: SourceHanSansCN-Regular, SourceHanSansCN;
			font-weight: 400;
			color: #333333;
		}
	}
	.install-pic {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		.pic-list {
			img {
				padding-top: 10px;
				vertical-align: top;
				width: 200px;
			}
			img + img {
				padding-left: 10px;
			}
		}
	}
}
</style>
