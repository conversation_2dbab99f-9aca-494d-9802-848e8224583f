<template lang="pug">
es-modal.es-modal(
	:transfer='false',
	:is-direct-close-modal='false',
	width='500',
	v-model='showModal',
	:title='formData && formData.id ? "编辑分类" : "添加分类"',
	@on-cancel='handleClose',
	@on-visible-change='changeModal'
)
	template(slot='body')
		.popup-content
			Form.form(ref='form', :model='formData', :rules='formRules', :label-width='80')
				FormItem(label='分类名称', prop='name')
					Input(v-model='formData.name', placeholder='请输入', maxlength='16')
				FormItem(label='排序号', prop='sort')
					Input(v-model='formData.sort', placeholder='请输入', maxlength='4')

	template(slot='footer')
		Button(@click='handleClose') 关闭
		Button(type='primary', @click='handleSubForm') 保存
</template>

<script>
import { addGuideType, updateGuideType } from '@/api/other'
import { ruleOnlyInteger } from '@/utils/formRules.js'

export default {
	name: 'create-control-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {},
	props: {
		showModal: Boolean,
		data: Object,
	},
	data() {
		return {
			formData: {
				name: '',
				sort: '',
			},
			formRules: {
				name: [
					{
						required: true,
						message: '请输入',
						trigger: 'change',
					},
				],
				sort: [
					{
						required: true,
						validator: (rule, value, callback) => {
							if (value === '' || value === null) {
								callback(new Error('请输入排序值'))
							} else if (typeof value !== 'number' && typeof value !== 'string') {
								callback(new Error('排序值必须是数字或字符串'))
							} else {
								callback()
							}
						},
						message: '请输入',
						trigger: 'change',
					},
					ruleOnlyInteger,
				],
			},
		}
	},

	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				this.formData = { ...this.data }
			} else {
				this.$refs.form.resetFields()
			}
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this._submitForm()
				}
			})
		},
		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},
		// 添加及编辑分类
		_submitForm() {
			const { id, name, sort } = this.formData
			const payload = { name, sort }

			const handleSuccess = () => {
				this.$Message.success('操作成功')
				this.handleClose()
				this.$emit('submit-form', this.formData)
			}

			if (id) {
				updateGuideType({ id, ...payload }).then(handleSuccess)
			} else {
				addGuideType(payload).then(handleSuccess)
			}
		},
	},
}
</script>

<style lang="less" scoped>
.es-modal {
	.popup-content {
		width: 100%;
		padding: 0 40px;
		.form {
			width: 100%;
			/deep/ .ivu-form-item {
				margin-bottom: 20px;
			}
		}
	}
}
</style>
