/*
 * @Descripttion:
 * @version:
 * @Author: heliping
 * @Date: 2021-04-20 13:22:59
 * @LastEditors: heliping
 * @LastEditTime: 2022-02-18 13:46:57
 */
module.exports = {
	presets: ['@vue/cli-plugin-babel/preset'],
	plugins: [
		[
			'import',
			{
				libraryName: '@eslink/esvcp-pc-ui',
				customName: name => {
					if (name.indexOf('es') === 0) {
						let some = name.replace('es-', '')
						return `@eslink/esvcp-pc-ui/packages/es-components/${some}`
					} else {
						return `@eslink/esvcp-pc-ui/packages/components/${name}`
					}
				},
			},
		],
	],
}
