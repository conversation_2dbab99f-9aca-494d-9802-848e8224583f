/*
 * @Description:
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2021-07-28 10:15:49
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2024-08-15 11:20:22
 */
import { POST, GET } from '@/utils/request'

// 工艺图查询
export function queryPage(params, requestType = 'json') {
	return POST({
		url: '/processFlowChart/queryPage',
		params,
		requestType,
	})
}
// 工艺图创建
export function create(params, requestType = 'json') {
	return POST({
		url: '/processFlowChart/create',
		params,
		requestType,
	})
}
//工艺图更新
export function update(params, requestType = 'json') {
	return POST({
		url: '/processFlowChart/update',
		params,
		requestType,
	})
}
//通过id查询
export function queryById(code, params) {
	return GET({
		url: '/processFlowChart/queryByStation/' + code,
		params,
	})
}
//通过流程图id查询
export function queryByFlowId(id, params, screen) {
	let headers = {}
	if (screen) {
		headers = {
			source: 'screen',
			ownership: '2W04',
		}
	}

	return GET({
		url: '/processFlowChart/query/' + id,
		params,
		headers,
	})
}

//缩略图上传
export function upload(params, requestType = 'json') {
	return GET({
		url: '/processFlowChart/thumbnail/upload',
		params,
		requestType,
	})
}
//工艺图发布
export function release(params) {
	return POST({
		url: '/processFlowChart/release/' + params.id,
		params,
	})
}
// 工艺图删除
export function deleteProcess(params) {
	return POST({
		url: '/processFlowChart/delete/' + params.id,
	})
}
// 站点绑定工艺图
export function bind(params, requestType = 'json') {
	return POST({
		url: '/processFlowChart/station/bind',
		params,
		requestType,
	})
}
// 文件夹查询
export function dirList(params) {
	return POST({
		url: '/backgroundImage/dir/list',
		params,
	})
}
// 创建文件夹
export function createDir(params, requestType = 'json') {
	return POST({
		url: '/backgroundImage/dir/create',
		params,
		requestType,
	})
}
export function updateDir(params, requestType = 'json') {
	return POST({
		url: '/backgroundImage/dir/update',
		params,
		requestType,
	})
}
export function deleteDir(params) {
	return POST({
		url: '/backgroundImage/dir/delete/' + params.id,
	})
}

// 站点查询(参数可以控制数据项是否展示)
export function stationList(params, requestType = 'json') {
	return POST({
		url: '/processFlowChart/station/list',
		params,
		requestType,
	})
}
// 站点数据项值查询
export function itemDate(params, query = '', requestType = 'json') {
	return POST({
		url: `/processFlowChart/station/itemDate?sysUserId=${query}`,
		params,
		requestType,
	})
}
// 站点数据项值查询
export function rename(params) {
	return POST({
		url: '/processFlowChart/rename/' + params.id,
		params,
	})
}
// 删除图片
export function deleteImage(params) {
	return POST({
		url: '/backgroundImage/image/delete/' + params.id,
		params,
	})
}
// 解除绑定
export function cancelBind(params, requestType = 'json') {
	return POST({
		url: '/processFlowChart/station/cancelBind',
		params,
		requestType,
	})
}
// 查询控件
export function controls(params) {
	return POST({
		url: '/processFlowChart/controls/all',
		params,
	})
}
// 删除控件
export function deleteControl(params) {
	return POST({
		url: '/processFlowChart/controls/delete/' + params.id,
		params,
	})
}
// 添加控件
export function insertControl(params, requestType = 'json') {
	return POST({
		url: '/processFlowChart/controls/insert',
		params,
		requestType,
	})
}

// 编辑控件
export function updateControl(params, requestType = 'json') {
	return POST({
		url: '/processFlowChart/controls/update',
		params,
		requestType,
	})
}

// 温州BIM水厂全览实时曲线
export function getAnalysis(params) {
	return POST({
		url: '/processFlowChart/curve/analysis',
		params,
	})
}

// 添加/编辑权限
export function saveOrUpdateRole(params, requestType = 'json') {
	return POST({
		url: '/processAuth/saveOrUpdate',
		params,
		requestType,
	})
}

// 查询工艺图权限信息
export function queryAuthInfo(params) {
	return GET({
		url: '/processAuth/queryAuthInfo',
		params,
	})
}
// 删除工艺图权限
export function deleteAuth(params) {
	return GET({
		url: '/processAuth/delete/' + params.id,
	})
}

//  查询控件分组
export function getProcessControls(params, requestType = 'json') {
	return POST({
		url: '/waterPlat/processControlsGroup/list',
		params,
		requestType,
	})
}

//  新增分组
export function addProcessControlsGroup(params, requestType = 'json') {
	return POST({
		url: '/waterPlat/processControlsGroup/add',
		params,
		requestType,
	})
}
