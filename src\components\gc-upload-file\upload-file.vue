<!--
 * @Description: 图片上传
 * @Author: shenxh
 * @Date: 2024-03-12 11:25:29
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2024-08-14 15:25:45
-->

<template lang="pug">
.upload-file
	Upload(
		ref='upload',
		:show-upload-list='true',
		:default-file-list='uploadList',
		:before-upload='handleBeforeUpload',
		type='drag',
		:disabled='disabled',
		action='',
		style='display: inline-block',
		:on-remove='handleRemove'
	)
		Button(icon='ios-cloud-upload-outline') 添加指南
	.tip
		Icon(type='ios-information-circle-outline')
		span 支持pdf、docx、xlsx、jpg等，最大不超过16M
</template>

<script>
export default {
	name: 'upload-file',
	components: {},
	model: {
		prop: 'value',
		event: 'set-value',
	},
	props: {
		value: Array,
		format: {
			type: Array,
			default: () => ['jpg', 'jpeg', 'png', 'webp', 'xlsx', 'pdf', 'docx'],
		},
		disabled: Boolean,

		maxSize: {
			type: Number,
			default: 20480, // kb
		},
	},
	data() {
		return {
			uploadList: [],
		}
	},
	computed: {},
	watch: {
		value: {
			handler(val) {
				this.uploadList = val || []
			},
			deep: true,
			immediate: true,
		},
	},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		handleBeforeUpload(file) {
			if (file.size / 1024 > this.maxSize) {
				this.$Message.warning('文件大小超过限制')

				return false
			}

			if (!this.format.includes(file.name.split('.')[1])) {
				this.$Message.warning('文件格式不正确')

				return false
			}

			const data = new FormData()

			data.append('files', file)

			this.$axios.post('/backgroundImage/image/upload', data).then((res = {}) => {
				const { imageUrl, name } = res.result[0] || {}

				// this.uploadList.push({
				// 	name,
				// 	url: imageUrl,
				// })
				this.uploadList = [
					{
						name,
						url: imageUrl,
					},
				]

				this.$emit('set-value', this.uploadList)
			})

			return false
		},
		handleRemove(file, fileList) {
			this.uploadList = fileList
			this.$emit('set-value', this.uploadList)
		},
	},
}
</script>
<style lang="less" scoped>
.tip {
	color: #babbca;
}
</style>
