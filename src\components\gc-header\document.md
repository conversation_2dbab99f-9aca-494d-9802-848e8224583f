<gc-header :header-data="headerData">
  <!-- 状态标签 背景色自设 -->
  <template #status>
    <span class="status">已安装</span>
  </template>
  <!-- 按钮操作 -->
  <template #ops>
    <el-button>1</el-button>
    <el-button>2</el-button>
  </template>
  <!-- tab切换 -->
  <template #tabs>
  </template>
</gc-header>

headerData: {
pic: require("@/assets/imgs/pic/empty.png"), //url
title: "台账管理", //标题
desc: "理设备台账信息，目前可管理设备类型包含理设备台账信息", //描述信息（字符串||数组）
desc: [
{
icon: "11324234",
text: "Shebei001",
},
{
icon: "11324234",
text: "计量仪表",
},
{
icon: "11324234",
text: "杭州市钱塘区学院街艺术小区 9#1001",
},
],
},
