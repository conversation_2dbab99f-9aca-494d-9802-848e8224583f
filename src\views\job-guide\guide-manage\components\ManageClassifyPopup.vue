<template lang="pug">
Modal(
	v-model='isShow',
	width='1000',
	title='管理指南分类',
	:transfer='false',
	:footer-hide='true',
	@on-visible-change='changeModal'
)
	water-row(justify='flex-end', align='center')
		Button(type='primary', @click='handleSet({})') 添加
	Table.table-container.mt10(border, :columns='columns', :data='tableData', :height='470')
	Page(
		:total='pageTotal',
		show-elevator,
		show-sizer,
		@on-page-size-change='handleChangeSize',
		@on-change='handleChangePage'
	)
	classify-popup(v-model='showModal', :data='currentRow', @submit-form='_getGuideTypePage')
</template>
<script>
import ClassifyPopup from './ClassifyPopup.vue'
import WaterRow from '@/components/gc-water-row'
import { Poptip, Button } from '@eslink/esvcp-pc-ui'
import { getGuideTypePage, deleteGuideType } from '@/api/other'
export default {
	props: ['value', 'title'],
	components: { WaterRow, ClassifyPopup },
	data() {
		return {
			columns: [
				{
					title: '分类名称',
					key: 'name',
					align: 'center',
				},
				{
					title: '排序号',
					align: 'center',
					key: 'sort',
				},
				{
					title: '操作',
					key: 'deal',
					align: 'center',
					render: (h, params) => {
						const arr = [
							h(
								Poptip,
								{
									props: {
										placement: 'bottom-end',
										title: '确定要删除吗？',
										confirm: true,
										transfer: true,
									},
									on: {
										'on-ok': () => {
											const { id } = params.row
											this._deleteGuideType(id)
										},
									},
								},
								[
									h(
										Button,
										{
											class: 'mr10',
											props: {
												type: 'error',
												size: 'small',
											},
										},
										'删除',
									),
								],
							),
							h(
								Button,
								{
									props: {
										type: 'primary',
										size: 'small',
									},
									on: {
										click: () => {
											this.handleSet(params.row)
										},
									},
								},
								'修改',
							),
						]
						return h('div', arr)
					},
				},
			],
			tableData: [],
			pageSize: 10,
			pageNum: 1,
			pageTotal: 0,
			showModal: false,
			currentRow: {},
			timer: null,
		}
	},
	computed: {
		isShow: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit('input', val)
			},
		},
	},
	methods: {
		changeModal(isShow) {
			if (isShow) {
				this._getGuideTypePage()
			} else {
				this.timer = setTimeout(() => {
					this.$emit('close')
				}, 300)
			}
		},
		_getGuideTypePage() {
			const params = {
				needPage: true,
				pageNum: this.pageNum,
				pageSize: this.pageSize,
			}

			getGuideTypePage(params)
				.then(res => {
					const { result } = res
					const { list, total } = result

					this.tableData = list
					this.pageTotal = total
				})
				.catch(error => {
					console.error(error)
				})
		},
		_deleteGuideType(id) {
			deleteGuideType({ id }).then(() => {
				this.$Message.success('删除成功')
				this._getGuideTypePage()
			})
		},
		handleChangePage(val) {
			this.pageNum = val
			this._getGuideTypePage()
		},
		handleChangeSize(val) {
			this.pageSize = val
			this._getGuideTypePage()
		},
		handleSet(row) {
			this.currentRow = row
			this.showModal = true
		},
	},
	destroyed() {
		this.timer && clearTimeout(this.timer)
	},
}
</script>
<style lang="less" scoped>
::v-deep {
	.ivu-modal-header {
		padding: 8px;
	}
}
</style>
