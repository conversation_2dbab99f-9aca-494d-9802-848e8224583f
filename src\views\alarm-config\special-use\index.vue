<!--
 * @Description: 报警配置
 * @Author: shenxh
 * @Date: 2022-03-07 09:37:32
 * @LastEditors: shenxh
 * @LastEditTime: 2022-03-07 09:44:15
-->

<template lang="pug">
.special-use
	warn-list(@handle-batch-clear-btn='handleBatchClearBtn')
</template>

<script>
import WarnList from '../modules/WarnList.vue'

export default {
	name: 'special-use',
	components: {
		WarnList,
	},
	props: {},
	data() {
		return {}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 批量消缺按钮
		handleBatchClearBtn(data) {
			console.log(data)
		},
	},
}
</script>

<style lang="less" scoped>
.special-use {
	width: 100%;
	padding: 12px 16px;
}
</style>
