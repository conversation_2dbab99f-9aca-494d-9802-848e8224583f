<template>
	<Modal
		class-name="custom-modal"
		width="480"
		class="station-modal"
		:value="show"
		:transfer="false"
		:title="title"
		@on-cancel="handleCancel"
	>
		<Spin fix v-if="listLoading">加载中。。。</Spin>
		<div class="water-modal-content station-modal-content scroll">
			<Form ref="formValidate" :model="formItem" :label-width="80">
				<Form-item label="项目名称">
					<Select
						v-model="formItem.applicationName"
						placeholder="请选择"
						filterable
						transfer
						@on-change="handleChange"
					>
						<Option :value="item.code" v-for="(item, key) in sysList" :key="key">
							{{ item.name }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="站点">
					<Select v-model="formItem.stationCode" placeholder="请选择" filterable transfer>
						<Option :value="item.stationCode" v-for="(item, key) in stationList" :key="key">
							{{ item.stationName }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="工艺名称">
					<Input v-model="formItem.processName" placeholder="请输入"></Input>
				</Form-item>
			</Form>
		</div>
		<div slot="footer">
			<Button type="primary" @click="handleCheck">确定</Button>
		</div>
	</Modal>
</template>

<script>
import { getStationList, insertOrUpdateMtStationProcess } from '@/api/setting'
import { querySysList } from '@/api/common'

export default {
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '新增',
		},
	},
	mounted() {
		// 查询系统
		querySysList().then(res => {
			this.formItem.applicationName = 'dd'
			this.handleChange('dd')
			this.sysList = res.result
		})
	},
	data() {
		return {
			listLoading: false,
			stationList: [],
			formItem: {
				applicationName: '',
				stationId: '',
				stationCode: '',
				processName: '',
			},
			uploadList: [],
			defaultList: [],
			sysList: [],
		}
	},
	methods: {
		// 项目修改
		handleChange(val) {
			// 查询系统站点
			getStationList({
				applicationName: val,
				pageNum: 1,
				pageSize: 100,
			}).then(res => {
				this.stationList = res.result
			})
		},

		// 站点改变
		// handleStationChange(val) {
		// 	for (let index = 0; index < this.stationList.length; index++) {
		// 		const element = this.stationList[index]
		// 		if (element.stationCode === val) {
		// 			console.log(element)
		// 			this.formItem.stationId = 'dd'
		// 		}
		// 		break
		// 	}
		// },

		handleRemove() {
			this.formItem.processImage = ''
		},
		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},
		// 确定
		handleCheck() {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					insertOrUpdateMtStationProcess(this.formItem)
						.then(() => {
							this.$Message.success('提交成功!')
							this.formItem = {
								applicationName: '',
								stationId: '',
								stationCode: '',
								processName: '',
							}
							this.$emit('update:show', false)
							this.$emit('initList')
						})
						.catch(() => {
							this.listLoading = false
						})
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
	},
}
</script>
<style lang="less">
.station-modal {
	.custom-modal {
		.ivu-modal {
			height: 48% !important;
		}
		.ivu-modal-body {
			height: calc(100% - 96px);
		}
	}
}
</style>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
.demo-upload-list {
	display: inline-block;
	width: 60px;
	height: 60px;
	text-align: center;
	line-height: 60px;
	border: 1px solid transparent;
	border-radius: 4px;
	overflow: hidden;
	background: #fff;
	position: relative;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
	margin-right: 4px;
}
.demo-upload-list img {
	width: 100%;
	height: 100%;
}
.demo-upload-list-cover {
	display: none;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.6);
}
.demo-upload-list:hover .demo-upload-list-cover {
	display: block;
}
.demo-upload-list-cover i {
	color: #fff;
	font-size: 20px;
	cursor: pointer;
	margin: 0 2px;
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	// .ivu-input-number-input {
	// 	color: #fff;
	// 	background: #133a5e;
	// 	border: none;
	// }
}
</style>
