<template>
	<div class="create-item-popup">
		<!-- 弹窗 -->
		<es-modal
			class="es-modal"
			:transfer="false"
			:is-direct-close-modal="false"
			width="600"
			:value="show"
			:title="title"
			@on-cancel="handleClose"
			@on-visible-change="changeModal"
		>
			<template v-slot:body>
				<div class="popup-content">
					<Form class="form" ref="form" :model="formData" :rules="formRules" :label-width="95">
						<FormItem label="数据项名称" prop="baseItemName">
							<Input
								v-model="formData.baseItemName"
								:disabled="!!formData.id"
								placeholder="请输入数据项名称"
							></Input>
						</FormItem>
						<FormItem label="数据项编号" prop="baseItemCode">
							<Input
								v-model="formData.baseItemCode"
								:disabled="!!formData.id"
								placeholder="请输入数据项编号"
							></Input>
						</FormItem>
						<FormItem label="数据项单位" prop="unit">
							<Input v-model="formData.unit" placeholder="请输入数据项单位"></Input>
						</FormItem>
						<FormItem label="有效位数" prop="precision">
							<InputNumber
								v-model="formData.precision"
								style="width: 400px"
								placeholder="有效位数"
								:max="5"
								:min="0"
							></InputNumber>
						</FormItem>
						<FormItem label="备注">
							<Input v-model="formData.memo" type="textarea" placeholder="请输入备注"></Input>
						</FormItem>
					</Form>
				</div>
			</template>
			<template v-slot:footer>
				<Button @click="handleClose">关闭</Button>
				<Button type="primary" @click="handleSubForm">保存</Button>
			</template>
		</es-modal>
	</div>
</template>

<script>
import { saveBaseDataItem, updateBaseDataItem } from '@/api/base-item.js'
// import dataRepairVue from '../../../data-repair/data-repair.vue'
export default {
	name: 'CreateItemPopup',
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			title: '添加',
			formData: {
				baseItemName: '',
				baseItemCode: '',
				type: 'jz',
				unit: '',
				precision: null,
				memo: '',
			},
			formRules: {
				baseItemName: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
				baseItemCode: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal() {
			this.$refs.form.resetFields()
		},

		// 按钮-保存
		handleSubForm() {
			console.log('保存按钮this.formData', this.formData)
			this.$refs.form.validate(valid => {
				if (valid) {
					if (this.formData.id) {
						updateBaseDataItem(this.formData)
							.then(() => {
								this.$Message.success('提交成功!')
								this.$refs.form.resetFields()
								this.formData.memo = '' // 手动重置备注resetFields无法重置
								this.$emit('update:show', false)
								this.$emit('initList')
							})
							.catch(() => {
								this.listLoading = false
							})
					} else {
						saveBaseDataItem(this.formData)
							.then(() => {
								this.$Message.success('提交成功!')
								this.$refs.form.resetFields()
								this.formData.memo = '' // 手动重置备注resetFields无法重置
								this.$emit('update:show', false)
								this.$emit('initList')
							})
							.catch(() => {
								this.listLoading = false
							})
					}
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},

		setData(data) {
			console.log(data)
			this.title = '编辑'
			this.formData = data
			// this.formData =  {
			// 	baseItemName,
			// 	baseItemCode: '',
			// 	type: null,
			// 	unit: '',
			// 	precision: null,
			// 	memo: '',
			// },
		},

		// 按钮-关闭
		handleClose() {
			this.formData = {
				baseItemName: '',
				baseItemCode: '',
				type: 'jz',
				unit: '',
				precision: null,
				memo: '',
			}
			this.$emit('update:show', false)
		},
	},
}
</script>

<style lang="less" scoped>
.create-item-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			padding: 0 40px;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
