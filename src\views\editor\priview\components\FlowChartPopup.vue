<template lang="pug">
.flow-chart-popup
	Tabs(v-model='currentTab', @on-click='changeTab')
		TabPane(v-for='item in tabList', :key='item.value', :label='item.label', :name='item.value')
	.popup-content
		.date-bar
			.date-picker-wrap
				DatePicker.date-picker(
					v-show='currentTab === \'3\'',
					v-model='dateVal',
					type='datetimerange',
					:clearable='false',
					:options='dateOpt',
					@on-change='changeDate'
				)
			.menu
				.menu-item(
					v-for='item in dateTypeList',
					:key='item.value',
					:class='currentDateType === item.value ? "active" : ""',
					@click='handleDateType(item)'
				) {{ item.label }}
		.chart
			bar-line(
				ref='chart',
				:y-axis='yAxis',
				:grid='grid',
				:loading='loading',
				:data-zoom='dataZoom',
				:series='series',
				:chart-data='chartData'
			)
</template>

<script>
import BarLine from './BarLine.vue'

import { getAnalysis } from '@/api/editor'

export default {
	name: 'flow-chart-popup',
	components: {
		BarLine,
	},
	props: {
		data: Object,
	},
	data() {
		return {
			loading: false,
			dateVal: [
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59',
			],
			dateValOld: [
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59',
			],
			dateValInit: [
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59',
			],
			currentTab: '0',
			tabList: [
				{
					label: '今日',
					value: '0',
				},
				{
					label: '昨日',
					value: '1',
				},
				{
					label: '近24小时',
					value: '2',
				},
				{
					label: '自定义时间',
					value: '3',
				},
			],
			currentDateType: 'min',
			dateTypeList: [
				{
					label: '分',
					// value: 'minutes',
					value: 'min',
				},
				{
					label: '时',
					value: 'day',
				},
				{
					label: '日',
					value: 'month',
				},
			],
			chartData: [],
			grid: {
				top: 30,
				left: 20,
				right: 50,
				bottom: 50,
			},
			yAxis: {
				name: '',
				scale: true,
			},
			series: {},
			dataZoom: [
				{
					type: 'inside',
					start: 0,
					end: 100,
				},
				{
					start: 0,
					end: 50,
					textStyle: '#00000000',
				},
			],
			dateOpt: {
				disabledDate(date) {
					return date && date.valueOf() > Date.now()
				},
			},
		}
	},
	computed: {
		dateForm() {
			let date = {}

			// 今日
			if (this.currentTab === '0') {
				date = {
					beginTime: this.$moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
					endTime: this.$moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59',
				}
			}
			// 昨日
			if (this.currentTab === '1') {
				const yesterday = this.$moment().subtract(1, 'days')

				date = {
					beginTime: this.$moment(yesterday).format('YYYY-MM-DD') + ' 00:00:00',
					endTime: this.$moment(yesterday).format('YYYY-MM-DD') + ' 23:59:59',
				}
			}
			// 近24小时
			if (this.currentTab === '2') {
				const newDate = this.$moment().subtract(24, 'hours')

				date = {
					beginTime: this.$moment(newDate).format('YYYY-MM-DD HH:mm:ss'),
					endTime: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
				}
			}
			// 自定义时间
			if (this.currentTab === '3') {
				date = {
					beginTime: this.$moment(this.dateVal[0]).format('YYYY-MM-DD HH:mm:ss'),
					endTime: this.$moment(this.dateVal[1]).format('YYYY-MM-DD HH:mm:ss'),
				}
			}

			return date
		},
	},
	mounted() {
		this.getChartData()
	},
	methods: {
		// 选择tab
		changeTab() {
			this.getChartData()
		},

		// 选择日期
		changeDate(date) {
			const beginTime = date[0]
			const endTime = date[1]
			const subtract = this.$moment(endTime).diff(this.$moment(beginTime), 'days')
			let max = 7
			let _date = [beginTime, endTime.split(' ')[0] + ' 23:59:59']

			this.dateVal = _date

			if (this.currentDateType === 'month') {
				max = 31
			}
			if (subtract > max - 1) {
				this.$Message.warning(`最多选择${max}天`)
				this.dateVal = this.dateValOld

				return
			}

			this.dateValOld = _date

			this.getChartData()
		},

		// 选择日期类型
		handleDateType(data) {
			this.currentDateType = data.value
			this.dateVal = this.dateValOld = this.dateValInit

			this.getChartData()
		},

		// 获取数据
		getChartData() {
			getAnalysis({
				stationCode: this.data.stationCode,
				itemCode: this.data.itemRealCode,
				type: this.currentDateType,
				...this.dateForm,
			}).then(res => {
				const { result } = res
				let list = []

				if (result && result.length) {
					const data = result[0]

					list = data.dataList.map(item => {
						return {
							name: this.currentTab === '3' ? item.x : item.xAxis,
							value: item.yAxis,
						}
					})
					this.yAxis.name = data.unit
				}
				if (this.currentTab === '3') {
					this.grid.left = 60
					this.grid.right = 70
				} else {
					this.grid.left = 20
					this.grid.right = 30
				}
				this.chartData = [list]
				this.$refs.chart.initChart()
			})
		},
	},
}
</script>

<style lang="less" scoped>
.flow-chart-popup {
	.popup-content {
		.date-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 38px;
			.date-picker {
				width: 350px;
			}
			.menu {
				display: flex;
				border: 1px solid #0094ff;
				.menu-item {
					text-align: center;
					width: 40px;
					height: 24px;
					line-height: 24px;
					background: #041627;
					cursor: pointer;
					&:not(:last-child) {
						border-right: 1px solid #0094ff;
					}
					&.active {
						background: #144e84;
					}
				}
			}
		}
		.chart {
			height: 580px;
			padding-top: 10px;
		}
	}
}
</style>
