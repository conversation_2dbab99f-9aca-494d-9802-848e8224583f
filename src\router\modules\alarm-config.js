/*
 * @Description: 报警配置
 * @Author: shenxh
 * @Date: 2022-03-03 15:07:04
 * @LastEditors: shenxh
 * @LastEditTime: 2024-10-09 11:10:24
 */

export default [
	// 实时报警
	{
		path: '/real-time-alarm',
		name: 'real-time-alarm',
		component: (/* webpackChunkName: 'real-time-alarm' */) =>
			import('@/views/alarm-manage/real-time-alarm/real-time-alarm'),
	},
	// 历史报警
	{
		path: '/history-alarm',
		name: 'history-alarm',
		component: (/* webpackChunkName: 'history-alarm' */) =>
			import('@/views/alarm-manage/history-alarm/history-alarm'),
	},
	// 专用报警配置
	{
		path: '/special-use-alarm',
		name: 'special-use-alarm',
		component: (/* webpackChunkName: 'special-use-alarm' */) => import('@/views/alarm-config/special-use/index'),
	},
	// 通用方案配置
	{
		path: '/common-alarm',
		name: 'common-alarm',
		component: (/* webpackChunkName: 'common-alarm' */) => import('@/views/alarm-config/special-use/index'),
	},
	// 报警方案
	{
		path: '/alarm-scheme',
		name: 'alarm-scheme',
		component: (/* webpackChunkName: 'alarm-scheme' */) => import('@/views/alarm-config/alarm-scheme/alarm-scheme'),
	},
	// DMA报警方案
	{
		path: '/dma-alarm-scheme',
		name: 'dma-alarm-scheme',
		component: (/* webpackChunkName: 'dma-alarm-scheme' */) =>
			import('@/views/alarm-config/alarm-scheme/dma-alarm-scheme'),
	},
	// 可视化报警配置
	{
		path: '/visual-alarm',
		name: 'visual-alarm',
		component: (/* webpackChunkName: 'visual-alarm' */) => import('@/views/alarm-config/visual-alarm/index'),
	},
	// 告警配置(鸡泽-专用)
	{
		path: '/device-alarm-config',
		name: 'device-alarm-config',
		component: (/* webpackChunkName: 'device-alarm-config' */) =>
			import('@/views/alarm-config/device-alarm-config/index.vue'),
	},
	// 告警配置(首页,可选择通用和专用配置)
	{
		path: '/device-alarm-config-home',
		name: 'device-alarm-config-home',
		component: (/* webpackChunkName: 'device-alarm-config-home' */) =>
			import('@/views/alarm-config/device-alarm-config-home/index.vue'),
		children: [
			{
				path: '',
				redirect: '/device-alarm-config-home/common-alarm',
			},
			// 专用报警配置
			{
				path: 'special-use-alarm',
				name: 'special-use-alarm',
				component: (/* webpackChunkName: 'special-use-alarm' */) =>
					import('@/views/alarm-config/device-alarm-config-home/modules/index'),
			},
			// 通用方案配置
			{
				path: 'common-alarm',
				name: 'common-alarm',
				component: (/* webpackChunkName: 'common-alarm' */) =>
					import('@/views/alarm-config/device-alarm-config-home/modules/index'),
			},
		],
	},
	{
		path: '/device-alarm-config',
		name: 'device-alarm-config',
		component: (/* webpackChunkName: 'device-alarm-config' */) =>
			import('@/views/alarm-config/device-alarm-config/index.vue'),
	},
	// 设备类型配置
	{
		path: '/device-type-config',
		name: 'device-type-config',
		component: (/* webpackChunkName: 'device-type-config' */) =>
			import('@/views/alarm-config/device-type-config/indexVersion2.vue'),
	},
	// 告警记录
	{
		path: '/alarm-record',
		name: 'alarm-record',
		component: () => import('@/views/alarm-record/index.vue'),
	},
	// 告警记录详情
	{
		path: '/alarm-record/detail',
		name: 'alarm-record-detail',
		// component: () => import('@/views/alarm-record/index.vue'),
		component: () => import('@/views/alarm-record/detail/index.vue'),
	},
	// 告警一张图
	{
		path: '/alarmOnePicture',
		name: 'alarmOnePicture',
		// component: () => import('@/views/alarm-record/index.vue'),
		component: () => import('@/views/alarm-one-pictrue/index.vue'),
	},
	// 告警一张图设备详情
	// {
	// 	path: '/device-detail',
	// 	name: 'deviceDetail',
	// 	meta: {
	// 		title: '设备详情',
	// 		keepAlive: false,
	// 		hidden: true,
	// 		customCrumb: [{ customCrumbTitle: '设备管理', path: '/device' }, { customCrumbTitle: '设备详情' }],
	// 	},
	// 	component: () => import('@/views/alarm-one-pictrue/detail/index.vue'),
	// },
]
