<template lang="pug">
.create-control-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='500',
		v-model='showModal',
		title='新增分组',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='80')
					FormItem(label='分组名称', prop='name')
						Input(v-model='formData.name', placeholder='请输入名称')
					//- FormItem(label="图片", prop="iconUrl")
					//- 	Button(icon="ios-cloud-upload-outline") 添加控件图片
					//- 		input(
					//- 			style="position: absolute; left: 36px; opacity: 0; height: 30px; width: 130px",
					//- 			type="file",
					//- 			@change="modelUpload($event)")
					//- .demo-upload-list(v-if="formData.iconUrl")
					//- 	template
					//- 		img(:src="formData.iconUrl")
					//- div(style="width: 58px; height: 58px; line-height: 58px")
					//- 	Icon(type="camera", size="20")
		template(slot='footer')
			Button(@click='handleClose') 关闭
			Button(type='primary', @click='handleSubForm') 保存
</template>

<script>
import { addProcessControlsGroup } from '@/api/editor'

export default {
	name: 'create-control-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {},
	props: {
		showModal: Boolean,
		data: Object,
	},
	data() {
		return {
			formData: {
				iconUrl: '',
			},
			platformList: [
				{ value: '0', label: '全局' },
				{ value: '1', label: '租户' },
			],
			formRules: {
				name: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
				iconUrl: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
					},
				],
			},
			imgName: '',
			visible: false,
			uploadList: [],
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				this.formData = { ...this.data }
				// this.formData.val = this.data.val === 'true' ? true : false
			} else {
				this.$refs.form.resetFields()
			}
		},
		// 上传图片
		modelUpload(event) {
			let file = event.target.files[0]
			if (!file) {
				return
			}
			const { type } = file
			if (!['image/jpeg', 'image/png', 'image/svg+xml'].includes(type)) {
				this.$Message.error('请选择正确的控件类型')
				return
			}
			let data = new FormData()

			data.append('files', file)
			this.$axios.post('/backgroundImage/image/upload', data).then(res => {
				this.$set(this.formData, 'iconUrl', res.result[0].imageUrl)
				this.$Message.info('上传成功')
			})
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this._updSecurityPlatform()
				}
			})
		},
		handleView(name) {
			this.imgName = name
			this.visible = true
		},
		handleRemove(file) {
			// 从 upload 实例删除数据
			const fileList = this.$refs.upload.fileList
			this.$refs.upload.fileList.splice(fileList.indexOf(file), 1)
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		// 添加及编辑安防平台
		_updSecurityPlatform() {
			const { name } = this.formData

			addProcessControlsGroup({
				name,
			}).then(res => {
				console.log(res)
				this.$Message.success('操作成功')

				this.handleClose()
				this.$emit('submit-form', this.formData)
			})
		},
	},
}
</script>

<style lang="less" scoped>
.create-control-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			padding: 0 40px;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
.demo-upload-list {
	display: inline-block;
	width: 60px;
	height: 60px;
	text-align: center;
	line-height: 60px;
	border: 1px solid transparent;
	border-radius: 4px;
	overflow: hidden;
	background: #fff;
	position: relative;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
	margin-left: 80px;
}
.demo-upload-list img {
	width: 100%;
	height: 100%;
}
// .demo-upload-list-cover {
// 	display: none;
// 	position: absolute;
// 	top: 0;
// 	bottom: 0;
// 	left: 0;
// 	right: 0;
// 	background: rgba(0, 0, 0, 0.6);
// }
// .demo-upload-list:hover .demo-upload-list-cover {
// 	display: block;
// }
// .demo-upload-list-cover i {
// 	color: #fff;
// 	font-size: 20px;
// 	cursor: pointer;
// 	margin: 0 2px;
// }
</style>
