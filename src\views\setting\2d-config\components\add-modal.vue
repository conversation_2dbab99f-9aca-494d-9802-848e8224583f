<template>
	<Modal
		class-name="custom-modal"
		width="480"
		class="station-modal"
		:value="show"
		:transfer="false"
		:title="title"
		@on-cancel="handleCancel"
		@on-visible-change="handleVisibleChange"
	>
		<Spin fix v-if="listLoading">加载中。。。</Spin>
		<div class="water-modal-content station-modal-content scroll">
			<Form ref="formValidate" :model="formItem" :label-width="80">
				<Form-item label="项目名称">
					<Select
						v-model="formItem.applicationName"
						placeholder="请选择"
						filterable
						@on-change="handleChange"
					>
						<Option :value="item.code" v-for="(item, key) in sysList" :key="key">
							{{ item.name }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="站点">
					<Select v-model="formItem.stationCode" placeholder="请选择" filterable>
						<Option :value="item.stationCode" v-for="(item, key) in stationList" :key="key">
							{{ item.stationName }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="序号">
					<Input v-model="formItem.processIndex" placeholder="请输入"></Input>
				</Form-item>
				<Form-item label="工艺流程名称">
					<Input v-model="formItem.processName" placeholder="请输入"></Input>
				</Form-item>
				<Form-item label="工艺流程底图">
					<div class="demo-upload-list" v-if="formItem.processImage">
						<template>
							<img :src="formItem.processImage" />
							<div class="demo-upload-list-cover">
								<!-- <Icon
									type="ios-eye-outline"
									@click.native="handleView(item)"
								></Icon> -->
								<Icon type="ios-trash-outline" @click.native="handleRemove"></Icon>
							</div>
						</template>
					</div>
					<div style="position: relative" v-if="!formItem.processImage">
						<Button type="warning">上传</Button>
						<input
							multiple
							style="position: absolute; left: 0px; opacity: 0; height: 30px; width: 130px"
							type="file"
							@change="multiUpload($event)"
							id="fileInput"
						/>
					</div>
				</Form-item>
				<Form-item label="获取数据间隔(秒)">
					<Input-number :max="1000" :min="1" v-model="formItem.dataInterval"></Input-number>
				</Form-item>
			</Form>
		</div>
		<div slot="footer">
			<!-- <Button @click="handleReset">重置</Button> -->
			<Button type="primary" @click="handleCheck">确定</Button>
		</div>
	</Modal>
</template>

<script>
import { saveOrUpdate2dProcess, getStationList } from '@/api/setting'
import { querySysList } from '@/api/common'

export default {
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '新增',
		},
	},
	mounted() {
		// 查询系统
		querySysList().then(res => {
			this.sysList = res.result
		})
	},
	data() {
		return {
			listLoading: false,
			stationList: [],
			formItem: {
				applicationName: '',
				stationCode: '',
				processIndex: '',
				processName: '',
				processImage: '',
				dataInterval: 60,
			},
			uploadList: [],
			defaultList: [],
			sysList: [],
		}
	},
	methods: {
		// 项目修改
		handleChange(val) {
			// 查询系统站点
			getStationList({
				applicationName: val,
				pageNum: 1,
				pageSize: 100,
			}).then(res => {
				this.stationList = res.result
			})
		},
		// 弹窗显隐事件
		handleVisibleChange() {},

		// 文件批量上传
		multiUpload(event) {
			let file = event.target.files
			this.listLoading = true
			let data = new FormData()
			for (let key in file) {
				if (key < file.length) {
					data.append('files[' + key + ']', file[key])
				}
			}
			data.append('needPrefix', false)
			this.$axios.post('/screen/common/oss/batch/upload', data).then(result => {
				this.formItem.processImage = result.result[0]
				this.$Message.info('上传成功')
				this.listLoading = false
			})
		},
		handleRemove() {
			this.formItem.processImage = ''
		},
		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},
		// 重置
		handleReset() {},
		// 确定
		handleCheck() {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					saveOrUpdate2dProcess(this.formItem)
						.then(() => {
							this.$Message.success('提交成功!')
							this.formItem = {
								applicationName: '',
								stationCode: '',
								processIndex: '',
								processName: '',
								processImage: '',
								dataInterval: 0,
							}
							this.$emit('update:show', false)
							this.$emit('initList')
						})
						.catch(() => {
							this.listLoading = false
						})
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
	},
}
</script>
<style lang="less">
.station-modal {
	.custom-modal {
		.ivu-modal {
			height: 48% !important;
		}
		.ivu-modal-body {
			height: calc(100% - 96px);
		}
	}
}
</style>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
.demo-upload-list {
	display: inline-block;
	width: 60px;
	height: 60px;
	text-align: center;
	line-height: 60px;
	border: 1px solid transparent;
	border-radius: 4px;
	overflow: hidden;
	background: #fff;
	position: relative;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
	margin-right: 4px;
}
.demo-upload-list img {
	width: 100%;
	height: 100%;
}
.demo-upload-list-cover {
	display: none;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.6);
}
.demo-upload-list:hover .demo-upload-list-cover {
	display: block;
}
.demo-upload-list-cover i {
	color: #fff;
	font-size: 20px;
	cursor: pointer;
	margin: 0 2px;
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	// .ivu-input-number-input {
	// 	color: #fff;
	// 	background: #133a5e;
	// 	border: none;
	// }
}
</style>
