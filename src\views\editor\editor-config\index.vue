<template lang="pug">
.config-page
	con-header(@drawShape='drawShape', @rename='rename', ref='head', @handleClick='handleSave')
	div(style='display: flex; flex: 1')
		con-left(:can-delete='canDelete', @bindingData='handleBindingData', @deleteConfirm='handleCustomDeleteConfirm')
		.config-content
			editor(ref='editor', @showLevel='handleShowConfig', @handleDelete='handleDelete')
		.right-aside
			Tabs(v-model='tabName')
				TabPane(
					:label='item.name',
					:name='item.name',
					:disabled='item.disabled',
					v-for='(item, index) in tabList',
					:key='index'
				)

			//-  线段
			pipeline-config(
				v-if='type === "LINE_SEGMENT" && tabName === "样式"',
				:value='value',
				:index='index',
				@changeControl='changeControl'
			)

			//- 编辑时线段
			editor-pipeline-config(
				v-if='type === "LINE_SEGMENT_EDITOR" && tabName === "样式"',
				:value='value',
				@changeControl='changeControl'
			)
			//- 控件
			control-config(
				v-if='isInTypes(type) && tabName === "样式"',
				:value='value',
				:index='index',
				@changeImageColor='changeImageColor',
				@changeControl='changeControl'
			)
			//- 绑定数据项
			bind-data(v-if='isInTypes(type) && tabName === "交互"', :value='value', :index='index')

			//- 文本
			text-bind-config(
				v-if='type === "TEXT" && tabName === "样式"',
				:value='value',
				:index='index',
				@changeControl='changeControl'
			)
			//- 纯文本控件
			text-config(
				v-if='type === "PLAINTEXT" && tabName === "样式"',
				:value='value',
				:index='index',
				@changeControl='changeControl'
			)

			//- 交互 TOTO 可以通用  现在只用在绑定文本上
			interaction-config(
				v-if='type === "TEXT" && tabName === "交互"',
				:value='value',
				:index='index',
				@changeControl='changeControl'
			)
			//- 方形
			rect-config(
				v-if='type === "SQUARE" && tabName === "样式"',
				:value='value',
				:index='index',
				@changeControl='changeControl'
			)
			//- 链接
			link-config(
				v-if='type === "LINK" && tabName === "样式"',
				:value='value',
				:index='index',
				@changeControl='changeControl'
			)
			//- 圆形
			circle-config(
				v-if='type === "ROUND" && tabName === "样式"',
				:value='value',
				:index='index',
				@changeControl='changeControl'
			)
			//-  背景
			back-config(
				v-show='type === 1',
				:value='value',
				:canvasHeight='canvasHeight',
				:canvasWidth='canvasWidth',
				@pickImage='handlePickImage',
				@pickColor='handlePickColor'
			)
</template>
<script>
import { controlTypes2AttrtempMap } from '../enum'

import conHeader from './components/Head.vue'
import conLeft from './components/Leftor.vue'
import backConfig from './components/BackConfig.vue'
import controlConfig from './components/ControlConfig.vue'
import textConfig from './components/TextConfig.vue'
import rectConfig from './components/RectConfig.vue'
import circleConfig from './components/CircleConfig.vue'
import textBindConfig from './components/TextBindConfig.vue'
import interactionConfig from './components/InteractionConfig.vue'
import pipelineConfig from './components/PipelineConfig.vue'
import linkConfig from './components/LinkConfig.vue'
import editorPipelineConfig from './components/EditorPipelineConfig.vue'
import bindData from './components/BindData.vue'
import editor from './components/Edit.vue'

import { create, queryByFlowId, update } from '@/api/editor'

let backimg = {}
export default {
	name: 'config-page',
	components: {
		controlConfig,
		backConfig,
		textConfig,
		rectConfig,
		circleConfig,
		textBindConfig,
		interactionConfig,
		pipelineConfig,
		editorPipelineConfig,
		editor,
		conHeader,
		conLeft,
		bindData,
		linkConfig,
	},
	data() {
		return {
			sysCode: 'dd',
			isChangeCheckbox: false,
			isLoading: false,
			checkedKeys: [],
			multiple: false,
			treeData: [],
			type: 1,
			value: {
				controls: [],
				points: {},
				refreshTime: 30,
				rotate: 0,
				appZoomFlag: true,
				pcZoomFlag: false,
				backgroundColor: '#ddd',
			},
			index: 0,
			tabName: '样式',
			tabList: [
				{ name: '样式', disabled: false },
				// { name: '标签', disabled: true },
				{ name: '交互', disabled: true },
			],
			name: '未命名工艺图',
			canvasWidth: 0,
			canvasHeight: 0,

			// 自定义控件图片删除标记
			canDelete: true,
		}
	},
	mounted() {
		const { id, sysUserId } = this.$route.query

		if (id) {
			queryByFlowId(id, { needData: true, sysUserId }).then(res => {
				const { result = {} } = res
				this.render(result)
			})
		}
	},
	methods: {
		async render(result) {
			const { controlsBinds = [], canvasStyle, backgroundImageUrl, name } = result
			this.$refs.head.title = name
			this.name = name
			const style = JSON.parse(canvasStyle)
			const { backgroundColor, refreshTime, rotate, appZoomFlag, canvasHeight, canvasWidth } = style
			this.value.refreshTime = refreshTime
			this.value.rotate = rotate
			this.value.appZoomFlag = appZoomFlag
			this.value.backgroundColor = backgroundColor

			this.$refs.editor.setBackImage({ imageUrl: backgroundImageUrl })
			this.$refs.editor.setBackColor(backgroundColor || '#ddd')

			for (let index = 0; index < controlsBinds.length; index++) {
				const control = controlsBinds[index]
				const { baseStyle, iconUrl } = control
				this.$set(this.value.controls, index, {
					...JSON.parse(baseStyle),
					canvasHeight,
					canvasWidth,
					iconUrl,
				})
				control.canvasHeight = canvasHeight
				control.canvasWidth = canvasWidth

				await this.$refs.editor.priviewRender(control, true, true)
			}
		},

		// 显示控件并赋值
		handleShowConfig(target) {
			const {
				type,
				left,
				top,
				height,
				width,
				index,
				name,
				code,
				canvasWidth,
				canvasHeight,
				strokeWidth,
				stroke,
				fontWeight,
				fontStyle,
				underline,
				linethrough,
				overline,
				charSpacing,
				angle = 0,
			} = target
			this.type = type
			console.log('type', type)
			this.tabName = '样式'
			this.canvasWidth = canvasWidth
			this.canvasHeight = canvasHeight

			this.index = index
			if (type === 1) {
				this.tabList = [
					{ name: '样式', disabled: false },
					{ name: '交互', disabled: true },
				]
				this.canvasWidth = canvasWidth
				this.canvasHeight = canvasHeight
			}
			// 折线
			if (type === 'LINE_SEGMENT') {
				const { points, stroke, strokeWidth, strokeDashArray, initValue, animate, animateColor } = target
				// debugger
				const startPoint = points[0]
				const endPoint = points[points.length - 1]
				this.$set(this.value.controls, index, {
					left,
					top,
					height,
					width,
					startPoint,
					type,
					endPoint,
					points,
					canvasHeight,
					canvasWidth,
					name,
					index,
					code,
					stroke,
					animateColor,
					strokeWidth,
					// animate:
					// 	(this.value.controls[index] &&
					// 		this.value.controls[index].animate) ||
					// 	false,
					animate,
					strokeDashArray: strokeDashArray.length ? 2 : 1,
					initValue,
				})
				this.tabList = [
					{ name: '样式', disabled: false },
					{ name: '交互', disabled: true },
				]
			}
			// 编辑折线
			if (type === 'LINE_SEGMENT_EDITOR') {
				const { linePath } = target
				this.tabList = [
					{ name: '样式', disabled: false },
					{ name: '交互', disabled: true },
				]
				// debugger
				if (linePath && linePath.length) {
					const startPoint = linePath[0]
					const endPoint = linePath[linePath.length - 1]
					this.tabName = '样式'
					this.$set(this.value, 'points', {
						linePath,
						startPoint,
						endPoint,
						canvasHeight,
						canvasWidth,
					})
				}
			}
			// 文本
			if (type === 'TEXT' || type === 'PLAINTEXT') {
				const {
					backgroundColor,
					fontSize,
					content,
					scaleX,
					scaleY,
					itemName,
					itemRealCode,
					sysCode,
					stationCode,
					fill,
					text,
					unit,
					value,
					angle,
					textAlign,
					hideTitle,
					hideUnit,
				} = target
				this.tabList = [
					{ name: '样式', disabled: false },
					{ name: '交互', disabled: type !== 'TEXT' },
				]
				const current = this.value.controls[index]
				this.$set(this.value.controls, index, {
					left: parseInt(left),
					top: parseInt(top),
					height: parseFloat(height.toFixed(2)),
					type,
					width: parseFloat(width.toFixed(2)),
					name,
					code,
					fill,
					fontSize,
					canvasHeight,
					canvasWidth,
					backgroundColor,
					content,
					scaleX,
					scaleY,
					hideTitle,
					hideUnit,
					clickable: (current && current.clickable) ?? type === 'TEXT',
					itemName,
					itemRealCode,
					sysCode,
					stationCode,
					text,
					unit,
					value,
					angle,
					fontWeight,
					fontStyle,
					underline,
					linethrough,
					overline,
					charSpacing,
					stroke: stroke || '#ffffff',
					strokeWidth,
					textAlign,
				})
			}
			if (type === 'SQUARE') {
				const { fill, scaleY, scaleX, strokeWidth, stroke } = target
				console.log('SQUARE', target)
				this.tabList = [
					{ name: '样式', disabled: false },
					{ name: '交互', disabled: true },
				]
				this.$set(this.value.controls, index, {
					left: parseFloat(left.toFixed(2)),
					top: parseFloat(top.toFixed(2)),
					height,
					type,
					width,
					name,
					code,
					scaleY,
					scaleX,
					canvasHeight,
					canvasWidth,
					fill,
					strokeWidth,
					stroke,
				})
			}
			if (type === 'ROUND') {
				const { fill, scaleY, scaleX } = target

				this.tabList = [
					{ name: '样式', disabled: false },
					{ name: '交互', disabled: true },
				]
				console.log(left, top, target)
				this.$set(this.value.controls, index, {
					left: parseFloat(left.toFixed(2)),
					top: parseFloat(top.toFixed(2)),
					height,
					type,
					width,
					name,
					code,
					scaleY,
					scaleX,
					canvasHeight,
					canvasWidth,
					fill,
				})
			}
			if (type === 'LINK') {
				const { fill, scaleY, scaleX, interactionType, jumpType, link } = target

				this.tabList = [
					{ name: '样式', disabled: false },
					{ name: '交互', disabled: true },
				]
				this.$set(this.value.controls, index, {
					left: parseFloat(left.toFixed(2)),
					top: parseFloat(top.toFixed(2)),
					height,
					type,
					width,
					name,
					code,
					scaleY,
					scaleX,
					canvasHeight,
					interactionType: interactionType || 'click',
					jumpType: jumpType || 'open',
					canvasWidth,
					fill,
					link,
				})
			}
			// 固定控件、自定义控件
			if (this.isInTypes(type)) {
				const {
					scaleY,
					scaleX,
					iconUrl,
					flipX,
					flipY,
					video,
					controlsDetails,
					interactive,
					imageStandards,
				} = target
				this.tabList = [
					{ name: '样式', disabled: false },
					{ name: '交互', disabled: false },
				]
				this.tabName = '样式'
				const current = this.value.controls[index]
				this.$set(this.value.controls, index, {
					colorObj: current ? current.colorObj : {},
					left: parseFloat(left.toFixed(2)),
					top: parseFloat(top.toFixed(2)),
					angle: parseFloat(angle.toFixed(2)),
					iconUrl,
					type,
					height: parseFloat(height.toFixed(2)),
					width: parseFloat(width.toFixed(2)),
					name,
					code,
					canvasHeight,
					flipX,
					flipY,
					canvasWidth,
					scaleY: parseFloat(scaleY.toFixed(2)),
					scaleX: parseFloat(scaleX.toFixed(2)),
					strokeWidth,
					stroke,
					video,
					sysCode: current && current.sysCode,
					stationCode: current && current.stationCode,
					itemRealCode: current && current.itemRealCode,
					itemValue: current && current.itemValue,
					itemData: (current && current.itemData) ?? [],
					scene: (current && current.scene) ?? [],
					clickable: (current && current.clickable) ?? true,
					controlsDetails,
					interactive,
					imageStandards,
				})
				// }
			}
		},

		// 删除相应的绑定值
		handleDelete(index) {
			this.value.controls[index] = null
		},

		handlePickImage(item) {
			backimg = item
			this.$refs.editor.setBackImage(item)
		},
		handlePickColor(item) {
			this.$refs.editor.setBackColor(item)
		},
		// 控件绑定数据
		handleBindingData(data) {
			this.$refs.editor.controlBindData({ left: 100, top: 100 }, data)
		},
		// 自定义控件删除确认
		handleCustomDeleteConfirm(id) {
			const objs = this.$refs.editor.getObjects()
			this.canDelete = !objs.some(item => {
				if (item.customControlId) {
					return item.customControlId === id
				}
				return false
			})
		},
		rename(name) {
			this.name = name
		},
		handleClick(from) {
			this.handleSave(from)
		},
		// 保存
		async handleSave(from) {
			// let img = new Image()
			if (this.$refs.editor.isDrawingMode) {
				this.$Message.info('请先结束当前绘制')
				return
			}

			if (from === 'back') {
				this.$router.push({
					path: '/editorList',
					query: { filterAuth: this.$route.query.filterAuth },
				})
				return
			}
			let svg = this.$refs.editor.toDataURL()
			let file = this.dataURLtoFile(svg, new Date().getTime() + '.jpg')
			svg = await this.upload(file)
			let back = this.$refs.editor.getBackgroundImage()
			const { refreshTime, backgroundColor, controls, rotate, appZoomFlag, pcZoomFlag } = this.value
			let data = {
				backgroundImageId: backimg.id || '',
				canvasStyle: JSON.stringify({
					backgroundColor,
					canvasWidth: this.canvasWidth || 1385,
					canvasHeight: this.canvasHeight || 889,
					canvasScale: back ? back.scaleY : '',
					refreshTime,
					rotate,
					appZoomFlag,
					pcZoomFlag,
				}),
				controlsBinds: [],
				name: this.name,
				thumbnail: svg,
				id: this.$route.query.id,
			}

			// const canvas = this.$refs.editor.getCanvas()
			// // 画布失去焦点 为了保证获取所有元素时 元素的left、top值正确
			// canvas.discardActiveObject()
			// const allObjects = canvas.getObjects()

			controls.forEach(item => {
				if (!item) {
					return false
				}
				const {
					type,
					left,
					top,
					height,
					width,
					angle,
					index,
					points,
					color,
					red,
					green,
					blue,
					fontSize,
					backgroundColor,
					content,
					scaleY,
					scaleX,
					fill,
					stroke,
					strokeWidth,
					strokeDashArray,
					animate,
					clickable,
					itemName,
					stationCode = '',
					itemRealCode,
					sysCode = '',
					text,
					hideTitle,
					hideUnit = false,
					value,
					unit,
					iconUrl,
					flipX,
					flipY,
					fontWeight,
					fontStyle,
					underline,
					linethrough,
					overline,
					charSpacing,
					textAlign,
					itemData,
					scene,
					video,
					interactive,
					controlsDetails,
					link,
					interactionType,
					jumpType,
				} = item
				// const { left, top } = allObjects[idx]
				let baseStyle = ''
				if (this.isInTypes(type)) {
					const { colorObj } = item
					baseStyle = JSON.stringify({
						type,
						left,
						top,
						height,
						width,
						angle,
						index,
						color,
						red,
						green,
						blue,
						scaleX,
						scaleY,
						colorObj,
						sysCode,
						stationCode,
						itemRealCode,
						flipX,
						flipY,
						stroke,
						strokeWidth,
						itemData,
						scene,
						clickable,
						video,
						interactive,
						controlsDetails,
					})
				} else if (['TEXT', 'PLAINTEXT'].includes(type)) {
					baseStyle = JSON.stringify({
						type,
						left: parseInt(left),
						top: parseInt(top),
						height,
						width,
						angle,
						index,
						fontSize,
						backgroundColor,
						content,
						fill,
						scaleX,
						scaleY,
						itemName,
						clickable,
						itemRealCode,
						stationCode,
						sysCode,
						text,
						hideTitle,
						hideUnit,
						value,
						unit,
						fontWeight,
						fontStyle,
						underline,
						linethrough,
						overline,
						charSpacing,
						textAlign,
						strokeWidth,
						stroke,
						controlsDetails,
					})
				} else if (type === 'LINE_SEGMENT') {
					const { initValue, animateColor } = item

					let newStrokeDashArray = ''
					if (strokeDashArray === 1) {
						newStrokeDashArray = []
					} else if (strokeDashArray === 2) {
						newStrokeDashArray = [5, 5]
					} else {
						newStrokeDashArray = strokeDashArray
					}
					baseStyle = JSON.stringify({
						type,
						left,
						top,
						points,
						index,
						stroke,
						strokeWidth,
						animate,
						strokeDashArray: newStrokeDashArray,
						initValue,
						animateColor,
					})
				} else if (['SQUARE', 'ROUND'].includes(type)) {
					baseStyle = JSON.stringify({
						type,
						left,
						top,
						fill,
						index,
						scaleX,
						scaleY,
						stroke,
						strokeWidth,
					})
				} else if (type === 'LINK') {
					baseStyle = JSON.stringify({
						type,
						left,
						top,
						fill,
						index,
						scaleX,
						scaleY,
						stroke,
						strokeWidth,
						link,
						interactionType,
						jumpType,
					})
				}

				data.controlsBinds.push({
					baseStyle,
					controlsType: item.type,
					defAction: '',
					name: '',
					iconUrl: type.indexOf('controls-') !== -1 ? iconUrl : '',
					specialStyle: '',
					code: '',
					sysCode,
					stationCode,
				})
			})
			const url = window.location.origin
			if (data.id) {
				update({ ...data }).then(res => {
					this.$Message.info('操作成功')

					switch (from) {
						case 'save':
							// this.$router.push('/editorList')
							// this.$refs.left.getTableData()
							break
						case 'priview':
						case 'pub':
							this.$router.push({
								path: '/editorList',
								query: {
									filterAuth: this.$route.query.filterAuth,
								},
							})
							window.open(url + '/priview?id=' + res.result.id)
							break
						default:
							break
					}
				})
			} else {
				create({ ...data }).then(res => {
					this.$Message.info('操作成功')
					switch (from) {
						case 'save':
							this.$router.push({
								path: '/editorConfig',
								query: {
									id: res.result.id,
									filterAuth: this.$route.query.filterAuth,
								},
							})
							break
						case 'priview':
						case 'pub':
							// this.$router.push('/editorList')
							// this.$router.go(-1)
							window.open(url + '/priview?id=' + res.result.id)
							break
						default:
							break
					}
				})
			}
		},

		// 上传图片
		upload(file) {
			if (!file) {
				return
			}
			const { type } = file
			if (!['image/jpeg', 'image/png', 'image/svg+xml'].includes(type)) {
				this.$Message.error('请选择正确的控件类型')
				return
			}
			let data = new FormData()

			data.append('files', file)
			return this.$axios.post('/backgroundImage/image/upload', data).then(res => {
				return res.result[0].imageUrl
			})
		},

		//将base64转换为文件
		dataURLtoFile(dataurl, filename) {
			var arr = dataurl.split(','),
				mime = arr[0].match(/:(.*?);/)[1],
				bstr = atob(arr[1]),
				n = bstr.length,
				u8arr = new Uint8Array(n)
			while (n--) {
				u8arr[n] = bstr.charCodeAt(n)
			}
			return new File([u8arr], filename, { type: mime })
		},

		drawShape(type) {
			switch (type) {
				case 'line':
					this.$refs.editor.drawLine()
					break
				case 'rect':
					this.$refs.editor.createRect()
					break
				case 'link':
					this.$refs.editor.createRect({
						type: 'LINK',
						fill: 'pink',
						opacity: 0.5,
					})
					break
				case 'circle':
					this.$refs.editor.createCircle()
					break
				case 'text':
					this.$refs.editor.createTextBox('文本', {
						type: 'PLAINTEXT',
						fill: '#0000ff',
						// lockScalingX: true,
						// lockScalingY: true,
					})
					break
				default:
					break
			}
		},

		// 改变控件属性
		changeControl(attr, value, opts) {
			// 控件调整宽高 更新对应的scaleX scaleY值
			if (opts && opts.type) {
				const { type, index, key, value } = opts
				if (type === 'controls') {
					this.$set(this.value.controls[index], key, value)
				}
			}
			this.$refs.editor.changeControl(attr, value, opts)
		},

		// 修改属性
		changeImageColor(val, gamma) {
			this.$refs.editor.setImageGamma(val, gamma)
		},

		// 判断type是否在controlTypes中
		isInTypes(type) {
			return controlTypes2AttrtempMap[type] || `${type}`.indexOf('controls-') !== -1
		},
	},
	beforeDestroy() {
		backimg = {}
		this.value = {
			controls: [],
			points: {},
		}
	},
}
</script>
<style lang="less" scoped>
.config-page {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	overflow-y: hidden;
	.config-content {
		flex: 1;
		position: relative;
		overflow: auto;
	}
	.right-aside {
		width: 280px;
		border-left: 1px solid #e8e8e8;
	}
}
::v-deep {
	.ivu-drawer-close {
		top: -1px;
		right: -1px;
	}
}
</style>
