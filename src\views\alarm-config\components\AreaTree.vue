<!--
 * @Description: 告警配置左侧区域选择树
 * @Author: fengjialing
 * @Date: 2025-04-15 19:18:01
 * @LastEditors: fengjialing
 * @LastEditTime: 2025-04-15 19:18:01
-->

<template>
	<div class="personnel-config-select">
		<WaterTree :treeData="treeData" @change-select="handleChangeSelect"></WaterTree>
	</div>
</template>

<script>
import WaterTree from '@/components/gc-multiple-tree'
// import { getDepartment } from '@/api/common.js'
export default {
	name: 'personnel-config-select',
	components: {
		WaterTree,
	},
	props: {
		// // 已选择的value数组
		// selectedValue: Array,
		// // 已选择的label数组 (用于标签展示, 顺序要与value对应)
		// selectedLabel: Array,
	},
	data() {
		return {
			currentNode: {},
			ownershipList: [],
			usersList: [],
			firstTree: '',
			treeData: [],
		}
	},
	computed: {},
	created() {
		this._getDepartment()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 选中树形图节点
		changeTree(selectedNodes, currentNode) {
			this.page.pageNum = 1
			this.currentNode = currentNode

			this._getUsers()
		},
		handleChangeSelect(selectList) {
			this.$emit('change-select', selectList)
		},
		// 过滤
		_filterUsers(list) {
			if (!list || !list.length) return []
			return list.map(item => {
				return {
					...item,
					title: item.name,
					expand: true,
					selected: item.id === this.firstTree,
					children: this._filterUsers(item.nodes),
				}
			})
		},
		clearSelectData() {
			this.$refs.multifunctionalSelect.clearSelectValue()
		},
		// 部门组织树
		_getDepartment() {
			this.treeData = [
				{
					checked: false,
					children: [
						{
							checked: false,
							expand: false,
							fullTitle: ' 运行调度 一级 总览',
							id: '9999',
							nodeCode: 'SC-zhuangYuan2',
							nodeType: 'WATER_WORKS',
							ownership: '2W01',
							parentId: '37338239',
							selected: false,
							subSysCode: 'zt-dd',
							title: '总览',
						},
					],
					expand: false,
					fullTitle: ' 运行调度 一级',
					id: '37338239',
					nodeType: 'area',
					ownership: '2W01',
					parentId: '0',
					selected: false,
					title: '一级',
				},
				{
					checked: false,
					children: [
						{
							checked: false,
							expand: false,
							fullTitle: ' 运行调度 一级测试 45350536测试',
							id: 'b115bc120f3a43179d6af9a603ab97b7',
							nodeCode: 'XFS-45350536',
							nodeType: 'FIRE_HYDRANTS_POINT',
							ownership: '2W01',
							parentId: '37338240',
							selected: false,
							subSysCode: 'zt-xfs',
							title: '45350536测试',
						},
						{
							checked: false,
							expand: false,
							fullTitle: ' 运行调度 一级测试 45350538',
							id: '90413cdcada04f129c4972de7944b069',
							nodeCode: 'XFS-45350538',
							nodeType: 'FIRE_HYDRANTS_POINT',
							ownership: '2W01',
							parentId: '37338240',
							selected: false,
							subSysCode: 'zt-xfs',
							title: '45350538',
						},
						{
							checked: false,
							expand: false,
							fullTitle: ' 运行调度 一级测试 45350539',
							id: '605178d888b4446c86743c62ce6ee848',
							nodeCode: 'XFS-45350539',
							nodeType: 'FIRE_HYDRANTS_POINT',
							ownership: '2W01',
							parentId: '37338240',
							selected: false,
							subSysCode: 'zt-xfs',
							title: '45350539',
						},
					],
					expand: false,
					fullTitle: ' 运行调度 一级测试',
					id: '37338240',
					nodeType: 'area',
					ownership: '2W01',
					parentId: '0',
					selected: false,
					title: '一级测试',
				},
			]
			// getDepartment().then(res => {
			// 	const data = res.result

			// 	if (data) {
			// 		this.firstTree = data[0].id
			// 		this.currentNode = data[0]
			// 		this._getUsers()
			// 		this.ownershipList = this._filterUsers(data)
			// 	}
			// })
		},
	},
}
</script>

<style lang="less" scoped>
.personnel-config-select {
	position: relative;
	user-select: none;
}
</style>
