<template lang="pug">
.video-monitor
	.header-wrap
		es-header.header(title='视频监控')
	.video-monitor-wrap
		.video-monitor-tree
			areaTree.tree-box(
				ref='tree',
				:data='treeData',
				:load='loadNode',
				prefix-icon,
				:defaultExpandedKeys='defaultExpandedKeys',
				lazy,
				highlight-current,
				search-type='lazy',
				@node-click='nodeClick'
			)
			platform-control(:id='cameraIndexCode')

		.video-monitor-content
			video-native-hk(ref='HKRef', :list='list', @cb-integration='handleCb')
</template>
<script>
import areaTree from './components/AreaTree.vue'
import PlatformControl from './components/platform-control'
import VideoNativeHk from '@/components/gc-video-native-hk/VideoNativeHk.vue'
import { integratresources, getVideoTree } from '@/api/security-manage'
export default {
	name: 'video-monitor',
	components: {
		areaTree,
		PlatformControl,
		VideoNativeHk,
	},
	data() {
		return {
			selectedNode: {},
			treeData: [],
			// 当前选中的视频code
			cameraIndexCode: '',
			list: [],
			pathList: [],
			defaultExpandedKeys: [],
		}
	},
	mounted() {
		this.initPage()
	},
	methods: {
		initPage() {
			const id = this.$route.query.areaId
			const code = this.$route.query.code
			if (code && id && code !== undefined && id !== undefined) {
				this.defaultExpandedKeys = [Number(id)]
				this.getTreeList(code, [])
			}
		},
		getTreeList(type, cameraList) {
			integratresources({
				type,
			}).then(res => {
				const { result = [] } = res
				const data = this._setTreeData(result, cameraList)
				this.treeData = data
			})
		},
		// 加载节点
		loadNode(node, resolve) {
			const { cameraList, id, hasChild } = node.data
			if (!hasChild) {
				return resolve(this._setTreeData([], cameraList))
			} else {
				getVideoTree({
					id,
				}).then(res => {
					const { result = [] } = res
					const data = this._setTreeData(result, cameraList)
					return resolve(data)
				})
			}
		},
		// 设置树形图数据
		_setTreeData(result, cameraList) {
			let data = result.map(item => {
				return {
					...item,
					type: 'area',
					leaf: item.cameraList && item.cameraList.length ? false : !item.hasChild,
				}
			})
			data.sort((a, b) => {
				return a.sort - b.sort
			})
			if (cameraList && cameraList.length) {
				data.push(
					...cameraList.map(item => {
						return {
							...item,
							type: 'equipment',
							name: item.cameraName,
							leaf: true,
						}
					}),
				)
			}

			return data
		},
		// 节点被点击时的回调
		nodeClick(data) {
			if (data) {
				const { cameraIndexCode, cameraList, type } = data

				this.selectedNode = data

				this.$refs.HKRef.stopAllPreview()
				// 点击区域
				if (type === 'area') {
					if (cameraList && cameraList.length) {
						this.list = cameraList.slice(0, 9)
						this.list.forEach((item, index) => {
							this.$refs.HKRef.playVideo(index + 1, item.cameraIndexCode)
							this.$refs.HKRef.resizeVideo()
							this.$refs.HKRef.oWebControl.JS_CuttingPartWindow(372, 200) // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
						})
					}
				} else {
					// 点击设备
					this.$refs.HKRef.playVideo(1, cameraIndexCode)
					this.$refs.HKRef.resizeVideo()
					this.$refs.HKRef.oWebControl.JS_CuttingPartWindow(372, 200) // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
				}
			}

			// 选中状态
			this.$refs.tree.$refs['el-tree'].setCurrentKey(this.selectedNode.id)
		},
		handleCb(code) {
			this.cameraIndexCode = code
		},
	},
}
</script>
<style lang="less" scoped>
.video-monitor {
	width: 100%;
	height: 100%;
	.header-wrap {
		padding: 0 16px;
		.header {
			border-bottom: 1px solid #efefef;
		}
	}
	.video-monitor-wrap {
		display: flex;
		width: 100%;
		height: calc(100% - 40px);
		.video-monitor-tree {
			display: flex;
			flex-direction: column;
			flex-shrink: 0;
			width: 200px;
			padding-top: 16px;
			.tree-box {
				padding: 0 16px;
				flex: 1;
			}
		}
		.video-monitor-content {
			flex-grow: 1;
			border-left: 1px solid #efefef;
			height: 100%;
			background-color: #333;
		}
	}
}
</style>
