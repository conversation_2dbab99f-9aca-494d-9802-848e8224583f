<!--
 * @Description: 药品用量
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-31 11:00:24
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-08 17:19:29
-->
<template lang="pug">
.drug-fill 
	water-row(justify='flex-end', align='center')
		.drug-fill-title 月份:
		DatePicker(
			v-model='date',
			:editable='false',
			format='yyyy-MM',
			type='month',
			:clearable='false',
			style='width: 215px',
			:options='options',
			@on-change='handleQuery()',
			placement='bottom-end'
		)
		Button.water-margin-left-16(v-if='state === "fill"', type='primary', @click='handleSave()', :loading='buttonLoading') 提交
		Button.water-margin-left-16(v-if='state === "record"', type='primary', @click='handleQuery()') 查询
		Button.water-margin-left-16(v-if='state === "record"', type='primary', @click='handleExport') 导出
	#qrcodeDowm(style='display: none')
	WaterTable.drug-fill-table(border, :columns='columns', :data='tableData', :loading='loading')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { EventBus } from '@/utils/eventBus.js'
import { exportFile } from '@/utils/function.js'
import { queryDrugData, insertDrugData } from '@/api/water-screen-data.js'
export default {
	components: { WaterTable, WaterRow },
	props: ['state'],
	data() {
		return {
			date: this.$moment(new Date(`${new Date().getFullYear()}-${new Date().getMonth() + 1}`)).format('yyyy-MM'),
			buttonLoading: false,
			loading: false,
			tableData: [],
			columns: [],
			typeEnumObj: {
				0: 'PAC',
				1: '次氯酸钠',
				2: '石灰',
				3: 'PAM',
			},
			options: {
				disabledDate(date) {
					return date.getTime() > new Date().getTime()
				},
			},
		}
	},
	mounted() {
		this.getColumns()
		this.handleQuery()
		EventBus.$on('fresh-drug-record', () => {
			this.state === 'record' && this.handleQuery()
		})
	},
	methods: {
		handleQuery() {
			queryDrugData({ date: this.$moment(this.date).format('YYYY-MM') })
				.then(res => {
					const { result = [] } = res
					this.tableData = []
					result.forEach((item, index) => {
						this.tableData.push({
							...item,
							type: this.typeEnumObj[index],
							editable: this.state === 'fill',
						})
					})
				})
				.catch(() => {
					this.tableData = []
				})
		},

		//提交
		handleSave() {
			try {
				this.buttonLoading = true
				const list = []
				this.tableData.forEach(item => {
					const {
						stationName = '',
						limeUse = '',
						naClOUse = '',
						pacUse = '',
						pamUse = '',
						limeDrugConsumption = '',
						naClODrugConsumption = '',
						pacDrugConsumption = '',
						pamDrugConsumption = '',
						stationId = '',
					} = item
					list.push({
						stationName,
						stationId,
						limeUse,
						naClOUse,
						pacUse,
						pamUse,
						limeDrugConsumption,
						naClODrugConsumption,
						pacDrugConsumption,
						pamDrugConsumption,
					})
				})
				const params = {
					date: this.$moment(this.date).format('YYYY-MM'),
					list,
				}
				insertDrugData(params)
					.then(() => {
						this.$Message.success('提交成功!')
						this.buttonLoading = false
						this.handleQuery()
						EventBus.$emit('fresh-drug-record')
					})
					.catch(() => {
						this.buttonLoading = false
					})
			} catch {
				this.buttonLoading = false
			}
		},
		handleExport() {
			const baseUrl = `${location.origin}/api`
			let url =
				baseUrl + '/waterPlat/fillData/selectDrugDosageExport?date=' + this.$moment(this.date).format('YYYY-MM')
			exportFile(url)
		},
		getColumns() {
			const columns = [
				{
					title: '类型',
					key: 'type',
					align: 'center',
					children: [
						{
							title: '水厂名称',
							key: 'stationName',
							align: 'center',
							width: 120,
						},
					],
				},
			]
			const columnNameList = [
				{
					title: 'PAC',
					itemCode: 'pacUse',
					itemValue: 'pacDrugConsumption',
				},
				{
					title: '次氯酸钠',
					itemCode: 'naClOUse',
					itemValue: 'naClODrugConsumption',
				},
				{
					title: '石灰',
					itemCode: 'limeUse',
					itemValue: 'limeDrugConsumption',
				},
				{
					title: 'PAM',
					itemCode: 'pamUse',
					itemValue: 'pamDrugConsumption',
				},
			]
			if (columnNameList && columnNameList.length) {
				columnNameList.forEach(item => {
					const obj = {
						title: item.title,
						key: 'typeName',
						align: 'center',
						children: [
							{
								title: '用量(kg)',
								key: item.itemCode,
								align: 'center',
								minWidth: 120,
								render: (h, params) => {
									const { editable } = params.row
									return editable
										? h('Input', {
												props: {
													value: params.row[item.itemCode],
													maxlength: 18,
												},
												on: {
													'on-change': e => {
														this.valueChange(e)
														const value = e.target.value
														this.handleInputValue(params.index, value, item.itemCode)
													},
													'on-keyup': e => {
														this.valueChange(e)
													},
												},
										  })
										: h('span', params.row[item.itemCode])
								},
							},
							{
								title: '千吨水药耗',
								key: item.itemValue,
								align: 'center',
								minWidth: 120,
								render: (h, params) => {
									const { editable } = params.row
									return editable
										? h('Input', {
												props: {
													value: params.row[item.itemValue],
													maxlength: 18,
												},
												on: {
													'on-change': e => {
														this.valueChange(e)
														const value = e.target.value
														this.handleInputValue(params.index, value, item.itemValue)
													},
													'on-keyup': e => {
														this.valueChange(e)
													},
												},
										  })
										: h('span', params.row[item.itemValue])
								},
							},
						],
					}
					columns.push(obj)
				})
			}
			this.columns = columns
		},
		//正则
		valueChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			e.target.value = e.target.value.replace(/^(([0-9]{15}\d))(.\d{1,2})?$/, '')
		},
		//输入值
		handleInputValue(index, value, key) {
			this.tableData[index][key] = value
			// const item = this.tableData[index]
			// this.tableData.splice(index, 1, {
			// 	...item,
			// 	[key]: value,
			// })
		},
	},
}
</script>
<style lang="less" scoped>
.drug-fill {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-title {
		margin-right: 4px;
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
}
</style>
