<template>
	<div class="device-category-search">
		<div class="category-tab">
			<div class="first-category">
				<div
					class="first-category-item"
					v-for="item in categoryTab"
					:key="item.code"
					:class="{
						active: item.deviceTypeCode == activeTab,
					}"
					@click="categoryClick(item)"
				>
					<div class="icon-box">
						<i class="iconfont" :class="item.icon"></i>
					</div>
					<span class="category-text">{{ item.name }}</span>
				</div>
			</div>
		</div>
		<!-- 列表展示 -->
		<list ref="list" :activeTab="activeTab" @initList="list => $emit('getInitView', list)"></list>
	</div>
</template>

<script>
import List from './List.vue'
// import { apiGetCategoryAndStatus } from '@/api/iotOnePicture.js'
import { queryTemplateByName } from '@/api/base-item'
// import { mapGetters, mapActions } from 'vuex'
import { mapGetters } from 'vuex'
// const activeColorObj = {
// 	0: '#748AFE',
// 	normal: '#29C287',
// 	offline: '#AFB4CC',
// 	alarm: '#FF9419',
// 	fault: '#E5662E',
// }

export default {
	name: 'CategoryTab',
	components: { List },
	props: {},
	data() {
		return {
			activeTab: null,
			categoryTab: [],
			deviceStatusArr: [],
		}
	},

	computed: {
		...mapGetters({
			websocket: 'alarmPush/websocket',
			// boardUpdate: 'alarmPush/boardUpdate',
		}),
	},

	watch: {
		// boardUpdate(val) {
		// 	if (val) {
		// 		// 判断当前是否处于综合监控-告警
		// 		if (this.activeTab == '0') {
		// 			this.$refs.list.showList = true
		// 			this.$refs.list.alarmTypeSearch = ''
		// 			this.$refs.list.alarmCreateTimeFlag = 1
		// 			this.$refs.list.getDeviceList(1)
		// 		} else {
		// 			this.activeTab = '0'
		// 		}
		// 		this.$store.commit('alarmPush/SET_BOARD_UPDATE', false)
		// 	}
		// },
	},
	mounted() {
		this.getCategoryAndStatus()
		// if (this.$has('monitor:run:dashboard') && !this.websocket) {
		// 	this.initWebSocket()
		// }
	},

	methods: {
		// ...mapActions({
		// 	initWebSocket: 'alarmPush/initWebSocket',
		// 	clearWebSocket: 'alarmPush/clearWebSocket',
		// }),

		getCategoryAndStatus() {
			queryTemplateByName().then(res => {
				this.categoryTab = res.result
					? res?.result?.map(item => {
							return {
								...item,
								icon: 'icon-zonghejiankong',
							}
					  })
					: []
				this.activeTab = this.categoryTab[0]?.deviceTypeCode
			})
		},

		categoryClick(item) {
			this.activeTab = item.deviceTypeCode
		},
	},

	beforeDestroy() {
		// this.clearWebSocket()
	},
}
</script>
<style lang="less" scoped>
.category-tab {
	height: 450px;
	background: #fff;
	position: absolute;
	top: 88px;
	left: 20px;
	border-radius: 8px;
	padding: 30px 20px;
	display: flex;
	overflow: auto;
	.first-category {
		height: 100%;
		padding: 0 10px 0 10px;
		&-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			cursor: pointer;
			.icon-box {
				width: 46px;
				height: 46px;
				background: #e6e7f0;
				border-radius: 6px;
				display: flex;
				justify-content: center;
				align-items: center;
				i {
					font-size: 30px;
					color: #98a1cb;
				}
			}
			.category-text {
				display: inline-block;
				font-size: 14px;
				font-weight: 600;
				color: #333333;
				line-height: 21px;
				margin-top: 8px;
			}
		}
		&-item.active {
			.icon-box {
				background: rgba(77, 107, 255, 0.12);
				i {
					color: #4d6bff;
				}
			}
			.category-text {
				color: #4d6bff;
			}
		}
		.first-category-item + .first-category-item {
			padding-top: 30px;
		}
	}
}
</style>
