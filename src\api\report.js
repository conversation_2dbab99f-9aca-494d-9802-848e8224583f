import { GET, POST } from '@/utils/request'

// 获取报表列表
export function getReportList(params) {
	return GET({
		url: '/smart/report/configuration/queryAllByLimit',
		params,
	})
}

// 报表删除
export const deleteReportById = params => {
	return POST({
		url: '/smart/report/configuration/deleteReport',
		params,
	})
}

// 更新报表
export const updateReport = params => {
	return POST({
		url: '/smart/report/configuration/saveOrUpdate',
		params,
	})
}

// 获取角色列表
export const getRolesList = params => {
	return GET({
		url: '/smart/report/configuration/queryRoles',
		params,
	})
}

// 获取报表列表
export const getReportConfigList = () => {
	return GET({
		url: '/smart/report/configuration/queryUrls',
	})
}

// 角色和报表的关联关系 - 报表权限配置
export const saveOrUpdateRoleReport = params => {
	return POST({
		url: '/smart/report/configuration/saveOrUpdateRoleReport',
		params,
		requestType: 'json',
	})
}

// 获取报表展示左侧列表数据
export const getReportShowList = params => {
	return GET({
		url: '/smart/report/configuration/queryUrlsByConditiion',
		params,
	})
}

// 查询所属业务列表
export const getBusinessList = params => {
	return GET({
		url: '/smart/report/business/list',
		params,
	})
}

// 删除某个所属业务数据
export const deleteBusiness = params => {
	return POST({
		url: '/smart/report/business/delete',
		params,
	})
}

//  新增、编辑所属业务数据
export const updateBusiness = params => {
	return POST({
		url: '/smart/report/business/addOrEdit',
		params,
	})
}
