<template lang="pug">
//- 内容
.content-wrapper(ref='wrapper')
	fabric#canvas(
		@dragover.prevent,
		@click.stop,
		ref='canvas',
		:width='width',
		:height='height',
		@mouse:down='handleMouseDown',
		@mouse:dblclick='handleDbMouseDown',
		@mouse:wheel='handleMouseWheel'
	)

	//- 动画曲线
	svg.priview-flow-svg(
		xmlns='http://www.w3.org/2000/svg',
		version='1.1',
		v-if='svgFlag',
		style='width: 1385px; height: 889px'
	)
		template(v-for='(line, index) in animateLines')
			polyline.polyline(:key='index', :style='{ stroke: line.animateColor }', :points='getPoints(line)')

	//- 曲线弹窗
	Modal.curve-modal(v-model='showCurveModal', :title='curveModalData.itemName', footer-hide, width='1010')
		flow-chart-popup(v-if='showCurveModal', :data='curveModalData')

	//- 视频弹窗
	Modal(v-model='showVideoModal', width='800', :title='videoModalData.name', footer-hide)
		div(style='height: 500px')
			video-h5(v-if='showVideoModal && videoModalData.url', :url='videoModalData.url')
	//- 工艺图弹框
	Modal.curve-process(v-model='showProcessModal', title='工艺图', footer-hide, fullscreen, width='1010')
		.process
			iframe.iframe.auto(v-if='iframeUrl', :src='iframeUrl')
			.no-process.auto(v-else) 暂无链接，请配置
</template>

<script>
import Vue from 'vue'
import { controlTypes2AttrtempMap } from '../../enum'
import FlowChartPopup from './FlowChartPopup.vue'
import fabricC from '@/components/gc-fabric'
import { fabric } from 'fabric'
import { itemDate } from '@/api/editor'
import VideoH5 from '@/components/gc-video-h5/VideoH5.vue'
import VideoPanel from './VideoPanel.vue'

let controls = []
export default {
	components: {
		fabric: fabricC,
		FlowChartPopup,
		VideoH5,
		VideoPanel,
	},
	props: {
		appZoomFlag: {
			type: Boolean,
			default: false,
		},
		imgWidth: {
			type: Number,
			default: 1385,
		},
		imgHeight: {
			type: Number,
			default: 889,
		},
	},
	computed: {
		deviceType() {
			return this.$route.query.deviceType || 'pc'
		},
	},
	watch: {
		appZoomFlag(val) {
			if (val && this.deviceType === 'app') {
				console.log('缩放。。。')
				this.handleMobileScale()
			}
		},
	},
	data() {
		return {
			imgUrl: '',
			width: 300,
			height: 500,
			// 前一个circle实例 为了放置后面线段的实例
			prevCircleInstance: {},
			type: null,
			animateLines: [],

			// 曲线弹窗
			showCurveModal: false,
			curveModalData: {
				itemName: '',
				itemRealCode: '',
			},
			// 视频弹窗
			showVideoModal: false,
			// 工艺图弹框
			showProcessModal: false,
			iframeUrl: '',
			videoModalData: {
				url: '',
				platformId: '',
				platformName: '--',
				name: '--',
				code: '',
				sourceName: '',
			},
			svgFlag: true,
		}
	},
	created() {
		this.width = this.imgWidth
		this.height = this.imgHeight
	},
	mounted() {
		// document.getElementsByClassName('flow-svg')[0].style['margin-left'] =
		// 	'calc(50% - ' + 1385 / 2 + 'px)'
		// document.getElementsByClassName('flow-svg')[0].style['margin-top'] =
		// 	'calc(50% - ' + 889 / 2 + 'px)'

		if (this.deviceType === 'app') {
			// 移动端 拖拽
			this.handleMobileDrag()

			// this.$nextTick(() => {
			// 	// 移动端 缩放
			// 	this.appZoomFlag && this.handleMobileScale()
			// })
		}
	},
	methods: {
		async setBackImage(item, flag) {
			return new Promise(resolve => {
				const { imageUrl, id, canvasScale } = item
				let img = new Image()
				if (!imageUrl) resolve()
				img.src = imageUrl
				img.onload = async () => {
					let width = img.naturalWidth
					let height = img.naturalHeight
					let xS = this.width / width
					let yS = this.height / height
					let scale = xS > yS ? yS : xS
					let options = {
						imgUrl: imageUrl,
						imgId: id,
						width: width,
						height: height,
						opacity: 1,
						scaleX: flag ? canvasScale : scale,
						scaleY: flag ? canvasScale : scale,
					}
					// 可跨域设置
					await this.$refs.canvas.test(img, options)

					// this.$refs.canvas.setCanvas(width * scale, height * scale)
					resolve()
				}
			})
		},
		setBackColor(item) {
			this.$refs.canvas.setBackgroundColor(item)
		},

		// 预览方法
		async priviewRender(item, selectable = false, evented = false, extraData) {
			const { controlsType, baseStyle, canvasWidth, canvasHeight, iconUrl } = item
			const style = JSON.parse(baseStyle)
			const { type, width, height, top, left, flipX, flipY, scaleX, scaleY, stroke, strokeWidth, video } = style

			// 绘制视频面板数据
			if (type === 'VIDEO_PANEL') {
				this.createVideoPanel({
					...extraData,
					left,
					top,
					width,
					height,
					scaleX,
					scaleY,
					video,
				})
			}

			let control = null
			// 自定义控件类型
			if (controlTypes2AttrtempMap[controlsType] || controlsType.indexOf('controls-') !== -1) {
				const {
					angle,
					color,
					red,
					green,
					blue,
					scaleX,
					scaleY,
					stationCode,
					itemRealCode,
					sysCode,
					type,
					colorObj,
					itemData,
					scene,
					video,
					clickable,
					interactive,
				} = style
				if (clickable || clickable === undefined) {
					evented = true
				}
				let imgUrl = controlTypes2AttrtempMap[controlsType]?.url || iconUrl
				control = await this.$refs.canvas.createImage(imgUrl, {
					angle,
					top,
					left,
					color,
					red,
					green,
					blue,
					scaleX,
					scaleY,
					index: controls.length,
					stationCode,
					itemRealCode,
					sysCode,
					type,
					colorObj,
					canvasWidth,
					canvasHeight,
					selectable,
					evented,
					flipX,
					flipY,
					stroke,
					strokeWidth,
					iconUrl,
					itemData,
					scene,
					video,
					hoverCursor: evented ? 'pointer' : 'default',
					interactive,
				})
				this.$refs.canvas.toTopLayer(control)
			} else if (['TEXT', 'PLAINTEXT'].includes(controlsType)) {
				const {
					backgroundColor,
					fontSize,
					clickable,
					fill,
					angle,
					itemName,
					stationCode,
					itemRealCode,
					sysCode,
					type,
					text,
					unit,
					value,
					hideTitle,
					hideUnit,
					width,
					strokeWidth,
					textAlign,
					// stroke,
					fontWeight,
					fontStyle,
					underline,
					linethrough,
					overline,
					charSpacing,
					scaleX,
					scaleY,
				} = style
				if (clickable || clickable === undefined) {
					evented = true
				}
				control = this.$refs.canvas.createTextbox(text, {
					width,
					top,
					left,
					type,
					angle,
					backgroundColor,
					fill,
					unit,
					value,
					fontSize,
					selectable,
					evented,
					lockScalingX: true,
					lockScalingY: true,
					stationCode,
					canvasWidth,
					canvasHeight,
					itemRealCode,
					sysCode,
					itemName,
					index: controls.length,
					hideTitle,
					hideUnit,
					editable: false,
					textAlign,
					// width,
					strokeWidth: strokeWidth,
					// stroke,
					fontWeight,
					fontStyle,
					underline,
					linethrough,
					overline,
					charSpacing,
					scaleX,
					scaleY,
					hoverCursor: evented ? 'pointer' : 'default',
					originX: 'left',
					originY: 'center',
				})
				this.$refs.canvas.toTopLayer(control)
			} else if (controlsType === 'LINE_SEGMENT') {
				const {
					points,
					stroke,
					strokeWidth,
					strokeDashArray,
					animate,
					left,
					top,
					initValue,
					animateColor,
				} = style
				if (animate) {
					this.animateLines.push({ ...style })
				}
				control = this.$refs.canvas.createPolyline({
					xyData: points,
					opt: {
						type: 'LINE_SEGMENT',
						stroke: stroke || 'red',
						strokeWidth,
						strokeDashArray,
						index: controls.length,
						left,
						top,
						selectable,
						canvasWidth,
						canvasHeight,
						evented,
						initValue,
						animateColor,
					},
				})
				this.$refs.canvas.toBottomLayer(control)
			} else if (controlsType === 'SQUARE') {
				const { fill, scaleX, scaleY, opacity } = style
				control = this.$refs.canvas.createRect({
					type,
					fill,
					top,
					left,
					scaleX,
					scaleY,
					opacity,
					selectable,
					canvasWidth,
					canvasHeight,
					index: controls.length,
					evented,
					stroke: stroke || 'red',
					strokeWidth: strokeWidth || 1,
				})
			} else if (controlsType === 'ROUND') {
				const { fill, scaleX, scaleY } = style
				control = this.$refs.canvas.createCircle({
					fill,
					top,
					left,
					scaleX,
					scaleY,
					canvasWidth,
					canvasHeight,
					selectable,
					index: controls.length,
					evented,
				})
			} else if (controlsType === 'LINK') {
				const { fill, scaleX, scaleY, interactionType, jumpType, link } = style
				control = this.$refs.canvas.createRect({
					type,
					fill,
					top,
					left,
					scaleX,
					scaleY,
					selectable,
					canvasWidth,
					canvasHeight,
					index: controls.length,
					evented: true,
					stroke: stroke || 'red',
					strokeWidth: strokeWidth || 1,
					opacity: 0,
					hoverCursor: 'pointer',
					interactionType,
					jumpType,
					link,
				})
			}

			controls.push(control)
			return control
		},
		// api 查询数据
		itemData(params, item) {
			return itemDate(params).then(res => {
				const { result } = res
				let obj = {}

				result.forEach(it => {
					const { showItemCode, stationCode, sysCode } = it
					it.stationDataItem.forEach(data => {
						obj = {
							...item,
							...data,
							showItemCode,
							stationCode,
							sysCode,
							isShow: true,
						}
					})
				})
				return obj
			})
		},

		handleMouseDown(options) {
			if (!options.target) return
			console.log('发送消息', options.target)
			// 路由携带参数
			const { id: stationId = '', curve = 'in', video = 'in' } = this.$route.query

			const {
				type = '',
				link = '',
				itemData = [],
				interactionType,
				jumpType,
				video: videoData = {},
			} = options.target
			if (!type) return

			// 视频控件点击
			if (type === 'VIDEO') {
				if (!videoData.code || !videoData.platformId) return
				if (video === 'out') {
					window.parent.postMessage({ type, ...videoData }, '*')
				} else {
					// 展示公共平台视频弹窗
					if (videoData && videoData.url) {
						this.showVideoModal = true
						this.videoModalData = videoData
					}
				}
			} else if (type === 'LINK') {
				// console.log('test')
				// 未嵌套 则展示项目内部弹窗
				if (interactionType === 'click') {
					if (jumpType === 'replace') {
						// window.open(link, '_blank')
						window.location.href = link
					}
					if (jumpType === 'open') {
						window.open(link, '_blank')
					}
					if (jumpType === 'dialog') {
						this.showProcessModal = true
						this.iframeUrl = link
					}
				} else {
					return
				}
			} else {
				// 其他控件或元素（文本框）点击
				let params = {
					stationId: '',
					stationCode: '',
					itemName: '--',
					itemRealCode: '',
				}
				// 控件元素 绑定多个数据项 默认取第一个数据项的相关参数进行曲线展示
				if (itemData && itemData.length > 0) {
					const { stationCode, itemName, itemRealCode } = itemData[0]
					params = {
						stationId,
						stationCode,
						itemName,
						itemRealCode,
					}
				} else {
					const { stationCode, itemName, itemRealCode } = options.target
					params = {
						stationId,
						stationCode,
						itemName,
						itemRealCode,
					}
				}
				// 如果code值不存在 则不显示弹窗
				if (!params.stationCode || !params.itemRealCode) return

				if (curve === 'out') {
					window.parent.postMessage({ type, ...params }, '*')
				} else {
					// 未嵌套 则展示项目内部弹窗
					this.showCurveModal = true
					this.curveModalData = params
				}
			}
		},
		handleDbMouseDown(options) {
			if (!options.target) return
			console.log('发送消息11', options.target)

			const { type = '', link = '', interactionType, jumpType } = options.target
			if (!type) return

			if (type === 'LINK') {
				// console.log('test')
				// 未嵌套 则展示项目内部弹窗
				if (interactionType === 'dbclick') {
					if (jumpType === 'replace') {
						// window.open(link, '_blank')
						window.location.href = link
					}
					if (jumpType === 'open') {
						window.open(link, '_blank')
					}
					if (jumpType === 'dialog') {
						this.showProcessModal = true
						this.iframeUrl = link
					}
				}
			}
		},
		handleMouseWheel(opt) {
			console.log('🚀 ~ handleMouseWheel ~ options:', opt)
			let delta = opt.deltaY // 滚轮向上滚一下是 -100，向下滚一下是 100
			let zoom = this.$refs.canvas.canvas.getZoom() // 获取画布当前缩放值

			console.log('new', delta, zoom)
			// 控制缩放范围在 0.01~20 的区间内
			// zoom *= 0.999 ** delta
			zoom *= 0.9999 ** delta
			if (zoom > 20) zoom = 20
			if (zoom < 0.01) zoom = 0.01

			console.log('old', opt, opt.clientX, opt.clientY)

			// 设置画布缩放比例
			// 关键点！！！
			// 参数1：将画布的所放点设置成鼠标当前位置
			// 参数2：传入缩放值
			this.$refs.canvas.canvas.zoomToPoint(
				{
					x: opt.clientX, // 鼠标x轴坐标
					y: opt.clientY, // 鼠标y轴坐标
				},
				zoom, // 最后要缩放的值
			)
			// this.$refs.canvas.canvas.requestRenderAll()
			// this.$refs.canvas.canvas.setZoom(zoom)
		},
		changeObjControlAttr(obj, attr, value) {
			this.$refs.canvas.changeObjControlAttr(obj, attr, value)
			return obj
		},
		changeObjControl(obj, attr, value) {
			this.$refs.canvas.changeObjControl(obj, attr, value)
			return obj
		},

		toTopLayer(obj) {
			this.$refs.canvas.toTopLayer(obj)
		},

		// 获取 svg动画点坐标
		getPoints(line) {
			const { initValue = {}, points = [], left, top } = line
			let diffLeft = 0
			let diffTop = 0
			const canvas = this.$refs.canvas.canvas
			let arr = []
			let intervalTopNum = 0

			// 判断是否有偏移
			if (initValue) {
				const initLeft = initValue.left
				const initTop = initValue.top
				diffLeft = left - initLeft
				diffTop = top - initTop
			}

			// 区分svg背景和普通的图片背景
			if (canvas.backgroundImage) {
				if (canvas.backgroundImage.imgUrl.indexOf('.svg') !== -1) {
					intervalTopNum = 105
				} else {
					intervalTopNum = 55
				}
			} else {
				intervalTopNum = 0
			}
			intervalTopNum = 0
			//  小于931的105
			// console.log('canvas.backgroundImage.imgUrl', intervalTopNum)

			// 计算偏移量
			points.forEach(item => {
				arr.push(item.x + diffLeft + ',' + (item.y + intervalTopNum + diffTop))
			})
			return arr.join(' ')
		},

		// 根绝VIDEO_PANEL控件类型 创建视频面板
		createVideoPanel(params) {
			const { viewportTransform, zoomValue, left, top, width, height, scaleX, scaleY, video } = params
			const { x, y } = fabric.util.transformPoint({ x: left, y: top }, viewportTransform)
			const obj = {
				width: width * scaleX * zoomValue,
				height: height * scaleY * zoomValue,
				left: x,
				top: y,
				platformName: video.platformName,
				url: video.url,
			}
			const videoPanel = Vue.extend({
				render: h =>
					h(VideoPanel, {
						props: {
							data: obj,
						},
					}),
			})
			this.$refs.wrapper.appendChild(new videoPanel().$mount().$el)
		},

		// 绑定resiz事件 重新计算视频面板的位置信息
		handleVideoPanelPosition(zoomValue, viewportTransform) {
			const videoPanelDoms = document.querySelectorAll('.video-panel')
			if (videoPanelDoms.length === 0) return
			this.$nextTick(() => {
				const videoPanels = this.$refs.canvas.canvas.getObjects().filter(item => {
					return item.type === 'VIDEO_PANEL'
				})

				videoPanelDoms.forEach((item, index) => {
					const { width, height, scaleX, scaleY, top, left } = videoPanels[index]
					const { x, y } = fabric.util.transformPoint({ x: left, y: top }, viewportTransform)
					const w = width * scaleX * zoomValue
					const h = height * scaleY * zoomValue
					item.style.width = `${w}px`
					item.style.height = `${h}px`
					item.style.left = `${x - w / 2}px`
					item.style.top = `${y - h / 2}px`
				})
			})
		},

		// 移动端 拖拽
		handleMobileDrag() {
			// 记录拖动开始时的鼠标位置或触摸位置
			var lastPosX, lastPosY

			// 监听鼠标按下事件
			document.addEventListener('touchstart', opt => {
				// var pointer = this.canvas.getPointer(event.e);
				// lastPosX = pointer.x;
				// lastPosY = pointer.y;

				// const evt = opt.e;
				// debugger
				// console.log('evt', evt);
				// console.log('touchstart', opt, opt.touches[0].clientX)
				lastPosX = opt.touches[0].clientX
				lastPosY = opt.touches[0].clientY
				// this.canvas.requestRenderAll()
			})

			// 监听鼠标移动事件
			document.addEventListener('touchmove', opt => {
				if (lastPosX && lastPosY) {
					// const evt = opt.e;
					var deltaX = opt.touches[0].clientX - lastPosX
					var deltaY = opt.touches[0].clientY - lastPosY

					// 移动画布位置
					if (window.rotationAngle === 0) {
						this.$refs.canvas.canvas.relativePan(new fabric.Point(deltaX, deltaY))
					} else if (window.rotationAngle === 90) {
						// y = -x , x=y
						this.$refs.canvas.canvas.relativePan(new fabric.Point(deltaY, -deltaX))
					} else if (window.rotationAngle === 180) {
						// y = -y , x=-x
						this.$refs.canvas.canvas.relativePan(new fabric.Point(-deltaX, -deltaY))
					} else if (window.rotationAngle === 270) {
						// y = x , x=-y
						this.$refs.canvas.canvas.relativePan(new fabric.Point(-deltaY, deltaX))
					}

					// 更新上一次的位置
					lastPosX = opt.touches[0].clientX
					lastPosY = opt.touches[0].clientY
				}
			})

			// 监听鼠标抬起事件
			document.addEventListener('touchend', () => {
				lastPosX = null
				lastPosY = null
			})
		},
		// 移动端 缩放
		handleMobileScale() {
			// console.log(333, this.appZoomFlag)
			// window.addEventListener('resize', () => {
			// 	this.svgFlag = false
			// 	this.svgFlag = true
			// })
			var initialDistance = null

			// 监听触摸开始事件
			document.addEventListener('touchstart', function (event) {
				console.log('touchstart', event)
				if (event.touches.length === 2) {
					// 记录两个触摸点之间的初始距离
					var touch1 = event.touches[0]
					var touch2 = event.touches[1]
					initialDistance = Math.sqrt(
						Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2),
					)
				}
			})

			// 监听触摸移动事件
			document.addEventListener('touchmove', event => {
				if (event.touches.length === 2 && initialDistance !== null) {
					// 计算两个触摸点之间的当前距离
					var touch1 = event.touches[0]
					var touch2 = event.touches[1]
					var currentDistance = Math.sqrt(
						Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2),
					)

					// 计算距离变化
					var distanceChange = currentDistance - initialDistance

					// 根据距离变化模拟滚轮事件
					if (distanceChange > 0) {
						// 向上滚动
						// var wheelEvent = new WheelEvent('wheel', { deltaY: -100 });
						// document.dispatchEvent(wheelEvent);
						this.handleMouseWheel({
							clientX: (touch2.clientX + touch1.clientX) / 2,
							clientY: (touch2.clientY + touch1.clientY) / 2,
							deltaY: -100,
						})
					} else if (distanceChange < 0) {
						// 向下滚动
						// var wheelEvent = new WheelEvent('wheel', { deltaY: 100 });
						// document.dispatchEvent(wheelEvent);
						this.handleMouseWheel({
							clientX: (touch2.clientX + touch1.clientX) / 2,
							clientY: (touch2.clientY + touch1.clientY) / 2,
							deltaY: 100,
						})
					}

					// 更新初始距离
					initialDistance = currentDistance

					// 阻止默认的触摸滚动行为
					event.preventDefault()
				}
			})

			// 监听触摸结束事件
			document.addEventListener('touchend', function () {
				// 重置初始距离
				initialDistance = null
			})
		},
	},
	beforeDestroy() {
		controls = []
	},
}
</script>

<style lang="less" scoped>
.content-wrapper {
	position: relative;
	display: flex;
	flex: 1;
	justify-content: space-between;
	.button {
		position: absolute;
		z-index: 2;
		left: 50%;
		top: 8px;
	}
	.list-wraper,
	.site-list-wraper {
		width: 300px;
		height: 100%;
		overflow-y: auto;
		flex-shrink: 0;
		box-sizing: border-box;

		display: -webkit-flex;
		display: -ms-flexbox;
		display: -moz-box;
		display: -webkit-box;
		display: flex;
		-webkit-box-orient: vertical;
		-moz-box-orient: vertical;
		-webkit-flex-direction: column;
		flex-direction: column;

		.image-wrapper {
			padding: 20px;
			display: -ms-flexbox;
			display: -moz-box;
			display: -webkit-box;
			display: -webkit-flex;
			display: flex;

			flex-shrink: 0;
			box-pack: center;
			-webkit-box-pack: center;
			-moz-box-pack: center;
			-moz-box-pack: center;
			-webkit-justify-content: center;
			justify-content: center;
			box-align: center;
			-moz-box-align: center;
			-webkit-box-align: center;
			-webkit-align-items: center;
			align-items: center;
			border-bottom: 1px solid #eee;
			position: relative;
			img {
				height: 120px;
			}
		}
	}
	.site-list-wraper {
		border-right: 1px solid #efefef;
	}
	.list-wraper {
		border-left: 1px solid #efefef;
	}
	.priview-flow-svg {
		position: absolute;
		pointer-events: none;
		transform-origin: 0 0;
		left: 50%;
		top: calc(50% - 318px);
	}
	.polyline {
		fill: transparent;
		stroke: #ddd;
		stroke-width: 2;
		stroke-dasharray: 1000;
		stroke-dashoffset: 1000;
		animation: run 20s linear infinite;
	}
}

// Modal 对话框
::v-deep {
	.ivu-modal {
		.ivu-modal-content {
			.ivu-modal-header {
				padding: 14px 16px !important;
			}
		}
	}
}
.curve-modal,
.curve-process {
	::v-deep {
		.ivu-modal-wrap {
			.ivu-modal {
				.ivu-modal-content {
					padding: 16px;
					border-radius: 0;
					background: #041627;
					border: 1px solid #0094ff;
					color: #fff;
					.ivu-modal-close {
						top: 25px;
						right: 16px;
						background: #00396d;
						border: 1px solid #0094ff;
						.ivu-icon {
							color: #fff;
						}
					}
					.ivu-modal-header {
						position: relative;
						border: none;
						background: linear-gradient(90deg, #17243f 0%, rgba(23, 36, 63, 0) 100%);
						padding: 14px 16px;
						&::before {
							content: '';
							position: absolute;
							left: 0;
							top: 50%;
							transform: translate(0, -50%);
							width: 4px;
							height: 28px;
							background: #44bcff;
						}
						.ivu-modal-header-inner {
							font-weight: 600;
							font-size: 20px;
							color: #ffffff;
						}
					}
					.ivu-modal-footer {
						border: none;
					}
				}
			}
		}
		.ivu-tabs .ivu-tabs-bar .ivu-tabs-nav {
			.ivu-tabs-tab {
				color: #b8c7d9;
				&.ivu-tabs-tab-active {
					font-weight: 600;
					color: #fff;
				}
			}
		}
		.ivu-input-wrapper {
			.ivu-input {
				border-radius: 0;
				color: #fff;
				background: #041627;
				border: 1px solid #0094ff;
			}
			.ivu-input-suffix .ivu-icon {
				color: #efefef;
			}
		}
		// 下拉框
		.ivu-select-dropdown {
			background: #133a5e;
			// top: 40px;
			border: 1px solid #0094ff;
			border-radius: 0;
			&::-webkit-scrollbar {
				width: 4px;
				height: 6px;
			}
			&::-webkit-scrollbar-thumb {
				background: linear-gradient(180deg, #117cae 0%, #124078 100%) !important;
			}
			.ivu-select-dropdown-list {
				.ivu-select-item {
					color: #fff;
					&:hover {
						background: #02305c;
					}
					&.ivu-select-item-selected {
						background: #1360a7;
					}
				}
			}
			/* 日期 */
			.ivu-picker-panel-body {
				.ivu-picker-panel-content {
					.ivu-date-picker-header {
						border-bottom: 1px solid #0094ff;
					}
					.ivu-date-picker-cells {
						.ivu-date-picker-cells-cell:hover em {
							background: #9fbfdd;
						}
						.ivu-date-picker-cells-cell-range:before {
							background: #9fbfdd;
						}
					}
				}
				.ivu-picker-confirm {
					border-top: 1px solid #0094ff;
					.ivu-btn {
						background: transparent;
						color: #fff;
						&:nth-child(2) {
							display: none;
						}
					}
				}
			}
		}
	}
}
.curve-process {
	::v-deep {
		.ivu-modal-wrap {
			.ivu-modal {
				.ivu-modal-content {
					padding: 16px;
					border-radius: 0;
					background: #041627;
					border: 1px solid #0094ff;
					color: #fff;
					.ivu-modal-close {
						top: 16px;
						right: 16px;
						background: #00396d;
						border: 1px solid #0094ff;
						.ivu-icon {
							color: #fff;
						}
					}
					.ivu-modal-header {
						display: none;
					}
					.ivu-modal-footer {
						border: none;
					}
					.ivu-modal-body {
						top: 0;
					}
				}
			}
		}
	}
}
.video-panel {
	position: absolute;
}
.iframe {
	border: none;
	// display: none;
	transition: width 0.3s ease-in-out;
	transition: height 0.3s ease-in-out;
}
.auto {
	width: 100%;
	height: 100%;
}
.process {
	height: 100%;
	// width:  100%;
}
.no-process {
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>
