<!--
 * @Description: 多功能选择器
 * @Author: shenxh
 * @Date: 2022-03-22 19:18:01
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-05-13 10:58:10
-->

<template>
	<div class="personnel-config-select">
		<div
			class="select-selection"
			ref="select-selection"
			:tabindex="0"
			:class="{ 'select-selection-disabled': disabled }"
			:style="{
				cursor: disabled ? 'not-allowed' : 'pointer',
				'background-color': disabled ? '#e8e8f0' : '#ffffff',
			}"
			@mouseover="mouseoverSelect"
			@mouseout="mouseoutSelect"
			@click="handleSelect"
			@blur="selectBlur"
		>
			<div class="select-content">
				<div class="select-label-wrap" v-if="selectedLabel.length">
					<Tag
						v-for="(item, index) in selectedLabel"
						:key="index"
						:name="item"
						:closable="!disabled"
						@on-close="handleCloseTag(item, index)"
					>
						{{ item }}
					</Tag>
				</div>
				<div class="select-placeholder-wrap" v-if="!selectedLabel.length">
					{{ newPlaceholder }}
				</div>
			</div>
			<div class="select-icon">
				<Icon class="arrow-down" :class="showDropdown ? 'up' : 'down'" type="ios-arrow-down" />
			</div>
		</div>

		<!-- 下拉框 -->
		<div
			v-show="showDropdown"
			class="select-dropdown"
			@mouseenter="dropdownMouseenter"
			@mouseleave="dropdownMouseleave"
		>
			<div class="select-dropdown-content">
				<div class="select-dropdown-content-tree" :style="{ width: treeWidth + 'px' }">
					<Tree :data="treeData" @on-select-change="changeTree"></Tree>
				</div>
				<div class="select-dropdown-content-table">
					<div class="search-wrap">
						<Input search v-model="searchText" :placeholder="placeholder" @on-search="searchData" />
					</div>
					<div class="table-wrap" :style="{ width: tableWidth + 'px' }">
						<Table
							:key="tableKey"
							ref="selection"
							border
							:height="255"
							:loading="tableLoading"
							:columns="columns"
							:data="tableData"
							@on-selection-change="onSelectionChange"
							@on-select="onSelect"
							@on-select-cancel="onSelectCancel"
							@on-select-all="onSelectAll"
							@on-select-all-cancel="onSelectAllCancel"
						></Table>
					</div>
					<div class="page-wrap">
						<Page
							:total="total"
							:current="current"
							:page-size="pageSize"
							size="small"
							show-elevator
							show-sizer
							show-total
							:page-size-opts="pageSizeOpts"
							@on-page-size-change="onPageSizeChange"
							@on-change="changePage"
						/>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'personnel-config-select',
	components: {},
	props: {
		// 已选择的value数组
		selectedValue: Array,
		// 已选择的label数组 (用于标签展示, 顺序要与value对应)
		selectedLabel: Array,
		// 禁用
		disabled: Boolean,
		// 搜索框 placeholder
		placeholder: {
			type: String,
			default: '请输入搜索内容',
		},
		newPlaceholder: {
			type: String,
			default: '',
		},
		// 表格宽度
		tableWidth: {
			type: Number,
			default: 460,
		},
		// 树形图宽度
		treeWidth: {
			type: Number,
			default: 160,
		},
		// 表格加载中
		tableLoading: Boolean,
		// 左侧树形图数据
		treeData: {
			type: Array,
			default() {
				return [
					{
						title: '1',
						expand: true,
						children: [
							{
								title: '1-1',
								expand: true,
							},
						],
					},
				]
			},
		},
		// 右侧表格 columns
		columns: {
			type: Array,
			default() {
				return [
					{
						type: 'selection',
						width: 60,
						align: 'center',
					},
					{
						title: '名称',
						// width: 390,
						key: 'label',
					},
				]
			},
		},
		// 右侧表格数据 (label/value 必传)
		tableData: {
			type: Array,
			default() {
				return [
					{
						label: '张三',
						value: '1',
					},
					{
						label: '李四',
						value: '2',
					},
				]
			},
		},
		// 分页-总数
		total: {
			type: Number,
			default: 0,
		},
		// 分页-每页数量
		pageSize: {
			type: Number,
			default: 20,
		},
		// 分页-当前页码
		current: {
			type: Number,
			default: 1,
		},
		// 分页-每页条数切换的配置
		pageSizeOpts: {
			type: Array,
			default() {
				return [10, 20, 50, 100]
			},
		},
	},
	data() {
		return {
			tableKey: 0,
			showDropdown: false, // 显示下拉框
			isHover: false, // 鼠标移入
			isClickDropdown: false,
			searchText: '',
		}
	},
	computed: {},
	watch: {
		tableData: {
			handler() {
				this._setTableData()
			},
			deep: true,
		},
		showDropdown: {
			handler(v) {
				if (!v) {
					this.searchText = ''
					if (this.selectedValue.length === 0) {
						this.$emit('update:current', 1)
						this.$emit('on-search', '')
						console.log(this.tableData)
						console.log(this.columns)
					}
				}
			},
		},
	},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 点击选择框
		handleSelect() {
			if (this.disabled) return

			this.showDropdown = !this.showDropdown

			this.$emit('handle-select', this.showDropdown)
		},

		// 弹窗-鼠标移入事件
		dropdownMouseenter() {
			this.isClickDropdown = true
		},

		// 弹窗-鼠标移出事件
		dropdownMouseleave() {
			this.isClickDropdown = false
			this.$refs['select-selection'].focus()
		},

		// 选择框-光标消失事件
		selectBlur() {
			if (!this.isClickDropdown) {
				this.showDropdown = false
			}
		},

		// 选择框-鼠标移入事件
		mouseoverSelect() {
			if (this.disabled) return

			this.isHover = true
		},

		// 选择框-鼠标移出事件
		mouseoutSelect() {
			if (this.disabled) return

			this.isHover = false
		},

		// 选中树形图节点
		changeTree(selectedNodes, currentNode) {
			this.$emit('on-select-change-tree', selectedNodes, currentNode)
		},

		// 关闭tag
		handleCloseTag(itm, idx) {
			this.selectedValue.splice(idx, 1)
			this.selectedLabel.splice(idx, 1)
			this._setTableData()

			// 重新渲染表格数据
			this.tableKey++
		},

		// 选中某一项时触发
		onSelect(selection, row) {
			this.selectedValue.push(row.value)
			this.selectedLabel.push(row.label)

			this.$emit('on-select', {
				selectedValue: this.selectedValue,
				selectedLabel: this.selectedLabel,
				row,
			})
		},

		// 取消选中某一项时触发
		onSelectCancel(selection, row) {
			let idx = this.selectedValue.indexOf(row.value)

			this.selectedValue.splice(idx, 1)
			this.selectedLabel.splice(idx, 1)

			this.$emit('on-select', {
				selectedValue: this.selectedValue,
				selectedLabel: this.selectedLabel,
				row,
			})
		},

		// 点击全选时触发
		onSelectAll(selection) {
			selection.forEach(item => {
				if (!this.selectedValue.includes(item.value)) {
					this.selectedValue.push(item.value)
					this.selectedLabel.push(item.label)
				}
			})
			this.$emit('on-select-all', selection)
		},

		// 点击取消全选时触发
		onSelectAllCancel(selection) {
			this.tableData.forEach(item => {
				if (this.selectedValue.includes(item.value)) {
					let idx = this.selectedValue.indexOf(item.value)

					this.selectedValue.splice(idx, 1)
					this.selectedLabel.splice(idx, 1)
				}
			})
			this.$emit('on-select-all-cancel', selection)
		},

		// 只要选中项发生变化时就会触发
		onSelectionChange(selection) {
			this.$emit('on-selection-change', selection)
		},

		// 开启 search 时可用，点击搜索或按下回车键时触发
		searchData(val) {
			this.$emit('on-search', val)
		},

		// 切换每页条数时的回调，返回切换后的每页条数
		onPageSizeChange(quantity) {
			this.$emit('update:current', 1)
			this.$emit('update:pageSize', quantity)
			this.$emit('on-page-size-change', quantity)
		},

		// 页码改变的回调，返回改变后的页码
		changePage(pageNo) {
			this.$emit('update:current', pageNo)
			this.$emit('on-change-page', pageNo)
		},

		// 设置表格数据
		_setTableData() {
			// if (
			// 	this.tableData &&
			// 	this.tableData.length &&
			// 	this.selectedValue &&
			// 	this.selectedValue.length
			// ) {
			this.tableData.forEach(item => {
				item._checked = false
				this.selectedValue.forEach(item1 => {
					if (item.value == item1) {
						item._checked = true
					}
				})
			})
			this.tableKey++
			// }
		},
		//清除选中的选项
		clearSelectValue() {
			this.tableData.forEach(item => {
				item._checked = false
			})
		},

		// 根据属性获取对应行数据
		// _getRowData(attributeValue, attributeName) {
		// 	let obj = {}

		// 	this.tableDataAll.forEach(item => {
		// 		if (item[attributeName] === attributeValue) {
		// 			obj = item
		// 		}
		// 	})

		// 	return obj
		// },
	},
}
</script>

<style lang="less" scoped>
.personnel-config-select {
	position: relative;
	user-select: none;
	.select-selection {
		position: relative;
		min-height: 32px;
		background-color: #fff;
		border-radius: 4px;
		border: 1px solid #d1d1de;
		overflow: hidden;
		cursor: pointer;
		&:focus {
			outline: none;
			border-color: #41a8ed;
			box-shadow: 0 0 0 2px #41a8ed33;
		}
		&:hover {
			border-color: #57a3f3;
		}
		.select-content {
			.select-label-wrap {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: transparent;
				padding: 0 24px 0 8px;
			}
			.select-placeholder-wrap {
				padding: 0 24px 0 8px;
				background-color: transparent;
				color: #b8bacf;
			}
		}
		.select-icon {
			position: absolute;
			display: flex;
			align-items: center;
			justify-content: center;
			right: 0;
			top: 50%;
			transform: translate(0, -50%);
			width: 24px;
			height: 24px;
			z-index: 1;
			cursor: pointer;
			.ios-close-circle {
				z-index: 2;
			}
			.arrow-down {
				transition: all 0.2s;
				&.up {
					transform: rotate(180deg);
				}
			}
			.xx-close {
				color: #808695;
			}
		}
	}

	.select-dropdown {
		position: absolute;
		top: -6px;
		left: 0;
		transform: translate(0, -100%);
		border-radius: 1px;
		background-color: #fff;
		box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
		// width: 700px;
		height: 350px;
		overflow: hidden;
		z-index: 9999;
		.select-dropdown-content {
			display: flex;
			width: 100%;
			height: 100%;
			.select-dropdown-content-tree {
				flex-shrink: 0;
				height: 100%;
				padding: 0 8px;
				overflow: auto;
				border-right: 1px solid #efefef;
				/deep/ .ivu-tree {
					.ivu-tree-arrow {
						i {
							margin-top: -10px;
						}
					}
				}
			}
			.select-dropdown-content-table {
				flex-grow: 1;
				height: 100%;
				padding: 8px;
				.search-wrap {
					margin-bottom: 10px;
				}
				.page-wrap {
					white-space: nowrap;
					.ivu-page {
						margin-bottom: 0;
						margin-top: 8px;
					}
				}
			}
		}
	}
}
</style>
