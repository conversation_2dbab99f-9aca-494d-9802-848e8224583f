.flex {
  display: flex;
}
.text-right {
  text-align: right;
}
.w-100 {
  width: 100%;
}
.h40 {
  height: 40px;
}
.fn-13 {
  font-size: 13px;
}
.mg-l-5 {
  margin-left: 5px;
}
.config-set {
  display: flex;
  align-items: center;
  padding: 0 15px;
}
.player-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	.main-container {
		flex-grow: 1;
	}
  .player-box {
    height: 100%;
    border: 1px solid #222;
    position: relative;
    overflow: hidden;
    background: url('./img/default.png');
    background-color: #2D2F35;
    background-position: center center;
    background-repeat: no-repeat;
  }
  .active-player {
    border: 1px solid #6B5FDD;
  }
  .el-slider-step {
    width: 100px;
    display: inline-block;
    position: absolute;
    right: 94px;
  }
  .el-footer {
    padding: 0px;
    .video-icon {
      width: 20px;
      height: 20px;
      display: inline-block;
      margin-left: 15px;
      transform: translate(0, 50%);
    }
    .p4-normal-icon {
      background: url('./img/4-normal.png');
      background-size: 100% 100%;

      position: absolute;
      right: 214px;
      &:hover {
        background: url('./img/4-hover.png');
      }
    }
    .p16-normal-icon {
      background: url('./img/16-normal.png');
      background-size: 100% 100%;
      &:hover {
        background: url('./img/16-hover.png');
      }
    }
    .recording-icon {
      width: 28px;
      height: 28px;
      margin-top: 6px;
      margin-left: 16px;
      cursor: pointer;
      background: url('./img/record.png');
      background-size: 100% 100%;
      &:hover,
      &.active {
        background: url('./img/record-h.png');
        background-size: 100% 100%;
      }
    }
    .talk-icon {
      width: 20px;
      height: 20px;
      margin-top: 9.5px;
      margin-left: 16px;
      cursor: pointer;
      background: url('./img/talk.png');
      background-size: 100% 100%;
      &:hover,
      &.active {
        background: url('./img/talk-h.png');
        background-size: 100% 100%;
      }
    }
    .play-icon {
      width: 20px;
      height: 20px;
      margin-top: 9.5px;
      margin-left: 16px;
      cursor: pointer;
      background: url('./img/play.png');
      background-size: 100% 100%;
    }
    .pause-icon {
      width: 20px;
      height: 20px;
      margin-top: 9.5px;
      margin-left: 16px;
      cursor: pointer;
      background: url('./img/pause.png');
      background-size: 100% 100%;
    }
    .fast-icon {
      width: 18px;
      height: 18px;
      margin-top: 10.5px;
      margin-left: 16px;
      cursor: pointer;
      background: url('./img/fastForward.png');
      background-size: 100% 100%;
    }
    .download-icon {
      width: 18px;
      height: 18px;
      margin-top: 10.5px;
      margin-left: 16px;
      cursor: pointer;
      background: url('./img/download.png');
      background-size: 100% 100%;
    }
    .rew-icon {
      width: 18px;
      height: 18px;
      margin-top: 10.5px;
      margin-left: 16px;
      cursor: pointer;
      background: url('./img/fastRewind.png');
      background-size: 100% 100%;
    }
    .sound-icon {
      width: 24px;
      height: 24px;
      margin-top: 9px;
      margin-left: 16px;
      cursor: pointer;
      background: url('./img/sound.png');
      background-size: 100% 100%;
      &.active {
        background: url('./img/mute.png');
        background-size: 100% 100%;
      }
    }
    .close-icon {
      width: 22px;
      height: 22px;
      margin-top: 9px;
      margin-left: 16px;
      cursor: pointer;
      background: url('./img/stop.png');
      background-size: cover;
      &:hover {
        background: url('./img/stop-hover.png');
        background-size: cover;
      }
    }
    .closeall-normal-icon {
      background: url('./img/closeall-normal.png');
      background-size: 100% 100%;
      margin-right: 5px;
      cursor: pointer;
      &:hover {
        background: url('./img/closeall-hover.png');
      }
    }
    .screenshot-icon {
      width: 20px;
      height: 20px;
      background-image: url('./img/snap1.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-top: 10px;
      margin-left: 16px;
      cursor: pointer;
      &:hover {
        background-image: url('./img/snap1-h.png');
      }
      &+span {
        line-height: 40px;
        margin-left: 10px;
      }
    }
  }
}