<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-03-15 08:58:24
 * @LastEditors: houyan
 * @LastEditTime: 2024-03-19 15:01:52
-->
<template lang="pug">
Modal(v-model='isShow', title='管理报表业务', :transfer='false', width='1000', :footer-hide='true')
	water-row(justify='flex-end', align='center')
		Button(type='primary', @click='handleSet("add")') 添加
	Table.table-container.mt10(border, :columns='columns', :data='tableData', :height='470')
	Page(
		:total='pageTotal',
		show-elevator,
		show-sizer,
		@on-page-size-change='handleChangeSize',
		@on-change='handleChangePage'
	)
	business-edit-modal(v-model='showEdit', ref='modalRef', :title='editTitle', @updateSuccess='_getBusinessList')
</template>
<script>
import BusinessEditModal from './BusinessEditModal.vue'
import WaterRow from '@/components/gc-water-row'
import { Poptip, Button } from '@eslink/esvcp-pc-ui'
import { getBusinessList, deleteBusiness } from '@/api/report.js'
export default {
	props: ['value', 'title'],
	components: { WaterRow, BusinessEditModal },
	data() {
		return {
			columns: [
				{
					title: 'id',
					key: 'id',
					align: 'center',
				},
				{
					title: '业务名称',
					key: 'name',
					align: 'center',
				},
				{
					title: '排序号',
					align: 'center',
					key: 'sequence',
				},
				{
					title: '操作',
					key: 'deal',
					align: 'center',
					render: (h, params) => {
						const arr = [
							h(
								Poptip,
								{
									props: {
										placement: 'bottom-end',
										title: '确定要删除吗？',
										confirm: true,
										transfer: true,
									},
									on: {
										'on-ok': () => {
											const { id } = params.row
											this._deleteBusiness(id)
										},
									},
								},
								[
									h(
										Button,
										{
											class: 'mr10',
											props: {
												type: 'error',
												size: 'small',
											},
										},
										'删除',
									),
								],
							),
							h(
								Button,
								{
									props: {
										type: 'primary',
										size: 'small',
									},
									on: {
										click: () => {
											this.handleSet('edit', params.row)
										},
									},
								},
								'修改',
							),
						]
						return h('div', arr)
					},
				},
			],
			tableData: [],
			pageSize: 10,
			pageNum: 1,
			pageTotal: 0,
			showEdit: false,
			editTitle: '',
		}
	},
	computed: {
		isShow: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit('input', val)
			},
		},
	},
	methods: {
		_getBusinessList() {
			const params = {
				needPage: true,
				pageNum: this.pageNum,
				pageSize: this.pageSize,
			}

			getBusinessList(params)
				.then(res => {
					const { result } = res
					const { list, total } = result

					this.tableData = list
					this.pageTotal = total
				})
				.catch(error => {
					console.error(error)
				})
		},
		_deleteBusiness(id) {
			deleteBusiness({ id }).then(() => {
				this.$Message.success('删除成功')

				this._getBusinessList()
			})
		},
		handleChangePage(val) {
			this.pageNum = val
			this._getBusinessList()
		},
		handleChangeSize(val) {
			this.pageSize = val
			this._getBusinessList()
		},
		handleSet(val, data) {
			this.showEdit = true
			this.editTitle = val === 'add' ? '新增报表业务' : '修改报表业务'
			if (val === 'edit') {
				const obj = this.$common.deepCopy(data)
				delete obj['_index']
				delete obj['_rowKey']

				this.$refs.modalRef.initData(obj)
			} else {
				this.$refs.modalRef.initData({
					name: '',
					sequence: 1,
				})
			}
		},
	},
}
</script>
<style lang="less" scoped>
::v-deep {
	.ivu-modal-header {
		padding: 8px;
	}
}
</style>
