/*
 * @Description: 按需引入组件
 * @Version: 2.0
 * @Autor: z<PERSON>yi
 * @Date: 2022-02-18 13:50:36
 * @LastEditors: shenxh
 * @LastEditTime: 2023-04-12 14:00:37
 */
import Vue from 'vue'
import {
	Button,
	ButtonGroup,
	Table,
	Page,
	Select,
	Option,
	DatePicker,
	Input,
	InputNumber,
	Tabs,
	TabPane,
	Spin,
	Message,
	Switch,
	Row,
	Col,
	Icon,
	Modal,
	Tag,
	CheckboxGroup,
	Checkbox,
	Tooltip,
	Dropdown,
	DropdownMenu,
	Drawer,
	Progress,
	Divider,
	RadioGroup,
	Radio,
	Tree,
	NoanimationTree,
	Upload,
	FormItem,
	Form,
	Circle,
	Cascader,
	Badge,
	Poptip,
	TimePicker,
	EsForm,
	EsTable,
	EsSearch,
	EsHeader,
	EsModal,
	EsAlarmDetail,
	ColorPicker,
	EsHugeTree,
	Collapse,
	Panel,
	Slider,
	AutoComplete,
	Submenu,
	MenuItem,
	Menu,
} from '@eslink/esvcp-pc-ui'
import '@eslink/esvcp-pc-ui/dist/esvcp-pc-ui.css'

Message.config({
	top: parseInt(window.innerHeight / 12),
})
Vue.prototype.$Message = Message
Vue.prototype.$Modal = Modal
const iviewModule = {
	Button,
	ButtonGroup,
	Table,
	Tree,
	NoanimationTree,
	Page,
	Select,
	'i-select': Select,
	'i-option': Option,
	'i-switch': Switch,
	Option,
	DatePicker,
	Input,
	'i-input': Input,
	InputNumber,
	Tabs,
	TabPane,
	Spin,
	Message,
	Row,
	Col,
	'i-col': Col,
	Icon,
	Modal,
	Tag,
	CheckboxGroup,
	Checkbox,
	Tooltip,
	Dropdown,
	DropdownMenu,
	Drawer,
	Slider,
	Progress,
	Divider,
	RadioGroup,
	Radio,
	Upload,
	FormItem,
	Form,
	'i-circle': Circle,
	Cascader,
	Badge,
	Poptip,
	TimePicker,
	EsForm,
	EsTable,
	EsSearch,
	EsHeader,
	EsModal,
	EsAlarmDetail,
	ColorPicker,
	EsHugeTree,
	Collapse,
	Panel,
	AutoComplete,
	Submenu,
	MenuItem,
	Menu,
}
Object.keys(iviewModule).forEach(key => {
	Vue.component(key, iviewModule[key])
})
