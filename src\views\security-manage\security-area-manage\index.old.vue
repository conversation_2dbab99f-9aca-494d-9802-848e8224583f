<!--
 * @Description: 安保区域管理
 * @Author: shenxh
 * @Date: 2022-04-01 09:46:43
 * @LastEditors: shenxh
 * @LastEditTime: 2022-04-13 13:51:24
-->

<template lang="pug">
.security-area-manage
	.header-wrap
		es-header.header(title='安保区域管理')
	.security-area-manage-wrap
		.security-area-manage-tree
			tree(
				ref='tree',
				:data='treeData',
				:load='loadNode',
				tools,
				lazy,
				highlight-current,
				search-type='lazy',
				@handle-node-tools='handleNodeTools',
				@node-click='nodeClick',
				@search='searchNode'
			)
		.security-area-manage-content
			.security-area-manage-content-wrap
				.title-create(v-if='handleType === "create"')
					h3.form-title(v-if='selectedNode.id') 添加 {{ selectedNode.name }} 子区域
					h3.form-title(v-else) 添加区域
				h3.form-title(v-else) 编辑区域
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='80')
					FormItem(label='区域名称', prop='name')
						Input(v-model='formData.name', placeholder='请输入区域名称', clearable)
					FormItem(label='区域代码', prop='code')
						Input(v-model='formData.code', placeholder='请输入区域代码', clearable)
					FormItem(label='区域描述', prop='memo')
						Input(
							v-model='formData.memo',
							type='textarea',
							maxlength='200',
							:rows='4',
							show-word-limit,
							placeholder='请输入内容',
							clearable
						)
				.security-area-manage-button
					Button(type='primary', @click='handleSubForm') 保 存
</template>

<script>
import Tree from '@/components/gc-tree'
import { getSecurityArea, updSecurityArea, delSecurityArea, sortSecurityArea } from '@/api/security-manage'

export default {
	name: 'security-area-manage',
	components: {
		Tree,
	},
	props: {},
	data() {
		const validateAreaName = (rule, value, callback) => {
			const reg = /(\/|\\|:|\*|\?|<|>|"|\|)/g

			if (reg.test(value) || value.length > 32) {
				callback(new Error('1～32个字符；不能包含 / \\ : * ? " | 这些特殊字符'))
			} else {
				callback()
			}
		}
		const validateAreaCode = (rule, value, callback) => {
			const reg = /^[A-Za-z0-9_]{1,32}$/

			if (reg.test(value)) {
				callback()
			} else {
				callback(new Error('1～32个字符；只允许数字、字母、下划线'))
			}
		}

		return {
			searchVal: '',
			handleType: 'create',
			selectedNode: {},
			treeData: [],
			formData: {
				name: '',
				code: '',
				memo: '',
			},
			formRules: {
				name: [
					{
						required: true,
						message: '请输入区域名称',
						trigger: 'blur',
					},
					{ validator: validateAreaName, trigger: 'blur' },
				],
				code: [
					{
						required: true,
						message: '请输入区域代码',
						trigger: 'blur',
					},
					{ validator: validateAreaCode, trigger: 'blur' },
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 保存按钮
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this._updSecurityArea()
				}
			})
		},

		// 节点被点击时的回调
		nodeClick(data, node, nodeComponent) {
			console.log(data, node, nodeComponent)

			if (data) {
				this.handleType = 'update'
			} else {
				this.handleType = 'create'
			}
			this.selectedNode = data || {}
			this.formData = { ...data } || {}
			this.$refs.form.resetFields()
		},

		// 加载节点
		loadNode(node, resolve) {
			const { id } = node.data

			getSecurityArea({
				id,
				name: this.searchVal,
			}).then(res => {
				resolve(this._setTreeData(res))
			})
		},

		// 搜索节点
		searchNode(val) {
			this.searchVal = val

			this._getTreeData(val)
		},

		// 点击节点工具栏选项
		handleNodeTools(name, selectedNode, brothersNodes) {
			console.log(selectedNode, brothersNodes)

			this.handleType = name
			switch (name) {
				case 'create':
					this.formData = {}
					break
				case 'delete':
					this._delSecurityArea()
					break
				case 'up':
					this._sortSecurityArea('up', selectedNode)
					break
				case 'down':
					this._sortSecurityArea('down', selectedNode)
					break
			}
		},

		// 获取树形图数据
		_getTreeData(name) {
			getSecurityArea({
				name,
			}).then(res => {
				this.treeData = this._setTreeData(res) || []
				if (this.$refs.tree) {
					this.$refs.tree._clearCurrentNode()
				}
			})
		},

		// 设置树形图数据
		_setTreeData(res) {
			let data = res.result.map(item => {
				return {
					...item,
					leaf: !item.hasChild,
				}
			})
			data.sort((a, b) => {
				return a.sort - b.sort
			})

			return data
		},

		// 添加或编辑安防区域
		_updSecurityArea() {
			const { id, pathCode } = this.selectedNode || {}

			updSecurityArea({
				...this.formData,
				id: this.handleType === 'update' ? id : undefined,
				parentCode: this.handleType === 'create' ? pathCode : undefined,
			}).then(() => {
				this.$Message.success('操作成功')

				this.$refs.form.resetFields()
				this._getTreeData()
			})
		},

		// 删除安防区域
		_delSecurityArea() {
			const { id } = this.selectedNode || {}

			delSecurityArea({
				id,
			}).then(() => {
				this.$Message.success('操作成功')

				this._getTreeData()
			})
		},

		// 安防区域排序
		_sortSecurityArea(name, selectedNode) {
			const { previousNode, currentNode, nextNode } = selectedNode
			let params = {}

			if (name === 'up') {
				params = {
					id: currentNode.id,
					targetId: previousNode.id,
				}
			} else {
				params = {
					id: currentNode.id,
					targetId: nextNode.id,
				}
			}
			sortSecurityArea(params).then(() => {
				this.$Message.success('操作成功')

				this._getTreeData()
			})
		},
	},
}
</script>

<style lang="less" scoped>
.security-area-manage {
	width: 100%;
	height: 100%;
	.header-wrap {
		padding: 0 16px;
		.header {
			border-bottom: 1px solid #efefef;
		}
	}
	.security-area-manage-wrap {
		display: flex;
		height: calc(100% - 40px);
		.security-area-manage-tree {
			flex-shrink: 0;
			width: 200px;
			padding: 16px;
		}
		.security-area-manage-content {
			display: flex;
			justify-content: center;
			flex-grow: 1;
			border-left: 1px solid #efefef;
			height: 100%;
			padding-top: 80px;
			.security-area-manage-content-wrap {
				flex-direction: column;
				width: 500px;
				.form {
					width: 100%;
					/deep/ .ivu-form-item {
						margin-bottom: 20px;
					}
				}
			}
			.security-area-manage-button {
				display: flex;
				justify-content: flex-end;
				width: 100%;
				margin-top: 20px;
			}
		}
	}
}
</style>
