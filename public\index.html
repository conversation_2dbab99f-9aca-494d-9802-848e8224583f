<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title>公共平台</title>
    <!-- <script type="text/javascript" src="./js/fabric.js"></script> -->
    <!-- <script type="text/javascript" src="https://unpkg.com/fabric@latest/dist/fabric.js"></script> -->
    <!-- <script type="text/javascript" src="./js/customiseControls.js"></script> -->
    <link rel="stylesheet" href="//at.alicdn.com/t/font_820468_seg2d163zpc.css">
    <!--  智慧水务系统 -->
    <link rel="stylesheet" href="//at.alicdn.com/t/c/font_2899229_6xrj2a97krg.css">

    <!-- <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/vConsole/3.3.4/vconsole.min.css"> -->
    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/vConsole/3.3.4/vconsole.min.js"></script> -->
</head>

<body>
    <noscript>
            <strong
                >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't
                work properly without JavaScript enabled. Please enable it to
                continue.</strong
            >
        </noscript>
    <div id="app"></div>
    <script src="./EasyPlayer-element.min.js"></script>
    <script src="/static/config.js"></script>
    <!-- built files will be auto injected -->
</body>
<script>
    // 在页面加载完成后初始化 VConsole
    // document.addEventListener('DOMContentLoaded', function() {
    //     var vConsole = new window.VConsole();
    //     console.log('VConsole is enabled.');
    // });
</script>

</html>

</html>

</html></html>

</html>
