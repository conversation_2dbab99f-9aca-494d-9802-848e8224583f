<!--
 * @Description: 视频回放
 * @Author: shenxh
 * @Date: 2022-07-18 15:07:12
 * @LastEditors: shenxh
 * @LastEditTime: 2023-08-30 15:18:00
-->

<template lang="pug">
.playback
	.header-wrap
		es-header.header(title='视频回放')
	.playback-wrap
		.playback-tree
			tree.tree-box(
				ref='tree',
				:data='treeData',
				:load='loadNode',
				prefix-icon,
				lazy,
				highlight-current,
				search-type='lazy',
				@node-click='nodeClick',
				@search='searchNode'
			)

		.playback-content
			video-native-hk(v-if='videoType == "1"', ref='HKRef', :play-mode='1')
			video-native-dh(
				v-if='videoType == "2"',
				ref='DHRef',
				:num='1',
				:type='2',
				:windowType='3',
				videoId='testPlayerHis',
				:pathList='pathListHis'
			)
			VideoDemoMul(v-if='videoType == "3"', ref='YRRef', :data='selectedNode', playback)
</template>

<script>
import Tree from '@/components/gc-tree'
import VideoNativeHk from '@/components/gc-video-native-hk/VideoNativeHk.vue'
import VideoNativeDh from '@/components/gc-video-native-dh/DHPlayer.vue'
import VideoDemoMul from '@/components/gc-video-player/VideoDemoMul.vue'

import { getVideoTree, getVideoUrlHis, getUserConfig } from '@/api/security-manage'

export default {
	name: 'playback',
	components: {
		Tree,
		VideoNativeHk,
		VideoNativeDh,
		VideoDemoMul,
	},
	props: {},
	data() {
		return {
			videoType: 0,
			recordSource: 2,
			firstSearch: false,
			searchVal: '',
			selectedNode: {},
			treeData: [],
			// 当前选中的视频code
			cameraIndexCode: '',
			pathListHis: [],
			pathList: [],
		}
	},
	computed: {},
	watch: {},
	created() {
		this.getUserConfig()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 加载节点
		loadNode(node, resolve) {
			const { cameraList, id } = node.data

			getVideoTree({
				// parentCode: code,
				id,
				name: this.searchVal,
				type: id || id === 0 ? undefined : this.$route.query.type,
			}).then(res => {
				resolve(this._setTreeData(res, cameraList))
			})
		},

		// 搜索节点
		searchNode(val) {
			this.searchVal = val

			this._getTreeData(val)
		},

		// 节点被点击时的回调
		nodeClick(data) {
			if (data) {
				const { cameraIndexCode, type } = data

				this.selectedNode = data
				if (this.videoType == 1) {
					this.$refs.HKRef.stopAllPreview()
					// 点击设备
					if (type === 'equipment') {
						this.$refs.HKRef.playVideo(1, cameraIndexCode)
						this.$refs.HKRef.resizeVideo()
						this.$refs.HKRef.oWebControl.JS_CuttingPartWindow(372, 200) // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
					}
				} else if (this.videoType == 2) {
					this.$refs.DHRef.closeVideo({
						isAll: true,
					})
					// 点击设备
					if (type === 'equipment') {
						this.getVideoUrlHis()
					}
				} else if (this.videoType == 3) {
					// 点击设备
					if (type === 'equipment') {
						this.getVideoTreeYR(data)
					}
				}
			}

			// 选中状态
			this.$refs.tree.$refs['el-tree'].setCurrentKey(this.selectedNode.id)
		},

		// 获取树形图数据
		_getTreeData(name) {
			getVideoTree({
				name,
				type: this.$route.query.type,
			}).then(res => {
				this.treeData = this._setTreeData(res) || []
			})
		},

		// 设置树形图数据
		_setTreeData(res, cameraList) {
			let data = res.result.map(item => {
				return {
					...item,
					type: 'area',
					leaf: item.cameraList && item.cameraList.length ? false : !item.hasChild,
				}
			})
			data.sort((a, b) => {
				return a.sort - b.sort
			})
			if (cameraList && cameraList.length) {
				data.push(
					...cameraList.map(item => {
						return {
							...item,
							type: 'equipment',
							name: item.cameraName,
							leaf: true,
						}
					}),
				)
			}

			return data
		},

		getVideoTreeYR(data) {
			const { cameraIndexCode, platformId } = data

			setTimeout(() => {
				this.$refs.YRRef &&
					this.$refs.YRRef.dblclickChannel({
						channelAbility:
							'RemoteControl,AudioTalkV1,FrameReverse,AudioEncode,AudioEncodeControl,AlarmMD,MDW,MDS,HeaderDetect,RtFaceDetect,RtFaceCompa,SMDAI,SMDH,ChnLocalStorage,LEDS,MDMD,VC,WDRV2,WLTM,VQS,NVM',
						channelId: data.id,
						channelName: data.cameraName,
						deviceAbility:
							'Auth,DHP2P,HSEncrypt,CloudStorage,LocalStorage,PlaybackByFilename,MT,CK,RD,LocalRecord,XUpgrade,TimeSync,ModifyPassword,LocalStorageEnable,RTSV2,PBSV2,ESV1,DaySummerTime,WeekSummerTime,TAP,TimeFormat,InstantDisAlarm,IDAP,DevReset,TLSEnable,DHCP,Reboot,NEC,CCD,ME,DHPenetrate,MesTrans,PicTrans,DataTrans,LRRF,IOTTUNNEL,CDD-OSS,CDD-OBS,CDD-US3,CDD-BOS,CDD-COS,CDDV2,CDD,AudioTalk',
						deviceId: data.cameraIndexCode,
						deviceModel: 'DH-NVR4408-HDS2/I',
						deviceType: 'channel',
						onlineStatus: data.status || 1,
						protoType: 'rtsv',
						recordProtoType: 'rtsv',
						talkProtoType: 'rtsp',
						code: cameraIndexCode,
						platformId,
					})
			}, 50)
		},

		// 获取视频url-历史回放
		getVideoUrlHis() {
			const { cameraIndexCode, platformId } = this.selectedNode || {}

			getVideoUrlHis({
				channelId: cameraIndexCode,
				platformId,
				recordSource: this.recordSource, // 录像来源：2=设备，3=中心
				streamType: 0, // 码流类型：0=所有码流，1=主码流， 2=辅码流，3=辅码流2
				recordType: 0, // 录像类型：0=全部录像
				startTime: this.$moment().subtract(30, 'days').format('YYYY-MM-DD') + ' 00:00:00',
				endTime: this.$moment().add(1, 'days').format('YYYY-MM-DD') + ' 00:00:00',
			}).then(res => {
				const { result } = res
				let pathListHis = []

				if (result && result.length) {
					const { streamType, recordType, ip, channelId } = result[0] || {}
					const startTime = result[0].startTime
					const endTime = result[result.length - 1].endTime
					const path = `rtsp://${ip}/dss/playback/param?cameraid=${channelId}&substream=${streamType}&type=${this.recordSource}&recordType=${recordType}&begintime=${startTime}&endtime=${endTime}`
					// const path = `rtsp://**********:9090/dss/playback/param?cameraid=1000144$7&substream=1&type=2&recordType=1&begintime=${startTime}&endtime=${endTime}`

					pathListHis = [
						{
							path,
							records: result,
							startTime,
							endTime,
							redirect: true,
						},
					]
				} else {
					this.$Message.warning('暂无录像')
				}

				this.pathListHis = pathListHis
				setTimeout(() => {
					this.$refs.DHRef && this.$refs.DHRef.loadVideo()
				}, 50)
			})
		},

		// 获取用户配置
		getUserConfig() {
			getUserConfig({
				code: 'video.type',
			}).then(res => {
				const { result = [] } = res

				if (result.length) {
					this.videoType = result[0].val
				}
			})
		},
	},
}
</script>

<style lang="less" scoped>
.playback {
	width: 100%;
	height: 100%;
	.header-wrap {
		padding: 0 16px;
		.header {
			border-bottom: 1px solid #efefef;
		}
	}
	.playback-wrap {
		display: flex;
		width: 100%;
		height: calc(100% - 40px);
		.playback-tree {
			display: flex;
			flex-direction: column;
			flex-shrink: 0;
			width: 200px;
			padding-top: 16px;
			.tree-box {
				padding: 0 16px;
				flex: 1;
			}
		}
		.playback-content {
			flex-grow: 1;
			border-left: 1px solid #efefef;
			height: 100%;
			background-color: #333;
		}
	}
}
</style>
