<!--
 * @Description: 报表展示
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-03-14 16:32:31
 * @LastEditors: houyan
 * @LastEditTime: 2024-03-18 14:02:32
-->
<template lang="pug">
WaterRow
	.tree-list
		es-header.header(title='报表展示')
		.tree-container
			Tree(:data='treeData', @on-select-change='handleSelect')
	.report-list
		iframe(:src='activeUrl', v-show='activeUrl', frameborder='0', width='100%', height='100%')
		no-data(v-show='!activeUrl')
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import NoData from '@/components/gc-no-data'
import { getReportShowList } from '@/api/report.js'
export default {
	components: {
		WaterRow,
		NoData,
	},
	data() {
		return {
			treeData: [],
			activeUrl: '',
		}
	},
	methods: {
		_getReportShowList() {
			getReportShowList({ status: 1 }).then(res => {
				const { result } = res

				if (result && result.length) {
					this.treeData = this.getTreeChild(result)
				}
			})
		},
		getTreeChild(data) {
			return data.map(item => {
				let obj = {
					title: item.name,
					expand: true,
					...item,
					disabled: !item.url,
				}
				if (item.reports && item.reports.length > 0) {
					obj.children = this.getTreeChild(item.reports)
				}
				return obj
			})
		},
		handleSelect(data) {
			this.activeUrl = data[0].url
		},
	},
	mounted() {
		this._getReportShowList()
	},
}
</script>
<style lang="less" scoped>
.water-layout-flex {
	padding: 16px;
	height: 100%;
}
.tree-list {
	display: flex;
	flex-direction: column;
	margin-right: 10px;
	padding: 0 10px 10px 10px;
	width: 240px;
	height: 100%;
	border: 1px solid #e4e7ed;
	.tree-container {
		flex: 1;
		overflow: auto;
	}
	::v-deep {
		#headerBox {
			height: 30px;
		}
		.ivu-tree-empty {
			padding: 10px 0 0 10px;
		}
	}
}
.report-list {
	flex: 1;
	padding: 10px;
	height: 100%;
	border: 1px solid #e4e7ed;
	::v-deep {
		.no-data {
			background-color: #fff !important;
			color: #706a6a;
		}
	}
}
</style>
