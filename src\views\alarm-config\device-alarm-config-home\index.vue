<template >
	<div class="alarm-scheme">
		<div class="header-btn-container">
			<RadioGroup v-model="configType" type="button" button-style="solid" @on-change="changeConfigType">
				<Radio label="common" class="header-button" :class="{ active: configType == 'common' }">全局配置</Radio>
				<Radio label="special" class="header-button" :class="{ active: configType == 'special' }">
					类型配置
				</Radio>
			</RadioGroup>
		</div>
		<router-view></router-view>
	</div>
</template>	
<script>
export default {
	name: 'alarm-config',
	components: {},
	props: {},
	data() {
		return {
			// configType: 'common',
		}
	},
	computed: {
		configType() {
			if (this.$route.path === '/device-alarm-config-home/common-alarm') {
				return 'common'
			} else {
				return 'special'
			}
		},
	},
	watch: {},
	mounted() {},
	methods: {
		changeConfigType(str) {
			if (str === 'special') {
				this.$router.push('/device-alarm-config-home/special-use-alarm')
			} else {
				this.$router.push('/device-alarm-config-home/common-alarm')
			}
		},
	},
}
</script>
<style scoped lang="less">
.header-button {
	color: #57a3f3;
}
.active {
	background: #57a3f3;
	color: #fff;
}
</style>