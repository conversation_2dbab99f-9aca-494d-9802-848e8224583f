<!--
 * @Descripttion: 
 * @version: 
 * @Author: heliping
 * @Date: 2021-12-08 13:44:13
 * @LastEditors: heliping
 * @LastEditTime: 2021-12-08 13:52:40
-->
<template>
	<Modal v-model="modal" ref="modalk" width="300" id="delete">
		<p slot="header" style="font-size: 16px; font-weight: bold">
			<Icon type="ios-alert" color="#f99f15" size="22" style="height: 66px"></Icon>
			<span>提示</span>
		</p>
		<div style="padding-left: 20px">确定要删除这条数据么?</div>
		<div slot="footer">
			<Button @click="modalHidderFun">取消</Button>
			<Button type="primary" @click="deleteFun">确定</Button>
		</div>
	</Modal>
</template>
<script>
export default {
	components: {},
	props: {},
	data() {
		return {
			modal: false,
		}
	},
	methods: {
		modalShowFun() {
			this.modal = true
		},
		modalHidderFun() {
			this.modal = false
		},
		deleteFun() {
			this.$emit('deleteFun')
		},
	},
}
</script>
<style lang="less" scoped>
.preview {
	line-height: 50px;
	text-align: center;
	font-weight: bold;
	font-size: 18px;
}
#delete /deep/ .ivu-modal-header {
	border-bottom: none;
}
#delete /deep/ .ivu-modal-footer {
	border-top: none;
}
</style>
