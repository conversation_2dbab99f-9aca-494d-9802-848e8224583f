<!--
 * @Description: 巡检计划
 * @Version: 2.0
 * @Autor: z<PERSON>yi
 * @Date: 2022-02-21 17:09:59
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-07-29 15:33:27
-->
<template lang="pug">
.plan-list
	water-row(justify='space-between', align='center')
		water-row(justify='flex-start', align='center')
			water-row.plan-list-form(justify='flex-start', align='center')
				.plan-list-form-title 巡检任务名称:
				Input.select(v-model='taskName', placeholder='请输入名称') 
			water-row.plan-list-form(justify='flex-start', align='center')
				.plan-list-form-title 状态:
				Select.select(v-model='state', placeholder='请选择')
					Option(v-for='(item, index) in list', :key='index', :value='item.value') {{ item.label }}
			But<PERSON>(type='primary', @click='getList()') 查询
		Button(type='primary', @click='handleCreate("add")') 创建巡检计划
	.plan-list-content
		es-table.plan-list-table.water-table(
			border,
			:columns='columns',
			:data='tableData',
			:loading='loading',
			showPage,
			:pageData='pageData',
			@on-page-num-change='handlePageChange',
			@on-page-size-change='handlePageSize'
		)
			template(slot-scope='{ row, index }', slot='state')
				i-switch(
					v-model='row.Enable',
					true-color='#19be6b',
					style='color: #fff; width: 64px',
					@on-change='value => { changeState(value, row.Id, index); }'
				)
					span(:slot='row.Enable ? "open" : "close"') {{ row.Enable ? '已启用' : '已停用' }}
			template(slot-scope='{ row }', slot='action')
				Button(type='primary', size='small', @click='handleCreate("check", row)') 查看详情
				Button.water-margin-left-8(v-if='!row.Enable', type='primary', size='small', @click='handleCreate("modify", row)') 修改
				Poptip(v-if='!row.Enable', confirm, title='确定删除该巡检计划吗？', :transfer='true', @on-ok='handleDelete(row)')
					Button.water-margin-left-8(type='error', size='small') 删除
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import {
	getVillagePlanList,
	getSupplyPlanList,
	deleteVillagePlan,
	deleteSupplyPlan,
	updateSupplyPlan,
	updateVillagePlan,
	getSupplyPlanDetail,
	getVillagePlanDetail,
} from '@/api/inspection-management.js'
import { EventBus } from '@/utils/eventBus.js'
export default {
	name: 'plan-list',
	components: {
		WaterRow,
	},
	computed: {
		sysCode() {
			return this.$route.query.sysCode || 'dc'
		},
	},
	data() {
		return {
			loading: false,
			submitLoading: false,
			type: 'add',
			showPlanModal: false,
			tableData: [],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 30, 40, 50],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
			list: [
				{ label: '全部', value: '全部' },
				{ label: '已启用', value: 1 },
				{ label: '已停用', value: 2 },
			],
			state: '全部',
			taskName: '',
			columns: [
				{
					title: '序号',
					key: 'key',
					width: 70,
				},
				{
					title: '巡检名称',
					key: 'Name',
					minWidth: 120,
				},
				{
					title: '巡检人',
					key: 'Executor',
					minWidth: 120,
				},
				{
					title: '自动创建频率',
					key: 'PeriodUnit',
					minWidth: 80,
				},
				{
					title: '巡检站点数',
					key: 'PlanSptCount',
					minWidth: 80,
				},
				{
					title: '执行时段',
					key: 'PlanExeTime',
					minWidth: 120,
				},
				{
					title: '创建人',
					key: 'CreateUser',
					minWidth: 100,
				},
				{
					title: '创建时间',
					key: 'CreateTime',
					minWidth: 100,
				},
				{ title: '状态', slot: 'state', minWidth: 60 },
				{ title: '操作', slot: 'action', width: 210 },
			],
		}
	},
	mounted() {
		this.handleQuery()
		EventBus.$on('fresh-list', () => {
			this.handleQuery()
		})
	},
	methods: {
		getList() {
			this.pageData.current = 1
			this.handleQuery()
		},
		handleQuery() {
			const params = {
				PageIndex: this.pageData.current,
				PageSize: this.pageData.pageSize,
			}
			if (this.taskName) {
				params.Name = this.taskName
			}
			if (this.state && this.state !== '全部') {
				params.Enable = this.state === 1 ? true : false
			}
			if (this.sysCode === 'dc') {
				getVillagePlanList(params).then(res => {
					const { result = '' } = res
					const { Datas = [], Total = 0 } = result
					this.tableData = Datas.map((item, index) => {
						return {
							key: index + 1,
							...item,
						}
					})
					this.pageData.total = Total
				})
			} else if (this.sysCode === 'eg') {
				getSupplyPlanList(params).then(res => {
					const { result = '' } = res
					const { Datas = [], Total = 0 } = result
					this.tableData = Datas.map((item, index) => {
						return {
							key: index + 1,
							...item,
						}
					})
					this.pageData.total = Total
				})
			}
		},
		handleCreate(type, row) {
			this.$emit('create', { type, row })
		},
		//删除巡检计划
		handleDelete(row) {
			const { Id } = row
			if (this.sysCode === 'dc') {
				deleteVillagePlan(Id)
					.then(res => {
						// if (typeof res.result === 'boolean' && res.result) {
						if (res.result.indexOf('删除成功') > -1 && res.result) {
							this.$Message.success('删除成功!')
							this.handleQuery()
						} else {
							this.$Message.error(typeof res.result === 'string' ? res.result : '删除失败')
						}
					})
					.catch(() => {
						this.$Message.info('删除失败!')
					})
			} else if (this.sysCode === 'eg') {
				deleteSupplyPlan(Id)
					.then(res => {
						// if (typeof res.result === 'boolean' && res.result) {
						if (res.result.indexOf('删除成功') > -1 && res.result) {
							this.$Message.success('删除成功!')
							this.handleQuery()
						} else {
							this.$Message.error(typeof res.result === 'string' ? res.result : '删除失败')
						}
					})
					.catch(() => {
						this.$Message.info('删除失败!')
					})
			}
		},
		//切换页码
		handlePageChange(page) {
			this.pageData.current = page
			this.handleQuery()
		},
		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize
			this.handleQuery()
		},
		//改变巡检状态
		changeState(value, Id) {
			if (this.submitLoading) {
				return
			}
			this.submitLoading = true
			try {
				new Promise(resolve => {
					if (this.sysCode === 'eg') {
						getSupplyPlanDetail({ Id }).then(res => {
							resolve(res.result)
						})
					} else {
						getVillagePlanDetail({ Id }).then(res => {
							resolve(res.result)
						})
					}
				})
					.then(result => {
						const {
							Id,
							Name,
							Executor,
							ExecutorID,
							PeriodUnit,
							PlanPeriod,
							AdvanceNoticeDate,
							Devices,
							CreateUser,
						} = result
						const params = {
							PatrolName: Name,
							CheckingPeople: Executor,
							CheckingPeople_ID: ExecutorID,
							State: value,
							Models: Devices,
							AdvanceNoticeDate,
							CreateName: CreateUser,
							Frequency: PeriodUnit,
							Frequency_Date: PlanPeriod,
						}

						if (this.sysCode === 'eg') {
							updateSupplyPlan(Id, params).then(res => {
								if (typeof res.result === 'boolean' && res.result) {
									this.$Message.success('操作成功！')
									this.handleQuery()
									this.submitLoading = false
								} else {
									this.$Message.error(typeof res.result === 'string' ? res.result : '操作失败')
									this.submitLoading = false
									this.handleQuery()
								}
							})
						} else {
							updateVillagePlan(Id, params).then(res => {
								if (typeof res.result === 'boolean' && res.result) {
									this.$Message.success('操作成功！')
									this.handleQuery()
									this.submitLoading = false
								} else {
									this.$Message.error(typeof res.result === 'string' ? res.result : '操作失败')
									this.submitLoading = false
									this.handleQuery()
								}
							})
						}
					})
					.catch(() => {
						this.submitLoading = false
					})
			} catch {
				this.submitLoading = false
			}
		},
	},
}
</script>
<style lang="less" scoped>
.plan-list {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-content {
		margin-top: 16px;
		height: 100%;
	}
	&-form {
		width: fit-content;
		margin-right: 16px;
		&-title {
			margin-right: 4px;
		}
		.select {
			width: 180px;
		}
	}
}
::v-deep {
	.ivu-switch-checked {
		border-color: rgb(25, 190, 107);
		background-color: rgb(25, 190, 107);
	}
	.ivu-switch-checked:after {
		left: 43px;
	}
}
</style>
