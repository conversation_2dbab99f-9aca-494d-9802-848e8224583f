<template lang="pug">
.record-list
	water-row.record-list-header(justify='flex-start', align='center')
		.record-list-form-title 月份:
		DatePicker.water-margin-right-16(
			v-model='date',
			format='yyyy-MM',
			type='month',
			:options='options',
			style='width: 215px',
			placement='bottom-end'
		)
		Button(type='primary', @click='handleQuery') 查询
		Button.water-margin-left-16(type='primary', @click='handleExport()') 导出
	#qrcodeDowm(style='display: none')
	WaterTable.record-list-table(border, :columns='columns', :data='tableData', :loading='loading')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { queryEgFillDataRecord } from '@/api/water-screen-data.js'
import { EventBus } from '@/utils/eventBus.js'
import { exportFile } from '@/utils/function.js'
export default {
	name: 'record-list',
	components: {
		WaterTable,
		WaterRow,
	},
	mounted() {
		this.handleQuery()
		EventBus.$on('fresh-eg-record', () => {
			this.handleQuery()
		})
	},
	data() {
		return {
			loading: false,
			date: new Date(`${new Date().getFullYear()}-${new Date().getMonth() + 1}`),
			tableData: [],
			options: {
				disabledDate(date) {
					return date.getTime() > new Date().getTime()
				},
			},
			columns: [
				{
					title: '区域',
					key: 'stationName',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '服务小区数（个）',
					key: 'supplyCommunityNum',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '供水户数（户）',
					key: 'supplyUserNum',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '服务人口（人）',
					key: 'supplyPopulationNum',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '水质合格率',
					key: 'waterQualityRate',
					align: 'center',
					minWidth: 160,
				},
				{
					title: '设备完好率',
					key: 'integrityRate',
					align: 'center',
					minWidth: 160,
				},

				{
					title: '最近修改时间',
					key: 'updateTime',
					align: 'center',
					minWidth: 160,
				},
			],
		}
	},
	methods: {
		handleQuery() {
			queryEgFillDataRecord({
				date: this.$moment(this.date).format('YYYY-MM'),
			}).then(res => {
				const { result = [] } = res
				this.tableData = result
				this.tableData.sort((a, b) => {
					return this.$moment(a.updateTime) - this.$moment(b.updateTime)
				})
			})
		},
		handleExport() {
			const baseUrl = `${location.origin}/api`
			let url =
				baseUrl +
				'/waterPlat/fillData/queryEgFillDataRecordExport?date=' +
				this.$moment(this.date).format('YYYY-MM')
			exportFile(url)
		},
	},
}
</script>
<style scoped lang="less">
.record-list {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-form {
		margin-right: 24px;
		&-title {
			margin-right: 4px;
		}
		&-select {
			width: 160px;
			margin-right: 24px;
		}
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
}
</style>
