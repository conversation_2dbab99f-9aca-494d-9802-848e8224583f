<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-07-19 09:01:33
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-07-25 10:11:58
-->
<template lang="pug">
.change-config
	.change-config-container
		water-row(justify='space-between', align='center')
			water-row(justify='flex-start', align='center')
				.change-config-title 站点编码:
				Select(
					style='width: 200px',
					v-model='stationCode',
					filterable,
					remote,
					clearable,
					:remote-method='remoteMethod',
					:loading='loading1'
				)
					Option(v-for='(option, index) in stationCodeList', :value='option', :key='index') {{ option }}
				.change-config-title.water-margin-left-8 业务来源:
				Select.water-margin-left-8(
					v-model='sysCode',
					placeholder='请选择',
					style='width: 200px',
					:clearable='true',
					:transfer='true'
				)
					Option(v-for='(item, index) in list', :key='index', :value='item.value') {{ item.label }}
				Button.water-margin-left-8(type='primary', @click='getList()') 查询
			Button(type='primary', @click='handleCreate("add")') 添加
		.change-config-content
			es-table.change-config-table.water-table(
				border,
				:columns='columns',
				:data='tableData',
				:loading='loading',
				showPage,
				:pageData='pageData',
				@on-page-num-change='handlePageChange'
			)
				template(slot-scope='{ row }', slot='action')
					Button(type='primary', size='small', @click='handleCreate("modify", row)') 编辑
					Poptip(confirm, title='记录删除后用量统计可能会异常,请确认是否删除?', :transfer='true', @on-ok='handleDelete(row.id)')
						Button.water-margin-left-8(type='error', size='small') 删除
	add-modal(ref='addModalRef', :show.sync='showModal', :type='type')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import AddModal from './components/AddModal.vue'
import { queryPageList, deleteRecord, queryStationCode } from '@/api/change-config.js'
const obj = {
	dd: '运行调度',
	dc: '单村供水',
	eg: '二次供水',
}
export default {
	components: {
		WaterTable,
		WaterRow,
		AddModal,
	},
	data() {
		return {
			showModal: false,
			type: 'add',
			stationCode: null,
			stationCodeList: [],
			loading1: false,
			sysCode: '',
			list: [
				{ label: '运行调度', value: 'dd' },
				{ label: '二次供水', value: 'eg' },
				{ label: '单村供水', value: 'dc' },
			],
			columns: [
				{
					title: '序号',
					key: 'key',
					align: 'center',
					width: 70,
				},
				{
					title: '业务来源',
					key: 'sysCode',
					align: 'center',
					minWidth: 120,
					render: (h, { row }) => {
						const text = obj[row.sysCode] ? obj[row.sysCode] : ''
						return h('div', text)
					},
				},
				{
					title: '换表时间',
					key: 'fixTime',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '站点编码',
					key: 'stationCode',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '数据项',
					key: 'itemCode',
					align: 'center',
					minWidth: 120,
					render: (h, { row }) => {
						const { itemCode, itemName } = row
						return h('div', `${itemCode}(${itemName})`)
					},
				},
				{
					title: '原表底数',
					key: 'preFixData',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '新表底数',
					key: 'afterFixData',
					align: 'center',
					minWidth: 120,
				},

				{
					title: '换表人',
					key: 'createUser',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '备注',
					key: 'memo',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '操作',
					slot: 'action',
					align: 'center',
					width: 150,
				},
			],
			tableData: [],
			loading: false,
			pageData: {
				showTotal: true,
				current: 1,
				pageSize: 20,
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	mounted() {
		this.handleQuery()
	},
	methods: {
		getList() {
			this.pageData.current = 1
			this.handleQuery()
		},
		handleQuery() {
			const params = {
				pageNum: this.pageData.current,
				pageSize: this.pageData.pageSize,
				stationCode: this.stationCode,
				sysCode: this.sysCode,
			}
			queryPageList(params).then(res => {
				const { result = '' } = res
				const { list = [], total = 0 } = result
				this.tableData = list.map((item, index) => {
					return {
						...item,
						key: index + 1,
					}
				})
				this.pageData.total = total
			})
		},
		handleDelete(id) {
			deleteRecord(id)
				.then(() => {
					this.$Message.success('删除成功')
					this.handleQuery()
				})
				.catch(() => {
					this.$Message.error('删除失败')
				})
		},
		handleCreate(type, row) {
			this.type = type
			this.showModal = true
			if (type === 'modify' && row) {
				this.$refs.addModalRef.init(row)
			}
		},
		handlePageChange(pageNum) {
			this.pageData.current = pageNum
			this.handleQuery()
		},
		remoteMethod(query) {
			this.loading1 = true
			if (query) {
				queryStationCode(query)
					.then(res => {
						const { result = [] } = res
						this.stationCodeList = result
						this.loading1 = false
					})
					.catch(() => {
						this.loading1 = false
					})
			}
		},
	},
}
</script>
<style lang="less" scoped>
.change-config {
	height: 100%;
	padding: 16px;
	background-color: rgb(242 242 242) !important;
	display: flex;
	flex-direction: column;
	&-container {
		height: 100%;
		padding: 16px;
		background: #fff;
		flex: 1;
	}
	&-content {
		margin-top: 16px;
		height: calc(100% - 48px);
	}
	&-title {
		white-space: nowrap;
		margin-right: 4px;
	}
}
</style>
