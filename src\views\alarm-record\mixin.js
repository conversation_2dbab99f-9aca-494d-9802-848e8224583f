import { apiGetAlarmRecord } from '@/api/alarm-config.js'

export const alarmRecordMixin = {
  data() {
    return {
      showDialog: false,
      tableData: [],
      alarmId: 0,
    }
  },
  methods: {
    handleSelect(row) {
      console.log('点击行row', row)
    },
    handleShowDetail(row) {
      this.$router.push({
        path: '/alarm-record/detail',
        query: {
          id: row?.id,
        },
      })
      console.log('点击详情row', row)
    },
    handleShowDialog(row) {
      console.log('row=========111111111111111', row)
      this.showDialog = true
      this.alarmId = row?.id
      console.log('点击登记窗口this.alarmId', this.alarmId)
    },
    handleUpdate() {
      this.getAlarmRecord()
    },
    // 判断一个对象是否为空值
    removeEmptyProperties(obj) {
      for (const key in obj) {
        if (obj[key] === null || obj[key] === undefined || obj[key] === '') {
          delete obj[key]
        }
        // 空数组也清除属性
        if (key === 'nodeTypes' && Array.isArray(obj[key])) {
          const allEmpty = obj[key].every(
            item => item === null || item === undefined || item === ''
          )
          if (allEmpty) {
            delete obj[key]
          }
        }
      }
    },
    getAlarmRecord(searchParams = {}) {
      this.tableData = []
      const params = {
        ...searchParams,
        ...this.pageParams,
      }
      this.removeEmptyProperties(params)
      console.log('告警记录查询params', params)
      apiGetAlarmRecord(params).then(res => {
        const { result } = res
        if (result?.list && result?.list.length) {
          this.tableData = result?.list
        }
        this.pageParams.total = result.total
      })
    },
    // 查询分页表格
    handleSearch(params) {
      this.getAlarmRecord(params)
    },
    // 重置查询数据
    handleReset() {
      this.getAlarmRecord({})
    },
    changePage(pageNum) {
      this.pageParams.pageNum = pageNum
      this.getAlarmRecord({})
    },
    onPageSizeChange(pageSize) {
      this.pageParams.pageSize = pageSize
      this.getAlarmRecord({})
    },
  },
  mounted() {
    this.getAlarmRecord()
  },
}