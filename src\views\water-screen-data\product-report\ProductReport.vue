<!--
 * @Description: 大屏生成调度填报
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-30 14:34:21
 * @LastEditors: ho<PERSON><PERSON>
 * @LastEditTime: 2022-12-06 14:30:04
-->
<template lang="pug">
.product-report
	.product-report-content
		Tabs(v-model='tabValue', :animated='false')
			TabPane(label='生产调度填报', name='fill')
				fill-list(v-if='tabValue === "fill"', :ownership='ownership')
			TabPane(label='填报记录', name='record')
				record-list(v-if='tabValue === "record"', :ownership='ownership')
</template>
<script>
import FillList from './modules/product-report-fill.vue'
import RecordList from './modules/product-report-record.vue'
import { currentUser } from '@/api/common'
export default {
	name: 'data-fill-management',
	components: {
		FillList,
		RecordList,
	},
	created() {
		// 获取登录用户信息 保存到session中
		currentUser().then(res => {
			const { result = {} } = res
			this.ownership = result.ownership || ''
		})
	},
	data() {
		return {
			tabValue: 'fill',
			ownership: '',
		}
	},
}
</script>
<style lang="less" scoped>
.product-report {
	height: 100%;
	padding: 16px;
	background-color: rgb(242 242 242) !important;
	&-content {
		height: 100%;
		width: 100%;
		padding: 16px;
		background: #fff;
	}
	::v-deep {
		.ivu-tabs {
			display: flex;
			flex-direction: column;
			width: 100%;
			height: 100%;
			.ivu-tabs-content {
				flex: 1;
			}
			.ivu-tabs-tabpane {
				height: 100%;
			}
		}
	}
}
</style>
