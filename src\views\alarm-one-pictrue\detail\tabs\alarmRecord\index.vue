<template>
	<div class="alarm-record" v-loading="loading">
		<div class="filter">
			<gc-custom-search
				class="search"
				width="500px"
				key-word="告警日期"
				type="date"
				date-picker-type="datetimerange"
				:search.sync="params['first']"
			>
				<template v-slot:icon>
					<i class="iconfont icon-riqi"></i>
				</template>
			</gc-custom-search>
			<gc-custom-search
				class="search"
				width="300px"
				key-word="告警时序"
				type="select"
				required
				:search.sync="params['alarmSeq']"
				:search-option="options['alarmSeq']"
			></gc-custom-search>
			<gc-custom-search
				class="search"
				width="300px"
				key-word="告警等级"
				type="select"
				needAllForSearch
				:search.sync="params['alarmLevel']"
				:search-option="alarmLevelList"
				:search-option-key="{ label: 'name', value: 'defaultValue' }"
			></gc-custom-search>
			<gc-custom-search
				class="search"
				width="300px"
				key-word="告警分类"
				type="select"
				needAllForSearch
				:search.sync="params['alarmTypeId']"
				:search-option="alarmTypeList"
				:search-option-key="{ label: 'name', value: 'defaultValue' }"
			></gc-custom-search>
			<el-button v-click-blur type="primary" class="query" @click="handleSearch">查 询</el-button>
			<el-button v-click-blur @click="reset">重 置</el-button>
		</div>
		<div class="table-container">
			<Table border highlight-row :columns="columns" :data="tableData" @on-row-click="handleSelect" height="800">
				<template slot-scope="{ row }" slot="option">
					<div class="options-container">
						<Button type="text" :style="{ color: '#3AA7D8' }" size="small" @click="handleShowDetail(row)">
							详情
						</Button>
						<Button
							type="text"
							:style="{ color: '#3AA7D8' }"
							:disabled="row.disposeStatus == '10'"
							size="small"
							@click="handleShowDialog(row)"
						>
							处理登记
						</Button>
					</div>
				</template>
			</Table>
			<div class="page-wrap">
				<Page
					:total="pageParams.total"
					:current="pageParams.pageNum"
					:page-size="pageParams.pageSize"
					size="small"
					show-sizer
					show-total
					@on-page-size-change="onPageSizeChange"
					@on-change="changePage"
				/>
			</div>
		</div>
		<RegistrationDialog
			v-model="showDialog"
			title="告警处理登记"
			@update="handleUpdate"
			:id="alarmId"
		></RegistrationDialog>
	</div>
</template>

<script>
import { isBlank } from '@/utils/util.js'
import { alarmRecordMixin } from '../../../../alarm-record/mixin.js'
import RegistrationDialog from '../../../../alarm-record/dialog/index.vue'
import { apiGetAlarmLevelList, apiGetAlarmTypeList } from '@/api/alarm-config.js'

export default {
	name: 'alarmRecord',
	components: { RegistrationDialog },
	mixins: [alarmRecordMixin],
	props: {
		curTabName: String,
	},
	data() {
		return {
			pageParams: {
				pageNum: 1,
				pageSize: 20,
				total: 0,
				first: null,
				alarmLevel: null,
				deviceId: null,
				alarmTypeId: null,
			},
			columns: [
				{
					title: '告警类型',
					key: 'alarmTypeName',
					align: 'center',
				},
				{
					title: '告警等级',
					key: 'alarmLevelName',
					align: 'center',
				},
				{
					title: '告警值',
					key: 'alarmValue',
					align: 'center',
				},
				{
					title: '设定值',
					key: 'alarmLimit',
					align: 'center',
				},
				{
					title: '开始时间',
					key: 'beginTime',
					align: 'center',
				},
				{
					title: '最后报警时间',
					key: 'lastActiveTime',
					align: 'center',
				},
				{
					title: '处理状态',
					key: 'disposeStatusDesc',
					align: 'center',
				},
				{
					title: '操作',
					slot: 'option',
					align: 'center',
					width: '160px',
				},
			],
			alarmTypeList: [], // 告警类型字典
			alarmLevelList: [], // 告警等级字典
			loading: false,
		}
	},
	computed: {
		// 告警状态
		alarmStatus() {
			return this.$store.getters.dataList.alarmStatus || []
		},
	},
	watch: {
		curTabName: {
			immediate: true,
			handler(val) {
				if (val === 'AlarmRecord' && !this.tableData.length) {
					this.reset()
				}
			},
		},
	},
	methods: {
		handleSizeChange(size) {
			this.params.size = size
			this.getAlarmList(1)
		},
		getAlarmList(page) {
			if (isBlank(this.params.alarmSeq)) {
				this.$message.warning('告警时序不能为空')
				return
			}
			this.loading = true
			this.params.deviceId = this.$attrs.common
			this.params.current = page
			let obj = {}
			console.log(this.params)
			for (var key in this.params) {
				let val = this.params[key]
				if (this.params[key]) {
					if (key === 'first') {
						obj['startLastActiveTime'] = this.dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss')
						obj['endLastActiveTime'] = this.dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss')
					} else {
						obj[key] = val
					}
				}
			}
		},
		query() {
			this.getAlarmList(1)
		},
		pageChange(page) {
			this.getAlarmList(page)
		},
		reset() {
			this.pageParams.first = null
			this.pageParams.alarmLevel = null
			this.pageParams.alarmTypeId = null
			this.pageParams.pageNum = 1
			this.handleSearch(this.pageParams)
		},
	},
	mounted() {
		apiGetAlarmTypeList({ sysCode: 'jz' }).then(res => {
			const { result } = res
			if (result) {
				this.alarmTypeList = Object.keys(result).map(key => ({
					label: key,
					value: result[key],
				}))
			} else {
				this.alarmTypeList = []
			}
		})
		apiGetAlarmLevelList().then(res => {
			const { result } = res
			if (result.length) {
				this.alarmLevelList =
					result.length > 0
						? result?.map(item => {
								return {
									label: item?.name,
									value: item?.code,
								}
						  })
						: []
			}
		})
	},
}
</script>
<style lang="scss" scoped>
.alarm-record {
	@include base-button(80px);
	padding: 0 24px;
	height: 100%;
	display: flex;
	flex-direction: column;
	.filter {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin: 20px 0 24px;
		.query {
			margin-left: 20px;
		}
		.search {
			margin-right: 20px;
		}
	}
	.table {
		flex: 1;
		height: 0;
	}
}
</style>
