<template lang="pug">
.guide-classify
	.header-wrap
		es-header.header(title='指南分类')
	.guide-classify-content
		WaterRow
			.role-list
				.role(
					v-for='(item, index) in roleList',
					:key='index',
					:class='{ active: activeIndex === index }',
					@click='handleChangeRole(index)'
				) {{ item.name }}
			.guide-list
				Table.guide-classify-table(:columns='columns', :data='tableData', :loading='loading', :height='700', border)
					template(slot-scope='{ row }', slot='mapUrl')
						img(:src='row.mapUrl', :style='{ height: "30px" }')
					template(slot-scope='{ row }', slot='action')
						Button(type='primary', size='small', @click='handleDownLoad(row)') 下载查看
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import { getGuideTypeList, queryRoleGuide } from '@/api/other'

export default {
	name: 'guide-classify',
	components: {
		WaterRow,
	},
	props: {},
	data() {
		return {
			currentRow: {},
			loading: false,
			columns: [
				{
					title: '指南标题',
					key: 'title',
				},
				{
					title: '所属分类',
					key: 'typeName',
				},
				{
					title: '操作',
					align: 'center',
					slot: 'action',
				},
			],
			activeIndex: 0,
			roleList: [],
			guideList: [],
		}
	},
	computed: {
		activeId() {
			return this.roleList[this.activeIndex]?.id || ''
		},
		tableData() {
			const arr = this.guideList.filter(item => item.id === this.activeId)
			if (arr.length > 0) {
				const newArr = arr[0].guideInfos.map(item => {
					item.typeName = arr[0].name
					return item
				})
				return newArr
			} else {
				return []
			}
		},
	},
	watch: {},
	created() {},
	mounted() {
		this._getGuideTypeList()
		this._queryRoleGuide()
	},
	beforeDestroy() {},
	methods: {
		_getGuideTypeList() {
			getGuideTypeList()
				.then(res => {
					const data = res.result
					if (data) {
						this.roleList = data
					}
				})
				.catch(() => {})
		},
		_queryRoleGuide() {
			queryRoleGuide().then(res => {
				this.guideList = res.result
			})
		},
		handleChangeRole(index) {
			this.activeIndex = index
		},
		downloadImage(url, name) {
			let link = document.createElement('a')

			fetch(url)
				.then(res => res.blob())
				.then(blob => {
					link.href = URL.createObjectURL(blob)
					link.style.display = 'none'
					link.download = name
					link.click()
				})
		},
		handleDownLoad(row) {
			const url = JSON.parse(row.filePath)[0].url
			const name = JSON.parse(row.filePath)[0].name

			this.downloadImage(url, name)
		},
	},
}
</script>

<style lang="less" scoped>
.guide-classify {
	width: 100%;
	height: 100%;
	padding: 0 16px;
	.table-btn-wrap {
		margin: 10px 0;
	}
	.guide-classify-content {
		width: 100%;
		height: calc(100vh - 160px);
		.icon-eye {
			margin-left: 5px;
			cursor: pointer;
			color: #41a8ed;
		}
	}
}

.role-list {
	margin-right: 10px;
	padding: 10px;
	width: 240px;

	overflow: auto;
	border: 1px solid #e4e7ed;
	.role {
		padding: 4px 10px;
		cursor: pointer;
		font-size: 14px;
	}
	.active {
		background-color: #ecf5ff;
	}
}
.guide-list {
	display: flex;
	flex-direction: column;
	flex: 1;
	padding: 10px;
	height: 100%;
	overflow: auto;
	border: 1px solid #e4e7ed;
	.save-button {
		margin-left: auto;
		margin-bottom: 10px;
	}
	.report-container {
		flex: 1;
		overflow: auto;
	}
}
</style>
