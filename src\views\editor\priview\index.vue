<template lang="pug">
.priview
	template(v-if='id')
		editor.config-content(ref='editor', :appZoomFlag='appZoomFlag', :imgHeight='imgHeight', :imgWidth='imgWidth')
	no-data(v-else, title='无流程图，请先检查是否关联')
	Button.back-button(v-show='back', size='small', @click='handleBackBtn') 返回
	//- Button.rotate-button(
	//- 	v-if="deviceType === 'app'",
	//- 	size="small",
	//- 	@click="handleRotate") 旋转
</template>
<script>
import editor from './components/EditorPriview.vue'
import NoData from '@/components/gc-no-data'
import { queryByFlowId, itemDate } from '@/api/editor'
import { getVal } from '@/utils/function.js'
import { fabricGif } from '@/components/gc-fabric/utils/fabricGif'
import { controlType2FileNameMap, controlTypes2AttrtempMap } from '../enum.js'
// import { useScreenOrientation } from '@vueuse/core'

// 路由参数说明
// id: 厂站id
// back: 是否显示返回按钮
// sysUserId: app端需要的用户id
// deviceType: 展示的设备类型 pc app
// curve: 数据项控件 点击触发in内部（该项目自带的曲线弹窗）、out外部（自定义）
// video: 视频控件 点击后触发in内部(该项目自带的视频弹窗)、out外部（自定义）

let refreshObj = {}
let countObj = {}

// 上传OSS图片地址
const imgOnOssBaseUrl = `${window.location.origin}/group1/eslink-iot`

// 多个数据项控制的一个控件状态 = 变量名+控件序号={详细信息multipleItem}
let multipleObj = {}
// 动态变量缓存  varContext[index]={变量名1:multipleItem1,变量名1:multipleItem2}
let varContext = {}
export default {
	name: 'config-page',
	components: {
		NoData,
		editor,
	},
	data() {
		return {
			sysCode: 'dd',
			Interval: 30000,
			rotate: 0,
			appZoomFlag: false,
			timer: 0,
			imgHeight: 0,
			imgWidth: 0,

			// 厂站id
			id: '',
			// 是否显示返回按钮
			back: false,
			// app端需要的用户id
			sysUserId: '',
			// 设备类型：pc app
			deviceType: 'pc',

			// 上一次 画布元素偏移量
			// lastTimeLeft: 0,
			// lastTimeTop: 0,
		}
	},
	async mounted() {
		const { id = '', back = false, sysUserId = '', deviceType = 'pc', screencheck } = this.$route.query
		this.id = id
		this.back = !!back
		this.sysUserId = sysUserId
		this.deviceType = deviceType
		window.rotationAngle = 0

		// const {
		// 	isSupported,
		// 	orientation,
		// 	angle,
		// 	lockOrientation,
		// 	unlockOrientation,
		// } = useScreenOrientation()

		// console.log('angle', angle);
		// lockOrientation('portrait-primary')
		// unlockOrientation()

		if (this.id) {
			const { result = {} } = await this.queryByFlowId(this.id, this.sysUserId, screencheck)

			const { canvasWidth, canvasHeight, refreshTime = 30, rotate, appZoomFlag } = JSON.parse(result.canvasStyle)
			this.Interval = refreshTime * 1000
			this.rotate = rotate
			this.appZoomFlag = appZoomFlag
			await this.render(result)

			this.setPolling()

			window.addEventListener('resize', () => {
				this.autoScale(canvasWidth, canvasHeight)
			})

			if (deviceType === 'app') {
				this.handleRotate()
			}
		}
	},
	methods: {
		// api 请求
		queryByFlowId(id, sysUserId = '', screencheck) {
			return queryByFlowId(id, { needData: true, sysUserId }, screencheck).then(res => {
				return res
			})
		},
		// 轮询模块
		setPolling() {
			// 刷新方法体
			this.updateData()
			this.updateMultipleData()

			this.timer && clearTimeout(this.timer)
			this.timer = setTimeout(this.setPolling, this.Interval)
		},
		// 轮询更新数据
		updateData() {
			let obj = {}
			let params = []
			for (const key in refreshObj) {
				if (Object.hasOwnProperty.call(refreshObj, key)) {
					const element = refreshObj[key]
					const { stationCode, sysCode, itemRealCode } = element
					if (stationCode && sysCode && itemRealCode) {
						let keyName = stationCode + sysCode

						if (!obj[keyName]) {
							params.push({
								stationCode: stationCode,
								sysCode: sysCode,
							})
							obj[keyName] = []
						}
						obj[keyName].push(itemRealCode)
					} else if (element.itemData && element.itemData.length > 0) {
						element.itemData.forEach(item => {
							let keyName = item.stationCode + item.sysCode

							if (!obj[keyName]) {
								params.push({
									stationCode: item.stationCode,
									sysCode: item.sysCode,
								})
								obj[keyName] = []
							}
							obj[keyName].push(item.itemRealCode)
						})
					}
				}
			}

			params.forEach(item => {
				const { stationCode, sysCode } = item

				item.itemCodes = obj[stationCode + sysCode]
			})

			itemDate(params, this.sysUserId).then(res => {
				const { result } = res
				result.forEach(item => {
					const { stationCode, sysCode } = item
					item.stationDataItem.forEach(data => {
						const { itemRealCode, itemName, value, unit } = data
						const key = sysCode + stationCode + itemRealCode
						if (countObj[key] > 1) {
							for (let index = 1; index < countObj[key] + 1; index++) {
								let option = null
								let newKey = key
								if (index === 1) {
									option = refreshObj[key]
								} else {
									newKey = key + index
									option = refreshObj[key + index]
								}
								this.updateControl({
									key: newKey,
									itemName,
									value,
									unit,
									option,
								})
							}
						} else {
							const option = refreshObj[key]
							this.updateControl({
								key,
								itemName,
								value,
								unit,
								option,
							})
						}
					})
				})
			})
		},
		// 多个数据项控制的一个控件状态
		updateMultipleData() {
			let obj = {}
			let params = []
			for (const key in multipleObj) {
				if (Object.hasOwnProperty.call(multipleObj, key)) {
					const { itemRealCode, sysCode, stationCode } = multipleObj[key]
					let keyName = stationCode + sysCode

					if (!obj[keyName]) {
						params.push({
							stationCode,
							sysCode,
						})
						obj[keyName] = []
					}
					obj[keyName].push(itemRealCode)
				}
			}

			params.forEach(item => {
				const { stationCode, sysCode } = item

				item.itemCodes = obj[stationCode + sysCode]
			})

			itemDate(params, this.sysUserId).then(res => {
				for (const key in multipleObj) {
					if (Object.hasOwnProperty.call(multipleObj, key)) {
						const multipleItem = multipleObj[key]
						res.result.forEach(item => {
							const { stationCode, sysCode } = item
							if (multipleItem.stationCode === stationCode && multipleItem.sysCode === sysCode) {
								item.stationDataItem.forEach(it => {
									const { itemRealCode } = it
									if (itemRealCode === multipleItem.itemRealCode) {
										multipleItem.value = it.value
									}
								})
							}
						})
						this.updateMultipleControl({
							option: multipleItem.option,
						})
					}
				}
			})
		},
		updateControl(obj) {
			const { key, itemName, value, unit, option } = obj
			let newTextObj = ''
			if (option) {
				if (option.type === 'TEXT') {
					const { hideTitle, hideUnit = false } = option
					let text = hideTitle ? `${getVal(value)}` : `${itemName}:${getVal(value)}`
					text = hideUnit ? text : `${text}${unit}`
					newTextObj = this.$refs.editor.changeObjControlAttr(option, 'text', text)
					this.$refs.editor.toTopLayer(newTextObj)
				}
				refreshObj[key] = newTextObj
			}
		},
		async updateMultipleControl(obj) {
			let { option } = obj
			if (option) {
				if (option.itemData && option.scene) {
					const { type, index, iconUrl = '', scene = [] } = option
					scene.forEach(async item => {
						// condition格式  a === 1 && b ===2
						const { condition, filter, switchMode = 'filter', color, auto } = item

						// 处理判断条件
						// const regex = /([\u4e00-\u9fa5\da-z]+)\s*(>=|>|<|<=|===|==)/gi

						// const prefix = `multipleObj.`
						// const newCond = condition.replace(
						// 	regex,
						// 	prefix + `$1${index}.value$2`,
						// )
						let newCond = ''
						const canvas = this.$refs.editor.$refs.canvas.canvas
						try {
							// 重写 根据上下文判断逻辑；
							Object.keys(varContext[index]).forEach(function (varName) {
								let val = varContext[index][varName].value
								if (val === '') {
									val = "''"
								}
								newCond += 'let ' + varName + '=' + val + ';'
							})
							newCond += condition
							console.log(index + '表达式配置：' + JSON.stringify(item) + '\r\n 生成表达式：' + newCond)
							let flag = eval(newCond)
							console.log('解析场景:' + item.name + '，结果:' + flag)
							if (flag) {
								// 滤镜切换
								if (switchMode === 'filter') {
									this.$refs.editor.changeObjControl(option, 'red', filter)
								} else if (switchMode === 'image') {
									// 后期数据添加场景且自定义数据
									if (auto && option.interactive) {
										// option.setSrc(`${color}`, () => {
										// 	canvas.renderAll()
										// })
										const suffix = this.getSuffix(iconUrl)
										const url = `${color}`
										this.renderControl(suffix, url, option, canvas)
									} else {
										const suffix = this.getSuffix(iconUrl)
										const url = `${imgOnOssBaseUrl}/${type}-${color}.${suffix}`
										// 图片切换：使用setSrc重新设置控件图片
										// 支持自定义控件（type值中包含controls）状态图片切换
										if (type.indexOf('controls') !== -1) {
											// 获取图片后缀名=
											this.renderControl(suffix, url, option, canvas)
										} else {
											// 固定控件状态切换
											const fileName = controlType2FileNameMap[type]
											option.setSrc(
												require(`@/assets/images/controls/${fileName}-${color}.png`),
												() => {
													canvas.renderAll()
												},
											)
										}
									}
								}
							}
						} catch (error) {
							console.log('🚀 ~ file: index.vue:275 ~ updateMultipleControl ~ error:', error)
							console.log(index + '表达式配置：' + JSON.stringify(item) + '\r\n 生成表达式：' + newCond)
						}
					})
				}
			}
		},
		// 页面渲染
		async render(result) {
			return new Promise(resolve => {
				const { controlsBinds = [], canvasStyle, backgroundImageUrl } = result

				const { canvasWidth, canvasHeight, canvasScale, backgroundColor = '#ddd' } = JSON.parse(canvasStyle)

				this.imgWidth = canvasWidth
				this.imgHeight = canvasHeight

				this.$nextTick(async () => {
					const priview = document.querySelector('.priview')
					priview.style.backgroundColor = backgroundColor

					await this.$refs.editor.setBackImage(
						{
							imageUrl: backgroundImageUrl,
							canvasScale,
						},
						true,
					)
					this.$refs.editor.setBackColor(backgroundColor)

					const { viewportTransform, zoomValue } = this.getCanvasPositionData(canvasWidth, canvasHeight)
					let linkArray = []
					for (let index = 0; index < controlsBinds.length; index++) {
						const control = controlsBinds[index]

						const type = JSON.parse(control.baseStyle).type

						if (this.deviceType === 'app' && (type === 'VIDEO' || type === 'VIDEO_PANEL')) {
							continue
						}

						const controlObj = await this.$refs.editor.priviewRender(control, false, false, {
							viewportTransform,
							zoomValue,
						})
						if (type === 'LINK') {
							linkArray.push(controlObj)
						}
						if (!controlObj) {
							return
						}
						const { itemRealCode = '', sysCode = '', stationCode = '', itemData = [], index } = controlObj
						// 三个值都存在 则是单个数据项控制控件状态
						if (sysCode && stationCode && itemRealCode) {
							const key = sysCode + stationCode + itemRealCode
							if (refreshObj[key]) {
								countObj[key] += 1
								refreshObj[key + countObj[key]] = controlObj
							} else {
								countObj[key] = 1
								refreshObj[key] = controlObj
							}
						}
						// 存在itemData 且长度大于0 则是支持多个数据项控制控件状态
						if (itemData && itemData.length > 0) {
							itemData.forEach(item => {
								const {
									name,
									// itemRealCode,
									// sysCode,
									// stationCode,
								} = item
								multipleObj[name + index] = {
									...item,
									value: '',
									option: controlObj,
								}
								// varContext 首次加载初始化
								if (varContext[index] == null) {
									varContext[index] = {}
								}
								// 控件index+变量名 = 实际的绑定变量
								varContext[index][name] = multipleObj[name + index]
							})
						}
					}

					linkArray.forEach(item => {
						this.$refs.editor.toTopLayer(item)
					})

					this.autoScale(canvasWidth, canvasHeight)

					resolve(refreshObj)
				})
			})
		},

		renderControl(suffix, url, option, canvas) {
			// 获取图片后缀名=
			if (suffix === 'gif') {
				fabricGif(url).then(gif => {
					gif.set(
						option.toObject([
							'index',
							'stationCode',
							'itemRealCode',
							'sysCode',
							'type',
							'colorObj',
							'canvasWidth',
							'canvasHeight',
							'selectable',
							'evented',
							'iconUrl',
							'itemData',
							'scene',
							'hoverCursor',
						]),
					)
					canvas.remove(option)
					canvas.add(gif)
					canvas.renderAll()
				})
			} else {
				option.setSrc(url, () => {
					canvas.renderAll()
				})
			}
		},

		// 自动缩放处理
		autoScale(canvasWidth, canvasHeight) {
			this.$nextTick(() => {
				const { canvas, bodyWidth, bodyHeight, zoomValue, viewportTransform } = this.getCanvasPositionData(
					canvasWidth,
					canvasHeight,
				)

				// 将浏览器的宽高设置给画布大小
				canvas.setWidth(bodyWidth)
				canvas.setHeight(bodyHeight)
				canvas.renderAll()

				// 获取新的画布的中心点
				const center = canvas.getCenter()

				// 设置视口的变换矩阵，使得缩放的中心是画布的中心
				canvas.setViewportTransform([zoomValue, 0, 0, zoomValue, -center.left, -center.top])
				canvas.setZoom(zoomValue)
				// 重新设置视口的变换矩阵，使得画布的内容回到原来的位置
				canvas.setViewportTransform(viewportTransform)

				// 设置svg 动画的缩放
				var svgPanel = document.querySelector('.priview-flow-svg')
				svgPanel.style.transform = 'scale(' + zoomValue + ') translate(-50%, 0%)'
				svgPanel.style.top = viewportTransform[5] + 'px'

				if (this.deviceType !== 'app') {
					// 重新计算设置视频面板位置
					this.$refs.editor.handleVideoPanelPosition(zoomValue, viewportTransform)
				}
				// window.fabricView0 = viewportTransform[0]
				// window.fabricView4 = viewportTransform[4]
				// window.fabricView5 = viewportTransform[5]

				canvas.renderAll()
			})
		},
		// 统一获取 bodyWidth、bodyHeight、zoomValue、viewportTransform值
		getCanvasPositionData(canvasWidth, canvasHeight) {
			const canvas = this.$refs.editor.$refs.canvas.canvas
			const backImgWidth = canvas.backgroundImage?.width || canvasWidth
			const backImgHeight = canvas.backgroundImage?.height || canvasHeight
			const bodyWidth = document.body.offsetWidth
			const bodyHeight = document.body.offsetHeight
			const scaleValue = canvas.backgroundImage?.scaleX || 1
			const zoomValue = this.getZoomValue(canvasWidth, canvasHeight)
			// 背景图偏移量计算
			const offset = {
				left: bodyWidth - backImgWidth * zoomValue * scaleValue,
				top: bodyHeight - backImgHeight * zoomValue * scaleValue,
			}
			return {
				canvas,
				bodyWidth,
				bodyHeight,
				zoomValue,
				backImgWidth,
				backImgHeight,
				viewportTransform: [zoomValue, 0, 0, zoomValue, Math.abs(offset.left) / 2, Math.abs(offset.top) / 2],
			}
		},
		// 获取画布zoom缩放比例
		getZoomValue(canvasWidth, canvasHeight) {
			const bodyWidth = document.body.offsetWidth
			const bodyHeight = document.body.offsetHeight
			let scale = 1
			if (canvasHeight !== 0 && canvasWidth !== 0) {
				scale =
					bodyHeight / canvasHeight > bodyWidth / canvasWidth
						? bodyWidth / canvasWidth
						: bodyHeight / canvasHeight
			}
			return scale
		},

		// 判断元素是否是 固定控件 或 自定义控件
		isInTypes(type) {
			return controlTypes2AttrtempMap[type] || `${type}`.indexOf('controls-') !== -1
		},
		// 获取链接资源文件的后缀名
		getSuffix(url) {
			// 获取问号之前的部分
			const cleanUrl = url.split('?')[0]
			return cleanUrl.substring(cleanUrl.lastIndexOf('.') + 1)
		},

		// 返回操作
		handleBackBtn() {
			this.$router.push({
				path: '/editorList',
				query: {
					filterAuth: this.$route.query.filterAuth,
				},
			})
		},

		// 旋转操作
		handleRotate() {
			if (this.rotate > 0) {
				let element = document.querySelector('.canvas-container')

				window.rotationAngle = this.rotate

				element.children[0].style.transformOrigin = 'center'
				element.children[1].style.transformOrigin = 'center'
				element.children[0].style.transform = 'rotate(' + this.rotate + 'deg)'
				element.children[1].style.transform = 'rotate(' + this.rotate + 'deg)'

				// 旋转 90度时  放大画布
				if (this.rotate === 90) {
					const offsetHeight = document.querySelector('.priview').offsetHeight
					// const offsetWidth = document.querySelector('.priview').offsetWidth
					const canvasWidth = document.getElementById('canvas').offsetWidth
					// const canvasHeight = document.getElementById('canvas').offsetHeight
					const scale = offsetHeight / canvasWidth

					this.$refs.editor.$refs.canvas.setCanvas(canvasWidth * scale, canvasWidth * scale)

					const canvas = this.$refs.editor.$refs.canvas.canvas

					const viewportTransform = canvas.viewportTransform

					viewportTransform[0] *= scale // 水平缩放
					viewportTransform[3] *= scale // 垂直缩放

					canvas.setViewportTransform(viewportTransform)
				}
			}

			// element.style.transform = 'rotate(' + rotationAngle + 'deg)'

			// let element = document.querySelector('.canvas-wrapper')
			// window.rotationAngle = this.rotate
			// // window.rotationAngle = window.rotationAngle % 360

			// // element.style.transform = 'rotate(' + rotationAngle + 'deg)'
			// element.style.transform = 'rotate(' + this.rotate + 'deg)'
			// }
		},

		// 分离字符串条件中的变量名称
		getConditionParams(condition) {
			const regex = /([a-z]\w*)\s*===/gi
			let match
			const variables = []
			while ((match = regex.exec(condition)) !== null) {
				variables.push(match[1])
			}
			return variables
		},
	},
	beforeDestroy() {
		refreshObj = {}
		multipleObj = {}
		varContext = {}
		this.timer && clearTimeout(this.timer)
	},
}
</script>
<style lang="less" scoped>
.priview {
	width: 100%;
	height: 100%;
	overflow: hidden;
}
.back-button,
.rotate-button {
	position: absolute;
	left: 10px;
	top: 10px;
}
.rotate-button {
	top: 60px;
}
::v-deep {
	.text-input .ivu-input {
		border: none;
		height: 40px;
		width: 100%;
		margin: 4px auto;
		text-align: center;
		cursor: pointer;
		font-size: 18px;
		&:focus {
			border: none;
			box-shadow: none;
		}
	}
}
</style>
