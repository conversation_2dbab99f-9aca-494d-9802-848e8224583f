/*
 * @Description: 安防管理
 * @Author: shenxh
 * @Date: 2022-04-01 09:52:36
 * @LastEditors: shenxh
 * @LastEditTime: 2024-10-09 13:21:27
 */

export default [
	// 安防平台管理
	{
		path: '/security-manage/security-platform-manage',
		name: 'security-platform-manage',
		component: (/* webpackChunkName: 'security-platform-manage' */) =>
			import('@/views/security-manage/security-platform-manage'),
	},
	// 安保区域管理
	{
		path: '/security-manage/security-area-manage',
		name: 'security-area-manage',
		component: (/* webpackChunkName: 'security-area-manage' */) =>
			import('@/views/security-manage/security-area-manage'),
	},
	// 视频绑定
	{
		path: '/security-manage/video-binding',
		name: 'video-binding',
		component: (/* webpackChunkName: 'video-binding' */) => import('@/views/security-manage/video-binding'),
	},
	// 视频监控
	{
		path: '/security-manage/video-monitor',
		name: 'video-monitor',
		component: (/* webpackChunkName: 'video-monitor' */) => import('@/views/security-manage/video-monitor'),
	},
	{
		path: '/video-monitor',
		name: 'videoMonitor',
		component: (/* webpackChunkName: 'videoMonitor' */) => import('@/views/security-manage/video-monitor'),
	},
	// 视频监控
	{
		path: '/security-manage/area-video',
		name: 'area-video',
		component: (/* webpackChunkName: 'rea-video' */) => import('@/views/security-manage/video-monitor/areaVideo'),
	},
	// 视频回放
	{
		path: '/security-manage/playback',
		name: 'playback',
		component: (/* webpackChunkName: 'playback' */) => import('@/views/security-manage/playback'),
	},
	{
		path: '/playback',
		name: 'playbackVideo',
		component: (/* webpackChunkName: 'playbackVideo' */) => import('@/views/security-manage/playback'),
	},
]
