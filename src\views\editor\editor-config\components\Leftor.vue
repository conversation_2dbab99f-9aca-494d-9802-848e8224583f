<template lang="pug">
.left-aside
	.action-title
		span 模型图形库
		Icon(type='ios-list-box', size='16')
	Tabs(v-model='activeGroup')
		TabPane(v-for='(item, index) in groupIdList', :key='index', :label='item.name', :name='item.id')
	.plugin-list
		.plugin-list-box
			//- 控件列表
			.plugin-item(v-for='(item, index) in filterList', :key='index', @click='handleClickPlugin(item)')
				.plugin-item-icon(@dragstart='dragstart($event, item)')
					img(@click.stop='() => {}', :src='item.url', draggable, :id='index')
				.plugin-item-name(:title='item.name') {{ item.name }}
				.mask-box(v-if='item.type.indexOf("controls-") > -1')
					Icon(type='ios-create-outline', color='#fff', v-show='item.use', size='18', @click='handleEditPlugin(item)')
					Icon(type='md-trash', color='#fff', size='18', @click='handleDeletePlugin(item)')
			//- 新增按钮
			.plugin-item
				Button.plugin-item-icon(@click='addPluginModalVis = true')
					Icon(type='ios-add', size='36')
				.plugin-item-name 新增控件
	.action-title.water-margin-top-8
		span 监测标签（可拖拽）
		Radio-group(v-model='disabledGroup', size='small', @on-change='handleChange')
			Radio(label='单选')
			Radio(label='多选')
		Icon(type='ios-list-box', size='16')
	Tabs(v-model='tabName', @on-click='handleTabClick')
		TabPane(:label='item.name', :name='item.name', v-for='item in tabList', :key='item.name')
	.tree-list(:class='{ "tree-list-multiple": showCheckbox }')
		//- Button(@click="handleToggleExpand") 按钮
		.tree-list-content
			es-huge-tree.es-huge-tree(
				ref='huge-tree',
				:showCheckbox='showCheckbox',
				:hasInput='true',
				expandLevel='1',
				checked-action='dblclick',
				expand-level='all',
				placeholder='搜索',
				filter-key='label',
				:is-loading='isLoading',
				:multiple='true',
				:default-checked-keys='checkedKeys',
				@onClickCheckbox='onClickCheckbox',
				@onDragbox='onDragbox',
				@onChange='onChange'
			)
				span(slot-scope='{ slotScope }')
					span {{ slotScope.label }}
				i(slot='loading') 加载中...

	water-row.water-margin-top-8.water-margin-left-8(v-if='showCheckbox', justify='space-between', align='center')
		div 已经勾选({{ selectedArr.length }})
		Button.water-margin-right-8(:disabled='selectedArr.length === 0', @click='bindingData') 绑定

	//- 新增控件弹框
	create-control-popup(
		:show.sync='addPluginModalVis',
		:list='list',
		:groupIdList='groupIdList',
		:activeGroup='activeGroup',
		@operation-suc='handleSuc',
		ref='form'
	)
</template>
<script>
import { controlTypes2AttrMap } from '../../enum.js'
import { querySysList } from '@/api/common.js'
import WaterRow from '@/components/gc-water-row'
import { stationList, controls, deleteControl, getProcessControls } from '@/api/editor'
import CreateControlPopup from '../../control-list/components/CreateControlPopup.vue'

export default {
	name: '',
	components: { WaterRow, CreateControlPopup },
	model: {
		prop: 'value',
		event: 'set-value',
	},
	props: {
		value: Array,
		sysCode: {
			type: String,
			default: 'dc',
		},
		search: Boolean,
		canDelete: Boolean,
	},
	data() {
		return {
			expandLevel: '1',
			showCheckbox: false,
			disabledGroup: '单选',
			selectedArr: [],
			isLoading: false,
			checkedKeys: [],
			list: [],
			tabList: [],
			tabName: '',
			code: '',
			single: true,
			addPluginModalVis: false,
			groupIdList: [],
			activeGroup: '',
		}
	},
	watch: {},
	computed: {
		// 按控件分组过滤控件
		filterList() {
			return this.list.filter(item => {
				return item.groupId == this.activeGroup
			})
		},
	},
	async mounted() {
		await this._getProcessControls()
		this.initContorlImgs()
		this.initData()
	},
	methods: {
		// 获取控件分组
		_getProcessControls() {
			return new Promise((resolve, reject) => {
				getProcessControls()
					.then(res => {
						resolve(res)
						const { result } = res
						if (result && result.length > 0) {
							this.groupIdList = result.map(item => {
								return {
									id: item.id + '',
									name: item.name,
								}
							})
							this.activeGroup = this.groupIdList[0].id
						}
					})
					.catch(err => {
						reject(err)
					})
			})
		},
		// 初始化本地控件图片
		initContorlImgs() {
			const findObj = this.groupIdList.find(item => {
				return !item.allowDelete
			})

			Object.keys(controlTypes2AttrMap).forEach(type => {
				if (type === 'custom') return
				const control = controlTypes2AttrMap[type]
				control['groupId'] = findObj.id
				this.list.push(control)
			})
		},
		handleToggleExpand() {
			this.expandLevel = this.expandLevel === '1' ? 'all' : '1'
			this.handleTabClick(this.tabName)
		},
		bindingData() {
			this.$emit('bindingData', this.$common.deepCopy(this.selectedArr))
			this.selectedArr = []
			this.$refs['huge-tree'].clearChecked()
		},
		async initData() {
			this.getTableData()
			await this.querySysList()

			this.code = this.tabList[0].code
			this.stationList({
				sysCode: this.tabList[0].code,
			})
		},

		getTableData() {
			this.loading = true
			controls().then(res => {
				const data = res.result

				this.loading = false
				if (data) {
					this.list = this.list.filter(item => {
						return item.type.indexOf('controls-') === -1
					})
					data.forEach(item => {
						const { id, name, iconUrl, controlsType } = item
						this.list.push({
							id,
							name,
							url: iconUrl,
							type: controlsType,
							...item,
						})
					})
				}
			})
		},

		// API 查询平台
		querySysList() {
			return querySysList().then(res => {
				const { result = [] } = res

				this.tabList = result
				this.tabName = result[0].name
			})
		},

		stationList(params) {
			stationList({
				showItemCode: true,
				formulaTypes: ['0', '1', '2'],
				...params,
			}).then(res => {
				this.$nextTick(() => {
					this.setTreeData(res.result, this.code)
				})
			})
		},

		// tab 点击事件
		handleTabClick(name) {
			this.showCheckbox = this.disabledGroup === '多选' ? true : false

			let obj = this.tabList.find(item => {
				return item.name === name
			})
			const { code } = obj
			this.code = code

			this.stationList({ sysCode: code })
		},

		// 设置树的值
		setTreeData(data, sysCode) {
			data.forEach(item => {
				const { stationDataItem } = item
				item.parentId = null
				item.id = item.stationCode
				item.label = item.stationName
				item.sysCode = sysCode || item.sysCode
				item.hideCheckbox = true
				if (stationDataItem) {
					stationDataItem.forEach((data, index) => {
						data.parentId = item.stationCode
						data.id = item.stationCode + index
						// data.stationCode = data.stationCode
						data.label = data.itemName
						data.sysCode = sysCode
						data.isShow = true
						data.dragabled = true
						data.hideCheckbox = false
					})
					item.children = stationDataItem
				}
			})
			this.$refs['huge-tree'].setData(data)
		},

		handleChange(val) {
			if (val === '多选') {
				this.showCheckbox = true
			} else {
				this.showCheckbox = false
			}
		},

		// 开始拖动
		dragstart(e, item) {
			e.dataTransfer.setData(
				'widget-config',
				JSON.stringify({
					startX: e.offsetX,
					startY: e.offsetY,
					...item,
				}),
			)
		},

		handleClickPlugin() {},

		onChange({ checkedNodes }) {
			if (this.sysCode === 'dma') {
				checkedNodes.forEach(node => {
					this.$set(node, 'indeterminate', false)
				})
			}
		},

		// 点击复选框时触发
		onClickCheckbox(node) {
			let idx = null
			if (this.selectedArr.length > 9) {
				node.checked = false
				return
			}
			if (node.checked) {
				this.selectedArr = [...this.selectedArr, node]
			} else {
				idx = this.selectedArr.findIndex(i => i.id === node.id)
				if (idx !== null) {
					this.selectedArr.splice(idx, 1)
				}
				idx = null
			}
		},

		// 拖动叶子节点的树结构
		async onDragbox(e, item) {
			e.dataTransfer.setData(
				'text-config',
				JSON.stringify({
					startX: e.offsetX,
					startY: e.offsetY,
					...item,
				}),
			)
		},

		// 操作成功方法
		handleSuc() {
			this.getTableData()
		},

		// 删除控件
		handleDeletePlugin(data) {
			this.$Modal.confirm({
				title: '确定要删除吗？',
				content: '',
				onOk: () => {
					this.$emit('deleteConfirm', data.id)
					this.$nextTick(() => {
						if (!this.canDelete) {
							this.$Message.info('控件已经绑定了工艺图，无法被删除！')
							return
						}
						deleteControl({ id: data.id }).then(() => {
							this.getTableData()
							this.$Message.success('删除成功！')
						})
					})
				},
			})
		},

		// 编辑控件
		handleEditPlugin(data) {
			this.addPluginModalVis = true
			this.$nextTick(() => {
				this.$refs.form.initEditData(data)
			})
		},
	},
}
</script>
<style lang="less" scoped>
.left-aside {
	width: 255px;
	border-right: 1px solid #e8e8e8;
	.tree-list {
		height: calc(100% - 423px);
		padding: 0 8px;
		display: flex;
		&-content {
			position: relative;
			flex-grow: 1;
			overflow: auto;
			::v-deep {
				.es-huge-tree {
					border: none;
					.search-bar {
						padding: 0;
						margin-bottom: 10px;
					}
					.expand-node {
						&::before {
							font-size: 13px !important;
							color: #999 !important;
						}
					}
				}
			}
		}
	}
	.tree-list-multiple {
		height: calc(100% - 463px);
	}
}
.action-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 8px;
	font-size: 16px;
	line-height: 28px;
	border-bottom: 1px solid #e8e8e8;
	font-size: 12px;
}
.plugin-list {
	height: 320px;
	padding: 8px;
	overflow-y: auto;
	border-bottom: 1px solid #e8e8e8;
	.plugin-list-box {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: flex-start;
		flex-wrap: wrap;
		align-content: flex-start;
		.plugin-item {
			position: relative;
			width: 71px;
			cursor: pointer;
			border: 1px solid #e8e8e8;
			border-radius: 4px;
			&:first-child,
			&:nth-child(2n + 2),
			&:nth-child(2n + 3) {
				margin-right: 3px;
				margin-top: 4px;
			}
			// &:nth-child(n + 5) {
			// 	// margin-top: 4px;
			// }

			&-icon {
				width: 68px;
				height: 48px;
				border-bottom: 1px solid #e8e8e8;
				background: #f2f3f5;
				padding: 6px;
				img {
					height: 100%;
					width: 100%;
					object-fit: contain;
				}
			}
			&-name {
				text-align: center;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				font-size: 12px;
				padding: 4px 0px;
			}
			&:hover {
				.mask-box {
					display: flex;
				}
			}
		}
		.mask-box {
			position: absolute;
			display: none;
			align-items: center;
			justify-content: space-around;
			left: 1px;
			right: 4px;
			top: 1px;
			background-color: rgba(0, 0, 0, 0.5);
			.ivu-icon {
				&:hover {
					&::before {
						color: #2d8cf0;
					}
				}
			}
		}
	}
}
</style>
