<!--
 * @Descripttion: 
 * @version: 
 * @Author: heliping
 * @Date: 2021-11-22 09:09:45
 * @LastEditors: shenxh
 * @LastEditTime: 2022-04-06 14:56:52
-->
<template lang="pug">
//- Table(:columns="columns1", :data="data1")
.setting2d
	Input.search(v-model='message', placeholder='请输入设备名称', search, @on-search='handalList')
	Input.search(v-model='codeMessage', placeholder='请输入设备编号', search, @on-search='handalList')
	es-table(:columns='columns', :pageData='pageData', :data='data', @on-page-num-change='pageChange', showPage)
		template(slot-scope='{ row }', slot='operation')
			Button(type='primary', @click='btnClick("editLabel", row)') 编辑

	//- 绑定数据
	edit-label(ref='dataBindingModalref', :show.sync='dataModalShow', @initList='handalList')
</template>
<script>
import editLabel from './components/edit-label'
import { queryMonitorDataStateList, delete2dProcess } from '@/api/setting'
export default {
	components: {
		editLabel,
	},
	data() {
		return {
			message: '',
			codeMessage: '',
			pageData: {
				showTotal: true,
				// showSizer: true,
				current: 1,
				pageSize: 20,
				// pageSizeOpts: [10, 20, 30, 40, 50],
				total: 300,
				// simple: true, // 是否简单模式
				pageDisabled: false, // 是否禁止输入
			},
			total: 0,
			listQuery: {
				page: 1,
				pageSize: 10,
			},
			loading: false,
			columns: [
				{
					type: 'index',
					width: 60,
					align: 'center',
				},
				{
					title: '采集项名称',
					key: 'monitorTypeName',
				},
				{
					title: '设备编号',
					key: 'equipmentUniqueRepresentation',
				},
				{
					title: '设备名称',
					key: 'equipmentName',
				},
				{
					title: '设备单位',
					key: 'monitorTypeUnit',
				},
				{
					title: '操作',
					slot: 'operation',
				},
			],
			data: [],
			dataModalShow: false,
			currentRow: {},
			deleteShow: false,
		}
	},
	computed: {},
	props: {},
	watch: {},
	created() {},
	mounted() {
		this.handalList()
	},
	methods: {
		btnClick(from, row) {
			switch (from) {
				case 'editLabel':
					this.dataModalShow = true
					this.$refs.dataBindingModalref.setData(row)
					break
				default:
					break
			}
		},
		handleDelete() {
			delete2dProcess({ id: this.currentRow.id }).then(() => {
				this.deleteShow = false
				this.handalList()
			})
		},
		pageChange(num) {
			this.pageData.current = num
			this.handalList()
		},

		handalList() {
			const params = {
				pageNum: this.pageData.current,
				pageSize: this.pageData.pageSize,
				equipmentName: this.message,
				equipmentUniqueRepresentation: this.codeMessage,
			}
			queryMonitorDataStateList(params).then(res => {
				this.data = res.result.list
				this.pageData.total = res.result.total
			})
		},
		handlePagination(data) {
			this.listQuery = data
			this.getAlarmLogging()
		},
	},
}
</script>
<style lang="less" scoped>
.setting2d {
	height: 600px;
	.header {
		display: flex;
		justify-content: flex-end;
		padding: 8px;
	}
	.search {
		width: 300px;
		margin-left: 20px;
		margin-top: 10px;
	}
}
::v-deep {
	.imgClass {
		height: 20px;
	}
	.ivu-btn {
		margin-right: 5px;
	}
}
</style>
