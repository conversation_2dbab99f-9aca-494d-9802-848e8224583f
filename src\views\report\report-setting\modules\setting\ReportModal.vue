<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-03-15 08:58:24
 * @LastEditors: houyan
 * @LastEditTime: 2024-03-19 15:10:07
-->
<template lang="pug">
Modal(
	v-model='isShow',
	:title='title',
	:transfer='false',
	:loading='loading',
	@on-ok='handleSubmit',
	@on-cancel='cancel'
)
	Form(ref='formRef', :model='formData', :rules='ruleValidate', :label-width='80')
		FormItem(label='报表名称', prop='name')
			Input(v-model='formData.name', maxlength='20', clearable, placeholder='请输入报表名称')
		FormItem(label='所属业务', prop='businessId')
			Select(v-model='formData.businessId', clearable)
				Option(v-for='item in businessData', :key='item.id', :value='item.id') {{ item.name }}
		FormItem(label='报表编码', prop='code')
			Input(v-model='formData.code', maxlength='20', clearable, placeholder='请输入报表编码')
		FormItem(label='报表url', prop='url')
			Input(
				v-model='formData.url',
				:autosize='{ minRows: 2, maxRows: 5 }',
				placeholder='请输入报表url',
				type='textarea',
				clearable,
				maxlength='1000'
			)
		FormItem(label='报表参数', prop='param')
			Input(v-model='formData.param', maxlength='100', clearable, placeholder='请输入报表参数')
		FormItem(label='排序', prop='sort')
			InputNumber(v-model='formData.sort', :min='1', :max='999999999', controls-outside)
		FormItem(label='是否启用', prop='status')
			i-switch(v-model='formData.status', :true-value='1', :false-value='0', size='large')
				template(#open) 
					span 启用
				template(#close)
					span 关闭
</template>
<script>
import { updateReport, getBusinessList } from '@/api/report.js'
export default {
	props: ['value', 'title'],
	data() {
		return {
			formData: {
				name: '',
				businessId: '',
				code: '',
				url: '',
				param: '',
				status: 1,
				sort: 1,
			},
			ruleValidate: {
				name: [
					{
						required: true,
						message: '请输入报表名称',
						trigger: 'blur',
					},
				],
				businessId: [
					{
						required: true,
						message: '请选择所属业务',
						type: 'number',
						trigger: 'change',
					},
				],
			},
			loading: true,
			businessData: [],
		}
	},
	computed: {
		isShow: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit('input', val)
			},
		},
	},
	methods: {
		async _getBusinessList() {
			this.businessData = []
			const { result } = await getBusinessList({ needPage: false })

			const { list = [] } = result

			if (list && list.length) {
				this.businessData = list
			}
		},
		handleSubmit() {
			this.loading = false
			this.$refs.formRef.validate(async valid => {
				if (valid) {
					const params = {
						...this.formData,
					}
					await updateReport(params)
					this.loading = true
					this.$refs.formRef.resetFields()
					this.isShow = false
					this.$Message.success('修改成功')
					this.$emit('updateSuccess')
				} else {
					this.$nextTick(() => {
						this.loading = true
					})
				}
			})
		},
		cancel() {
			this.$refs.formRef.resetFields()
		},
		initData(data) {
			this.formData = data
		},
	},
	mounted() {
		this._getBusinessList()
	},
}
</script>
<style lang="less" scoped>
::v-deep {
	.ivu-form {
		width: 100%;
	}
	.ivu-form-item {
		margin-bottom: 24px;
	}
	.ivu-modal-header {
		padding: 8px;
	}
}
</style>
