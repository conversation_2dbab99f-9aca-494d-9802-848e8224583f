<!--
 * @Description: 多功能选择器
 * @Author: fengjialing
 * @Date: 2025-04-17 19:18:01
 * @LastEditors: Fengjialing
 * @LastEditTime: 2025-04-17 19:18:01
-->

<template>
	<div class="personnel-config-select">
		<!-- 下拉框 -->
		<div class="select-dropdown">
			<div class="select-dropdown-content">
				<div
					class="select-dropdown-content-tree"
					:style="{ width: treeType === 1 ? treeWidth + 'px' : '100%' }"
				>
					<Tree
						:data="treeData"
						@on-select-change="changeTree"
						@on-check-change="changeTreeCheckBox"
						:show-checkbox="treeType === 2"
					></Tree>
				</div>
				<div class="select-dropdown-content-table" v-show="treeType === 1">
					<div class="search-wrap">
						<Input
							search
							v-model="searchText"
							:placeholder="placeholder"
							@on-search="searchData"
							:disabled="disabled"
						/>
					</div>
					<div class="table-wrap" :style="{ width: tableWidth + 'px' }">
						<Table
							ref="selection"
							border
							:height="255"
							:loading="tableLoading"
							:columns="columns"
							:data="tableData"
							@on-selection-change="onSelectionChange"
							@on-select="onSelect"
							@on-select-cancel="onSelectCancel"
							@on-select-all="onSelectAll"
							@on-select-all-cancel="onSelectAllCancel"
						></Table>
					</div>
					<div class="page-wrap">
						<Page
							:total="total"
							:current="current"
							:page-size="pageSize"
							:disabled="disabled"
							size="small"
							show-elevator
							show-sizer
							show-total
							:page-size-opts="pageSizeOpts"
							@on-page-size-change="onPageSizeChange"
							@on-change="changePage"
						/>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'personnel-config-select',
	components: {},
	props: {
		// 已选择的value数组
		selectedValue: Array,
		// 已选择的label数组 (用于标签展示, 顺序要与value对应)
		selectedLabel: Array,
		// 禁用
		disabled: {
			type: Boolean,
			default: false,
		},
		// 搜索框 placeholder
		placeholder: {
			type: String,
			default: '请输入搜索内容',
		},
		newPlaceholder: {
			type: String,
			default: '',
		},
		// 表格宽度
		tableWidth: {
			type: Number,
			default: 460,
		},
		// 树形图宽度
		treeWidth: {
			type: Number,
			default: 160,
		},
		// 表格加载中
		tableLoading: Boolean,
		// 左侧树形图数据
		treeData: {
			type: Array,
			default() {
				return []
			},
		},
		// 右侧表格 columns
		columns: {
			type: Array,
			default() {
				return [
					{
						type: 'selection',
						width: 60,
						align: 'center',
					},
					{
						title: '名称',
						// width: 390,
						key: 'label',
					},
				]
			},
		},
		// 右侧表格数据 (label/value 必传)
		tableData: {
			type: Array,
			default() {
				return []
			},
		},
		// 分页-总数
		total: {
			type: Number,
			default: 0,
		},
		// 分页-每页数量
		pageSize: {
			type: Number,
			default: 20,
		},
		// 分页-当前页码
		current: {
			type: Number,
			default: 1,
		},
		// 分页-每页条数切换的配置
		pageSizeOpts: {
			type: Array,
			default() {
				return [10, 20, 50, 100]
			},
		},
		// 是否显示右侧人员,1显示，2不显示
		treeType: {
			type: Number,
			default: 1,
		},
	},
	data() {
		return {
			showDropdown: false, // 显示下拉框
			isHover: false, // 鼠标移入
			isClickDropdown: false,
			searchText: '',
		}
	},
	computed: {},
	watch: {
		// tableData: {
		// 	handler() {
		// 		this._setTableData()
		// 	},
		// 	deep: true,
		// },
		showDropdown: {
			handler(v) {
				if (!v) {
					this.searchText = ''
					if (this.selectedValue.length === 0) {
						this.$emit('update:current', 1)
						this.$emit('on-search', '')
					}
				}
			},
		},
	},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 点击选择框
		handleSelect() {
			if (this.disabled) return

			this.showDropdown = !this.showDropdown

			this.$emit('handle-select', this.showDropdown)
		},

		// 选中树形图节点
		changeTree(selectedNodes, currentNode) {
			this.$emit('on-select-change-tree', selectedNodes, currentNode)
		},
		// 选中树形图节点勾选框
		changeTreeCheckBox(selectedNodes, currentNode) {
			this.$emit('change-tree-check-box', selectedNodes, currentNode)
		},

		// 选中某一项时触发
		onSelect(selection, row) {
			this.selectedValue.push(row.value)
			this.selectedLabel.push(row.label)
			this.$emit('on-select', {
				selectedValue: this.selectedValue,
				selectedLabel: this.selectedLabel,
				row,
			})
		},

		// 取消选中某一项时触发
		onSelectCancel(selection, row) {
			let idx = this.selectedValue.indexOf(row.value)

			this.selectedValue.splice(idx, 1)
			this.selectedLabel.splice(idx, 1)

			this.$emit('on-select', {
				selectedValue: this.selectedValue,
				selectedLabel: this.selectedLabel,
				row,
			})
		},

		// 点击全选时触发
		onSelectAll(selection) {
			selection.forEach(item => {
				if (!this.selectedValue.includes(item.value)) {
					this.selectedValue.push(item.value)
					this.selectedLabel.push(item.label)
				}
			})
			this.$emit('on-select-all', selection)
		},

		// 点击取消全选时触发
		onSelectAllCancel(selection) {
			this.tableData.forEach(item => {
				if (this.selectedValue.includes(item.value)) {
					let idx = this.selectedValue.indexOf(item.value)

					this.selectedValue.splice(idx, 1)
					this.selectedLabel.splice(idx, 1)
				}
			})
			this.$emit('on-select-all-cancel', selection)
		},

		// 只要选中项发生变化时就会触发
		onSelectionChange(selection) {
			this.$emit('on-selection-change', selection)
		},

		// 开启 search 时可用，点击搜索或按下回车键时触发
		searchData(val) {
			this.$emit('on-search', val)
		},

		// 切换每页条数时的回调，返回切换后的每页条数
		onPageSizeChange(quantity) {
			this.$emit('update:current', 1)
			this.$emit('update:pageSize', quantity)
			this.$emit('on-page-size-change', quantity)
		},

		// 页码改变的回调，返回改变后的页码
		changePage(pageNo) {
			this.$emit('update:current', pageNo)
			this.$emit('on-change-page', pageNo)
		},

		//清除选中的选项
		clearSelectValue() {
			// 原来的清空方法不生效,table是从props中读取的
			// this.tableData.forEach(item => {
			// 	item._checked = false
			// })
			this.searchText = ''
		},
	},
}
</script>

<style lang="less" scoped>
.personnel-config-select {
	position: relative;
	user-select: none;
	height: 100%;
	width: 100%;
	.select-dropdown {
		border-radius: 1px;
		background-color: #fff;
		box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
		width: 100%;
		height: 350px;
		overflow: hidden;
		.select-dropdown-content {
			display: flex;
			width: 100%;
			height: 100%;
			.select-dropdown-content-tree {
				flex-shrink: 0;
				height: 100%;
				padding: 0 8px;
				overflow: auto;
				border-right: 1px solid #efefef;
				/deep/ .ivu-tree {
					.ivu-tree-arrow {
						i {
							margin-top: -10px;
						}
					}
				}
			}
			.select-dropdown-content-table {
				flex-grow: 1;
				height: 100%;
				padding: 8px;
				.search-wrap {
					margin-bottom: 10px;
				}
				.page-wrap {
					white-space: nowrap;
					.ivu-page {
						margin-bottom: 0;
						margin-top: 8px;
					}
				}
			}
		}
	}
}
</style>
