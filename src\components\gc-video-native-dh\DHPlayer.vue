<template lang="pug">
.video-control-native
	.DHPlayer(v-if='hasInstalledPlugin', :id='videoId', :class='{ "full-width": fullWidth }')
	plugin-tip(v-else)
	//- div(v-if="!noTip")
	//- 	.dh-video-modal(
	//- 		:footer="null",
	//- 		v-model="DHPVisible",
	//- 		:mask="false",
	//- 		width="280px")
	//- 		.message
	//- 			.Modalcontent
	//- 				span(v-if="upgradeDHP") 视频插件已更新了~
	//- 				span(v-else="") 系统未检测到视频插件
	//- 				a(href="#", @click="downloadDhp")
	//- 					span(v-if="upgradeDHP") 更新插件
	//- 					span(v-else="") 下载插件
	.audio(v-if='data', @click='handleAudio()')
		But<PERSON>(v-if='!currentAudio', type='text', icon='md-mic', title='开启对讲', :disabled='!data.id')
		But<PERSON>(v-else, type='text', icon='md-mic-off', title='结束对讲', :disabled='!data.id')
</template>

<script>
import './videoPlayer'
import fn from './factory'
import PluginTip from './PluginTip'
import { getAudioTalk, stopAudioTalk } from '@/api/security-manage'

export default {
	name: 'video-control-native',
	components: {
		PluginTip,
	},
	props: {
		videoId: {
			type: String,
			default: 'DHVideoPlayer',
		},
		type: {
			//视频类型，1-实时视频，2-录像回放
			type: Number,
			default: 1,
		},
		realTimeList: {
			//实时预览列表
			type: Array,
			default() {
				return []
			},
		},
		videoPlayList: {
			//录像回放对象列表
			type: Array,
			default() {
				return []
			},
		},
		pathList: {
			//录像回放/实时视频路径列表
			type: Array,
			default() {
				return []
			},
		},
		windowType: {
			//窗口类型
			type: Number,
			default: 0,
		},
		num: {
			//子窗口个数
			type: Number,
			default: 9,
		},
		connectTime: {
			//尝试连接事件，默认60s，一定时间连接断开，并弹出下载插件提示。
			type: Number,
			default: 60,
		},
		outContent: {
			//当前窗口所处可视窗口的位置，比如iframe外部距离浏览器的位置
			type: Object,
			default() {
				return {
					top: 0,
					left: 0,
				}
			},
		},
		downloadUrl: {
			//插件下载地址，ICC框架默认相对路径
			type: String,
			// default: '/download/DHPlayer.zip',
			default: 'https://eslink-iot.oss-cn-beijing.aliyuncs.com/DHPlayerSetup.exe',
		},
		showBar: {
			//是否显示下方控制栏。 true: 显示， false：隐藏
			type: Boolean,
			default: true,
		},
		noTip: {
			//是否需要下载提示,默认需要，false。如果非ICC平台/未引入antd，可置为true
			type: Boolean,
			default: false,
		},
		shieldClass: {
			//遮挡的元素class，尽量保证class元素的唯一
			type: Array,
			default() {
				return []
			},
		},
		parentIframeShieldClass: {
			//遮挡的父iframe元素class，尽量保证class元素的唯一
			type: Array,
			default() {
				return []
			},
		},
		fullWidth: Boolean,
		data: Object,
	},
	data() {
		return {
			// hasInstalledPlugin: Boolean(
			// 	window.dhPlayerControl.DHPlayerVersion || 0,
			// ), // 已安装插件
			hasInstalledPlugin: true, // 已安装插件
			videoPlayer: null,
			playbackList: [],
			DHPVisible: false,
			DHPUrl: this.downloadUrl,
			DHPVersion: '',
			upgradeDHP: false,
			api: fn(this),
			currentWindow: 0,
			oldWindow: '',
			currentAudio: false, // false-关闭 true-开启
			audioData: null,
			disabledAudio: false,
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {
		console.log('大华插件')
		window.functionForJs = this.functionForJs
		this.initVideo()
		window.addEventListener('message', e => {
			console.log(e)
			if (e && e.data) {
				if (e.data.action && e.data.action === 'updateInitParam') {
					if (e.data.page && e.data.page.show) {
						this.showWindow()
					} else {
						this.hideWindow()
					}
				}
			}
		})
	},
	beforeDestroy() {
		this.destroy()
	},
	methods: {
		handleAudio() {
			this.currentAudio = !this.currentAudio

			this.audioTalk()
		},

		audioTalk() {
			if (this.currentAudio) {
				this.getAudioTalk()
			} else {
				this.stopAudioTalk()
			}
		},

		// 销毁视频
		destroy() {
			if (this.videoPlayer) {
				this.videoPlayer.destroy()
			}
		},
		// 初始化视频插件
		initVideo() {
			console.log('initVideo')
			this.destroy()

			this.videoPlayer = new window.VideoPlayer({
				videoId: this.videoId,
				windowType: this.windowType,
				connectTime: this.connectTime,
				shieldClass: this.shieldClass,
				parentIframeShieldClass: this.parentIframeShieldClass,
				showBar: this.showBar,
				num:
					Math.pow(
						Math.ceil(
							Math.sqrt(
								this.pathList.length ||
									(this.type === 1 ? this.realTimeList.length : this.videoPlayList.length),
							),
						),
						2,
					) || this.num,
				outContent: this.outContent,
				connectSuccess: () => {
					console.log('connectSuccess 初始化成功')
					this.DHPVersion = window.dhPlayerControl.DHPlayerVersion
					// this.getVideoInfo(true)
					this.hasInstalledPlugin = true
				},
				//抓图成功
				snapshotSuccess: ({ base64Url, path }) => {
					console.log('snapshotSuccess 抓图成功')
					this.$emit('picCap', {
						base64Url,
						path,
					})
				},
				//录像回放时间栏拖拽回调
				switchStartTime: ({ startTime, snum }) => {
					const record = this.playbackList[snum]
					if (record) {
						if (record.request) {
							const videoParams = this.videoPlayList[snum]
							let endTime = videoParams.endTime
							if (startTime > endTime) {
								!this.noTip && this.$message.warning('开始时间不得大于结束时间')
								this.$emit('getError', '开始时间不得大于结束时间')
								return false
							}
							this.dealRecord(
								{
									...videoParams,
									startTime,
								},
								snum,
							)
						} else {
							let endTime = this.getUrlParam(record.path, 'endtime') - 0
							if (startTime > endTime) {
								!this.noTip && this.$message.warning('开始时间不得大于结束时间')
								this.$emit('getError', '开始时间不得大于结束时间')
								return false
							}
							this.setPlayback(
								{
									...record,
									playStartTime: startTime,
								},
								snum,
							)
						}
					}
					this.$emit('switchStartTime', {
						startTime,
						snum,
					})
				},
				replay: snum => {
					//录像回放播放结束回调，返回窗口
					this.$emit('replay', snum)
				},
				videoDownloadSuccess: path => {
					console.log('videoDownloadSuccess 下载成功')
					!this.noTip && this.$message.success(`本地录像已存到 ${path}`)
					this.$emit('videoDownloadSuccess', path)
				},
				//关闭视频窗口回调
				closeWindowSuccess: ({ isAll, snum }) => {
					console.log('closeWindowSuccess 关闭视频窗口')
					this.$emit('closeWindowSuccess', {
						isAll,
						snum,
					})
				},
				getCurrentWindow: snum => {
					this.oldWindow = this.currentWindow
					this.currentWindow = snum

					this.audioTalk()
					this.$emit('getCurrentWindow', snum)
				},
				connectClose: () => {
					console.log('connectClose 初始化失败')
					this.videoPlayer = null
					// this.getVideoInfo(false)
					this.hasInstalledPlugin = false
				},
				createSuccess: () => {
					console.log('createSuccess', this.videoPlayer)
					if (!this.showBar) {
						this.setShowBar(false)
					}
					if (this.pathList.length) {
						this.loadVideo()
					} else {
						this.getRTSPURL()
					}
				},
			})
		},
		getUrlParam(url, name) {
			var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)') //构造一个含有目标参数的正则表达式对象
			var r = url.match(reg) //匹配目标参数
			if (r != null) return unescape(r[2])
			return null //返回参数值
		},
		//显隐控制栏
		setShowBar(show) {
			this.videoPlayer.showControlBar(show)
		},
		// 设置悬浮字体
		setOSDInfo(options) {
			this.videoPlayer.setOSDInfo(options)
		},
		// 是否开启拖拽
		setDragEnable(enable) {
			this.videoPlayer.setWindowDragEnable({ enable })
		},
		// 全屏
		setFullScreen() {
			this.videoPlayer.setFullScreen()
		},
		// 关闭视频
		closeVideo(options) {
			this.videoPlayer.closeVideo(options)
		},

		// 根据路径获取视频
		loadVideo(type) {
			console.log('loadVideo', this.pathList, this.videoPlayer)
			if (this.type === 1) {
				const selectedWindowIdx = this.videoPlayer.setting.snum

				if (type !== 'equipment') {
					this.closeVideo({
						isAll: true,
					})
				}

				this.pathList.forEach((val, index) => {
					let item = {
						// snum: index,
						snum: type === 'equipment' ? selectedWindowIdx : index,
					}
					if (typeof val === 'string') {
						item.path = val
					} else if (typeof val === 'object') {
						item = { ...item, ...val }
					} else {
						return val
					}
					this.setRealTime(item)
				})
			} else {
				const list = []
				this.pathList.forEach((val, index) => {
					list.push({
						...val,
						request: false,
					})
					this.setPlayback(val, index)
				})
				this.playbackList = list
			}
		},
		//获取录像回放/实时预览路径
		getRTSPURL() {
			let streamType = 1
			if (this.type === 1) {
				streamType = this.realTimeList.length >= 4 ? 2 : 1
				this.realTimeList.forEach((val, index) => {
					this.api
						.fetchRealRTSP({
							data: {
								streamType: val.streamType || streamType,
								channelId: val.channelId,
								dataType: val.dataType || 1,
							},
						})
						.then(data => {
							if (data.url) {
								const url = this.dealUrl(data)
								this.setRealTime({
									path: url,
									redirect:
										typeof val.redirect === 'boolean' ? val.redirect : url.indexOf(':9090') !== -1,
									snum: index,
								})
							}
						})
				})
			} else {
				streamType = this.videoPlayList.length >= 4 ? 2 : 1
				this.videoPlayList.forEach((val, index) => {
					this.dealRecord(val, index)
				})
			}
		},
		//处理录像数据
		async dealRecord(val, index) {
			let streamType = 1
			streamType = this.realTimeList.length >= 4 ? 2 : 1
			const records = await this.api.fetchRecord({
				data: {
					...val,
					streamType: val.streamType || streamType,
				},
			})
			if (records.records && records.records.length) {
				const data = await this.api.fetchRTSP({
					data: {
						...val,
						streamType: val.streamType || streamType,
						dataType: val.dataType || 1,
					},
				})
				if (data.url) {
					const url = this.dealUrl(data)
					const item = {
						...val,
						path: url,
						redirect: typeof val.redirect === 'boolean' ? val.redirect : url.indexOf(':9090') !== -1,
						records: records.records,
					}
					this.setPlayback(item, index)
					this.playbackList[index] = {
						...item,
						request: true,
					}
				}
			}
		},
		//实时预览
		setRealTime(options) {
			this.videoPlayer.realmonitor({
				...options,
			})
			this.audioTalk()
		},
		//录像回放
		setPlayback(val, snum = 0) {
			this.videoPlayer.playback({
				snum,
				records: val.records || [],
				path: val.url || val.path,
				startTime: val.startTime,
				endTime: val.endTime,
				redirect: val.redirect,
				playStartTime: val.playStartTime || val.startTime,
				playEndTime: val.playEndTime || val.endTime,
			})
		},
		//rtsp路径拼接token
		dealUrl(data) {
			let path = data.url
			if (path.includes('|')) {
				path = path
					.split('|')
					.map(item => {
						return item + '?token=' + data.token
					})
					.reverse()
					.join('|')
			} else {
				path = path + '?token=' + data.token
			}
			return path
		},
		//隐藏插件更新/下载提示
		hiddenDhp() {
			window.DHPVisible = this.DHPVisible = false
		},
		//隐藏窗口
		hideWindow(callback) {
			this.videoPlayer.hide(callback)
		},
		//显示窗口
		showWindow(callback) {
			this.videoPlayer.show(callback)
		},
		//元素遮挡处理
		windowShield(option) {
			this.videoPlayer.windowShield(option)
		},
		/**
		 * @method 获取录像插件版本
		 * @param {Boolean} success 判断ws是否连接成功，true: 成功，比较版本号；false: 失败,下载插件
		 **/
		getVideoInfo(success) {
			this.api
				.videoInfo()
				.then(res => {
					this.DHPUrl = res.url
					if (res.version) {
						res.version = '000000'
					}
					if (success && res.version > this.DHPVersion) {
						this.upgradeDHP = true
						!window.DHPVisible && (window.DHPVisible = this.DHPVisible = true)
					}
					if (!success) {
						!window.DHPVisible && (window.DHPVisible = this.DHPVisible = true)
					}
				})
				.catch(() => {
					if (!success) {
						!window.DHPVisible && (window.DHPVisible = this.DHPVisible = true)
					}
				})
			this.upgradeDHP = true
			!window.DHPVisible && (window.DHPVisible = this.DHPVisible = true)
		},
		downloadDhp() {
			window.location.href = this.DHPUrl
			this.hiddenDhp()
		},

		functionForJs() {
			this.hasInstalledPlugin = false
		},

		// 语音对讲-开始
		getAudioTalk() {
			const { cameraIndexCode, platformId } = this.data || {}

			this.stopAudioTalk()
			getAudioTalk({
				code: cameraIndexCode,
				platformId: platformId,
			}).then(res => {
				const { result } = res
				let data = {}

				if (Array.isArray(result)) {
					data = result.length ? result[0] : {}
				} else {
					data = result || {}
				}
				const { url = '', errorCode } = data

				if (errorCode) {
					this.$Message.error(`对讲功能启动失败, 错误编码: ${errorCode}`)
					this.currentAudio = false
					return
				} else {
					this.$Message.success(`对讲功能已启动`)
				}

				this.audioData = data
				this.videoPlayer.talk(
					{
						snum: this.currentWindow,
						path: url,
						redirect: false,
						audioType: 1,
						talkType: 1,
						audioBit: 16,
						sampleRate: 8000,
					},
					msg => {
						console.log(msg)
					},
				)
			})
		},
		// 语音对讲-结束
		stopAudioTalk() {
			if (!this.audioData) return

			const { cameraIndexCode, platformId } = this.data || {}
			const { session = '' } = this.audioData

			stopAudioTalk({
				code: cameraIndexCode,
				platformId: platformId,
				session: session,
			}).then(() => {
				this.videoPlayer.closeTalk(
					{
						snum: this.currentWindow,
						isAll: true,
					},
					msg => {
						console.log(msg)
					},
				)
				this.audioData = null
			})
		},
	},
}
</script>

<style lang="less" scoped>
.video-control-native {
	width: 100%;
	height: 100%;
	.DHPlayer {
		width: 100%;
		height: 100%;
		// width: calc(100vw - 370px);
		// height: calc(100vh - 190px);
		// &.full-width {
		// 	width: calc(100vw - 128px);
		// }
	}
	.dh-video-modal {
		width: 100%;
	}

	.dh-video-modal .ant-modal-content {
		border: 1px solid #ffa302;
		background: #fff7e9;
		color: #ffa302;
	}

	.dh-video-modal .message {
		display: flex;
		align-items: center;
	}

	.dh-video-modal .ant-modal-close-x {
		display: none;
	}

	.dh-video-modal .ant-modal-body {
		padding: 0 20px;
		font-size: 14px;
		word-wrap: break-word;
		height: 40px;
		line-height: 40px;
		margin-left: 0;
	}

	.dh-video-modal .messageIcon {
		background: #ffa302;
		border-radius: 50%;
		color: #fff;
		font-size: 16px;
		margin-right: 5px;
	}

	.dh-video-modal .Modalcontent a {
		margin-left: 15px;
	}

	.audio {
		position: absolute;
		top: 4px;
		right: 15px;
		.video-icon {
			font-size: 20px;
			cursor: pointer;
		}
	}
}
</style>
