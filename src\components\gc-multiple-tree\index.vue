<!--
 * @Description: 多选树
 * @Author: fengjialing
-->
<template>
	<div class="water-tree">
		<Input
			:disabled="disabled"
			v-show="search"
			v-model="searchKeyword"
			suffix="ios-search"
			placeholder="请输入类型名称"
			clearable
			style="width: auto"
		/>
		<div class="water-tree-content">
			<Tree ref="tree" :data="filteredTreeData" @on-check-change="changeTree" multiple show-checkbox></Tree>
		</div>
	</div>
</template>
	
<script>
export default {
	name: 'water-tree',
	components: {},
	model: {
		prop: 'value',
		event: 'set-value',
	},
	props: {
		treeData: {
			type: Array,
			default: () => [],
		},
		search: {
			type: Boolean,
			default: true,
		},
		multiple: {
			type: Boolean,
			default: true,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			isChangeCheckbox: false,
			searchKeyword: '',
		}
	},
	computed: {
		// 动态生成过滤后的树数据
		filteredTreeData() {
			const keyword = this.searchKeyword
			return this.treeData.map(node => this.filterNode(node, keyword)).filter(item => item !== null)
		},
	},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 点击复选框时触发
		onClickCheckbox(node) {
			console.log('击复选框时触发node', node)
		},
		onChange({ checkedNodes }) {
			console.log('多选庶onChange---checkedNodes', checkedNodes)
		},

		handleNodeLabel(data) {
			console.log('handleNodeLabel data', data)
		},
		// changeTree(selectedNodes, currentNode) {
		changeTree(selectedNodes) {
			this.$emit('change-select', selectedNodes)
		},
		// 递归过滤树节点
		filterNode(node, keyword) {
			if (node.title.includes(keyword)) {
				return node // 如果当前节点匹配，直接返回
			}
			if (node.children) {
				const filteredChildren = node.children.map(child => this.filterNode(child, keyword)).filter(Boolean) // 递归过滤子节点，并过滤掉空值
				if (filteredChildren.length > 0) {
					return { ...node, children: filteredChildren } // 如果有子节点匹配，返回当前节点及其过滤后的子节点
				}
			}
			return null // 如果当前节点及其子节点都不匹配，返回 null
		},
		// 清空树的搜索框
		clearSearchValue() {
			this.searchKeyword = ''
		},
	},
}
</script>
	
<style lang="less" scoped>
.water-tree {
	display: flex;
	flex-direction: column;
	height: 100%;
	.water-tree-content {
		height: 310px;
		overflow: auto;
		/deep/ .ivu-tree {
			height: 100%;
		}
	}
}
</style>
	