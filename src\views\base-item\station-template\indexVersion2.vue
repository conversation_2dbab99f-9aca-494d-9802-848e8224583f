<template>
	<div class="layout">
		<div class="fixed">
			<span class="page-title">站点数据项信息</span>
			<span>
				<Button class="water-margin-right-12" @click="deleteShow = true" size="small">删除</Button>
				<Button type="primary" @click="handleRelation" size="small">更换</Button>
			</span>
		</div>

		<div class="layout-content">
			<Row>
				<i-col span="3" class="wrapper">
					<WaterRow>
						<Input style="width: 130px" clearable v-model="stationCode" placeholder="请输入站点编号" />
						<Button type="primary" class="water-margin-left-8" @click="queryStations">查询</Button>
					</WaterRow>
					<WaterTable
						class="water-table water-margin-top-8"
						:loading="listLoading"
						:width="200"
						:show-header="false"
						:border="false"
						highlight-row
						:columns="columns"
						:data="data"
						@on-row-click="handleSelect"
					></WaterTable>

					<Page
						size="small"
						show-total
						:transfer="true"
						:current="pageData.pageNum"
						:total="pageData.total"
						:page-size="pageData.pageSize"
						@on-change="handlePageChange"
					/>
				</i-col>
				<i-col span="21" class="wrapper">
					<div style="font-size: 18px">
						{{ title }}
					</div>
					<WaterRow justify="flex-start" class="water-margin-top-12">
						<div>
							<Input
								style="width: 400px; margin-right: 10px"
								clearable
								v-model="itemRealCode"
								class="content-input"
								placeholder="数据项编号:"
							/>
							<Input
								style="width: 400px; margin-right: 10px"
								clearable
								v-model="itemName"
								class="content-input"
								placeholder="数据项名称:"
							/>
						</div>
						<Button type="primary" @click="getDetail" class="water-margin-right-10">查询</Button>
						<Button type="primary" icon="plus" @click="handlerAdd()">+ 新增</Button>
					</WaterRow>
					<WaterTable
						stripe
						border
						class="water-table water-margin-top-12"
						:columns="tableColumns"
						:data="stationDatas"
						:loading="tableLoading"
					>
						<template slot-scope="{ row }" slot="formulaType">
							<span>{{ getFormulaType(row.formulaType) }}</span>
						</template>
						<template slot-scope="{ row, index }" slot="edit">
							<Button v-if="editIndex === -1" size="small" type="primary" @click="handleEdit(index)">
								修改
							</Button>
							<Button
								v-if="editIndex === index"
								size="small"
								type="primary"
								@click="handleUpdateStationItem(row, index)"
							>
								保存
							</Button>
							<Button
								v-if="editIndex === index"
								size="small"
								type="primary"
								@click="editIndex = -1"
								class="water-margin-left-8"
							>
								取消
							</Button>
							<Button size="small" @click="handleDeleteItemShow(row.id)" class="water-margin-left-8">
								删除
							</Button>
						</template>
					</WaterTable>
					<Page
						size="small"
						show-total
						:transfer="true"
						:current="tablePageData.pageNum"
						:total="tablePageData.total"
						:page-size="tablePageData.pageSize"
						@on-change="handleTablePageChange"
					/>
				</i-col>
			</Row>
		</div>
		<temp-select
			:show.sync="modalShow"
			:sys-code="this.sysCode"
			:station-code="this.selectedStationCode"
			@initList="queryStations"
		></temp-select>
		<EsConfirmModal
			v-model="deleteShow"
			title="提示"
			content="是否删除当前项"
			@on-ok="handleDelete"
			@on-cancel="deleteShow = false"
		></EsConfirmModal>
		<EsConfirmModal
			v-model="deleteItemShow"
			title="提示"
			content="是否删除当前项"
			@on-ok="handleDeleteItem"
			@on-cancel="deleteItemShow = false"
		></EsConfirmModal>
		<add-item
			:show.sync="showAddModal"
			ref="addItem"
			:type="this.sysCode"
			:station="this.selectedStationCode"
			@init="getDetail()"
		></add-item>
	</div>
</template>
<script>
import {
	deleteByStationCode,
	getStationTemp,
	queryPage,
	queryStationPage,
	deleteById,
	updateStationDataItem,
} from '@/api/base-item'
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { EsConfirmModal } from '@eslink/esvcp-pc-ui'
import TempSelect from '@/views/base-item/station-template/components/TempSelect'
import AddItem from './components/AddItem'

export default {
	components: { AddItem, TempSelect, EsConfirmModal, WaterTable, WaterRow },
	props: {},
	computed: {},
	mounted() {},
	data() {
		return {
			itemName: '',
			itemRealCode: '',
			editForm: {},
			showAddModal: false,
			deleteItemShow: false,
			itemId: null,
			sysCode: 'dc',
			tableLoading: true,
			baseDataItems: [],
			pageData: {
				total: 0,
				current: 1,
				pageSize: 15,
			},
			tablePageData: {
				total: 0,
				current: 1,
				pageSize: 15,
			},
			stationDatas: [],
			formRules: {
				baseItemId: [
					{
						required: true,
						message: '请输入数据项名称',
						trigger: 'change',
						type: 'number',
					},
				],
				itemName: [
					{
						required: true,
						message: '请输入数据项名称',
						trigger: 'change',
						type: 'string',
					},
				],
				itemRealCode: [
					{
						required: true,
						message: '请输入数据项编号',
						trigger: 'change',
						type: 'string',
					},
				],
				formulaType: [
					{
						required: true,
						message: '请选择公式类型',
						trigger: 'change',
					},
				],
			},
			formItem: {
				stationTempDatas: [],
			},
			formulaType: [
				{
					value: '0',
					label: '原始数据',
				},
				{
					value: '1',
					label: '单位换算',
				},
				{
					value: '2',
					label: '公式计算',
				},
				{
					value: '3',
					label: '状态数据',
				},
			],
			title: '站点关联数据项',
			modalShow: false,
			deleteShow: false,
			selectedStationCode: '',
			listLoading: false,
			stationCode: '',
			editIndex: -1,
			columns: [{ key: 'stationCode' }],
			data: [],
			itemData: [],
			tableColumns: [
				{
					title: '基础数据项',
					key: 'baseItemId',
					align: 'center',
					type: Number,
					minWidth: 150,
					render: (h, { row, index }) => {
						let edit
						if (this.editIndex === index) {
							this.editForm.baseItemId = row.baseItemId
							console.log('baseDataItemsbiao+' + this.baseDataItems.length)
							let options = this.baseDataItems.map(v => {
								return h(
									'Option',
									{
										props: {
											value: v.id,
											label: v.baseItemName,
										},
									},
									v,
								)
							})
							edit = [
								h(
									'Select',
									{
										props: {
											value: row.baseItemId,
											transfer: true,
										},
										on: {
											'on-change': val => {
												this.editForm.baseItemId = val
											},
										},
									},
									options,
								),
							]
						} else {
							edit = h('span', this.baseDataItems.find(this.findInOption(row.baseItemId)).baseItemName)
						}
						return h('div', [edit])
					},
				},
				{
					title: '数据项编号',
					key: 'itemRealCode',
					minWidth: 240,
					align: 'center',
					render: (h, { row, index }) => {
						let edit
						if (this.editIndex === index) {
							this.editForm.itemRealCode = row.itemRealCode
							edit = [
								h('Input', {
									props: {
										value: row.itemRealCode,
									},
									on: {
										input: val => {
											this.editForm.itemRealCode = val
										},
									},
								}),
							]
						} else {
							edit = row.itemRealCode
						}
						return h('div', [edit])
					},
				},
				{
					title: '数据项名称',
					key: 'itemName',
					align: 'center',
					minWidth: 120,
					render: (h, { row, index }) => {
						let edit
						if (this.editIndex === index) {
							this.editForm.itemName = row.itemName
							edit = [
								h('Input', {
									props: {
										value: row.itemName,
									},
									on: {
										input: val => {
											this.editForm.itemName = val
										},
									},
								}),
							]
						} else {
							edit = row.itemName
						}
						return h('div', [edit])
					},
				},
				{
					title: '公式类型',
					slot: 'formulaType',
					align: 'center',
					minWidth: 120,
					render: (h, { row, index }) => {
						let edit
						if (this.editIndex === index) {
							this.editForm.formulaType = row.formulaType
							let options = this.formulaType.map(v => {
								return h(
									'Option',
									{
										props: {
											value: v.value,
											label: v.label,
										},
									},
									v,
								)
							})
							edit = [
								h(
									'Select',
									{
										props: {
											value: row.formulaType,
											transfer: true,
										},
										on: {
											'on-change': val => {
												this.editForm.formulaType = val
											},
										},
									},
									options,
								),
							]
						} else {
							edit = h('span', this.formulaType.find(this.findObjectInOption(row.formulaType)).label)
						}
						return h('div', [edit])
					},
				},
				{
					title: '公式',
					key: 'formula',
					align: 'center',
					minWidth: 250,
					render: (h, { row, index }) => {
						let edit
						if (this.editIndex === index) {
							this.editForm.formula = row.formula
							edit = [
								h('Input', {
									props: {
										value: row.formula,
									},
									on: {
										input: val => {
											this.editForm.formula = val
										},
									},
								}),
							]
						} else {
							edit = row.formula
						}
						return h('div', [edit])
					},
				},
				{
					title: '序号',
					key: 'orderBy',
					align: 'center',
					width: 80,
					render: (h, { row, index }) => {
						let edit
						if (this.editIndex === index) {
							this.editForm.orderBy = row.orderBy
							edit = [
								h('Input', {
									props: {
										value: row.orderBy,
									},
									on: {
										input: val => {
											this.editForm.orderBy = val
										},
									},
								}),
							]
						} else {
							edit = row.orderBy
						}
						return h('div', [edit])
					},
				},
				{
					title: '数值',
					key: 'itemValue',
					align: 'center',
					width: 80,
				},
				{
					title: '最后上传时间',
					key: 'itemUploadTime',
					align: 'center',
					minWidth: 150,
				},
				{
					title: '操作',
					align: 'center',
					slot: 'edit',
					width: 200,
				},
			],
		}
	},
	created() {
		this.queryStations()
	},
	watch: {
		sysCode: {
			handler() {
				this.getBaseDataItems()
			},
			immediate: true,
		},
	},
	methods: {
		handleUpdateStationItem(row, index) {
			this.stationDatas[index].formulaType = this.editForm.formulaType
			this.stationDatas[index].formula = this.editForm.formula
			this.stationDatas[index].baseItemId = this.editForm.baseItemId
			this.stationDatas[index].itemRealCode = this.editForm.itemRealCode
			this.stationDatas[index].itemName = this.editForm.itemName
			this.stationDatas[index].orderBy = this.editForm.orderBy

			this.editForm.stationCode = this.selectedStationCode
			this.editForm.type = this.sysCode
			this.editForm.id = row.id
			updateStationDataItem(this.editForm)
				.then(() => {
					this.$Message.success('提交成功!')
					this.editIndex = -1
				})
				.catch(() => {
					this.listLoading = false
				})
			/*this.$refs.formValidate.validate(valid => {
				if (valid) {
					this.editForm.stationCode = this.selectedStationCode
					this.editForm.type = this.sysCode
					updateStationDataItem(this.editForm)
						.then(() => {
							this.$Message.success('提交成功!')
							this.queryStations()
						})
						.catch(() => {
							this.listLoading = false
						})
				}
			})*/
		},

		findInOption(value) {
			return function (item) {
				return item.id === value
			}
		},

		findObjectInOption(value) {
			return function (item) {
				return item.value === value
			}
		},
		handleDeleteItem() {
			let params = { id: this.itemId }
			deleteById(params).then(() => {
				this.$Message.success('操作成功')
				this.deleteItemShow = false
				this.queryStations()
			})
		},
		handleDeleteItemShow(id) {
			this.deleteItemShow = true
			this.itemId = id
		},

		getFormulaType(val) {
			let formulaName = ''

			switch (val) {
				case '0':
					formulaName = '原始数据'
					break
				case '1':
					formulaName = '单位换算'
					break
				case '2':
					formulaName = '公式计算'
					break
				case '3':
					formulaName = '状态数据'
					break
			}
			return formulaName
		},

		delItem(index) {
			this.formItem.stationTempDatas.splice(index, 1)
		},
		getBaseDataItems() {
			queryPage({ needPage: false, type: this.sysCode }).then(res => {
				this.baseDataItems = res.result.list.map(item => {
					if (item.unit != null && item.unit != '' && item.unit != undefined) {
						item.baseItemName = item.baseItemName + '(' + item.unit + ')'
					}
					return { ...item }
				})
			})
		},
		handleEdit(index) {
			this.editIndex = index
		},
		addItem() {
			this.formItem.stationTempDatas.push({
				baseDataItemId: null,
				itemRealCode: '',
				itemName: '',
				formulaType: null,
				formula: '',
				orderBy: null,
				unit: '',
			})
		},

		/*handleSubForm() {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					this.formItem.stationCode = this.selectedStationCode
					this.formItem.type = this.sysCode
					updateStationData(this.formItem)
						.then(() => {
							this.$Message.success('提交成功!')
							this.queryStations()
						})
						.catch(() => {
							this.listLoading = false
						})
				}
			})
		},*/

		handlePageChange(pageNum) {
			this.pageData.current = pageNum
			this.queryStations()
		},
		handleTablePageChange(pageNum) {
			this.tablePageData.current = pageNum
			this.getDetail()
		},
		handleDelete() {
			let params = { stationCode: this.selectedStationCode }
			deleteByStationCode(params).then(() => {
				this.$Message.success('操作成功')
				this.deleteShow = false
				this.handleSearch()
			})
		},
		queryStations() {
			let params = {
				stationCode: this.stationCode,
				needPage: true,
				pageSize: this.pageData.pageSize,
				pageNum: this.pageData.current,
			}
			queryStationPage(params).then(res => {
				this.data = res.result.list
				this.pageData.total = res.result.total
				this.data[0]._highlight = true
				this.sysCode = this.data[0].type
				this.selectedStationCode = this.data[0].stationCode
				this.getDetail()
			})
		},

		handleRelation() {
			this.modalShow = true
		},

		handleSelect(selection) {
			this.itemName = ''
			this.itemRealCode = ''
			this.selectedStationCode = selection.stationCode
			this.sysCode = selection.type
			if (this.sysCode === selection.type) {
				this.selectedStationCode = selection.stationCode
				this.getDetail()
			} else {
				this.selectedStationCode = selection.stationCode
				this.sysCode = selection.type
				this.onload()
			}
		},
		getDetail() {
			this.tableLoading = true
			let params = {
				stationCode: this.selectedStationCode,
				itemName: this.itemName,
				itemRealCode: this.itemRealCode,
				pageNum: this.tablePageData.current,
				pageSize: this.tablePageData.pageSize,
			}
			getStationTemp(params)
				.then(res => {
					const { result = {} } = res
					const { list = [], total = 0 } = result
					this.stationDatas = list
					this.tablePageData.total = total
				})
				.catch(() => {
					this.tableLoading = false
				})
			this.tableLoading = false
			this.editIndex = -1
		},
		handlerAdd() {
			this.$refs.addItem.getBaseDataItems(this.sysCode)
			this.showAddModal = true
		},
		onload() {
			this.getBaseDataItems()
			this.$nextTick(() => {
				this.getDetail(this.selectedStationCode)
			})
		},
	},
}
</script>
<style lang="less" scoped>
.layout {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
}
.layout-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 0 15px 12px 15px;
	overflow: hidden;
}
.water-table {
	flex: 1;
}
.dotted {
	display: flex;
	justify-content: center;
	align-items: center;
	border-style: dotted;
	background-color: azure;
	border-width: 1px;
	margin-top: 10px;
}
.fixed {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 16px;
	.page-title {
		font-size: 18px;
		font-weight: 600;
	}
}
.button {
	margin-left: 16px;
	margin-top: 16px;
}
.rightButton {
	display: flex;
	justify-content: flex-end;
}
.wrapper {
	flex: 1;
	height: 100%;
	display: flex;
	flex-direction: column;
}
::v-deep {
	.ivu-row {
		flex: 1;
	}
}
</style>
