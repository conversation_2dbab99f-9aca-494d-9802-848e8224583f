<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-03-15 08:58:24
 * @LastEditors: houyan
 * @LastEditTime: 2024-03-19 10:14:53
-->
<template lang="pug">
Modal(
	v-model='isShow',
	:title='title',
	:transfer='false',
	:loading='loading',
	@on-ok='handleSubmit',
	@on-cancel='cancel'
)
	Form(ref='formRef', :model='formData', :rules='ruleValidate', :label-width='80')
		FormItem(label='报表名称', prop='name')
			Input(v-model='formData.name', maxlength='20', clearable, placeholder='请输入报表名称')
		FormItem(label='排序', prop='sequence')
			InputNumber(v-model='formData.sequence', :min='1', :max='999999999', controls-outside)
</template>
<script>
import { updateBusiness } from '@/api/report.js'
export default {
	props: ['value', 'title'],
	data() {
		return {
			formData: {
				name: '',
				sequence: 1,
			},
			ruleValidate: {
				name: [
					{
						required: true,
						message: '请输入报表名称',
						trigger: 'blur',
					},
				],
			},
			loading: true,
		}
	},
	computed: {
		isShow: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit('input', val)
			},
		},
	},
	methods: {
		handleSubmit() {
			this.loading = false
			this.$refs.formRef.validate(async valid => {
				if (valid) {
					const params = {
						...this.formData,
					}
					await updateBusiness(params)
					this.loading = true
					this.$refs.formRef.resetFields()
					this.isShow = false

					this.$emit('updateSuccess')
				} else {
					this.$nextTick(() => {
						this.loading = true
					})
				}
			})
		},
		cancel() {
			this.$refs.formRef.resetFields()
		},
		initData(data) {
			this.formData = data
		},
	},
}
</script>
<style lang="less" scoped>
::v-deep {
	.ivu-form {
		width: 100%;
	}
	.ivu-form-item {
		margin-bottom: 24px;
	}
	.ivu-modal-header {
		padding: 8px;
	}
}
</style>
