/*
 * @Description:
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-31 11:37:04
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2022-12-06 11:29:11
 */
import { POST, GET } from '@/utils/request'

// 查询水源地指标
export function queryWaterSourceIndex(params) {
	return POST({
		url: '/waterPlat/fillData/queryWaterSourceIndex',
		params,
	})
}
// 提交水源地
export function UpdateWaterSource(params) {
	return POST({
		url: '/waterPlat/fillData/insertWaterSourceIndex',
		params,
		requestType: 'json',
	})
}
// 查询二供数据
export function queryEgFillDataRecord(params) {
	return POST({
		url: '/waterPlat/fillData/queryEgFillDataRecord',
		params,
	})
}
//二供片区下拉列表框
export function queryEgSelectArea(params) {
	return POST({
		url: '/waterPlat/fillData/selectArea',
		params,
	})
}
//提交二供填报数据
export function insertEgFillDataRecord(params) {
	return POST({
		url: '/waterPlat/fillData/insertEgFillDataRecord',
		params,
		requestType: 'json',
	})
}
//查询客服热线统计填报数据
export function queryHotlineData(params) {
	return GET({
		url: '/waterPlat/fillData/service/query',
		params,
	})
}
//提交热线数据
export function insertHotlineFillData(params) {
	return POST({
		url: '/waterPlat/fillData/serviceCall/add',
		params,
		requestType: 'json',
	})
}
//查询 原水、产水年度指标+水厂耗电量统计
export function queryStatisticsData(params) {
	return POST({
		url: '/waterPlat/fillData/selectPowerConsumptionStatistics',
		params,
	})
}
//查询填报过的经营数据
export function queryManagementData(params) {
	return GET({
		url: '/waterPlat/fillData/lossWater/detail/query',
		params,
	})
}
//提交原水、产水年度指标+水厂耗电量统计数据
export function insertStatisticsData(params) {
	return POST({
		url: '/waterPlat/fillData/insertPowerConsumptionStatistics',
		params,
		requestType: 'json',
	})
}
//提交水务经营数据
export function insertManagementData(params) {
	return POST({
		url: '/waterPlat/fillData/lossWater/detail/add',
		params,
		requestType: 'json',
	})
}
//单村数据填报
export function villageSave(params) {
	return POST({
		url: '/waterPlat/fillData/village/save',
		params,
		requestType: 'json',
	})
}
//单村填报数据查询
export function villageQuery(params) {
	return GET({
		url: '/waterPlat/fillData/village/query',
		params,
	})
}
//查询 水厂药品用量
export function queryDrugData(params) {
	return POST({
		url: '/waterPlat/fillData/selectDrugDosage',
		params,
	})
}
//新增 水厂药品用量
export function insertDrugData(params) {
	return POST({
		url: '/waterPlat/fillData/insertDrugDosage',
		params,
		requestType: 'json',
	})
}
//水源地填报下拉框接口
export function getDict(params) {
	return GET({
		url: '/sysConfig/get/byCode',
		params,
	})
}
// 泽雅水库根据实时水位查询实际库容
export function getCapacityByElevation(params) {
	return GET({
		url: '/waterPlat/fillData/getCapacityByElevation',
		params,
	})
}
