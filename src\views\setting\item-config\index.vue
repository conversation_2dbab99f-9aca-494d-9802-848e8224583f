<!--
 * @Descripttion: 
 * @version: 
 * @Author: heliping
 * @Date: 2021-11-22 09:09:45
 * @LastEditors: heliping
 * @LastEditTime: 2022-03-28 09:39:50
-->
<template lang="pug">
//- Table(:columns="columns1", :data="data1")
.setting2d
	.header
		Button(type='primary', @click='btnClick("addProcess")') 新增
	div
		Table(:columns='columns', :loading='loading', :data='data', stripe)
			template(slot-scope='{ row }', slot='processImage')
				img.imgClass(:src='row.processImage', alt='altText')
			template(slot-scope='{ row }', slot='operation')
				Button(type='primary', @click='btnClick("databinding", row)') 绑定数据
				Button(type='primary', @click='btnClick("setflow", row)') 设置流向
				Button(type='primary', @click='btnClick("preview", row)') 预览
				Button(type='error', @click='btnClick("delete", row)') 删除

	addModal(:show.sync='modalShow', @initList='handalList')
	//- 绑定数据
	dataBindingModal(ref='dataBindingModalref', :show.sync='dataModalShow', @initList='handalList')
	//- 设置流向
	setFlowModal(ref='setFlowModalref', :show.sync='setFlowModalShow', @seccessBack='handalList')
	//- 预览
	previewModal(ref='previewModalref', :show.sync='previewModalShow')
	EsConfirmModal(
		v-model='deleteShow',
		title='提示',
		content='是否删除当前项',
		@on-ok='handleDelete',
		@on-cancel='deleteShow = false'
	)
</template>
<script>
import addModal from './components/AddModal.vue'
import dataBindingModal from './components/DataBindingModal.vue'
import setFlowModal from './components/SetFlowModal.vue'
import previewModal from './components/PreviewModal.vue'
// import Pagination from '@/components/Pagination'
import { queryStationProcessList } from '@/api/setting'
import { EsConfirmModal } from '@eslink/esvcp-pc-ui'
export default {
	components: {
		// Pagination,
		addModal,
		dataBindingModal,
		setFlowModal,
		previewModal,
		EsConfirmModal,
	},
	data() {
		return {
			total: 0,
			listQuery: {
				page: 1,
				pageSize: 10,
			},
			loading: false,
			columns: [
				{
					title: '序号',
					key: 'processIndex',
				},
				{
					title: '工艺流程名称',
					key: 'processName',
				},
				{
					title: '站点名称',
					key: 'stationName',
				},
				{
					title: '工艺流程底图',
					slot: 'processImage',
				},
				{
					title: '操作',
					slot: 'operation',
				},
			],
			data: [],
			modalShow: false,
			dataModalShow: false,
			setFlowModalShow: false,
			previewModalShow: false,
			currentRow: {},
			deleteShow: false,
		}
	},
	computed: {},
	props: {},
	watch: {},
	created() {},
	mounted() {
		this.handalList()
	},
	methods: {
		btnClick(from, row) {
			switch (from) {
				case 'addProcess':
					this.modalShow = true
					break
				case 'databinding':
					this.dataModalShow = true
					this.$refs.dataBindingModalref.openGy(row)
					this.$nextTick(() => {
						this.currentRow = row
					})
					break
				case 'setflow':
					this.currentRow = row
					this.$refs.setFlowModalref.openGy(row)
					this.$nextTick(() => {
						this.setFlowModalShow = true
					})
					break
				case 'preview':
					this.previewModalShow = true
					this.$refs.previewModalref.init(row)
					this.$nextTick(() => {
						this.currentRow = row
					})
					break
				case 'delete':
					// debugger
					this.currentRow = row
					this.deleteShow = true
					break

				default:
					break
			}
		},
		// handleDelete() {
		// 	delete2dProcess({ id: this.currentRow.id }).then(() => {
		// 		this.deleteShow = false
		// 		this.handalList()
		// 	})
		// },
		handalList() {
			// get2dProcessList({ applicationName: 'iwater' }).then(res => {
			queryStationProcessList().then(res => {
				this.data = res.result
			})
		},
		handlePagination(data) {
			this.listQuery = data
			this.getAlarmLogging()
		},
	},
}
</script>
<style lang="less" scoped>
.setting2d {
	height: 100%;
	.header {
		display: flex;
		justify-content: flex-end;
		padding: 8px;
	}
}
::v-deep {
	.imgClass {
		height: 20px;
	}
	.ivu-btn {
		margin-right: 5px;
	}
}
</style>
