/*
 * @Description: 报警配置
 * @Author: shenxh
 * @Date: 2022-03-09 10:17:23
 * @LastEditors: shenxh
 * @LastEditTime: 2022-03-15 10:46:59
 */

import { POST, GET } from '@/utils/request'

// 获取特定方案信息
export function getSchemeInfo(params) {
	return POST({
		url: '/alarmConfig/get',
		params,
	})
}

// 查询报警方案配置
export function getAlarmConfig(params) {
	return POST({
		url: '/alarmConfig/queryPage',
		params,
	})
}

// 保存更新报警方案
export function saveOrUpdate(params) {
	return POST({
		url: '/alarmConfig/saveOrUpdate',
		params,
		requestType: 'json',
	})
}

// 启用停用方案
export function setSwitch(params) {
	return POST({
		url: '/alarmConfig/toggle',
		params,
	})
}

// 删除报警方案
export function deleteAlarmConfigs(params) {
	return POST({
		url: '/alarmConfig/deleteAlarmConfigs',
		params,
		requestType: 'json',
	})
}

// 报警方案统计
export function queryAlarmConfigCount(params) {
	return POST({
		url: '/alarmConfig/queryAlarmConfigCount',
		params,
	})
}

// 查询可视化配置
export function getVisualAlarm(params) {
	return GET({
		url: '/alarmFilter/configPage',
		params,
	})
}
// 开启关闭过滤
export function setFilter(params) {
	return POST({
		url: '/alarmFilter/enableFilter',
		params,
		requestType: 'json',
	})
}

// 报警方案分页查询（过滤掉已经被配置的规则）
export function getSystemAlarmFilter(params) {
	return POST({
		url: '/alarmConfig/queryPage/systemAlarmFilter',
		params,
	})
}

// 查询已绑定报警规则
export function getAlarmFilter(code, params) {
	return GET({
		url: `/alarmFilter/sysBindInfo/${code}`,
		params,
	})
}

// 保存已选择的报警规则
export function bindAlarmRulers(params) {
	return POST({
		url: '/alarmFilter/bind',
		params,
		requestType: 'json',
	})
}

// 告警记录及详情接口
// 获取告警记录分页查询
export function apiGetAlarmRecord(params) {
	return POST({
		url: '/alarm/queryPage',
		params,
	})
}
// 获取告警记录详情
export function apiGetAlarmRecordDetail(params) {
	return GET({
		url: '/alarm/get',
		params,
	})
}

// 告警记录登记
export function apiSubmitAlarmRecord(params) {
	return POST({
		url: '/alarm/process/register',
		params,
		requestType: 'json',
	})
}
// 设备类型字典查询
export function apiDeviceTyleList(params) {
	return GET({
		url: '/waterPlat/baseEquipmentDataItemTemplate/queryTemplateByName',
		params,
	})
}
// 告警类型字典查询
export function apiGetAlarmTypeList(params) {
	return GET({
		url: '/alarm/queryType',
		params,
	})
}
// 告警等级字典查询
export function apiGetAlarmLevelList(params) {
	return GET({
		url: '/alarm/getAlarmLevelInfo',
		params,
	})
}
// 告警记录处理人字典
export function apiGetHandlePerson(params) {
	return GET({
		url: '/alarm/process/getUsers',
		params,
	})
}
