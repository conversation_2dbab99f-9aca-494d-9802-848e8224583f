<template lang="pug">
.create-item-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='600',
		:value='show',
		:title='title',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='95')
					FormItem(label='数据项名称', prop='baseItemName')
						Input(v-model='formData.baseItemName', :disabled='!!formData.id', placeholder='请输入数据项名称')
					FormItem(label='数据项编号', prop='baseItemCode')
						Input(v-model='formData.baseItemCode', :disabled='!!formData.id', placeholder='请输入数据项编号')
					FormItem(label='所属模块', prop='type')
						Select(v-model='formData.type', :disabled='!!formData.id', transfer, filterable, placeholder='请选择')
							Option(v-for='(item, key) in options', :key='key', :value='item.code') {{ item.name }}
					FormItem(label='数据项单位', prop='unit')
						Input(v-model='formData.unit', placeholder='请输入数据项单位')
					FormItem(label='有效位数', prop='precision')
						InputNumber(v-model='formData.precision', :style='{ width: "400px" }', placeholder='有效位数', :max='5', :min='0')

					FormItem(label='备注')
						Input(v-model='formData.memo', type='textarea', placeholder='请输入备注')
		template(slot='footer')
			Button(@click='handleClose') 关闭
			Button(type='primary', @click='handleSubForm') 保存
</template>

<script>
import { saveBaseDataItem, updateBaseDataItem } from '@/api/base-item.js'
import { querySysList } from '@/api/common.js'
// import dataRepairVue from '../../../data-repair/data-repair.vue'
export default {
	name: 'CreateItemPopup',
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			title: '添加',
			formData: {
				baseItemName: '',
				baseItemCode: '',
				type: null,
				unit: '',
				precision: null,
				memo: '',
			},
			options: [],
			formRules: {
				baseItemName: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
				type: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
					},
				],
				baseItemCode: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		querySysList().then(res => {
			this.options = res.result
		})
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal() {
			this.$refs.form.resetFields()
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					if (this.formData.id) {
						updateBaseDataItem(this.formData)
							.then(() => {
								this.$Message.success('提交成功!')
								this.$refs.form.resetFields()
								this.$emit('update:show', false)
								this.$emit('initList')
							})
							.catch(() => {
								this.listLoading = false
							})
					} else {
						saveBaseDataItem(this.formData)
							.then(() => {
								this.$Message.success('提交成功!')
								this.$refs.form.resetFields()
								this.$emit('update:show', false)
								this.$emit('initList')
							})
							.catch(() => {
								this.listLoading = false
							})
					}
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},

		setData(data) {
			console.log(data)
			this.title = '编辑'
			this.formData = data
			// this.formData =  {
			// 	baseItemName,
			// 	baseItemCode: '',
			// 	type: null,
			// 	unit: '',
			// 	precision: null,
			// 	memo: '',
			// },
		},

		// 按钮-关闭
		handleClose() {
			this.formData = {
				baseItemName: '',
				baseItemCode: '',
				type: null,
				unit: '',
				precision: null,
				memo: '',
			}
			this.$emit('update:show', false)
		},
	},
}
</script>

<style lang="less" scoped>
.create-item-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			padding: 0 40px;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
