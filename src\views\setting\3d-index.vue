<!--
 * @Descripttion: 
 * @version: 
 * @Author: heliping
 * @Date: 2021-11-22 09:09:45
 * @LastEditors: heliping
 * @LastEditTime: 2022-03-23 19:02:52
-->
<template lang="pug">
.setting3d
	.upload-item 
		Button(type='warning', style='margin-right: 50px', :disabled='steps !== 0') 模型文件
			input(
				multiple,
				style='position: absolute; left: 0px; opacity: 0; height: 30px; width: 130px',
				type='file',
				@change='modelUpload($event)'
			)
		Button(type='primary', @click='confirmUpload', :disabled='steps !== 0') 完成上传
	Spin(fix, v-if='listLoading')
	.constructing(v-if='steps !== 0 && steps !== -1') 
		i-input.wrap-input(v-model='modelUrl', name='input', placeholder='请输入模型名字', @on-enter='searchList')
		span 已经选择{{ selection.length }}个模型

		Button(type='warning', :disabled='steps !== 2', @click='confirmChoose') 确定选择

		Table(:columns='columns', :data='data', height=400, @on-selection-change='selecChangetHandle')

		.constructing-operation
			Button(type='warning', :disabled='steps !== 3') 地址文件
			input(
				multiple,
				style='position: absolute; left: 0px; opacity: 0; height: 30px; width: 130px',
				type='file',
				@change='daeUpload($event)'
			)
			//- i-input.wrap-input(v-model="fileUrl", name="input", placeholder="请输入文件地址")
			Button(type='primary', :disabled='steps !== 4', v-if='!listLoading', @click='parsingData') 解析

			i-select.wrap-select(v-if='modelData', v-model='endTarget', placeholder='请选择模型中心点')
				i-option(v-for='item in modelData', :key='item.position', :value='item.position', placeholder='请选择模型') {{ item.name }}
			i-input.wrap-input(v-model='modelName', name='input', v-if='modelData', placeholder='请输入模型名字')
			Button(type='primary', @click='cookingData') 构建数据
			Button(type='primary', :disabled='steps !== 6', @click='submit') 提交
		//- div
			span.constructing-header *此操作需要在D盘文件构建file文件夹,在file文件夹下构建glp文件夹存储模型文件,同级放入zuobiao.dae文件

		Input(v-model='message', type='textarea', :rows='14')

		//- div
		//- 	span.constructing-header *组装好的数据

		//- Input(v-model="info", type="textarea", :rows="14")
</template>
<script>
// import FillTable from '@/components/fill-table.js'
import { getJson } from '@/utils/construct-model-data'
// import Pagination from '@/components/Pa gination'

import gltfData from './3d-associated-config/data/gltfData.js'

import { save3dData, get3dList, addDiagram, screen3DbatchSave } from '@/api/setting.js'
export default {
	// components: { Pagination },
	data() {
		return {
			// ifBefore: '0',
			// 0:开始;
			// 1:上传结束(必须一个场景的模型全部上传结束);
			// 2:选择选中的文件
			// 3:加载地址文件之后
			// 4:解析文件内容和上传model生成arr
			// 5: 构建数据
			steps: 0,
			total: 0,
			loading: false,
			columns: [
				{
					type: 'selection',
					width: 60,
					align: 'center',
				},
				{
					title: '序号',
					key: 'index',
				},
				{
					title: '名称',
					key: 'modelName',
				},
				{
					title: 'URL地址',
					key: 'modelUrl',
				},
			],
			data: [],
			message: '',
			listLoading: false,
			files: [],
			endTarget: '',
			modelData: '',
			modelName: '',
			modelUrl: '',
			fileContent: '',
			allSelecttion: {},
			selection: [],
			modalArr: [],
		}
	},
	computed: {},
	props: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		// 确定上传
		confirmUpload() {
			this.steps = 1
			get3dList().then(res => {
				let arr = []
				let sele = []
				// let obj = {}
				res.result.forEach((item, index) => {
					// if (!obj[item.modelUrl]) {
					// arr.push({ ...item, index: index + 1 })
					// obj[item.modelUrl] = item.modelUrl
					// }
					if (this.modalArr.includes(item.modelUrl)) {
						arr.push({ ...item, index: index + 1, _checked: true })
						sele.push({ ...item, index: index + 1, _checked: true })
					} else {
						arr.push({ ...item, index: index + 1 })
					}
				})
				this.selecChangetHandle(sele)
				this.modalArr = []
				this.data = arr
			})
		},
		confirmChoose() {
			this.steps = 3
			let arr = []
			this.selection.forEach(item => {
				arr.push(item.modelUrl)
			})
			this.message = JSON.stringify(arr)
		},
		// 读取本地地址文件
		daeUpload(event) {
			if (window.FileReader) {
				this.listLoading = true
				let file = event.target.files
				let filename = file[0].name.split('.')[0]
				console.log(filename)
				var reader = new FileReader()
				const _this = this
				reader.onload = function () {
					_this.fileContent = this.result
					_this.message = this.result
					_this.listLoading = false
					_this.steps = 4
				}

				reader.readAsText(file[0])
			}
		},

		modelUpload(event) {
			let file = event.target.files
			this.listLoading = true
			let data = new FormData()
			// let count = 0
			// for (let key in file) {
			// 	if (key < file.length) {
			// 		data.append('files[' + count + ']', file[key])
			// 		count++
			// 	}
			// }
			data.append('files', file)
			// data.append('needPrefix', false)
			this.$axios.post('/backgroundImage/image/upload', data).then(result => {
				console.log(result)
				// this.batchSave(result)
				this.$Message.info('上传成功')
				this.listLoading = false
			})
		},
		batchSave(data) {
			const { result } = data
			let arr = []
			result.forEach(item => {
				if (item.indexOf('.glb') > 0) {
					arr.push({
						modelName: item.split('.com/')[1],
						modelUrl: item,
					})
					this.modalArr.push(item)
				}
			})
			// this.modalArr = [...this.modalArr, ...arr]
			screen3DbatchSave(arr).then(() => {
				this.$Message.info('绑定成功')
			})
		},
		// 解析数据
		parsingData() {
			// let str = this.fileUrl + '\\glb'
			// const test = str.replace(/\\/g, '//')
			// const test= 'C://Users/<USER>/Desktop/file/glb'
			// const context = require.context(
			// 	'C://Users//houyan//Desktop//file//glb', // 不能使用变量
			// 	false,
			// 	/\.glb$/,
			// )
			// this.files = context.keys()
			// // debugger
			this.steps = 5
			this.files = [...Object.values(this.allSelecttion)]
			let array = getJson(this.files, this.fileContent)
			// // console.log(array)
			this.message = JSON.stringify(array)
			// this.modelData = array
			// debugger
			this.endTarget = array[0].position
		},
		searchList() {
			get3dList({ modelUrl: this.modelUrl }).then(res => {
				this.data = res.result
			})
		},
		selecChangetHandle(selection) {
			this.steps = 2
			this.selection = selection
			this.selection.forEach(item => {
				this.allSelecttion[item.modelName] = item.modelUrl
			})
		},
		// 组装需要的数据结构
		cookingData() {
			// const objData = gltfData.dealPositionData({
			// 	allArr: JSON.parse(this.message),
			// })
			const objData = JSON.parse(this.message)
			const guandaoData = gltfData.getGuandaoData({
				endTargetData: this.endTarget,
				processFlowDiagramName: '保定荣投水厂测试',
			})
			this.steps = 6
			// const objData = gltfData.getObjDataEHZGLB(true)
			// const guandaoData = gltfData.getGuandaoDataEHTglb()
			const info = this.getDiagram({
				objData,
				guandaoData,
			})
			info.pointGroups = info.points

			console.log('info', info)
			this.message = JSON.stringify(info)
		},

		submit() {
			addDiagram(JSON.parse(this.message)).then(() => {
				this.$Message.info('提交成功')
			})
		},

		save3d(params) {
			save3dData(params).then(res => {
				// debugger
				console.log(res)
			})
		},
		getDiagram(data) {
			const { guandaoData, objData } = data
			const {
				redPoints,
				yellowPoints,
				normalPoints,
				whitePoints,
				brownPoints,
				blackPoints,
				greenPoints,
				points,
				endPosition,
				endTarget,
				startPosition,
				startTarget,
				processFlowDiagramName,
				guanDaoRadius,
			} = guandaoData
			const info = {}
			const {
				pointsRadius,
				greenPointsRadius,
				blackPointsRadius,
				brownPointsRadius,
				whitePointsRadius,
				redPointsRadius,
				yellowPointsRadius,
				normalPointsRadius,
			} = guanDaoRadius || {}
			//1是全局  2是站点
			info.processFlowDiagramType = 2
			info.processFlowDiagramName = processFlowDiagramName || 'CBD泵站挂在站点'
			let obj = {
				needFloor: 0,
				startPositionX: startPosition.x,
				startPositionY: startPosition.y,
				startPositionZ: startPosition.z,
				startTargetX: startTarget.x,
				startTargetY: startTarget.y,
				startTargetZ: startTarget.z,
				endPositionX: endPosition.x,
				endPositionY: endPosition.y,
				endPositionZ: endPosition.z,
				endTargetX: endTarget.x,
				endTargetY: endTarget.y,
				endTargetZ: endTarget.z,
			}
			info.property = JSON.stringify(obj)

			info.pointGroups = []
			if (points) {
				points.forEach((point, index) => {
					const data = {}
					const vectors = []
					point.forEach(p => {
						const vector = {}
						vector.x = p.x
						vector.y = p.y
						vector.z = p.z
						vectors.push(vector)
					})
					data.point = vectors
					data.name = 'blue' + index
					data.property = JSON.stringify({
						color: 'blue',
						size: pointsRadius || 4,
					})
					info.pointGroups.push(data)
				})
			}
			if (greenPoints) {
				greenPoints.forEach((point, index) => {
					const data = {}
					const vectors = []
					point.forEach(p => {
						const vector = {}
						vector.x = p.x
						vector.y = p.y
						vector.z = p.z
						vectors.push(vector)
					})
					data.point = vectors
					data.name = 'green' + index
					data.property = JSON.stringify({
						color: 'green',
						size: greenPointsRadius || 20,
					})
					info.pointGroups.push(data)
				})
			}
			if (blackPoints) {
				blackPoints.forEach((point, index) => {
					const data = {}
					// data.color = 'black'
					// data.size = blackPointsRadius || 6
					const vectors = []
					point.forEach(p => {
						const vector = {}
						vector.x = p.x
						vector.y = p.y
						vector.z = p.z
						vectors.push(vector)
					})
					data.point = vectors
					data.name = 'black' + index
					data.property = JSON.stringify({
						color: 'black',
						size: blackPointsRadius || 6,
					})
					info.pointGroups.push(data)
				})
			}
			if (brownPoints) {
				brownPoints.forEach((point, index) => {
					const data = {}
					const vectors = []
					point.forEach(p => {
						const vector = {}
						vector.x = p.x
						vector.y = p.y
						vector.z = p.z
						vectors.push(vector)
					})
					data.point = vectors
					data.name = 'brown' + index
					data.property = JSON.stringify({
						color: 'brown',
						size: brownPointsRadius || 6,
					})
					info.pointGroups.push(data)
				})
			}
			if (whitePoints) {
				whitePoints.forEach((point, index) => {
					const data = {}
					const vectors = []
					point.forEach(p => {
						const vector = {}
						vector.x = p.x
						vector.y = p.y
						vector.z = p.z
						vectors.push(vector)
					})
					data.point = vectors
					data.name = 'white' + index
					data.property = JSON.stringify({
						color: 'white',
						size: whitePointsRadius || 6,
					})
					info.pointGroups.push(data)
				})
			}
			if (redPoints) {
				redPoints.forEach((point, index) => {
					const data = {}
					const vectors = []
					point.forEach(p => {
						const vector = {}
						vector.x = p.x
						vector.y = p.y
						vector.z = p.z
						vectors.push(vector)
					})
					data.point = vectors
					data.name = 'red' + index
					data.property = JSON.stringify({
						color: 'red',
						size: redPointsRadius || 6,
					})
					info.pointGroups.push(data)
				})
			}
			if (yellowPoints) {
				yellowPoints.forEach((point, index) => {
					const data = {}
					const vectors = []
					point.forEach(p => {
						const vector = {}
						vector.x = p.x
						vector.y = p.y
						vector.z = p.z
						vectors.push(vector)
					})
					data.point = vectors
					data.name = 'yellow' + index
					data.property = JSON.stringify({
						color: 'yellow',
						size: yellowPointsRadius || 5,
					})
					info.pointGroups.push(data)
				})
			}
			if (normalPoints) {
				normalPoints.forEach((point, index) => {
					const data = {}
					const vectors = []
					point.forEach(p => {
						const vector = {}
						vector.x = p.x
						vector.y = p.y
						vector.z = p.z
						vectors.push(vector)
					})
					data.point = vectors
					data.name = '' + index
					data.property = JSON.stringify({
						color: '',
						size: normalPointsRadius || 4,
					})
					info.pointGroups.push(data)
				})
			}
			info.processFlowDiagramModels = []
			// let index = 0
			objData.forEach((data, index) => {
				const data1 = {}
				data1.labelContent = data.labelContent || []
				data1.modelName = data.name ? data.name : data.type + index
				data1.property = JSON.stringify({
					modelUrl: data.type,
					labelScale: data.labelScale,
					showLabelType: data.showLabelType || 0,
					renderType: null,
					showMonitorType: data.showMonitorType || 0,
					alarmPrompt: data.alarmPrompt,
					disappear: data.disappear,
					extraAttribute: data.extraAttribute,
					nextDiagramId: null,
					clickColor: null,
					clickOpacity: null,
					modelType: data.modelType,
					needTransparent: data.needTransparent,
					needBeam: data.needBeam,
					positionX: data.position.x,
					positionY: data.position.y,
					positionZ: data.position.z,
					scaleX: data.scale ? data.scale.x : 1.0,
					scaleY: data.scale ? data.scale.y : 1.0,
					scaleZ: data.scale ? data.scale.z : 1.0,
					rotationX: data.rotation ? data.rotation.x : 0.0,
					rotationY: data.rotation ? data.rotation.y : 0.0,
					rotationZ: data.rotation ? data.rotation.z : 0.0,
					hasGlbRotateSet: data.hasGlbRotateSet || '',
				})
				info.processFlowDiagramModels.push(data1)
			})
			return info
		},
	},
}
</script>
<style lang="less" scoped>
.setting3d {
	height: 100%;
	padding-top: 20px;
	padding-left: 30px;
	.upload-item {
		display: flex;
		align-items: baseline;
		.upload-button {
			display: flex;
			justify-content: space-between;
			// flex: 1;
			width: 30%;
		}
	}
	.wrap-select,
	.wrap-input {
		display: inline-block !important;
		width: 200px !important;
	}
	.constructing {
		padding-top: 20px;
		.constructing-header {
			color: red;
		}
		.constructing-operation {
			margin: 10px 0;
			::v-deep {
				.ivu-btn {
					margin-right: 8px;
				}
			}
		}
	}
}
</style>
