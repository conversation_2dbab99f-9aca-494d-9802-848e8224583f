<!--
 * @Descripttion: 
 * @version: 
 * @Author: heliping
 * @Date: 2021-12-08 13:47:44
 * @LastEditors: heliping
 * @LastEditTime: 2021-12-08 13:56:43
-->
<template>
	<!-- draggable -->
	<div v-if="modal">
		<Modal v-model="modal" :width="width" :mask-closable="false" :footer-hide="footerHide" :styles="{ top: top }">
			<div slot="header">
				<Header>{{ title }}</Header>
			</div>
			<slot name="context"></slot>
			<div slot="footer">
				<slot name="btn"></slot>
			</div>
		</Modal>
	</div>
</template>
<script>
import Header from './header'
export default {
	components: {
		Header,
	},
	props: {
		top: {
			type: String,
			default: null,
		},
		width: {
			//modal 的宽度
			type: Number,
			default: 500,
		},
		title: {
			// modal 标签名
			type: String,
			default: null,
		},
		footerHide: {
			// 是否需要底部栏  默认展示
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			modal: false,
		}
	},
	methods: {
		testFun() {
			console.log('test0--------')
		},
	},
}
</script>
