<!--
 * @Description: 手动确认的组件
 * @Version: 2.0
 * @Autor: z<PERSON>yi
 * @Date: 2022-05-05 09:20:39
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-05-12 11:31:01
-->
<template lang="pug">
Modal(
	:value='show',
	class-name='info-modal',
	width='600',
	title='巡检确认',
	:mask='true',
	footer-hide,
	:transfer='false',
	@on-cancel='handleCancel',
	@on-visible-change='handleVisibleChange'
)
	es-header(title='该巡检任务已完成，请确认！')
	.check-modal-content
		.file-list(v-if='fileList.length')
			.file-title 巡检照片：
			.file-item(v-for='(item, index) in fileList', :key='index')
				img.file-item-img(:src='item.url', @click='handleView(fileList, index, true)')
				Icon.cursor-pointer(type='md-close-circle', size='16', @click.native='handleRemove(index)')
		.upload-wrapper
			input.image(type='file', name='image', capture='camera', accept='image/*', @change='handleImageUpload')
			Icon(type='ios-cloud-upload', size='20', style='color: #3399ff')
			span(style='margin-left: 4px') 上传照片
		.footer
			Button(type='primary', :loading='loading', @click='submit()') 提交
			Button.water-margin-left-16(@click='handleCancel()') 取消
</template>
<script>
import { axios } from '@/utils/request.js'
import { getUId, confirmTask } from '@/api/inspection-management.js'
import { init } from '@eslink/esvcp-pc-ui/packages/es-components/imageViewer/index.js'
export default {
	name: 'check-task',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		sysCode() {
			return this.$route.query.sysCode || 'dc'
		},
		uploadAction() {
			return 'http://*************:10014/FileCenter/ManagerService.asmx/uploadFile'
		},
	},
	data() {
		return {
			uploadExitData: {}, // 上传额外参数
			fileList: [],
			loading: false,
			file: null,
			uid: '',
			indentyDisplay: '',
			ContentID: null,
			TaskId: null,
		}
	},
	mounted() {
		this.getMessages()
	},
	methods: {
		getMessages() {
			window.addEventListener('message', messageEvent => {
				const data = messageEvent.data
				const params = data.params

				this.indentyDisplay = this.loginName = params && params.loginName ? params.loginName : 'yueqingTest'
				if (this.indentyDisplay) {
					this.getUid(this.indentyDisplay)
				}
			})
		},
		handleVisibleChange(value) {
			if (!value) {
				this.file = null
				this.fileList = []
			}
		},
		handleCancel() {
			this.file = null
			this.fileList = []
			this.loading = false
			this.$emit('update:show', false)
		},
		initTask(TaskId, row) {
			this.TaskId = TaskId
			this.ContentID = row.ContentID
		},
		handleImageUpload(event) {
			const check = this.fileList.length < 3
			if (!check) {
				this.$Message.info('最多上传三张图片')
				return false
			}
			this.file = event.target.files[0]
			const formData = new FormData()

			const localkey =
				(this.file.lastModifiedDate + '').replace(/\W/g, '') +
				this.file.size +
				this.file.type.replace(/\W/g, '')
			formData.append('name', this.file.name)
			formData.append('start', 0)
			formData.append('project', '巡检图片')
			formData.append('localkey', localkey)
			formData.append('size', this.file.size)
			formData.append('indenty', this.uid)
			formData.append('indentyDisplay', this.indentyDisplay)
			formData.append('file', this.file)
			const online = process.env.VUE_APP_ENV === 'online'
			const imageurl = online ? process.env.VUE_PLAT_IMAGE_URL : window.VUE_PLAT_IMAGE_URL

			const url = `${imageurl}/FileCenter/ManagerService.asmx/uploadFile`
			axios
				.post(url, formData, {
					withCredentials: false,
				})
				.then(res => {
					const { id } = res
					const url = `${imageurl}/FileCenter/IMG/Read/${id}/para?uid=${this.uid}`

					this.fileList.push({
						Type: this.file.type,
						Name: this.file.name,
						MediaId: id,
						url,
					})
				})
		},
		// 设备图片预览
		handleView(imgUrlList = [], idx, isGetUrl) {
			if (isGetUrl) {
				// 列表item对象获取url
				imgUrlList = imgUrlList.map(item => {
					return item.url
				})
			}
			init({
				imgUrlList,
				index: idx,
				title: '现场图片',
			})
		},
		// 设备图片移除
		handleRemove(picIndex) {
			this.fileList.splice(picIndex, 1)
		},
		getUid(UserName) {
			getUId({ UserName }).then(res => {
				this.uid = res.result
			})
		},
		//提交
		submit() {
			if (this.fileList.length < 1) {
				this.$Message.info('请上传图片！')
				return
			}
			const list = this.fileList.map(item => {
				return {
					MediaId: item.MediaId,
					Type: item.Type,
					Name: item.Name,
				}
			})
			const data = {
				ContentID: this.ContentID,
				DeviceImages: list,
			}
			confirmTask(this.TaskId, data).then(() => {
				this.$Message.info('操作成功！')
				this.handleCancel()
				this.$emit('fresh-detail')
			})
		},
	},
}
</script>
<style lang="less" scoped>
.check-modal-content {
	text-align: center;
	min-height: 130px;
}
.footer {
	margin-top: 32px;
}
.file-list {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	margin-left: 32px;
	margin-top: 24px;
}
.file-title {
	display: inline-block;
}
.file-item {
	width: 72px;
	height: 72px;
	position: relative;
	display: inline-block;
	cursor: pointer;
	margin-left: 8px;
	&-img {
		width: 100%;
		height: 100%;
	}
	.cursor-pointer {
		position: absolute;
		top: -6px;
		right: -6px;
		display: inline-block;
		cursor: pointer;
	}
}
.upload-wrapper {
	width: 130px;
	height: 32px;
	line-height: 32px;
	margin: 24px auto;
	position: relative;
	border: 1px solid #e4e4e4;
	cursor: pointer;
	.image {
		opacity: 0;
		width: 100%;
		height: 100%;
		z-index: 1;
		position: absolute;
		top: 0;
		left: 0;
	}
}
::v-deep {
	.ivu-icon {
		vertical-align: -0.3em;
	}
	.ivu-btn-text {
		span {
			color: #1192e8;
		}
	}
}
</style>
