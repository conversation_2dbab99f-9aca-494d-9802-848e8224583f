<!--
 * @Description: 水务经营填报
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-30 16:35:42
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-24 17:19:09
-->
<template lang="pug">
.water-management-fill
	water-row(justify='space-between', align='center')
		water-row(justify='flex-start', align='center')
			RadioGroup(v-model='type', type='button')
				Radio(label='month') 月漏损率
				Radio(label='year') 年漏损率
			water-row(v-show='type === \'month\'', justify='flex-start', align='center')
				.water-management-fill-title 年份:
				Select.water-management-fill-select(v-model='yearId', placeholder='请选择', @on-change='changeYear')
					Option(v-for='(item, index) in yearList', :key='index', :disabled='item.disabled', :value='item.value') {{ item.label }}
		Button.water-margin-left-16(type='primary', @click='handleSave()', :loading='buttonLoading') 提交
	WaterTable.water-management-fill-table(
		v-if='type === "month"',
		border,
		:columns='getColumns()',
		:data='tableData',
		:loading='loading'
	)

	WaterTable.water-management-fill-table(
		v-if='type === "year"',
		border,
		:columns='getYearColumns()',
		:data='yearTableData',
		:loading='loading'
	)
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { yearList } from '@/utils/enums.js'
import { EventBus } from '@/utils/eventBus.js'
import { queryManagementData, insertManagementData } from '@/api/water-screen-data.js'
export default {
	components: {
		WaterTable,
		WaterRow,
	},
	data() {
		return {
			date: new Date(),
			type: 'month',
			tableData: [],
			yearTableData: [],
			columns: [],
			loading: false,
			buttonLoading: false,
			yearId: new Date().getFullYear() + '',
			yearList: yearList,
		}
	},
	mounted() {
		this.yearList.forEach(y => {
			if (y.value > this.yearId) {
				y.disabled = true
			}
		})
		this.handleMonthQuery()
		this.handleYearQuery()
	},
	methods: {
		handleMonthQuery() {
			queryManagementData({
				timeType: 1,
				time: this.yearId,
			})
				.then(res => {
					const { result = [] } = res
					console.log(result, 'monnn')
					this.tableData = result.map((item, index) => {
						const obj = {
							key: index + 1,
							...item,
							editable: true,
						}
						if (item.list.length > 0) {
							item.list.forEach(currentMonth => {
								const key = `lossRate_${currentMonth.index}`
								obj[key] = currentMonth.lossRate ? currentMonth.lossRate : ''
							})
						}
						return obj
					})
				})
				.catch(() => {
					this.tableData = []
				})
		},
		handleYearQuery() {
			queryManagementData({
				timeType: 2,
			})
				.then(res => {
					const { result = [] } = res
					this.yearTableData = result.map((item, index) => {
						const obj = {
							key: index + 1,
							...item,
							editable: true,
						}
						if (item.list.length > 0) {
							item.list.forEach(currentMonth => {
								const key = `lossRate_${currentMonth.index}`
								obj[key] = currentMonth.lossRate ? currentMonth.lossRate : ''
							})
						}
						return obj
					})
				})
				.catch(() => {
					this.yearTableData = []
				})
		},
		//保存数据项
		handleSave() {
			try {
				this.buttonLoading = true
				const arr = []
				if (this.type === 'month') {
					this.tableData.forEach(item => {
						let { areaNum, list, nickName } = item
						list.forEach(c => {
							const key = `lossRate_${c.index}`
							if (item[key]) {
								c.lossRate = item[key]
							}
						})
						arr.push({ areaNum, nickName, list })
					})
					const params = {
						list: arr,
						time: this.yearId,
						timeType: 1,
					}

					this.handleSubmitMonth(params)
				} else {
					this.yearTableData.forEach(item => {
						let { areaNum, list, nickName } = item
						list.forEach(c => {
							const key = `lossRate_${c.index}`
							if (item[key]) {
								c.lossRate = item[key]
							}
						})
						arr.push({ areaNum, nickName, list })
					})
					const params = {
						timeType: 2,
						time: this.yearId,
						list: arr,
					}
					this.handleSubmitYear(params)
				}
			} catch {
				this.buttonLoading = false
			}
		},
		handleSubmitMonth(params) {
			insertManagementData(params)
				.then(() => {
					this.$Message.success('提交成功!')
					EventBus.$emit('fresh-month-record')
					this.buttonLoading = false
					this.handleMonthQuery()
				})
				.catch(() => {
					this.buttonLoading = false
				})
		},
		handleSubmitYear(params) {
			insertManagementData(params)
				.then(() => {
					this.$Message.success('提交成功!')
					EventBus.$emit('fresh-year-record')
					this.buttonLoading = false
					this.handleYearQuery()
				})
				.catch(() => {
					this.buttonLoading = false
				})
		},
		changeYear() {
			this.handleMonthQuery()
		},
		getColumns() {
			const columns = [
				{ title: '序号', align: 'center', key: 'key', width: 70 },
				{
					title: '水管所名称',
					align: 'center',
					key: 'nickName',
					minWidth: 110,
				},
			]
			const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
			if (list.length > 0) {
				list.forEach(item => {
					const key = `lossRate_${item}`
					columns.push({
						title: `${item}月`,
						align: 'center',
						key,
						minWidth: 120,
						render: (h, params) => {
							const itemValue = params.row[key]
							const disabled = this.getButtonDisable(item)
							return h('Input', {
								props: {
									value: itemValue,
									maxlength: 5,
									disabled,
								},
								on: {
									'on-change': e => {
										this.valueChange(e)
										const value = e.target.value
										this.handleInputValue(params.index, value, key)
									},
									'on-keyup': e => {
										this.valueChange(e)
									},
								},
							})
						},
					})
				})
			}
			return columns
		},
		getYearColumns() {
			const columns = [
				{ title: '序号', align: 'center', key: 'key', width: 70 },
				{
					title: '水管所名称',
					align: 'center',
					key: 'nickName',
					minWidth: 100,
				},
			]
			const list = [2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030]
			if (list.length > 0) {
				list.forEach(item => {
					const key = `lossRate_${item}`
					columns.push({
						title: item,
						align: 'center',
						key,
						minWidth: 120,
						render: (h, params) => {
							const itemValue = params.row[key]
							const disabled = this.getYearButtonDisable(item)
							return h('Input', {
								props: {
									value: itemValue,
									maxlength: 5,
									disabled,
								},
								on: {
									'on-change': e => {
										this.valueChange(e)
										const value = e.target.value
										this.handleInputYearValue(params.index, value, key)
									},
									'on-keyup': e => {
										this.valueChange(e)
									},
								},
							})
						},
					})
				})
			}

			return columns
		},
		//正则
		valueChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			e.target.value = e.target.value.replace(/^(([0-9]{3}\d))(.\d{1,2})?$/, '')
		},
		//输入值
		handleInputValue(index, value, key) {
			this.tableData[index][key] = value
			// const item = this.tableData[index]
			// this.tableData.splice(index, 1, {
			// 	...item,
			// 	[key]: value,
			// })
		},
		handleInputYearValue(index, value, key) {
			this.yearTableData[index][key] = value
		},
		getYearButtonDisable(number) {
			const year = new Date().getFullYear()
			return number > year
		},
		getButtonDisable(index) {
			const year = new Date().getFullYear()
			if (Number(this.yearId) > year) {
				return true
			} else if (Number(this.yearId) === year) {
				const nowDate = `${this.yearId}-${index}`
				const month = new Date().getMonth()
				const currentDate = `${year}-${month + 1}`
				return new Date(nowDate).getTime() > new Date(currentDate).getTime()
			} else {
				return false
			}
		},
	},
}
</script>
<style scoped lang="less">
.water-management-fill {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-title {
		margin-left: 16px;
		margin-right: 4px;
		white-space: nowrap;
	}
	&-select {
		width: 150px;
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
	::v-deep {
		.ivu-radio-wrapper-checked {
			background: #1192e8;
			color: #ffffff;
		}
	}
}
</style>
