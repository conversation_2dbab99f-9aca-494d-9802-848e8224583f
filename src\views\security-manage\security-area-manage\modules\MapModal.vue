<!--
 * @Description: 定位地图的弹窗
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-02-22 17:23:48
 * @LastEditors: zhangyi
 * @LastEditTime: 2023-05-08 14:23:10
-->
<template>
	<Modal
		width="950"
		title="地图定位"
		:value="show"
		class-name="custom-modal"
		@on-cancel="clickCancel"
		@on-visible-change="visibleChange"
	>
		<div class="map-container mt10">
			<div class="fix-search">
				<input
					type="text"
					id="keyword"
					class="keywork"
					name="keyword"
					placeholder="请输入地址"
					v-model="inputValue"
					autocomplete="off"
				/>
				<Button type="primary" size="small" @click="searchValue" class="search-btn">搜索</Button>
			</div>
			<div id="map"></div>
		</div>
		<div slot="footer" class="mt10">
			<Button type="primary" size="small" @click="changeSubmitFn()">确定位置</Button>
		</div>
	</Modal>
</template>

<script>
// 地图配置
import AMap from '@/utils/AmapController.js'
const ToolBarOptions = {
	position: 'RB',
	liteStyle: true,
	ruler: true,
	locate: true,
}
export default {
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		loading: {
			type: Boolean,
			default: false,
		},
		width: {
			type: [String, Number],
			default: '98%',
		},
		height: {
			type: [String, Number],
			default: 3000,
		},
		currentRow: {
			type: Object,
			default: function () {
				return {
					address: '', // 地址
					coordinate: '', // 经纬度字符串
				}
			},
		},
	},

	data() {
		return {
			showModal: false,
			inputValue: '', // 地图输入框搜索值
			map: null,
			circle: null, // 圆形区域
			marker: null, // 圆心
			center: [], // 定位经纬度
		}
	},
	watch: {
		show: {
			handler(v) {
				this.showModal = v
				if (v) {
					this.$nextTick(() => {
						this.init()
					})
				} else {
					this.map = null
				}
			},
		},
	},
	methods: {
		clickOk(lnglat, address) {
			this.$emit('on-ok', { lnglat, address })
		},
		clickCancel(value) {
			this.polyObject = null
			this.$emit('on-cancel', value)
		},
		visibleChange(value) {
			this.$emit('on-visible-change', value)
		},
		init() {
			this.map = new AMap({
				initNow: true,
				id: 'map',
				initData: {
					zoom: 13,
					zooms: [3, 20],
					resizeEnable: true,
				},
			})
			this.map.on('complete', () => {
				this.installMapPlugins('ToolBar', ToolBarOptions, 'control')
				//初始化中心点，地址为空或转换不成功通过IP地址城市定位
				let row = this.currentRow
				this.inputValue = row.address || ''

				const array = row.coordinate ? row.coordinate.split(',') : []
				if (row.coordinate && array[0] !== 'undefined') {
					this.initMap(array[0], array[1])
				} else if (row.address) {
					this.searchLocation(row.address)
				} else {
					this.getLocation()
				}
			})
		},

		// 安装插件
		installMapPlugins(pluginName, pluginOptions, type) {
			const plugin = this.map.install(pluginName, pluginOptions)
			this.map.usePlugins(pluginName, plugin, type)
		},
		// 2020-08-20新增根据搜索查询（结果第一条'）出定位
		searchLocation(address) {
			var placeSearch = this.map.install('PlaceSearch', {
				pageSize: 1,
				pageIndex: 1,
				city: '全国',
				citylimit: false,
				autoFitView: true,
			})
			placeSearch.search(address, (status, result) => {
				if (
					status === 'complete' &&
					result.poiList instanceof Object &&
					result.poiList.pois instanceof Array &&
					result.poiList.pois.length > 0
				) {
					let lng = result.poiList.pois[0].location.lng
					let lat = result.poiList.pois[0].location.lat
					this.initMap(lng, lat)
				} else {
					this.initMap(120.699107, 27.993849)
				}
			})
		},
		// 在地址为空或者找不到地址的情况下根据当前位置定位
		getLocation() {
			let geolocation = this.map.install('Geolocation', {
				enableHighAccuracy: true, //是否使用高精度定位，默认:true
				timeout: 10000, //超过10秒后停止定位，默认：5s
				buttonPosition: 'LT', //定位按钮的停靠位置
				zoomToAccuracy: true, //定位成功后是否自动调整地图视野到定位点
				panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
				showMarker: true,
				needAddress: true, // 必填
			})
			geolocation.getCurrentPosition((status, result) => {
				if (status == 'complete') {
					let { lng, lat } = result.position
					this.initMap(lng, lat)
					this.center = [lng, lat]
					this.inputValue = result.formattedAddress
				} else {
					this.initMap(120.699107, 27.993849)
				}
			})
		},
		// 渲染地图
		initMap(lng, lat) {
			this.map = new AMap({
				initNow: true,
				id: 'map',
				scrollWheel: false,
				initData: {
					resizeEnable: true,
					center: [lng, lat],
					zoom: 13,
				},
			})

			this.map.on('complete', () => {
				// 获取定位中心
				this.center = [lng, lat]
				this.drawMaker(lng, lat)
				// 设置搜索定位
				this.setKeySearch()
				this.map.setFitView()
			})
			this.map.on('click', e => {
				let lng = e.lnglat.getLng()
				let lat = e.lnglat.getLat()

				this.map.toAddress([lng, lat]).then(res => {
					this.inputValue = res
				})
				this.marker.setPosition(this.map.createLnglat(lng, lat))
				// 获取定位中心
				this.center = [lng, lat]
			})
		},
		// 查询提示搜索地址
		setKeySearch() {
			var autoOptions = {
				city: '全国', // 城市，默认全国
				input: 'keyword', // 使用联想输入的input的id
			}
			var autocomplete = this.map.install('AutoComplete', autoOptions)
			this.map.addListener(autocomplete, 'select', e => {
				let location = e.poi.location
				if (location) {
					this.inputValue = e.poi.name
					this.map.setCenter(location.lng, location.lat)
					this.marker.setPosition(this.map.createLnglat(location.lng, location.lat))
					this.center = [location.lng, location.lat]
				}
			})
		},
		drawMaker(lng, lat) {
			const icon = require('@/assets/image/local.svg')
			this.marker = this.map.createIconMarker(icon, lng, lat, {}, 0, 0, '', {
				zoom: 14,
				anchor: 'bottom-center',
			})
			this.marker.setMap(this.map)
			this.map.addMarker(this.marker)
		},
		searchValue() {
			this.searchLocation(this.inputValue)
		},
		// 确认定位
		changeSubmitFn() {
			this.clickOk(this.center, this.inputValue)
			this.visibleChange(false)
		},
	},
}
</script>
<style lang="less" scoped>
.transfer {
	&-dropdown {
		width: 190px;
		text-align: left;
	}

	&-container {
		::v-deep {
			.ivu-select-dropdown {
				left: -1px !important;
				top: 34px !important;
				box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1) !important;
			}
		}
	}

	&-list {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-right: 14px;
		padding-left: 14px;
	}
}
.map-container {
	position: relative;
	left: 0;
	top: 0;
	width: 926px;
	height: 350px;
	#map {
		position: relative;
		z-index: 1;
		width: 100%;
		height: 100%;
	}
	.fix-search {
		position: absolute;
		top: 15px;
		left: 50%;
		z-index: 9;
		margin-left: -145px;
		display: flex;
		.keywork {
			border: 1px solid #e8e8e8;
			border-radius: 3px;
			width: 300px;
			height: 28px;
			padding: 0 5px;
		}
		.search-btn {
			margin-left: 20px;
			height: 28px;
			width: 40px;
		}
	}
	.operate-btn {
		position: absolute;
		top: 15px;
		right: 20px;
		z-index: 9;
		display: flex;
		align-items: center;
	}
}
.form-item {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
::v-deep {
	.ivu-modal-body {
		flex-direction: column;
	}
	.ivu-modal-wrap {
		overflow: hidden;
	}
}
</style>
