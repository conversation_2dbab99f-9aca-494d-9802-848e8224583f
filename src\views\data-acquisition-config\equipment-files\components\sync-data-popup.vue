<!--
 * @Description: 同步的原始信息弹窗
 * @Author: shenxh
 * @Date: 2023-04-07 10:10:48
 * @LastEditors: shenxh
 * @LastEditTime: 2023-04-07 10:48:37
-->

<template lang="pug">
.sync-data-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='500',
		footer-hide,
		v-model='showModal',
		title='同步的原始信息',
		@on-cancel='handleClose'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :label-width='100')
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='最后更新时间', prop='updateTime')
							Input(v-model='formData.updateTime', disabled)
				es-table(:columns='columns', :data='tableData', :loading='loading', border, height='280')
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import { queryOriginalStationInfo } from '@/api/data-acquisition-config'

export default {
	name: 'sync-data-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		WaterRow,
	},
	props: {
		showModal: Boolean,
		data: Object,
	},
	data() {
		return {
			loading: false,
			formData: {},
			tableData: [],
			columns: [
				{
					type: 'index',
					title: '序号',
					align: 'center',
					width: 80,
				},
				{
					title: '属性',
					key: 'key',
				},
				{
					title: '值',
					key: 'value',
				},
			],
		}
	},
	computed: {},
	watch: {
		showModal(val) {
			if (val) {
				this.formData = this.data

				this.queryOriginalStationInfo()
			}
		},
	},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		// 设备档案同步信息查询
		queryOriginalStationInfo() {
			queryOriginalStationInfo({
				id: this.data.id,
			}).then(res => {
				const { result = [] } = res || {}

				this.tableData = result
			})
		},
	},
}
</script>

<style lang="less" scoped></style>
