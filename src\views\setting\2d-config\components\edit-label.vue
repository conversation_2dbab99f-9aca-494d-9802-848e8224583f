<template>
	<Modal
		class-name="custom-modal"
		width="480"
		class="station-modal"
		:value="show"
		:transfer="false"
		:title="title"
		@on-cancel="handleCancel"
		@on-visible-change="handleVisibleChange"
	>
		<Spin fix v-if="listLoading">加载中。。。</Spin>
		<div class="water-modal-content station-modal-content scroll">
			<Form ref="formValidate" :model="formItem" :label-width="80">
				<Form-item label="名称">
					<Input v-model="message" placeholder="请输入"></Input>
				</Form-item>
				<Form-item label="单位">
					<Input v-model="unit" placeholder="请输入"></Input>
				</Form-item>
			</Form>
		</div>
		<div slot="footer">
			<!-- <Button @click="handleReset">重置</Button> -->
			<Button type="primary" @click="handleCheck">确定</Button>
		</div>
	</Modal>
</template>

<script>
import { updateMonitorDataState } from '@/api/setting'

export default {
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '编辑',
		},
	},
	mounted() {},
	data() {
		return {
			listLoading: false,
			stationList: [],
			uploadList: [],
			defaultList: [],
			message: '',
			unit: '',
			id: '',
		}
	},
	methods: {
		// 设置时间
		setData(data) {
			// debugger
			this.message = data.monitorTypeName
			this.unit = data.monitorTypeUnit
			this.id = data.id
		},

		// 弹窗显隐事件
		handleVisibleChange() {},

		handleRemove() {
			this.formItem.processImage = ''
		},
		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},
		// 重置
		handleReset() {},
		// 确定
		handleCheck() {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					updateMonitorDataState({
						monitorTypeName: this.message,
						monitorTypeUnit: this.unit,
						id: this.id,
					})
						.then(() => {
							this.$Message.success('提交成功!')
							this.$emit('update:show', false)
							this.$emit('initList')
						})
						.catch(() => {
							this.listLoading = false
						})
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
	},
}
</script>
<style lang="less">
.station-modal {
	.custom-modal {
		.ivu-modal {
			height: 48% !important;
		}
		.ivu-modal-body {
			height: calc(100% - 96px);
		}
	}
}
</style>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
.demo-upload-list {
	display: inline-block;
	width: 60px;
	height: 60px;
	text-align: center;
	line-height: 60px;
	border: 1px solid transparent;
	border-radius: 4px;
	overflow: hidden;
	background: #fff;
	position: relative;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
	margin-right: 4px;
}
.demo-upload-list img {
	width: 100%;
	height: 100%;
}
.demo-upload-list-cover {
	display: none;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.6);
}
.demo-upload-list:hover .demo-upload-list-cover {
	display: block;
}
.demo-upload-list-cover i {
	color: #fff;
	font-size: 20px;
	cursor: pointer;
	margin: 0 2px;
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	// .ivu-input-number-input {
	// 	color: #fff;
	// 	background: #133a5e;
	// 	border: none;
	// }
}
</style>
