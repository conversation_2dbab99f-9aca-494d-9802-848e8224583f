<template>
	<div class="layout">
		<div class="fixed">
			<span style="margin-left: 16px; margin-top: 16px; font-size: 18px">站点数据项信息</span>
			<span>
				<Button class="button" @click="deleteShow = true" size="small">删除</Button>
				<Button class="button" type="primary" @click="handleRelation" size="small">更换</Button>
			</span>
		</div>

		<div class="layout-content">
			<Row>
				<i-col span="5">
					<Input
						style="width: 230px; margin-right: 10px"
						clearable
						v-model="stationCode"
						placeholder="请输入站点编号"
					></Input>
					<Button type="primary" @click="queryStations">查询</Button>
					<Table
						:loading="listLoading"
						style="width: 300px"
						:show-header="false"
						:border="false"
						highlight-row
						:columns="columns"
						:data="data"
						@on-row-click="handleSelect"
					></Table>

					<Page
						size="small"
						show-total
						:transfer="true"
						:current="pageData.pageNum"
						:total="pageData.total"
						:page-size="pageData.pageSize"
						@on-change="handlePageChange"
					/>
				</i-col>
				<i-col span="19">
					<div style="margin-left: 16px; font-size: 18px; border-bottom: 1px solid #dadae2">
						{{ title }}
					</div>
					<Form :rules="formRules" ref="formValidate" :model="formItem">
						<Table border :columns="tableColumns" :data="formItem.stationTempDatas" :loading="formLoading">
							<template slot-scope="{ row, index }" slot="baseItemId">
								<FormItem
									:prop="`stationTempDatas.${index}.baseItemId`"
									:label-width="0"
									:rules="formRules.baseItemId"
								>
									<Select
										v-model="formItem.stationTempDatas[index].baseItemId"
										transfer
										style="width: 120px"
									>
										<Option v-for="item in baseDataItems" :key="item.id" :value="item.id">
											{{ item.baseItemName }}
										</Option>
									</Select>
								</FormItem>
							</template>
							<template slot-scope="{ row, index }" slot="itemRealCode">
								<FormItem
									:prop="`stationTempDatas.${index}.itemRealCode`"
									:label-width="0"
									:rules="formRules.itemRealCode"
								>
									<Input v-model="formItem.stationTempDatas[index].itemRealCode" />
								</FormItem>
							</template>
							<template slot-scope="{ row, index }" slot="itemName">
								<FormItem
									:prop="`stationTempDatas.${index}.itemName`"
									:label-width="0"
									:rules="formRules.itemName"
								>
									<Input v-model="formItem.stationTempDatas[index].itemName" />
								</FormItem>
							</template>
							<template slot-scope="{ row, index }" slot="formulaType">
								<FormItem
									:prop="`stationTempDatas.${index}.formulaType`"
									:label-width="0"
									:rules="formRules.formulaType"
								>
									<Select v-model="formItem.stationTempDatas[index].formulaType" transfer>
										<Option v-for="item in formulaTypes" :key="item.value" :value="item.value">
											{{ item.label }}
										</Option>
									</Select>
								</FormItem>
							</template>
							<template slot-scope="{ row, index }" slot="formula">
								<FormItem :prop="`stationTempDatas.${index}.formula`" :label-width="0">
									<Input v-model="formItem.stationTempDatas[index].formula" />
								</FormItem>
							</template>
							<template slot-scope="{ row, index }" slot="unit">
								<FormItem :prop="`stationTempDatas.${index}.unit`" :label-width="0">
									<Input v-model="formItem.stationTempDatas[index].unit" />
								</FormItem>
							</template>
							<template slot-scope="{ row, index }" slot="orderBy">
								<FormItem :prop="`stationTempDatas.${index}.orderBy`" :label-width="0">
									<Input v-model="formItem.stationTempDatas[index].orderBy" />
								</FormItem>
							</template>
							<template slot-scope="{ row, index }" slot="edit">
								<Button type="text" size="small" @click="delItem(index)" style="color: #3b95e9">
									删除
								</Button>
							</template>
						</Table>
						<div class="dotted" @click="addItem">+新增数据项</div>

						<div class="rightButton">
							<Button class="button" @click="handleCancel" size="small">取消</Button>
							<Button class="button" type="primary" @click="handleSubForm" size="small">保存</Button>
						</div>
					</Form>
				</i-col>
			</Row>
		</div>
		<temp-select
			:show.sync="modalShow"
			:sys-code="this.sysCode"
			:station-code="this.selectedStationCode"
			@initList="queryStations"
		></temp-select>
		<EsConfirmModal
			v-model="deleteShow"
			title="提示"
			content="是否删除当前项"
			@on-ok="handleDelete"
			@on-cancel="deleteShow = false"
		></EsConfirmModal>
	</div>
</template>
<script>
import { deleteByStationCode, getStationTemp, queryPage, updateStationData, queryStationPage } from '@/api/base-item'

import { EsConfirmModal } from '@eslink/esvcp-pc-ui'
import TempSelect from '@/views/base-item/station-template/components/TempSelect'
/*const stationTempDatas = {
	baseDataItemId: null,
	itemRealCode: '',
	itemName: '',
	formulaType: null,
	formula: '',
	orderBy: null,
	unit: '',
}*/
export default {
	components: { TempSelect, EsConfirmModal },
	props: {},
	computed: {},
	mounted() {},
	data() {
		return {
			sysCode: 'dc',
			formLoading: true,
			baseDataItems: [],
			pageData: {
				total: 0,
				current: 1,
				pageSize: 15,
			},
			formRules: {
				baseItemId: [
					{
						required: true,
						message: '请输入数据项名称',
						trigger: 'change',
						type: 'number',
					},
				],
				itemName: [
					{
						required: true,
						message: '请输入数据项名称',
						trigger: 'change',
						type: 'string',
					},
				],
				itemRealCode: [
					{
						required: true,
						message: '请输入数据项编号',
						trigger: 'change',
						type: 'string',
					},
				],
				formulaType: [
					{
						required: true,
						message: '请选择公式类型',
						trigger: 'change',
					},
				],
			},
			formItem: {
				stationTempDatas: [],
			},
			formulaTypes: [
				{
					value: '0',
					label: '原始数据',
				},
				{
					value: '1',
					label: '单位换算',
				},
				{
					value: '2',
					label: '公式计算',
				},
			],
			title: '站点关联数据项',
			modalShow: false,
			deleteShow: false,
			selectedStationCode: '',
			listLoading: false,
			stationCode: '',
			columns: [{ key: 'stationCode' }],
			data: [],
			itemData: [],
			tableColumns: [
				{
					title: '基础数据项',
					slot: 'baseItemId',
					align: 'center',
				},
				{
					title: '数据项编号',
					slot: 'itemRealCode',
					align: 'center',
					width: 300,
				},
				{
					title: '数据项名称',
					slot: 'itemName',
					align: 'center',
				},
				{
					title: '公式类型',
					slot: 'formulaType',
					align: 'center',

					type: 'text',
				},
				{
					title: '公式',
					slot: 'formula',
					align: 'center',
					width: 300,
				},
				{
					title: '单位',
					slot: 'unit',
					align: 'center',
					width: 80,
				},
				{
					title: '序号',
					slot: 'orderBy',
					align: 'center',
					width: 80,
				},
				{
					title: '操作',
					slot: 'edit',
					align: 'center',
					key: 'edit',
					width: 80,
				},
			],
		}
	},
	created() {
		this.queryStations()
	},
	watch: {},
	methods: {
		handleCancel() {
			this.getDetail(this.selectedStationCode)
		},
		delItem(index) {
			this.formItem.stationTempDatas.splice(index, 1)
		},
		getBaseDataItems() {
			queryPage({ needPage: false, type: this.sysCode }).then(res => {
				this.baseDataItems = res.result.list
			})
		},
		addItem() {
			this.formItem.stationTempDatas.push({
				baseDataItemId: null,
				itemRealCode: '',
				itemName: '',
				formulaType: null,
				formula: '',
				orderBy: null,
				unit: '',
			})
		},

		handleSubForm() {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					this.formItem.stationCode = this.selectedStationCode
					this.formItem.type = this.sysCode
					updateStationData(this.formItem)
						.then(() => {
							this.$Message.success('提交成功!')
							this.queryStations()
						})
						.catch(() => {
							this.listLoading = false
						})
				}
			})
		},

		handlePageChange(pageNum) {
			this.pageData.current = pageNum
			this.queryStations()
		},
		handleDelete() {
			let params = { stationCode: this.selectedStationCode }
			deleteByStationCode(params).then(() => {
				this.$Message.success('操作成功')
				this.deleteShow = false
				this.handleSearch()
			})
		},
		queryStations() {
			let params = {
				stationCode: this.stationCode,
				needPage: true,
				pageSize: this.pageData.pageSize,
				pageNum: this.pageData.current,
			}
			queryStationPage(params).then(res => {
				this.data = res.result.list
				this.pageData.total = res.result.total
				this.data[0]._highlight = true

				if (this.data[0].type == this.sysCode) {
					this.selectedStationCode = this.data[0].stationCode
					this.getDetail(this.selectedStationCode)
				} else {
					this.sysCode = this.data[0].type
					this.selectedStationCode = this.data[0].stationCode
					this.onload()
				}
			})
		},

		handleRelation() {
			this.modalShow = true
		},

		handleSelect(selection) {
			this.selectedStationCode = selection.stationCode
			this.sysCode = selection.type
			if (this.sysCode == selection.type) {
				this.selectedStationCode = selection.stationCode
				this.getDetail(this.selectedStationCode)
			} else {
				this.selectedStationCode = selection.stationCode
				this.sysCode = selection.type
				this.onload()
			}
		},
		getDetail(val) {
			this.formLoading = true
			let params = { stationCode: val }
			getStationTemp(params).then(res => {
				this.formItem.stationTempDatas = res.result
			})
			this.formLoading = false
		},
		onload() {
			this.getBaseDataItems()
			this.$nextTick(() => {
				this.getDetail(this.selectedStationCode)
			})
		},
	},
}
</script>
<style lang="less" scoped>
.layout-content {
	margin: 15px;
	overflow: hidden;

	border-radius: 4px;
}

.dotted {
	display: flex;
	justify-content: center;
	align-items: center;
	border-style: dotted;
	background-color: azure;
	border-width: 1px;
	margin-top: 10px;
}
.fixed {
	display: flex;
	justify-content: space-between;
}
.button {
	margin-left: 16px;
	margin-top: 16px;
}
.rightButton {
	display: flex;
	justify-content: flex-end;
}
</style>
