user  root;
worker_processes  4;
daemon off;

#error_log  /var/logs/error.log  error;

events {
    worker_connections  1024;
}

http {
    gzip on;
    client_max_body_size 50m;
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    log_format  main  '$year-$month-$day $hour:$minute:$second|$request|$status|$request_time|$body_bytes_sent|$remote_addr|$upstream_addr|$upstream_status|$upstream_response_time';
    access_log  /var/logs/nginx/$year-$month-$day.access.log  main ;

    upstream tomcatwater-plat-server {
        server water-plat-server:8090;
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";
        check_http_expect_alive http_2xx http_3xx http_4xx;
        }

    upstream tomcatsecondsupply-server {
        server secondsupply-server:8090;
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";
        check_http_expect_alive http_2xx http_3xx http_4xx;
        }

    upstream tomcatvillage-water {
        server village-water:8090;
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";
        check_http_expect_alive http_2xx http_3xx http_4xx;
        }

    upstream tomcatesmart-server {
        server esmart-server:8090;
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";
        check_http_expect_alive http_2xx http_3xx http_4xx;
        }

    upstream tomcatyq-iwater-server {
        server yq-iwater-server:8090;
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";
        check_http_expect_alive http_2xx http_3xx http_4xx;
        }

    upstream tomcatyq-water-digital-server {
        server yq-water-digital-server:8090;
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";
        check_http_expect_alive http_2xx http_3xx http_4xx;
        }

    upstream tomcatyq-workorder-server {
        server yq-workorder-server:8090;
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";
        check_http_expect_alive http_2xx http_3xx http_4xx;
        }

    upstream tomcatyq-workplat-server {
        server yq-workplat-server:8090;
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";
        check_http_expect_alive http_2xx http_3xx http_4xx;
        }

    upstream tomcatswetbc-server {
        server swetbc-server:8090;
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";
        check_http_expect_alive http_2xx http_3xx http_4xx;
        }

        upstream tomcatwz-iwater-server {                            
        server wz-iwater-server:8090;                            
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";     
        check_http_expect_alive http_2xx http_3xx http_4xx;      
                }

        upstream tomcatdma-server {                                   
        server water-dma-server:8090;                        
        check interval=5000 rise=2 fall=3 timeout=3000 type=http; 
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";     
        check_http_expect_alive http_2xx http_3xx http_4xx;       
    } 

        upstream tomcatsw-patrol-server {                                   
        server sw-patrol-server:8090;                        
        check interval=5000 rise=2 fall=3 timeout=3000 type=http; 
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";     
        check_http_expect_alive http_2xx http_3xx http_4xx;       
    } 
    
        upstream tomcatcloud-eslink-iwater {                            
        server cloud-eslink-iwater:8090;                            
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";     
        check_http_expect_alive http_2xx http_3xx http_4xx;      
    }

        upstream tomcatgis-server {                            
        server gis-server:8090;                            
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";     
        check_http_expect_alive http_2xx http_3xx http_4xx;      
    } 
    
        upstream tomcatwz-water-digital-server {                     
        server wz-water-digital-server:8090;                     
        check interval=5000 rise=2 fall=3 timeout=3000 type=http;
        check_http_send "HEAD /check.html HTTP/1.0\r\n\r\n";     
        check_http_expect_alive http_2xx http_3xx http_4xx;      
    } 
    #upstreams

    server {
        listen 80;
        add_header Cache-Control no-cache;
        location ~* \.([a-zA-Z0-9]*)$ {
            root /home;
        }

        location = / {
            root /home;
            index index.html;
        }
        location / {
            try_files $uri $uri/ /index.html;
        }

        location ^~/group1 { 
        proxy_pass http://***************:8088; 
        proxy_set_header Host $host; 
        add_header X-Frame-Options SAMEORIGIN; 
        }

        location ^~/api {
        proxy_pass http://tomcatwater-plat-server/;
        proxy_set_header Host $host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        add_header X-Frame-Options SAMEORIGIN;
        if ($request_method = "OPTIONS") {
add_header Access-Control-Allow-Headers sysuserid;
                add_header Access-Control-Allow-Origin $http_origin;
                add_header Access-Control-Allow-Headers Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,X-Data-Type,X-Requested-With;
                add_header Access-Control-Allow-Methods GET,POST,OPTIONS,HEAD,PUT;
                add_header Access-Control-Allow-Credentials true;
                add_header Access-Control-Allow-Headers X-Data-Type,X-Auth-Token;
                return 200;
        }
        add_header 'Access-Control-Allow-Origin' "$http_origin";  
        add_header 'Access-Control-Allow-Credentials' 'true';  
        add_header 'Access-Control-Allow-Methods' 'GET,POST';
        }

        location ^~/proxy/eg {
        proxy_pass http://tomcatsecondsupply-server/;
        proxy_set_header Host $host;
        add_header X-Frame-Options SAMEORIGIN;
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){
                set $cors_origin $http_origin;
        }                                                        
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' 'true';   
        add_header 'Access-Control-Allow-Methods' 'GET,POST';
        }

        location ^~/proxy/dc {
        proxy_pass http://tomcatvillage-water/;
        proxy_set_header Host $host;
        add_header X-Frame-Options SAMEORIGIN;
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){
                set $cors_origin $http_origin;
        }                                                        
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' 'true';   
        add_header 'Access-Control-Allow-Methods' 'GET,POST';
        }

        location ^~/proxy/cj {
        proxy_pass http://tomcatesmart-server/;
        proxy_set_header Host $host;
        add_header X-Frame-Options SAMEORIGIN;
        add_header 'Access-Control-Allow-Origin' "*.eslink.net.cn";
        add_header 'Access-Control-Allow-Credentials' 'true';  
        }

        location ^~/proxy/dd {
        proxy_pass http://tomcatyq-iwater-server/;
        proxy_set_header Host $host;
        add_header X-Frame-Options SAMEORIGIN;
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){
                set $cors_origin $http_origin;
        }                                                        
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' 'true';   
        add_header 'Access-Control-Allow-Methods' 'GET,POST';
        }

        location ^~/proxy/dp {
        proxy_pass http://tomcatyq-water-digital-server/;
        proxy_set_header Host $host;
        add_header X-Frame-Options SAMEORIGIN;
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){
                set $cors_origin $http_origin;
        }                                                        
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' 'true';   
        add_header 'Access-Control-Allow-Methods' 'GET,POST';
        }

        location ^~/proxy/gd {
        proxy_pass http://tomcatyq-workorder-server/;
        proxy_set_header Host $host;
        add_header X-Frame-Options SAMEORIGIN;
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){
                set $cors_origin $http_origin;
        }
        more_clear_headers 'Access-Control-Allow-Origin';                   
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' 'true';   
        add_header 'Access-Control-Allow-Methods' 'GET,POST';
        }

        location ^~/proxy/gdapp {
        proxy_pass http://tomcatyq-workplat-server/;
        proxy_set_header Host $host;
        add_header X-Frame-Options SAMEORIGIN;
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){
                set $cors_origin $http_origin;
        }                                                        
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' 'true';   
        add_header 'Access-Control-Allow-Methods' 'GET,POST';
        }

        location ^~/proxy/swetbc/ {
        proxy_pass http://tomcatswetbc-server/;
        proxy_set_header Host $host;
        add_header X-Frame-Options SAMEORIGIN;
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){
                set $cors_origin $http_origin;
        }                                                        
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' 'true';   
        add_header 'Access-Control-Allow-Methods' 'GET,POST';
        }

                location ^~/proxy/wzdd {     
        proxy_pass http://tomcatwz-iwater-server/;
        proxy_set_header Host $host; 
        add_header X-Frame-Options SAMEORIGIN;
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){
                                set $cors_origin $http_origin;  
                }     
if ($request_method = "OPTIONS") {
                add_header Access-Control-Allow-Headers sysuserid;
                add_header Access-Control-Allow-Origin $http_origin;
                add_header Access-Control-Allow-Headers Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,X-Data-Type,X-Requested-With;
                add_header Access-Control-Allow-Methods GET,POST,OPTIONS,HEAD,PUT;
                add_header Access-Control-Allow-Credentials true;
                add_header Access-Control-Allow-Headers X-Data-Type,X-Auth-Token;
                return 200;
}
        add_header 'Access-Control-Allow-Origin' "$http_origin";                 
        add_header 'Access-Control-Allow-Credentials' 'true';   
        add_header 'Access-Control-Allow-Methods' 'GET,POST';   
                }  

                location ^~/proxy/dma/ {                                 
        proxy_pass http://tomcatdma-server/;                     
        proxy_set_header Host $host;                         
        add_header X-Frame-Options SAMEORIGIN;                    
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){
            set $cors_origin $http_origin;    
        }                                                  
        add_header 'Access-Control-Allow-Origin' "$http_origin";  
        add_header 'Access-Control-Allow-Credentials' 'true';     
        add_header 'Access-Control-Allow-Methods' 'GET,POST';
        } 

                location ^~/proxy/xj {                                     
        proxy_pass http://tomcatsw-patrol-server/;             
        proxy_set_header Host $host;                              
        add_header X-Frame-Options SAMEORIGIN;                     
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){ 
                set $cors_origin $http_origin;                     
        }                                                          
        add_header 'Access-Control-Allow-Origin' "$http_origin";  
        add_header 'Access-Control-Allow-Credentials' 'true';      
        add_header 'Access-Control-Allow-Methods' 'GET,POST';      
        }

                location ^~/proxy/ybdd {                                     
        proxy_pass http://tomcatcloud-eslink-iwater/;             
        proxy_set_header Host $host;                              
        add_header X-Frame-Options SAMEORIGIN;                     
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){ 
                set $cors_origin $http_origin;                     
        }
if ($request_method = "OPTIONS") {
                add_header Access-Control-Allow-Headers sysuserid;
                add_header Access-Control-Allow-Origin $http_origin;
                add_header Access-Control-Allow-Headers Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,X-Data-Type,X-Requested-With;
                add_header Access-Control-Allow-Methods GET,POST,OPTIONS,HEAD,PUT;
                add_header Access-Control-Allow-Credentials true;
                add_header Access-Control-Allow-Headers X-Data-Type,X-Auth-Token;
                return 200;
}
        proxy_hide_header Access-Control-Allow-Origin;
        proxy_hide_header Access-Control-Allow-Methods;
        proxy_hide_header Access-Control-Allow-Credentials;                                                          
        add_header 'Access-Control-Allow-Origin' "$http_origin";  
        add_header 'Access-Control-Allow-Credentials' 'true';      
        add_header 'Access-Control-Allow-Methods' 'GET,POST';      
        }

                 location ^~/proxy/gis {                                     
        proxy_pass http://tomcatgis-server/;             
        proxy_set_header Host $host;                              
        add_header X-Frame-Options SAMEORIGIN;                     
        if ($http_origin ~* ^(http(s)?:\/\/.*\.eslink\.net\.cn$)){ 
                        set $cors_origin $http_origin;                     
        }                                                          
        proxy_hide_header Access-Control-Allow-Origin;
        proxy_hide_header Access-Control-Allow-Methods;
        proxy_hide_header Access-Control-Allow-Credentials;
        add_header 'Access-Control-Allow-Origin' "$http_origin";  
        add_header 'Access-Control-Allow-Credentials' 'true';      
        add_header 'Access-Control-Allow-Methods' 'GET,POST';      
        }
        
        location ^~/proxy/wzdp {
        proxy_pass http://tomcatwz-water-digital-server/;
        proxy_set_header Host $host;
        add_header X-Frame-Options SAMEORIGIN;
        if ($request_method = "OPTIONS") {                       
                add_header Access-Control-Allow-Origin $http_origin;
                add_header Access-Control-Allow-Headers Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,X-Data-Type,X-Requested-With;
                add_header Access-Control-Allow-Methods GET,POST,OPTIONS,HEAD,PUT;
                add_header Access-Control-Allow-Credentials true;
                add_header Access-Control-Allow-Headers X-Data-Type,X-Auth-Token;
                return 200;                                 
        }                                                
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' 'true';   
        add_header 'Access-Control-Allow-Methods' 'GET,POST';
        }
        #route

        location /favicon.ico {
            root html;
        }

        location /status/upstream {
            check_status;
            access_log   off;
        }

        location /status/base {
            stub_status on;
            access_log   off;
        }
    }
}