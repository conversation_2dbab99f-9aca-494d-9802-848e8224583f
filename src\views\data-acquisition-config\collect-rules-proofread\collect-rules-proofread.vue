<!--
 * @Description: 采集规则配置
 * @Author: shenxh
 * @Date: 2023-04-03 09:22:40
 * @LastEditors: shenxh
 * @LastEditTime: 2023-04-17 10:42:59
-->

<template lang="pug">
.collect-rules-config
	.tab-wrap
		Tabs(:value='tabData.transChannelCode', @on-click='changeTab')
			TabPane(v-for='(item, index) in transChannelCodeList', :key='index', :label='item.label', :name='item.value')
		#qrcodeDowm(style='display: none')
	.collect-rules-config-wrap
		equipment-list.equipment-list(:collect-road-data='tabData', @handle-item='handleEqu')
		#conent-wrapper.collect-rules-config-con
			water-row.table-btn-wrap(justify='space-between', align='center')
				//- i
				Button(type='primary', @click='handleCustom') 自定义列
				.right
					Button.refresh(type='primary', @click='handleRefresh') 数据刷新
					Button(type='primary', @click='handleExport') 报表导出
			#table-wrapper.table-wrap
				Table(
					:key='tableKey',
					:columns='columns',
					:data='tableData',
					:loading='loading',
					:width='tableWidth',
					:height='tableHeight',
					ref='es-table',
					border,
					:showPage='false'
				)
					//- template(slot-scope="{ row }", slot="itemValue0")
					//- 	span 11
					template(slot-scope='{ row }', :slot='`itemValue${index}`', v-for='(item, index) in resultKey')
						span.clickAbled(@click='handleCurve(row, `readWriteFlag${index}`, index)') {{ row[`itemValue${index}`] }}
					template(slot-scope='{ row }', :slot='`readWriteFlag${index}`', v-for='(item, index) in resultKey')
						span {{ getText(row, `readWriteFlag${index}`) }}

	custom-column(:show.sync='showModal', @select-columns='handleSelectColumns')
	custom-curve(
		:show.sync='showCurveModal',
		:currentRow='currentRow',
		:currentIndex='currentIndex',
		@select-columns='handleSelectColumns'
	)
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import EquipmentList from './components/equipment-list.vue'
import CustomColumn from './components/custom-column.vue'
import CustomCurve from './components/custom-curve.vue'
// import CreateSystemPopup from './components/create-system-popup.vue'
import { queryTransChannelNoEnableRule, queryRules } from '@/api/data-acquisition-proofread'
import { exportFile } from '@/utils/function.js'

export default {
	name: 'collect-rules-config',
	components: {
		WaterRow,
		EquipmentList,
		CustomColumn,
		CustomCurve,
	},
	props: {},
	data() {
		return {
			tableKey: 0,
			popupType: 0,
			showCurveModal: false,
			showModal: false,
			loading: false,
			tabData: {},
			currentRow: {},
			currentIndex: 0,
			currentEqu: {},
			transChannelCodeList: [],
			tableData: [],
			columnKeys: [
				{ name: '平台变量', nameKey: 'itemCode' },
				{ name: '平台描述', nameKey: 'itemName' },
				{ name: '原始位号', nameKey: 'originalCode' },
				{ name: '原始描述', nameKey: 'originalName' },
				{ name: '数据类型描述', nameKey: 'baseItemName' },
				{ name: '数值', slot: 'itemValue' },
				{ name: '单位', nameKey: 'unit' },
				{ name: '读写状态', slot: 'readWriteFlag' },
				{ name: '最后上传时间', nameKey: 'updateTime' },
			],
			columns: [
				{
					title: '序号',
					align: 'center',
					children: [
						// {
						// 	title: '设备/对象名称',
						// 	key: 'stationName',
						// },
						// {
						// 	title: '平台唯一编码',
						// 	key: 'stationCode',
						// },
					],
				},
			],
			tableWidth: 800,
			tableHeight: 0,
			resultKey: [],
		}
	},
	watch: {},
	created() {
		this.queryTransChannelNoEnableRule()
		this.$nextTick(() => {
			setTimeout(() => {
				this.tableWidth = document.getElementById('conent-wrapper').clientWidth
				this.tableHeight = document.getElementById('table-wrapper').offsetHeight
			}, 100)
			// document.getElementById('es-table-wrapper').style.width = this.tableWidth + 'px'
		})
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		handleSelectColumns(columns) {
			this.columnKeys = columns
			this.columns.forEach((column, index) => {
				column.children = []
				columns.forEach(col => {
					let newCol = {
						title: col.name,
						minWidth: 90,
					}
					if (col.nameKey) {
						newCol.key = col.nameKey + index
					}
					if (col.slot) {
						newCol.slot = col.slot + index
					}

					column.children.push(newCol)
				})
			})
		},
		// 搜索按钮
		handleExport() {
			console.log(this.columnKeys)
			const codes = this.columnKeys.map(item => {
				return item.nameKey || item.slot
			})
			console.log('导出按钮', this.currentEqu.join(','))
			const baseUrl = `${location.origin}/api`
			const url =
				baseUrl +
				'/waterPlat/transDataCheck/exportRules' +
				'?stationCodes=' +
				this.currentEqu.join(',') +
				'&items=' +
				codes.join(',')

			exportFile(url)
		},

		// 刷新按钮
		handleRefresh() {
			this.getTableData()
		},

		// 添加按钮
		handleCustom() {
			this.showModal = true
		},

		// 曲线
		handleCurve(row, key, index) {
			this.showCurveModal = true
			this.currentRow = row
			this.currentIndex = index
			console.log(11111, row, key, index, row[`stationCode${index}`], row[`baseItemCode${index}`])
		},

		// 行-删除
		handleRowDel(row) {
			this.deleteTransRule(row)
		},

		getText(row, key) {
			if (row[key] === 0) {
				return '只读'
			} else if (row[key] === 1) {
				return '读写'
			} else {
				return ''
			}
		},

		// 改变tab
		changeTab(val) {
			this.currentEqu = {}
			this.tabData = this.getTabData(val)
			this.tableKey++

			this.getTableData()
		},

		getTabData(code) {
			return this.transChannelCodeList.find(item => item.transChannelCode === code)
		},

		// 点击设备
		handleEqu(itm, datas) {
			if (itm.length) {
				this.currentEqu = itm
				this.getTableData(datas)
			}
		},

		// 获取表格数据
		getTableData() {
			this.loading = true
			const params = {
				stationCodes: this.currentEqu,
			}

			// if (queryMethod && stationWord) {
			// 	params[queryMethod] = stationWord
			// }
			queryRules(params).then(res => {
				const { result = {} } = res || {}
				this.loading = false
				this.tableData = []
				result.forEach((item, index) => {
					const { transRules = [], stationCode, stationName } = item || {}
					this.resultKey.push({
						stationCode,
						stationName,
					})
					transRules.forEach((rule, rindex) => {
						const {
							baseItemCode,
							baseItemName,
							itemCode,
							itemName,
							itemValue,
							readWriteFlag,
							stationCode,
							unit,
							updateTime,
							originalCode,
							originalName,
						} = rule || {}
						if (!this.tableData[rindex]) {
							this.tableData[rindex] = {}
						}
						this.tableData[rindex]['baseItemCode' + index] = baseItemCode
						this.tableData[rindex]['baseItemName' + index] = baseItemName
						this.tableData[rindex]['itemCode' + index] = itemCode
						this.tableData[rindex]['itemName' + index] = itemName
						this.tableData[rindex]['itemValue' + index] = itemValue
						this.tableData[rindex]['readWriteFlag' + index] = readWriteFlag
						this.tableData[rindex]['stationCode' + index] = stationCode
						this.tableData[rindex]['unit' + index] = unit
						this.tableData[rindex]['updateTime' + index] = updateTime
						this.tableData[rindex]['originalCode' + index] = originalCode
						this.tableData[rindex]['originalName' + index] = originalName

						this.tableData[rindex]['stationName' + index] = stationName
					})
				})
				// 处理列
				// this.tableData = transRules
				this.columns = [
					{
						title: '序号',
						align: 'center',
						children: [],
					},
				]
				result.forEach((item, index) => {
					if (!this.columns[index]) {
						this.columns[index] = {
							align: 'center',
						}
					}
					this.columns[index].title = item.stationName
					this.columns[index].children = []
					this.columnKeys.forEach(column => {
						let newCol = {
							title: column.name,
							minWidth: 90,
						}
						if (column.nameKey) {
							newCol.key = column.nameKey + index
						}
						if (column.slot) {
							newCol.slot = column.slot + index
						}
						this.columns[index].children.push(newCol)
					})
				})
				// this.columns.push({
				// 	key: 'name',
				// 	fixed: 'right',
				// 	width: 10,
				// })
				console.log('this.columns', this.columns)
			})
		},

		// 查询支持配置的所有采集渠道
		queryTransChannelNoEnableRule() {
			queryTransChannelNoEnableRule().then(res => {
				const { result = [] } = res || {}

				this.transChannelCodeList = result.map(item => {
					return {
						...item,
						label: item.name,
						value: item.channelCode,
						transChannelCode: item.channelCode,
					}
				})
				if (this.transChannelCodeList.length) {
					// this.transChannelCode = this.transChannelCodeList[0].value
					this.tabData = this.transChannelCodeList[0]

					// this.$nextTick(() => {
					// 	this.getTableData()
					// })
				}
			})
		},

		// 删除采集渠道
		deleteTransRule() {
			// deleteTransRule(params).then(res => {
			// 	const { responseCode } = res || {}
			// 	if (responseCode === '100000') {
			// 		this.$Message.success('操作成功')
			// 		this.getTableData()
			// 	}
			// })
		},
	},
}
</script>

<style lang="less" scoped>
.collect-rules-config {
	padding: 0 16px;
	height: 100%;
	.tab-wrap {
		height: 44px;
	}

	.collect-rules-config-wrap {
		display: flex;
		width: 100%;
		height: calc(100vh - 45px);
		.equipment-list {
			flex-shrink: 0;
			margin-right: 10px;
			height: calc(100% - 10px);
		}
		.collect-rules-config-con {
			flex: 1;
			height: 100%;
			.es-search {
				height: 55px;
				padding: 8px 0;
				margin-bottom: 8px;
			}
			.table-btn-wrap {
				margin-bottom: 8px;
			}
			.table-wrap {
				position: relative;
				width: 100%;
				height: calc(100% - 105px);
			}
		}
	}
	.clickAbled {
		color: #3b96ff;
		text-decoration: underline;
		cursor: pointer;
	}
	.refresh {
		margin-right: 8px;
	}
}
</style>
