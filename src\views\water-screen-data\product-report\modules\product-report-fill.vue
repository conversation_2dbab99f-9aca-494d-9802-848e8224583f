<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-31 10:41:47
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-06 09:08:34
-->
<template lang="pug">
.product-report-fill
	.product-report-fill-tab
		RadioGroup(v-model='type', type='button')
			Radio(v-if='!isGsOwnership', label='1') 水源地指标
			Radio(v-if='!isGsOwnership', label='2') 原水/产水年度指标
			Radio(label='3') 水厂耗电量统计
			Radio(v-if='!isGsOwnership', label='4') 水厂药品用量/制水单耗统计
			Radio(v-if='isGsOwnership', label='5') 水厂药耗库存
	WaterSurceFill(v-if='type === "1"', state='fill')
	RawWaterFill(v-if='type === "2"', state='fill')
	ElectricityFill(v-if='type === "3"', state='fill')
	DrugFill(v-if='type === "4"', state='fill')
	DrugUseupFill(v-if='type === "5"', state='fill')
</template>
<script>
import DrugFill from './DrugFill.vue'
import RawWaterFill from './RawWaterFill.vue'
import ElectricityFill from './ElectricityFill.vue'
import WaterSurceFill from './WaterSourceFill.vue'
import DrugUseupFill from './DrugUseupFill.vue'
export default {
	components: {
		DrugFill,
		RawWaterFill,
		ElectricityFill,
		WaterSurceFill,
		DrugUseupFill,
	},
	props: {
		ownership: String,
	},
	computed: {
		// 是否为光山的租户：2w05、1292
		isGsOwnership() {
			return this.ownership === '2W05' || this.ownership.indexOf('1292') !== -1
		},
	},
	created() {
		this.type = this.isGsOwnership ? '3' : '1'
	},
	data() {
		return {
			type: '1',
		}
	},
}
</script>
<style scoped lang="less">
.product-report-fill {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-tab {
		position: absolute;
		top: 0;
		left: 0;
	}
	::v-deep {
		.ivu-radio-wrapper-checked {
			background: #1192e8;
			color: #ffffff;
		}
	}
}
</style>
