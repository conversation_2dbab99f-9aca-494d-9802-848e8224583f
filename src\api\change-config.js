/*
 * @Description:
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-07-19 10:29:28
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-07-19 13:48:21
 */
import { GET, POST } from '@/utils/request'

// 查询【系统列表】
export function queryPageList(params) {
	return POST({
		url: '/stationItemDataFix/page',
		params,
		requestType: 'json',
	})
}
export function queryItemCodes(params) {
	return POST({
		url: '/influxDb/queryItemCodes',
		params,
		requestType: 'json',
	})
}
export function deleteRecord(id) {
	return GET({
		url: `/stationItemDataFix/delete/${id}`,
	})
}
export function addRecord(params) {
	return POST({
		url: '/stationItemDataFix/add',
		params,
		requestType: 'json',
	})
}
export function updateRecord(params) {
	return POST({
		url: '/stationItemDataFix/update',
		params,
		requestType: 'json',
	})
}
export function queryStationCode(code) {
	return GET({
		url: `/stationItemDataFix/stationCode/${code}`,
	})
}
