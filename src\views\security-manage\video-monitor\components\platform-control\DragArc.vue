<template lang="pug">
.drag-arc
	#content
		//- 圆盘操作
		.center-ctrl(:class='{ disabled }')
			//- 中间装饰圆
			.circle
			//- 上下左右
			Tooltip.icon-box.top(content='向上', theme='light', placement='top', offset='0, 5')
				Icon.icon(
					custom='iconfont icon-fangxianga',
					@mousedown.native='handleDirMouseDown("UP")',
					@mouseup.native='handleDirMouseUp("UP")'
				)
			Tooltip.icon-box.bottom(content='向下', theme='light', placement='top', offset='0, 5')
				Icon.icon(
					custom='iconfont icon-fangxianga',
					@mousedown.native='handleDirMouseDown("DOWN")',
					@mouseup.native='handleDirMouseUp("DOWN")'
				)
			Tooltip.icon-box.left(content='向左', theme='light', placement='top', offset='0, 5')
				Icon.icon(
					custom='iconfont icon-fangxianga',
					@mousedown.native='handleDirMouseDown("LEFT")',
					@mouseup.native='handleDirMouseUp("LEFT")'
				)
			Tooltip.icon-box.right(content='向右', theme='light', placement='top', offset='0, 5')
				Icon.icon(
					custom='iconfont icon-fangxianga',
					@mousedown.native='handleDirMouseDown("RIGHT")',
					@mouseup.native='handleDirMouseUp("RIGHT")'
				)
			//- 45°角
			Tooltip.icon-box.top-right(content='右上', theme='light', placement='top', offset='0, 5')
				Icon.icon.slope(
					custom='iconfont icon-fangxiangb',
					@mousedown.native='handleDirMouseDown("RIGHT_UP")',
					@mouseup.native='handleDirMouseUp("RIGHT_UP")'
				)
			Tooltip.icon-box.top-left(content='左上', theme='light', placement='top', offset='0, 5')
				Icon.icon.slope(
					custom='iconfont icon-fangxiangb',
					@mousedown.native='handleDirMouseDown("LEFT_UP")',
					@mouseup.native='handleDirMouseUp("LEFT_UP")'
				)
			Tooltip.icon-box.bottom-right(content='右下', theme='light', placement='top', offset='0, 5')
				Icon.icon.slope(
					custom='iconfont icon-fangxiangb',
					@mousedown.native='handleDirMouseDown("RIGHT_DOWN")',
					@mouseup.native='handleDirMouseUp("RIGHT_DOWN")'
				)
			Tooltip.icon-box.bottom-left(content='左下', theme='light', placement='top', offset='0, 5')
				Icon.icon.slope(
					custom='iconfont icon-fangxiangb',
					@mousedown.native='handleDirMouseDown("LEFT_DOWN")',
					@mouseup.native='handleDirMouseUp("LEFT_DOWN")'
				)
	//- 按钮组操作
	.btn-group 
		ButtonGroup
			Button(
				:disabled='disabled',
				@mousedown.native='handleMouseDown("ZOOM_OUT")',
				@mouseup.native='handleMouseUp("ZOOM_OUT")'
			)
				Tooltip(content='焦点后移', theme='light', placement='top-start')
					Icon(custom='iconfont icon-suoxiao')
			Button(
				:disabled='disabled',
				@mousedown.native='handleMouseDown("ZOOM_IN")',
				@mouseup.native='handleMouseUp("ZOOM_IN")'
			)
				Tooltip(content='焦点前移', theme='light', placement='top')
					Icon(custom='iconfont icon-fangda')
		ButtonGroup
			Button(
				:disabled='disabled',
				@mousedown.native='handleMouseDown("FOCUS_FAR")',
				@mouseup.native='handleMouseUp("FOCUS_FAR")'
			)
				Tooltip(content='焦距变小', theme='light', placement='top')
					Icon(custom='iconfont icon-jiaojujian')
			Button(
				:disabled='disabled',
				@mousedown.native='handleMouseDown("FOCUS_NEAR")',
				@mouseup.native='handleMouseUp("FOCUS_NEAR")'
			)
				Tooltip(content='焦距变大', theme='light', placement='top')
					Icon(custom='iconfont icon-jiaojujia')
		ButtonGroup
			Button(
				:disabled='disabled',
				@mousedown.native='handleMouseDown("IRIS_REDUCE")',
				@mouseup.native='handleMouseUp("IRIS_REDUCE")'
			)
				Tooltip(content='光圈缩小', theme='light', placement='top')
					Icon(custom='iconfont icon-guangquanda')
			Button(
				:disabled='disabled',
				@mousedown.native='handleMouseDown("IRIS_ENLARGE")',
				@mouseup.native='handleMouseUp("IRIS_ENLARGE")'
			)
				Tooltip(content='光圈扩大', theme='light', placement='top-end')
					Icon(custom='iconfont icon-guangquanxiao')
</template>

<script>
import DragArc from 'drag-arc'

export default {
	model: {
		prop: 'value', // 默认是value
		event: 'change', // 默认是input
	},
	props: {
		value: {
			type: [Number, String],
			default: 0,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {}
	},
	created() {},
	mounted() {
		const dom = document.getElementById('content')
		new DragArc({
			el: dom,
			value: this.value,
			textShow: false,
			startDeg: 0.75,
			endDeg: 2.25,
			color: ['#EA6D46', '#f8d4c8'],
			outColor: 'transparent',
			outLineWidth: 10,
			innerColor: 'transparent',
			// 逆时针方向
			counterclockwise: false,
			// 内侧弧线宽
			// innerLineWidth: 1,
			sliderColor: '#fff',
			sliderBorderColor: 'transparent',
			change: v => {
				this.$emit('change', v)
			},
		})
	},
	methods: {
		// 镜头控制
		handleMouseDown(command) {
			this.$emit('action', { action: 0, command })
		},
		handleMouseUp(command) {
			this.$emit('action', { action: 1, command })
		},
		// 方向控制
		handleDirMouseDown(command) {
			this.$emit('direction', { action: 0, command })
		},
		handleDirMouseUp(command) {
			this.$emit('direction', { action: 1, command })
		},
	},
}
</script>

<style lang="less" scoped>
#content {
	position: relative;
	width: 100%;
	height: 168px;
	overflow: hidden;
	background: url(../../../../../assets/images/circle-bg.svg) no-repeat center;
	.center-ctrl {
		position: absolute;
		width: 100px;
		height: 100px;
		border-radius: 50%;
		background-color: #ebecf4;
		border: 1px solid #babbca;
		left: 50%;
		margin-left: -50px;
		top: 50px;
		.icon-box {
			position: absolute;
			cursor: pointer;
			.icon {
				display: inline-block;
				font-size: 20px;
				color: #5f627d;
				&.slope {
					color: #9a9cb0;
				}
				&:hover {
					color: #1076b1;
				}
			}
		}
		.top {
			top: 0;
			left: 50%;
			margin-left: -10px;
		}
		.bottom {
			bottom: 0;
			left: 50%;
			margin-left: -10px;
			.icon {
				transform: rotate(180deg);
			}
		}
		.left {
			left: 0;
			top: 50%;
			margin-top: -10px;
			.icon {
				transform: rotate(-90deg);
			}
		}
		.right {
			right: 0;
			top: 50%;
			margin-top: -10px;
			.icon {
				transform: rotate(90deg);
			}
		}
		.top-right {
			right: 10px;
			top: 12px;
		}
		.top-left {
			left: 10px;
			top: 12px;
			.icon {
				transform: rotate(-90deg);
			}
		}
		.bottom-right {
			right: 10px;
			bottom: 12px;
			.icon {
				transform: rotate(90deg);
			}
		}
		.bottom-left {
			left: 10px;
			bottom: 12px;
			.icon {
				transform: rotate(-180deg);
			}
		}
		&.disabled {
			.icon-box {
				pointer-events: none;
				cursor: not-allowed;
				.icon {
					color: #9a9cb0;
				}
			}
		}
	}
	.circle {
		position: absolute;
		left: 50%;
		top: 50%;
		margin-left: -16px;
		margin-top: -16px;
		width: 32px;
		height: 32px;
		border-radius: 50%;
		background: #fff;
		box-shadow: 0px 4px 4px #ced0e3;
	}
}
.btn-group {
	display: flex;
	justify-content: space-around;
	align-content: center;
	.ivu-btn[disabled],
	.ivu-btn[disabled]:hover {
		background-color: transparent;
	}
	.ivu-btn[disabled] > * {
		color: #babbca;
	}
	.ivu-btn {
		width: 24px;
		height: 24px;
		padding: 0;
	}
	.iconfont {
		font-size: 20px;
	}
}
</style>
