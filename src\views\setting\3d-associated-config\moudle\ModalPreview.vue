<template>
	<!--数据绑定-->
	<Modal id="flowModel" :value="show" :mask-closable="false" width="1500" @on-cancel="handleCancel" title="预览">
		<priview v-if="show" class="preview" ref="previewRef"></priview>
		<div slot="footer" style="height: 0; border-bottom: none"></div>
	</Modal>
</template>

<script>
import { get2dProcessConfigList, get2dProcessList } from '@/api/setting'

import priview from './Priviews.vue'

export default {
	components: { priview },
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '新增',
		},
	},
	computed: {
		stationListLength() {
			return this.pointList.length
		},
	},
	mounted() {},
	data() {
		return {
			lines: [],
			dataList: [],
			processImage: '',
			modalWidth: '',
			height: '',
			width: '',
		}
	},
	methods: {
		init(row) {
			this.$refs.previewRef.debugObj(row)
		},
		viewModel(row) {
			this.$refs.previewRef.viewModel(row)
		},

		get2dProcessList() {
			get2dProcessList({
				applicationName: 'iwater',
				id: 2,
			}).then(res => {
				const { flow } = res.result[0]
				this.lines = JSON.parse(flow)
			})
		},

		// 获取折线的点集合
		getPoints(line) {
			let arr = []
			line.points.forEach(item => {
				arr.push(item.x + ',' + item.y)
			})
			return arr.join(' ')
		},
		// TODO: 水厂的工艺图也从接口中获取背景图片
		// 获取二维水厂工艺图
		get2DPlantFlowChart() {
			get2dProcessConfigList({
				processId: 3,
			}).then(res => {
				const { result } = res
				debugger
				result.forEach(item => {
					let devices = {}
					item.groupConfigs.forEach(config => {
						if (
							Object.keys(devices).findIndex(f => {
								return config.mtMonitorObjectId === f
							}) === -1
						) {
							devices[config.mtMonitorObjectId] = []
						}
						devices[config.mtMonitorObjectId].push(config)
					})

					item.deviceIds = Object.keys(devices)
					item.devices = devices
					item.value = item.deviceIds[0]

					if (Object.keys(devices).length > 1) {
						item.selectGroup = true
						item.selectOptions = Object.keys(devices)
					}
				})
				this.dataList = result
				//deal data
			})
		},

		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},
	},
}
</script>
<style lang="less" scoped>
.preview {
	height: 400px;
}
</style>
