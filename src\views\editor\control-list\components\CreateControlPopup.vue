<template lang="pug">
.create-control-popup
	//- 新增控件弹窗
	Modal(
		:value='show',
		:title='`${isEdit ? "编辑" : "新增"}控件`',
		@on-cancel='handleCancel',
		@on-visible-change='handleAddPluginModalVisChange'
	)
		Form(:model='pluginForm', ref='pluginFormRef', :label-width='100', :rules='pluginRules')
			FormItem(label='控件分组', prop='groupId')
				Select(v-model='pluginForm.groupId', placeholder='请选择控件分组', clearable)
					Option(v-for='(item, index) in groupIdList', :value='item.id', :key='index') {{ item.name }}
			FormItem(label='控件名称', prop='name')
				Input(v-model.trim='pluginForm.name', placeholder='请输入控件名称')

			FormItem(label='交互方式', prop='interactive')
				Select(v-model='pluginForm.interactive', placeholder='请选择交互方式')
					Option(value='filter') 滤镜切换
					Option(value='image') 图片切换

			//- 滤镜切换
			FormItem(label='控件图片', prop='uploadImg', v-if='pluginForm.interactive === "filter"')
				.upload-list(v-for='(item, index) in uploadList')
					img(:src='item.imageUrl', fit='cover', width='100%', height='100%')
					.upload-list-cover
						Icon(type='ios-eye-outline', @click='handleView(item.imageUrl)')
						Icon(type='ios-trash-outline', @click='handleRemove(index)')
				Upload(
					v-show='uploadList.length === 0',
					ref='upload',
					v-model='pluginForm.uploadImg',
					action='',
					:show-upload-list='false',
					accept='image/jpeg,image/jpg,image/png,image/gif',
					:format='["jpg", "jpeg", "png", "gif"]',
					:before-upload='handleBeforeUpload',
					type='drag',
					style='width: 58px'
				)
					div(style='width: 58px; height: 58px; line-height: 58px')
						Icon(type='ios-camera', size='20')

			//- 图片切换
			template(v-if='pluginForm.interactive === "image"')
				FormItem(label='图片标准', prop='imageStandards')
					Select(v-model='pluginForm.imageStandards', placeholder='请选择图片标准', @on-change='changeStandards')
						Option(value='COMMON') 常规标准
						Option(value='CUSTOM') 自定义
				//- 自定义状态图片
				FormItem(label='图片上传')
					Row(style='width: 330px', type='flex', justify='center')
						Col.col-flex.col-title(span='12')
							div 状态
						Col.col-flex.col-title(span='8')
							div 图片
						Col.col-flex.col-title(span='4', v-show='pluginForm.imageStandards === \'CUSTOM\'')
							div 
				div(style='max-height: 300px; overflow: auto; overflow-x: hidden')
					FormItem(label='', v-for='(imgInfo, imgInfoIndex) in pluginForm.uploadImgs', :key='imgInfoIndex')
						Row(style='width: 330px', type='flex', justify='center', align='middle')
							Col.col-flex(span='12')
								Input(v-show='pluginForm.imageStandards === \'CUSTOM\'', v-model.trim='imgInfo.name', placeholder='输入状态名')
								div(v-show='pluginForm.imageStandards === \'COMMON\'') {{ imgInfo.name }}
							Col.col-flex(span='8')
								.upload-list(v-for='(item, index) in imgInfo.uploadList')
									img(:src='item.imageUrl', fit='cover', width='100%', height='100%')
									.upload-list-cover
										Icon(type='ios-eye-outline', @click='handleView(item.imageUrl)')
										Icon(type='ios-trash-outline', @click='handleRemoveImg(imgInfoIndex, index)')
								Upload(
									v-show='imgInfo.uploadList.length === 0',
									:ref='"upload" + imgInfoIndex',
									v-model='pluginForm.uploadImg',
									action='',
									:show-upload-list='false',
									accept='image/jpeg,image/jpg,image/png, image/gif',
									:format='["jpg", "jpeg", "png", "gif"]',
									:before-upload='file => { return handleBeforeUpload(file, imgInfoIndex); }',
									type='drag',
									style='width: 58px'
								)
									div(style='width: 58px; height: 58px; line-height: 58px')
										Icon(type='ios-camera', size='20')
							Col.col-flex(span='4', v-show='pluginForm.imageStandards === \'CUSTOM\'')
								.img-btn 
									//- Icon(
									//- 	type="ios-create-outline",
									//- 	v-show="!imgInfo.isEdit",
									//- 	@click="handleEditStatusImg(imgInfo, imgInfoIndex)")
									Icon(type='ios-trash-outline', @click='handleRemoveStatusImg(imgInfoIndex)', v-show='imgInfoIndex !== 0')
									//- Icon(
									//- 	type="ios-backspace-outline",
									//- 	v-show="imgInfo.isEdit",
									//- 	@click="imgInfo.isEdit = false")
				FormItem(v-show='pluginForm.imageStandards === \'CUSTOM\'')
					Row(style='width: 310px', type='flex', justify='center', align='middle')
						Col.col-flex(span='24')
							Button(type='dashed', @click='handleAddCustomImg', style='width: 100%') 添加
		template(#footer)
			Button(@click='handleCancel') 取消
			Button(type='primary', @click='handleAddPlugin') 确定
	//- 图片预览 弹窗
	Modal(v-model='viewVis', title='预览', width='600', footer-hide)
		img(:src='viewImgUrl', width='100%', height='100%')
</template>

<script>
import { insertControl, updateControl } from '@/api/editor'
// 切换时存储
let tempConstomUploadImgs = []
let tempCommonUploadImgs = []

export default {
	name: 'create-control-popup',
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		list: {
			type: Array,
			default: function () {
				return []
			},
		},
		groupIdList: {
			type: Array,
			default: function () {
				return []
			},
		},
		activeGroup: {
			type: String,
			default: '',
		},
	},
	data() {
		// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
		const validateUpload = (rule, value, callback) => {
			if (this.uploadList && this.uploadList.length === 0) {
				callback(new Error('请上传控件素材！'))
			} else {
				callback()
			}
		}
		return {
			uploadList: [],
			// 表单
			pluginForm: {
				name: '',
				interactive: 'filter',
				imageStandards: 'COMMON',
				uploadImgs: [
					{
						name: '常规',
						uploadList: [],
					},
					{
						name: '正常',
						uploadList: [],
					},
					{
						name: '异常',
						uploadList: [],
					},
					{
						name: '故障',
						uploadList: [],
					},
				],
				uploadImg: '',
				groupId: '',
			},
			pluginRules: {
				groupId: {
					required: true,
					message: '请选择控件分组',
					trigger: 'change',
				},
				name: {
					required: true,
					message: '请输入控件名称',
					trigger: 'blur',
				},
				uploadImg: {
					required: true,
					validator: validateUpload,
					trigger: 'change',
				},
			},
			// 图片预览
			viewVis: false,
			viewImgUrl: '',
			// 编辑自定义控件
			isEdit: false,
			editId: '',
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {
		tempConstomUploadImgs = []
		tempCommonUploadImgs = []
	},
	methods: {
		// 编辑表单初始化
		initEditData(data) {
			const { id, url, name, interactive, groupId, imageStandards = '', controlsDetails = [] } = data
			this.pluginForm.name = name
			this.pluginForm.interactive = interactive
			this.pluginForm.groupId = groupId + ''
			if (interactive === 'image') {
				this.pluginForm.imageStandards = imageStandards
				this.pluginForm.uploadImgs = controlsDetails.map(control => {
					const { imageUrl, imageStatusName } = control
					return {
						name: imageStatusName,
						uploadList: [{ imageUrl }],
					}
				})
				if (imageStandards === 'COMMON') {
					tempCommonUploadImgs = this.pluginForm.uploadImgs
				}
				if (imageStandards === 'CUSTOM') {
					tempConstomUploadImgs = this.pluginForm.uploadImgs
				}
			}

			this.uploadList = [{ imageUrl: url }]
			this.editId = id
			this.isEdit = true
		},
		// 添加自定义切片图片
		handleAddCustomImg() {
			if (this.pluginForm.uploadImgs.length === 5) {
				this.$Message.error('状态数最多五个！')
				return
			}

			this.pluginForm.uploadImgs.push({
				name: null,
				uploadImg: null,
				uploadList: [],
				isEdit: true,
			})
		},
		// 添加、编辑控件
		handleAddPlugin() {
			this.$refs.pluginFormRef.validate(valid => {
				if (valid) {
					// if (
					// 	!this.isEdit &&
					// 	this.list.some(
					// 		item => item.name === this.pluginForm.name,
					// 	)
					// ) {
					// 	this.$Message.warning('控件名称已存在, 请修改！')
					// 	return
					// }

					const { uploadImgs = [], interactive, imageStandards, name, groupId } = this.pluginForm
					let params = {}
					let controlsDetails = []
					let iconUrl = ''

					if (interactive === 'image') {
						let validateFlag = false
						uploadImgs.forEach((imag, index) => {
							// 检验填写了状态名称但是未上传图片情况
							if (imag.name && imag.uploadList.length === 0) {
								validateFlag = true
								return false
							}

							if (index === 0) {
								iconUrl = imag.uploadList[0].imageUrl
							}

							controlsDetails.push({
								imageStatusName: imag.name,
								imageUrl: imag.uploadList[0].imageUrl,
							})
						})
						// 图片验证
						if (validateFlag) {
							this.$Message.warning('存在未上传的图片请检查!')
							return
						}

						params = {
							controlsDetails,
							interactive,
							imageStandards,
							name,
							iconUrl,
							groupId,
						}
					} else {
						params = {
							interactive,
							name: name,
							iconUrl: this.uploadList[0].imageUrl,
							groupId,
						}
					}
					if (this.isEdit) {
						params.id = this.editId

						updateControl(params).then(() => {
							this.$Message.success('编辑成功！')
							// this.$refs.upload.clearFiles()
							this.resetForm()
							this.$emit('operation-suc')
							this.isEdit = false
							this.editId = ''
							this.$emit('update:show', false)
						})
					} else {
						// 调用添加接口
						insertControl(params).then(() => {
							this.$Message.success('新增成功！')
							this.resetForm()
							this.$emit('operation-suc')
							this.$emit('update:show', false)
						})
					}
				}
			})
		},

		// 表单清空
		resetForm() {
			tempConstomUploadImgs = []
			tempCommonUploadImgs = []

			this.pluginForm = {
				name: '',
				interactive: 'filter',
				imageStandards: 'COMMON',
				uploadImgs: [
					{
						name: '常规',
						uploadList: [],
					},
					{
						name: '正常',
						uploadList: [],
					},
					{
						name: '异常',
						uploadList: [],
					},
					{
						name: '故障',
						uploadList: [],
					},
				],
				uploadImg: '',
			}
		},

		// 取消操作
		handleCancel() {
			this.resetForm()
			this.$emit('update:show', false)
		},

		handleAddPluginModalVisChange(value) {
			if (!value) {
				this.pluginForm.uploadImg = null
				this.uploadList = []
				this.$refs.pluginFormRef.resetFields()
			}
			this.$nextTick(() => {
				this.pluginForm.groupId = this.activeGroup
			})
		},

		// 上传控件图片
		handleBeforeUpload(file, index) {
			let data = new FormData()
			data.append('files', file)

			this.$axios.post('/backgroundImage/image/upload', data).then(res => {
				if (this.pluginForm.interactive === 'image') {
					this.pluginForm.uploadImgs[index].uploadList = res.result
					// this.$refs.pluginFormRef.validateField('uploadImg')
				} else {
					this.uploadList = res.result
					// this.$refs.pluginFormRef.validateField('uploadImg')
				}
				this.$Message.info('上传成功')
			})
			return false
		},

		// 删除img
		handleRemove(index) {
			this.uploadList.splice(index, 1)
		},
		// 删除img
		handleRemoveImg(imgindex, index) {
			this.pluginForm.uploadImgs[imgindex].uploadList.splice(index, 1)
		},

		// 删除状态img
		handleRemoveStatusImg(index) {
			this.pluginForm.uploadImgs.splice(index, 1)
		},

		// 编辑状态img
		handleEditStatusImg(row) {
			row.isEdit = true
			// this.pluginForm.uploadImgs.splice(index, 1)
		},

		handleView(url) {
			this.viewVis = true
			this.viewImgUrl = url
		},

		// 切换图片标准
		changeStandards(val) {
			if (val === 'COMMON') {
				tempConstomUploadImgs = this.pluginForm.uploadImgs
				if (tempCommonUploadImgs.length !== 0) {
					this.pluginForm.uploadImgs = tempCommonUploadImgs
				} else {
					this.pluginForm.uploadImgs = [
						{
							name: '常规',
							uploadList: [],
						},
						{
							name: '正常',
							uploadList: [],
						},
						{
							name: '异常',
							uploadList: [],
						},
						{
							name: '故障',
							uploadList: [],
						},
					]
				}
			} else {
				tempCommonUploadImgs = this.pluginForm.uploadImgs
				if (tempConstomUploadImgs.length !== 0) {
					this.pluginForm.uploadImgs = tempConstomUploadImgs
				} else {
					this.pluginForm.uploadImgs = [
						{
							name: '',
							uploadList: [],
						},
					]
				}
			}
		},
	},
}
</script>

<style lang="less" scoped>
::v-deep {
	.ivu-form-item {
		margin-bottom: 20px;
	}
	.huge-tree .search-bar .input .filter-input {
		padding: 4px 5px;
	}
	.ivu-tabs-bar {
		margin-bottom: 0px;
	}
	.ivu-modal {
		.ivu-modal-content {
			.ivu-modal-header {
				padding: 14px 16px !important;
			}
		}
	}
	.ivu-upload-drag {
		display: block;
	}
	.ivu-form-item-content {
		display: flex;
		align-items: center;
	}
}
.upload-list {
	display: inline-block;
	width: 60px;
	height: 60px;
	text-align: center;
	line-height: 60px;
	border: 1px solid transparent;
	border-radius: 4px;
	overflow: hidden;
	background: #fff;
	position: relative;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
	margin-right: 4px;
}
.upload-list img {
	width: 100%;
	height: 100%;
}
.upload-list-cover {
	display: none;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.6);
}
.upload-list:hover .upload-list-cover {
	display: block;
}
.upload-list-cover i {
	color: #fff;
	font-size: 20px;
	cursor: pointer;
	margin: 0 2px;
}
.col-flex {
	display: flex;
	justify-content: center;
	.img-btn {
		cursor: pointer;
		font-size: 18px;
		color: #111010;
	}
}
.col-title {
	font-weight: 600;
}
</style>
