/*
 * @Descripttion:
 * @version:
 * @Author: heliping
 * @Date: 2022-03-18 10:00:59
 * @LastEditors: shenxh
 * @LastEditTime: 2024-03-13 15:41:34
 */
const setting = [
	// {
	// 	path: '/editor',
	// 	name: 'editor',
	// 	component: (/* webpackChunkName: 'editor' */) =>
	// 		import('@/views/editor'),
	// },
	{
		path: '/priview',
		name: 'priview',
		component: (/* webpackChunkName: 'priview' */) => import('@/views/editor/priview'),
	},
	{
		path: '/editorList',
		name: 'editorList',
		component: (/* webpackChunkName: 'editorList' */) => import('@/views/editor/editor-list'),
	},
	{
		path: '/editorConfig',
		name: 'editorConfig',
		component: (/* webpackChunkName: 'editorConfig' */) => import('@/views/editor/editor-config'),
	},
	{
		path: '/controlList',
		name: 'controlList',
		component: (/* webpackChunkName: 'controlList' */) => import('@/views/editor/control-list'),
	},
	// {
	// 	path: '/test',
	// 	name: 'test',
	// 	component: (/* webpackChunkName: 'editor' */) => import('@/views/test'),
	// },
	// {
	// 	path: '/treeDemo',
	// 	name: 'treeDemo',
	// 	component: (/* webpackChunkName: 'editor' */) => import('@/views/test/hugeTree/demo'),
	// },
	{
		path: '/limitManage',
		name: 'limitManage',
		component: (/* webpackChunkName: 'limitManage' */) => import('@/views/editor/permission/LimitManage.vue'),
	},
]
export default setting
