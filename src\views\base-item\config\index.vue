<!--
 * @Description: 安防平台管理
 * @Author: shenxh
 * @Date: 2022-04-01 09:46:43
 * @LastEditors: shenxh
 * @LastEditTime: 2022-04-15 10:07:59
-->
<template lang="pug">
.security-platform-manage
	.header-wrap
		es-header.header(title='系统参数配置')
	water-row.table-btn-wrap(justify='space-between', align='center')
		i
		Button(type='primary', @click='handleCreate') 添加

	.security-platform-manage-content
		Table.security-platform-manage-table(
			:columns='columns',
			:data='tableData',
			:loading='loading',
			:height='500',
			border,
			showPage,
			:pageData='pageData',
			@on-page-num-change='handlePageNum',
			@on-page-size-change='handlePageSize'
		)
			template(slot-scope='{ row }', slot='type')
				span {{ row.type == 0 ? '全局' : '租户' }}
			template(slot-scope='{ row }', slot='action')
				Button(type='text', :style='{ color: "#3AA7D8" }', @click='handleUpd(row)', size='small') 编辑
				Poptip(transfer, confirm='', title='确定删除吗？', @on-ok='handleDel(row)')
					Button(type='text', :style='{ color: "#EC5151" }', size='small') 删除

	//- 创建系统弹窗
	create-system-popup(v-model='showModal', :data='currentRow', @submit-form='handleSubForm')
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import CreateSystemPopup from './components/create-config-popup.vue'
import { getAllSysConfig, deleteSysConfig } from '@/api/base-item'

export default {
	name: 'security-platform-manage',
	components: {
		WaterRow,
		CreateSystemPopup,
	},
	props: {},
	data() {
		return {
			currentRow: {},
			showModal: false,
			loading: false,
			platformList: [],
			tableData: [],
			columns: [
				{
					title: '序号',
					type: 'index',
					align: 'center',
					width: 65,
				},
				{
					title: '配置编码',
					key: 'code',
				},
				{
					title: '配置类型',
					slot: 'type',
				},
				{
					title: '值',
					key: 'val',
				},
				{
					title: '备注',
					key: 'memo',
				},
				{
					title: '操作',
					align: 'center',
					slot: 'action',
					width: 130,
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 50, 100],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		// this._getPlatDic()
		this.getTableData()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 添加按钮
		handleCreate() {
			this.currentRow = {}
			this.showModal = true
		},

		// 编辑按钮
		handleUpd(row) {
			this.currentRow = { ...row }
			this.showModal = true
		},

		// 删除按钮
		handleDel(row) {
			this.currentRow = { ...row }
			this._delSecurityPlatform()
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum

			this.getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize

			this.getTableData()
		},

		// 显示/隐藏密码
		handleEye(row, showPwd) {
			let arr = [...this.tableData]

			if (arr[row._index]) {
				arr[row._index].showPwd = showPwd
				this.tableData = arr
			}
		},

		// 设置密码
		setPwd(pwd, showPwd = false) {
			let str = pwd

			if (pwd && !showPwd) {
				str = pwd[0] + '****' + pwd[pwd.length - 1]
			}

			return str
		},

		// 弹窗按钮-保存
		handleSubForm() {
			this.getTableData()
		},

		// 获取表格数据
		getTableData() {
			const formData = {}
			const { current: pageNum, pageSize } = this.pageData

			this.loading = true
			getAllSysConfig({
				...formData,
				pageNum,
				pageSize,
			})
				.then(res => {
					const data = res.result

					this.loading = false
					if (data) {
						this.tableData = data || []
					}
				})
				.catch(() => {
					this.loading = false
				})
		},

		// 删除安防平台
		_delSecurityPlatform() {
			deleteSysConfig({
				id: this.currentRow.id,
			}).then(() => {
				this.$Message.success('删除成功')

				this.getTableData()
			})
		},

		// 获取平台数据
		// _getPlatDic() {
		// 	getPlatDic().then(res => {
		// 		const data = res.result

		// 		this.platformList = data || []
		// 	})
		// },
	},
}
</script>

<style lang="less" scoped>
.security-platform-manage {
	width: 100%;
	height: 100%;
	padding: 0 16px;
	.table-btn-wrap {
		margin: 10px 0;
	}
	.security-platform-manage-content {
		width: 100%;
		height: calc(100vh - 160px);
		.icon-eye {
			margin-left: 5px;
			cursor: pointer;
			color: #41a8ed;
		}
	}
}
</style>
