<template lang="pug">
.video-panel(
	:style='{ width: `${data.width}px`, height: `${data.height}px`, left: `${position.left}px`, top: `${position.top}px` }'
)
	video-h5(v-if='data.url', :url='data.url')
</template>

<script>
import VideoH5 from '@/components/gc-video-h5/VideoH5.vue'

export default {
	components: {
		VideoH5,
	},
	props: {
		data: {
			type: Object,
			required: true,
		},
	},
	computed: {
		position() {
			const { width, height, left, top } = this.data
			return {
				left: left - width / 2,
				top: top - height / 2,
			}
		},
	},
	data() {
		return {}
	},
	methods: {},
}
</script>

<style lang="less" scoped>
.video-panel {
	position: absolute;
}
</style>
<style lang="less">
.video-panel {
	.vjs-control-bar {
		display: flex;
		flex-wrap: wrap;
		overflow: hidden;
	}
	.vjs-stretch-control,
	.vjs-bitrate-control,
	.vjs-snapshot-control {
		display: none;
	}
}
</style>
