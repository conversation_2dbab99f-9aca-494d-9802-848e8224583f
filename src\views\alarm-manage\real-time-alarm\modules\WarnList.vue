<!--
 * @Description: 实时报警-表格页
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-02-23 13:32:49
 * @LastEditors: shenxh
 * @LastEditTime: 2024-08-13 17:26:02
-->

<template lang="pug">
.warn-list
	es-search.es-search(
		col='4',
		:show-collapse='true',
		:modules='moduleList',
		@on-search='handleSearchBtn',
		@on-reset='handleResetBtn',
		@before-reset='handleBeforeReset'
	)
	water-row(justify='space-between', align='center')
		.water-tabs-wrap
			water-tabs(
				ref='water-tabs',
				v-show='!this.$route.query.sysCode',
				v-model='currentTab',
				:is-search.sync='isSearch',
				:form-data='formDataFormat',
				@handle-tab='handleTab',
				@get-tab-data='getTabData'
			)
		water-row.water-margin-top-8(justify='flex-start', align='center')
			.data-info
				.group
					.label 报警总数：
					.value {{ pageData.total }}条
				.group
					.label 未处理数：
					.value.active {{ currentTab === '_all' ? tabData.totalNoDealNum || 0 : tabData.noDealNum || 0 }}条
			Button.void(@click='handleBatchClearBtn()') 批量消缺

	.warn-list-content
		Row(style='height: 100%')
			Col(:span='showRight ? 20 : 24', style='height: 100%', :class='pageType === "eg" ? "pad_r12" : ""')
				es-table.water-table.warn-list-table(
					:columns='columns',
					:data='tableData',
					:loading='loading',
					border,
					showPage,
					:pageData='pageData',
					@on-selection-change='handleSelectChange',
					@on-page-num-change='handlePageNum',
					@on-page-size-change='handlePageSize'
				)
					template(slot-scope='{ row }', slot='status')
						.status-box(:class='getDataByAlarmStatus(row.disposeStatus).class') {{ getDataByAlarmStatus(row.disposeStatus).name }}
			Col(span='4', style='height: 100%', v-if='showRight')
				right-analysis(:analysisObj='RightAnalysisdata')

	//- 报警详情弹窗
	es-alarm-detail(
		:show.sync='showDetailModal',
		:nodeType='rowData.nodeType',
		:readonly='rowData.disposeStatus != 0',
		:sysCode='rowData.sysCode',
		:id='String(rowData.id)',
		:nodeId='rowData.nodeId',
		:hideCreatOrderButton='hideCreatOrderButton',
		showPumpInfo,
		@closeOrderModel='closeOrderModel'
	)
	//- 弹窗
	es-modal(width='400', v-model='showModal', title='请输入处理内容', @on-ok='handleModalSub', @on-cancel='showModal = false')
		Input(v-model='handleVal', slot='body', type='textarea', :rows='4', placeholder='请输入处理内容')
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import WaterTabs from '@/components/gc-water-tabs'
import { queryAlarmLevelList, queryAreaAndStationCodes } from '@/api/common.js'
import {
	disposeAlarmByEliminate,
	queryPage,
	queryAlarmInfoTotal,
	queryAlarmInfoGroupByStationTop,
	queryAlarmInfoGroupByArea,
} from '@/api/alarm-manage.js'
import { pushMessage } from '@/api/push-manage.js'
import RightAnalysis from '@/components/gc-rjght-warn-analysis'

export default {
	components: {
		WaterRow,
		WaterTabs,
		RightAnalysis,
	},
	// beforeRouteEnter(to, from, next) {
	// 	next(e => {
	// 		window.console.log('11111', to) //上一个页面前往的的页面(当前页面)
	// 		window.console.log('22222', from) //来自哪一个页面
	// 		// 这个回调参数e,包含setup中暴露出去的数据以及内置方法
	// 		window.console.log(e)
	// 		// 获取是否来自home1页面
	// 	})
	// },
	props: {
		hideRight: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			hideCreatOrderButton: !!this.$route.query.hideCreatOrderButton,
			form: {
				nodeName: '',
			},
			handleVal: '',
			tabData: {},
			tabList: [],
			formDataFormat: {},
			isSearch: false,
			rowData: {},
			showModal: false,
			showDetailModal: false,
			dateRange: '',
			loading: false,
			tableData: [],
			selectedRows: [],
			currentTab: this.$route.query.sysCode || '_all',
			currentTabObj: {},
			alarmTypes: [],
			moduleList: [
				{
					label: '',
					value: '1',
					isDefault: true,
					keys: ['nodeCodes', 'nodeName', 'alarmTypeName', 'alarmLevelId', 'disposeStatus', 'alarmDate'],
					model: {
						alarmLevelId: '',
						disposeStatus: '',
						nodeName: '',
						alarmTypeName: '',
						nodeCodes: '',
					},
					data: [
						// {
						// 	type: 2,
						// 	key: 'nodeCodes',
						// 	formItemProps: {
						// 		label: '所属区域',
						// 		prop: 'nodeCodes',
						// 		labelWidth: 80,
						// 	},
						// 	widgetProps: {
						// 		clearable: true,
						// 		disabled: false,
						// 	},
						// 	dataSourceList: [],
						// 	events: {
						// 		onChange: () => {
						// 			// console.log(this.formProps.model);
						// 		},
						// 	},
						// },
						{
							type: 1,
							key: 'nodeName',
							formItemProps: {
								label: '报警来源',
								prop: 'nodeName',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
						// {
						// 	type: 1,

						// 	key: 'alarmTypeName',
						// 	formItemProps: {
						// 		label: '报警类型',
						// 		prop: 'alarmTypeName',
						// 		labelWidth: 80,
						// 	},
						// 	widgetProps: {
						// 		clearable: true,
						// 		disabled: false,
						// 	},
						// 	dataSourceList: [],
						// 	events: {
						// 		onChange: e => {
						// 			// 替换特殊字符
						// 			this.moduleList[0].model.alarmTypeName = this.moduleList[0].model.alarmTypeName.replace(
						// 				/\*/g,
						// 				'',
						// 			)
						// 			this.moduleList[0].model.alarmTypeName = this.moduleList[0].model.alarmTypeName.replace(
						// 				/\?/g,
						// 				'',
						// 			)
						// 		},
						// 	},
						// },
						{
							type: 2,
							key: 'alarmLevelId',
							formItemProps: {
								label: '报警等级',
								prop: 'alarmLevelId',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
						{
							type: 2,
							key: 'disposeStatus',
							formItemProps: {
								label: '处理状态',
								prop: 'disposeStatus',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [
								{
									label: '未处理',
									value: 0,
								},
								{
									label: '已处理',
									value: 10,
								},
								{
									label: '已建单',
									value: 20,
								},
							],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
					],
				},
			],
			columns: [
				{
					type: 'selection',
					width: 60,
					align: 'center',
				},
				{
					title: '报警来源名称',
					key: 'nodeName',
				},
				{
					title: '报警等级',
					key: 'alarmLevelName',
					width: 100,
				},
				{
					title: '报警类型',
					key: 'alarmTypeName',
				},
				{
					title: '报警方案',
					key: 'alarmConfigName',
				},
				{
					title: '报警值',
					key: 'alarmValue',
				},
				{
					title: '设定值',
					key: 'alarmLimit',
				},
				{
					title: '开始时间',
					key: 'beginTime',
					sortable: true,
				},
				{
					title: '最后报警时间',
					key: 'lastActiveTime',
					sortable: true,
				},
				{
					title: '处理状态',
					slot: 'status',
					align: 'center',
				},
				{
					title: '操作',
					slot: 'action',
					align: 'center',
					width: 180,
					render: (h, params) => {
						return h('div', [
							h(
								'Button',
								{
									props: {
										type: 'primary',
									},
									on: {
										click: () => {
											this.handleManage(params.row)
										},
									},
								},
								params.row.disposeStatus == 0 ? '处理' : '详情',
							),
							h(
								'Button',
								{
									props: {
										type: 'default',
									},
									on: {
										click: () => {
											this.handlePush(params.row)
										},
									},
									style: {
										marginLeft: '10px',
									},
								},
								'推送',
							),
						])
					},
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 30, 40, 50],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
			isReset: false,
			pageType: '',
			RightAnalysisdata: {
				totalNumber: 0,
				maxAreaNumber: 0,
				maxWeekNumber: 0,
				areaList: [],
				weekList: [],
			},
			showRight: false,
			subCode: '',
		}
	},
	watch: {
		routerQuery: {
			handler(newVal) {
				if (newVal.time && new Date().getTime() - newVal.time > 3000) return
				if (JSON.stringify(newVal.rowData) === '{}') return
				this.rowData = newVal.rowData
				this.$nextTick(() => {
					this.showDetailModal = true
				})
			},
			immediate: true,
		},
	},
	computed: {
		sysCode() {
			return (
				this.getRealCode(this.$route.query.sysCode).sysCode ||
				(this.currentTab === '_all' ? '' : this.currentTab)
			)
		},
		getNodeName() {
			return this.$route.query.nameSpace ? this.$route.query.nodeName + ' ' : this.$route.query.nodeName
		},
		// 从其他页面跳转过来时携带的参数
		routerQuery() {
			const { rowData = '{}', time = '' } = this.$route.query
			return {
				rowData: JSON.parse(rowData),
				time,
			}
		},
	},
	async created() {
		this.form.nodeName = this.getNodeName
		this._setSearchForm()
		this._queryAlarmLevelList()
		this._setPageData()

		if (this.$route.query.sysCode === 'eg') {
			const item = {
				type: 2,
				key: 'nodeCodes',
				formItemProps: {
					label: '所属区域',
					prop: 'nodeCodes',
					labelWidth: 80,
				},
				widgetProps: {
					clearable: true,
					disabled: false,
				},
				dataSourceList: [],
				events: {
					onChange: () => {
						// console.log(this.formProps.model);
					},
				},
			}
			this.moduleList[0].data.unshift(item)
			const item1 = {
				type: 1,

				key: 'alarmTypeName',
				formItemProps: {
					label: '报警类型',
					prop: 'alarmTypeName',
					labelWidth: 80,
				},
				widgetProps: {
					clearable: true,
					disabled: false,
				},
				dataSourceList: [],
				events: {
					onChange: () => {
						// 替换特殊字符
						this.moduleList[0].model.alarmTypeName = this.moduleList[0].model.alarmTypeName.replace(
							/\*/g,
							'',
						)
						this.moduleList[0].model.alarmTypeName = this.moduleList[0].model.alarmTypeName.replace(
							/\?/g,
							'',
						)
					},
				},
			}
			this.moduleList[0].data.splice(2, 0, item1)
			const res = await this._queryAreaAndStationCodes()
			if (res && res.result && res.result.length) {
				const arr = []
				res.result.forEach(item => {
					if (item.childrenCodes && item.childrenCodes.length) {
						const obj = {
							label: item.title,
							value: item.childrenCodes.join(),
						}
						arr.push(obj)
					}
				})
				this.moduleList[0].data[0].dataSourceList = arr
			}
		}
	},
	mounted() {
		// 判断页面类型
		this.pageType = this.$route.query.sysCode
		if (this.hideRight || this.$route.query.hideRight) {
			this.showRight = false
		} else if (this.pageType === 'eg') {
			this.showRight = true

			this.rightAnalysisFn()
		}
		if (this.pageType === 'eg') {
			this.columns[1].title = '泵房/设备名称'
			this.moduleList[0].data[1].formItemProps.label = '泵房名称'
		} else {
			this.columns[1].title = '报警来源名称'
			// this.moduleList[0].data[2].formItemProps.label = '报警来源'
			// this.moduleList[0].data[0].visible = false
			// this.moduleList[0].data[2].visible = false
		}

		this._getTableData()
		setTimeout(() => {
			this.moduleList[0].model.nodeName = this.getNodeName
		}, 30)
	},
	methods: {
		//列表中获取最大值
		getMaxNumber(list, key) {
			let num = 0
			if (list.length) {
				const arr = list.map(o => o[key])
				num = Math.max(...arr)
			}
			return num
		},
		// 报警次数
		rightAnalysisFn() {
			let start = this.$moment().format('YYYY-MM') + '-01 00:00:00'
			let end = this.$moment().format('YYYY-MM-DD') + ' 23:59:59'
			// 告警次数
			queryAlarmInfoTotal({
				startTime: start,
				endTime: end,
				sysCode: this.pageType,
			}).then(res => {
				this.RightAnalysisdata.totalNumber = res.result
			})
			// top5
			queryAlarmInfoGroupByStationTop({
				startTime: start,
				endTime: end,
				sysCode: this.pageType,
			}).then(res => {
				if (res.result) {
					this.RightAnalysisdata.weekList = res.result
					this.RightAnalysisdata.maxWeekNumber = this.getMaxNumber(res.result, 'thisTypeNum')
				} else {
					this.RightAnalysisdata.weekList = []
				}
			})
			// 区域统计分布
			queryAlarmInfoGroupByArea({
				startTime: start,
				endTime: end,
				sysCode: this.pageType,
			}).then(res => {
				if (res.result.list) {
					this.RightAnalysisdata.areaList = res.result.list
					this.RightAnalysisdata.maxAreaNumber = this.getMaxNumber(res.result.list, 'thisTypeNum')
				} else {
					this.RightAnalysisdata.areaList = []
				}
			})
			console.log('最终值', this.RightAnalysisdata)
		},
		// 获取真正参数
		getRealCode(sysCode) {
			if (!sysCode) {
				return ''
			}
			if (sysCode.includes('&&')) {
				// this.subCode = sysCode.split('&&')[1]
				// return sysCode.split('&&')[0]
				return {
					sysCode: sysCode.split('&&')[0],
					subCode: sysCode.split('&&')[1],
				}
			}
			if (sysCode === '_all') {
				sysCode = ''
			}
			return { sysCode, subCode: '' }
		},
		// 搜索按钮
		handleSearchBtn(params) {
			console.log(params)
			this.form = params
			this.pageData.current = 1
			if (this.pageType === 'eg') {
				this.rightAnalysisFn()
			}

			// this.isSearch = true
			this._getTableData()
		},

		// 重置按钮
		handleResetBtn() {
			this.form = {}
			this.pageData.current = 1

			this._getTableData()
		},

		// 已选行
		handleSelectChange(selectedRows) {
			this.selectedRows = selectedRows
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum
			// this.isSearch = true
			this._getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize
			// this.isSearch = true
			this._getTableData()
		},

		// 重置之前
		handleBeforeReset() {
			this.isReset = true
		},

		// 批量消缺按钮
		handleBatchClearBtn() {
			if (!this.selectedRows.length) {
				this.$Message.warning('请勾选表格数据')
				return
			}

			this.handleVal = ''
			this.showModal = true
		},

		// 弹窗提交按钮
		handleModalSub() {
			const idList = this.selectedRows.map(item => {
				return item.id
			})

			this._disposeAlarmByEliminate(idList, this.handleVal)
		},

		// 处理按钮
		handleManage(row) {
			this.rowData = row
			this.showDetailModal = true
		},
		// 推送按钮
		handlePush(row) {
			pushMessage({
				id: row.id,
			}).then(() => {
				this.$Message.success('推送成功')
			})
		},

		// 创建工单结束
		closeOrderModel() {
			console.log('创建工单结束')
			setTimeout(() => {
				this._getTableData()
			}, 200)
		},

		// 点击 tab
		handleTab({ currentTabObj }) {
			this.currentTabObj = currentTabObj
			this.pageData.current = 1

			this._getTableData()
		},

		// 弹窗表单提交
		submitDlgForm(data) {
			this._disposeAlarmByEliminate([this.rowData.id], data)
		},

		// 获取tab数据
		getTabData(data) {
			this.tabList = data

			this._getNoDealNum()
		},

		_getNoDealNum() {
			let totalNoDealNum = 0
			let obj = {}

			if (this.tabList && this.tabList.length) {
				this.tabList.forEach(item => {
					totalNoDealNum += item.noDealNum
					if (item.sysCode == this.currentTab) {
						obj = item
					}
				})
			}

			this.tabData = {
				...obj,
				totalNoDealNum,
			}
		},

		// 设置页面数据
		_setPageData() {
			if (this.$route.name === 'real-time-alarm') {
				this.columns.splice(2, 0, {
					title: '所属系统',
					key: 'sysName',
				})
			}
		},

		// 设置搜索框
		_setSearchForm() {
			if (this.$route.name === 'history-alarm') {
				const item = {
					type: 'date_picker',
					key: 'alarmDate',
					dataSourceList: [],
					formItemProps: {
						label: '报警日期',
						labelWidth: 80,
						prop: 'alarmDate',
					},
					widgetProps: {
						type: 'daterange',
						clearable: true,
						disabled: false,
					},
				}

				this.moduleList[0].data.push(item)
			}
		},

		// 根据 id 获取报警项
		_getAlarmItemData(id) {
			let data = ''

			this.moduleList[0].data[0].dataSourceList.forEach(item => {
				if (item.value == id) {
					data = item
				}
			})

			return data
		},

		// 获取表格数据
		_getTableData() {
			const formData = this.moduleList[0].model

			if (this.$route.name === 'history-alarm') {
				if (formData.alarmDate && formData.alarmDate[0] && formData.alarmDate[1]) {
					formData.startTime = this.$moment(formData.alarmDate[0].getTime()).format('YYYY-MM-DD HH:mm:ss')
					formData.endTime = this.$moment(formData.alarmDate[1].getTime()).format('YYYY-MM-DD') + ' 23:59:59'
				} else {
					formData.startTime = formData.endTime = undefined
				}
			} else {
				// formData.startTime = this.$moment(
				// 	new Date(new Date().toLocaleDateString()),
				// ).format('YYYY-MM-DD HH:mm:ss')
				// formData.endTime =
				// 	this.$moment(new Date().getTime()).format('YYYY-MM-DD') +
				// 	' 23:59:59'
				formData.lastActiveDate = this.$moment(new Date()).format('YYYY-MM-DD')
			}

			this.formDataFormat = formData
			setTimeout(() => {
				this.isSearch = true
			}, 200)

			this.$nextTick(() => {
				if (this.$refs['water-tabs']) {
					this.$refs['water-tabs']._querySysList()
				}
			})
			this._queryPage(formData)
		},

		// 查询【报警等级列表】
		_queryAlarmLevelList() {
			queryAlarmLevelList().then(res => {
				const data = res.result

				if (data && data.length) {
					if (this.sysCode === 'eg') {
						this.moduleList[0].data[3].dataSourceList = data.map(item => {
							return {
								label: item.name,
								value: item.id,
							}
						})
					} else {
						this.moduleList[0].data[1].dataSourceList = data.map(item => {
							return {
								label: item.name,
								value: item.id,
							}
						})
					}
				}
			})
		},

		// 处理报警（消缺）
		_disposeAlarmByEliminate(idList, disposeMemo) {
			disposeAlarmByEliminate({
				ids: idList,
				disposeMemo,
			}).then(res => {
				console.log(res)
				this.$Message.success('操作成功')

				this.showModal = false

				this._getTableData()
			})
		},

		// 报警查询
		_queryPage(params) {
			const { nodeName } = this.form
			const { alarmLevelId, disposeStatus, startTime, endTime, lastActiveDate, alarmTypeName, nodeCodes } = params
			const { current: pageNum, pageSize } = this.pageData
			const { sysCode, subCode } = this.getRealCode(this.sysCode)
			this.loading = true
			const queryParams = {
				alarmLevelId,
				disposeStatus,
				nodeName,
				startTime,
				endTime,
				lastActiveDate,
				needPage: true,
				sysCode: this.$route.query.sysCode || sysCode,
				subSysCode: this.$route.query.subSysCode || subCode,
				pageNum,
				pageSize,
			}
			if (this.sysCode === 'eg') {
				queryParams['alarmTypeName'] = alarmTypeName
				nodeCodes && (queryParams['nodeCodes'] = nodeCodes)
			}
			console.log('this.sysCode', this.sysCode)
			queryPage(queryParams)
				.then(res => {
					const { list: data, total } = res.result

					if (data) {
						this.tableData = data
						// this.tableData = [
						// 	{
						// 		disposeStatus: 0,
						// 	},
						// 	{
						// 		disposeStatus: 10,
						// 	},
						// 	{
						// 		disposeStatus: 20,
						// 	},
						// ]
					}
					this.pageData.total = total
					this.loading = false
				})
				.catch(() => {
					this.loading = false
				})
		},
		// 根绝报警状态值返回对应数据
		getDataByAlarmStatus(status) {
			let data = {
				class: '',
				name: '',
			}
			switch (status) {
				case 0:
					data = {
						class: 'todo',
						name: '未处理',
					}
					break
				case 10:
					data = {
						class: 'done',
						name: '已处理',
					}
					break
				case 20:
					data = {
						class: 'created',
						name: '已建单',
					}
					break
			}
			return data
		},
		// 查询系统的区域和区域下的站点编号
		_queryAreaAndStationCodes() {
			return new Promise((resolve, reject) => {
				queryAreaAndStationCodes()
					.then(res => {
						resolve(res)
					})
					.catch(error => {
						reject(error)
					})
			})
		},
	},
}
</script>
<style scoped lang="less">
.pad_r12 {
	padding-right: 16px;
}
.status-box {
	display: flex;
	justify-content: center;
	align-items: center;
	&::before {
		content: '';
		display: block;
		width: 6px;
		height: 6px;
		border-radius: 50%;
		margin-right: 6px;
	}
	&.todo {
		&::before {
			background-color: #ff7022;
		}
	}
	&.done {
		&::before {
			background-color: #00b478;
		}
	}
	&.created {
		&::before {
			background-color: #225dff;
		}
	}
}
.warn-list {
	width: 100%;
	height: 100%;
	.es-search {
		padding-top: 4px;
		/deep/ .ivu-input-wrapper {
			overflow: hidden;
		}
		/deep/ .ivu-btn {
			background-color: #57a3f3;
			border-color: #57a3f3;
			color: #fff;
			padding: 0 10px;
			margin-left: 10px;
			&:last-child {
				color: #515a6e;
				background-color: #fff;
				border-color: #dcdee2;
			}
			.ivu-icon {
				display: none;
			}
			span {
				margin: 0;
			}
		}
	}
	&-content {
		padding-top: 8px;
		height: calc(100vh - 130px);
	}
	&-table {
		width: 100%;
	}
	&-form-item {
		width: fit-content;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-right: 16px;
		.title {
			margin-right: 4px;
		}
		.select {
			width: 140px;
		}
	}
	.data-info {
		display: flex;
		align-items: center;
		margin-right: 20px;
		.group {
			display: flex;
			align-items: center;
			color: #535567;
			font-size: 14px;
			margin-left: 10px;
			.value {
				&.active {
					color: #ca7cfc;
				}
			}
		}
	}
	.void {
		border: 1px solid #3aa7d8;
		color: #3aa7d8;
		background-color: #fff;
	}
}
</style>
