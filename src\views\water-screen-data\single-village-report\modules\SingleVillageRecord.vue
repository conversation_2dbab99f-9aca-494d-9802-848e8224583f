<template lang="pug">
.record-list
	water-row.record-list-header(justify='flex-start', align='center')
		.record-list-form-title 年份:
		Select.record-list-form-select(v-model='yearId', placeholder='请选择')
			Option(v-for='(item, index) in yearList', :key='index', :disabled='item.disabled', :value='item.value') {{ item.label }}
		Button(type='primary', @click='handleQuery') 查询
		Button.water-margin-left-16(type='primary', @click='handleExport') 导出
	#qrcodeDowm(style='display: none')
	//- a(style="display: none")
	WaterTable.record-list-table(border, :columns='columns', :data='tableData', :loading='loading')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { yearList } from '@/utils/enums.js'
import { exportFile } from '@/utils/function.js'
import { villageQuery } from '@/api/water-screen-data.js'
import { EventBus } from '@/utils/eventBus.js'
export default {
	name: 'fill-list',
	components: {
		WaterTable,
		WaterRow,
	},
	mounted() {
		this.handleQuery()
		this.yearList.forEach(y => {
			if (y.value > this.yearId) {
				y.disabled = true
			}
		})
		EventBus.$on('fresh-dc-record', () => {
			this.handleQuery()
		})
	},
	data() {
		return {
			loading: false,
			yearId: new Date().getFullYear() + '',
			yearList: yearList,
			tableData: [],
			columns: [
				{
					title: '月份',
					key: 'month',
					align: 'center',
					width: 120,
					render: (h, params) => {
						return h('div', `${params.row.month}月`)
					},
				},
				{
					title: '设备完好率',
					key: 'integrityRate',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						let num = ''
						if (params.row.integrityRate !== undefined) {
							num = params.row.integrityRate + '%'
						}
						return h('div', num)
					},
				},
				{
					title: '水质合格率',
					key: 'waterQualityRate',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						let num = ''
						if (params.row.waterQualityRate !== undefined) {
							num = params.row.waterQualityRate + '%'
						}
						return h('div', num)
					},
				},
				{
					title: '最近修改时间',
					key: 'updateTime',
					align: 'center',
					minWidth: 160,
				},
			],
		}
	},
	methods: {
		handleQuery() {
			villageQuery({ time: this.yearId }).then(res => {
				const { result = [] } = res
				this.tableData = result
			})
		},
		//导出
		handleExport() {
			const baseUrl = `${location.origin}/api`
			let url = baseUrl + '/waterPlat/fillData/village/export?time=' + this.yearId
			// this.$moment(this.date).format('YYYY')
			exportFile(url)
			// var alink = document.createElement('a')
			// alink.href = url
			// alink.click()
		},
	},
}
</script>
<style scoped lang="less">
.record-list {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-form {
		margin-right: 24px;
		&-title {
			margin-right: 4px;
		}
		&-select {
			width: 160px;
			margin-right: 24px;
		}
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
}
</style>
