//! moment-timezone.js
//! version : 0.5.5
//! author : <PERSON>
//! license : MIT
//! github.com/moment/moment-timezone

(function (root, factory) {
  "use strict";

  /*global define*/
  if (typeof define === 'function' && define.amd) {
    define(['moment'], factory);                 // AMD
  } else if (typeof module === 'object' && module.exports) {
    module.exports = factory(require('moment')); // Node
  } else {
    factory(root.moment);                        // Browser
  }
}(this, function (moment) {
  "use strict";

  // Do not load moment-timezone a second time.
  if (moment.tz !== undefined) {
    logError('Moment Timezone ' + moment.tz.version + ' was already loaded ' + (moment.tz.dataVersion ? 'with data from ' : 'without any data') + moment.tz.dataVersion);
    return moment;
  }

  var VERSION = "0.5.5",
    zones = {},
    links = {},
    names = {},
    guesses = {},
    cachedGuess,

    momentVersion = moment.version.split('.'),
    major = +momentVersion[0],
    minor = +momentVersion[1];

  // Moment.js version check
  if (major < 2 || (major === 2 && minor < 6)) {
    logError('Moment Timezone requires Moment.js >= 2.6.0. You are using Moment.js ' + moment.version + '. See momentjs.com');
  }

  /************************************
    Unpacking
  ************************************/

  function charCodeToInt(charCode) {
    if (charCode > 96) {
      return charCode - 87;
    } else if (charCode > 64) {
      return charCode - 29;
    }
    return charCode - 48;
  }

  function unpackBase60(string) {
    var i = 0,
      parts = string.split('.'),
      whole = parts[0],
      fractional = parts[1] || '',
      multiplier = 1,
      num,
      out = 0,
      sign = 1;

    // handle negative numbers
    if (string.charCodeAt(0) === 45) {
      i = 1;
      sign = -1;
    }

    // handle digits before the decimal
    for (i; i < whole.length; i++) {
      num = charCodeToInt(whole.charCodeAt(i));
      out = 60 * out + num;
    }

    // handle digits after the decimal
    for (i = 0; i < fractional.length; i++) {
      multiplier = multiplier / 60;
      num = charCodeToInt(fractional.charCodeAt(i));
      out += num * multiplier;
    }

    return out * sign;
  }

  function arrayToInt (array) {
    for (var i = 0; i < array.length; i++) {
      array[i] = unpackBase60(array[i]);
    }
  }

  function intToUntil (array, length) {
    for (var i = 0; i < length; i++) {
      array[i] = Math.round((array[i - 1] || 0) + (array[i] * 60000)); // minutes to milliseconds
    }

    array[length - 1] = Infinity;
  }

  function mapIndices (source, indices) {
    var out = [], i;

    for (i = 0; i < indices.length; i++) {
      out[i] = source[indices[i]];
    }

    return out;
  }

  function unpack (string) {
    var data = string.split('|'),
      offsets = data[2].split(' '),
      indices = data[3].split(''),
      untils  = data[4].split(' ');

    arrayToInt(offsets);
    arrayToInt(indices);
    arrayToInt(untils);

    intToUntil(untils, indices.length);

    return {
      name       : data[0],
      abbrs      : mapIndices(data[1].split(' '), indices),
      offsets    : mapIndices(offsets, indices),
      untils     : untils,
      population : data[5] | 0
    };
  }

  /************************************
    Zone object
  ************************************/

  function Zone (packedString) {
    if (packedString) {
      this._set(unpack(packedString));
    }
  }

  Zone.prototype = {
    _set : function (unpacked) {
      this.name       = unpacked.name;
      this.abbrs      = unpacked.abbrs;
      this.untils     = unpacked.untils;
      this.offsets    = unpacked.offsets;
      this.population = unpacked.population;
    },

    _index : function (timestamp) {
      var target = +timestamp,
        untils = this.untils,
        i;

      for (i = 0; i < untils.length; i++) {
        if (target < untils[i]) {
          return i;
        }
      }
    },

    parse : function (timestamp) {
      var target  = +timestamp,
        offsets = this.offsets,
        untils  = this.untils,
        max     = untils.length - 1,
        offset, offsetNext, offsetPrev, i;

      for (i = 0; i < max; i++) {
        offset     = offsets[i];
        offsetNext = offsets[i + 1];
        offsetPrev = offsets[i ? i - 1 : i];

        if (offset < offsetNext && tz.moveAmbiguousForward) {
          offset = offsetNext;
        } else if (offset > offsetPrev && tz.moveInvalidForward) {
          offset = offsetPrev;
        }

        if (target < untils[i] - (offset * 60000)) {
          return offsets[i];
        }
      }

      return offsets[max];
    },

    abbr : function (mom) {
      return this.abbrs[this._index(mom)];
    },

    offset : function (mom) {
      return this.offsets[this._index(mom)];
    }
  };

  /************************************
    Current Timezone
  ************************************/

  function OffsetAt(at) {
    var timeString = at.toTimeString();
    var abbr = timeString.match(/\([a-z ]+\)/i);
    if (abbr && abbr[0]) {
      // 17:56:31 GMT-0600 (CST)
      // 17:56:31 GMT-0600 (Central Standard Time)
      abbr = abbr[0].match(/[A-Z]/g);
      abbr = abbr ? abbr.join('') : undefined;
    } else {
      // 17:56:31 CST
      // 17:56:31 GMT+0800 (台北標準時間)
      abbr = timeString.match(/[A-Z]{3,5}/g);
      abbr = abbr ? abbr[0] : undefined;
    }

    if (abbr === 'GMT') {
      abbr = undefined;
    }

    this.at = +at;
    this.abbr = abbr;
    this.offset = at.getTimezoneOffset();
  }

  function ZoneScore(zone) {
    this.zone = zone;
    this.offsetScore = 0;
    this.abbrScore = 0;
  }

  ZoneScore.prototype.scoreOffsetAt = function (offsetAt) {
    this.offsetScore += Math.abs(this.zone.offset(offsetAt.at) - offsetAt.offset);
    if (this.zone.abbr(offsetAt.at).replace(/[^A-Z]/g, '') !== offsetAt.abbr) {
      this.abbrScore++;
    }
  };

  function findChange(low, high) {
    var mid, diff;

    while ((diff = ((high.at - low.at) / 12e4 | 0) * 6e4)) {
      mid = new OffsetAt(new Date(low.at + diff));
      if (mid.offset === low.offset) {
        low = mid;
      } else {
        high = mid;
      }
    }

    return low;
  }

  function userOffsets() {
    var startYear = new Date().getFullYear() - 2,
      last = new OffsetAt(new Date(startYear, 0, 1)),
      offsets = [last],
      change, next, i;

    for (i = 1; i < 48; i++) {
      next = new OffsetAt(new Date(startYear, i, 1));
      if (next.offset !== last.offset) {
        change = findChange(last, next);
        offsets.push(change);
        offsets.push(new OffsetAt(new Date(change.at + 6e4)));
      }
      last = next;
    }

    for (i = 0; i < 4; i++) {
      offsets.push(new OffsetAt(new Date(startYear + i, 0, 1)));
      offsets.push(new OffsetAt(new Date(startYear + i, 6, 1)));
    }

    return offsets;
  }

  function sortZoneScores (a, b) {
    if (a.offsetScore !== b.offsetScore) {
      return a.offsetScore - b.offsetScore;
    }
    if (a.abbrScore !== b.abbrScore) {
      return a.abbrScore - b.abbrScore;
    }
    return b.zone.population - a.zone.population;
  }

  function addToGuesses (name, offsets) {
    var i, offset;
    arrayToInt(offsets);
    for (i = 0; i < offsets.length; i++) {
      offset = offsets[i];
      guesses[offset] = guesses[offset] || {};
      guesses[offset][name] = true;
    }
  }

  function guessesForUserOffsets (offsets) {
    var offsetsLength = offsets.length,
      filteredGuesses = {},
      out = [],
      i, j, guessesOffset;

    for (i = 0; i < offsetsLength; i++) {
      guessesOffset = guesses[offsets[i].offset] || {};
      for (j in guessesOffset) {
        if (guessesOffset.hasOwnProperty(j)) {
          filteredGuesses[j] = true;
        }
      }
    }

    for (i in filteredGuesses) {
      if (filteredGuesses.hasOwnProperty(i)) {
        out.push(names[i]);
      }
    }

    return out;
  }

  function rebuildGuess () {

    // use Intl API when available and returning valid time zone
    try {
      var intlName = Intl.DateTimeFormat().resolvedOptions().timeZone;
      if (intlName){
        var name = names[normalizeName(intlName)];
        if (name) {
          return name;
        }
        logError("Moment Timezone found " + intlName + " from the Intl api, but did not have that data loaded.");
      }
    } catch (e) {
      // Intl unavailable, fall back to manual guessing.
    }

    var offsets = userOffsets(),
      offsetsLength = offsets.length,
      guesses = guessesForUserOffsets(offsets),
      zoneScores = [],
      zoneScore, i, j;

    for (i = 0; i < guesses.length; i++) {
      zoneScore = new ZoneScore(getZone(guesses[i]), offsetsLength);
      for (j = 0; j < offsetsLength; j++) {
        zoneScore.scoreOffsetAt(offsets[j]);
      }
      zoneScores.push(zoneScore);
    }

    zoneScores.sort(sortZoneScores);

    return zoneScores.length > 0 ? zoneScores[0].zone.name : undefined;
  }

  function guess (ignoreCache) {
    if (!cachedGuess || ignoreCache) {
      cachedGuess = rebuildGuess();
    }
    return cachedGuess;
  }

  /************************************
    Global Methods
  ************************************/

  function normalizeName (name) {
    return (name || '').toLowerCase().replace(/\//g, '_');
  }

  function addZone (packed) {
    var i, name, split, normalized;

    if (typeof packed === "string") {
      packed = [packed];
    }

    for (i = 0; i < packed.length; i++) {
      split = packed[i].split('|');
      name = split[0];
      normalized = normalizeName(name);
      zones[normalized] = packed[i];
      names[normalized] = name;
      if (split[5]) {
        addToGuesses(normalized, split[2].split(' '));
      }
    }
  }

  function getZone (name, caller) {
    name = normalizeName(name);

    var zone = zones[name];
    var link;

    if (zone instanceof Zone) {
      return zone;
    }

    if (typeof zone === 'string') {
      zone = new Zone(zone);
      zones[name] = zone;
      return zone;
    }

    // Pass getZone to prevent recursion more than 1 level deep
    if (links[name] && caller !== getZone && (link = getZone(links[name], getZone))) {
      zone = zones[name] = new Zone();
      zone._set(link);
      zone.name = names[name];
      return zone;
    }

    return null;
  }

  function getNames () {
    var i, out = [];

    for (i in names) {
      if (names.hasOwnProperty(i) && (zones[i] || zones[links[i]]) && names[i]) {
        out.push(names[i]);
      }
    }

    return out.sort();
  }

  function addLink (aliases) {
    var i, alias, normal0, normal1;

    if (typeof aliases === "string") {
      aliases = [aliases];
    }

    for (i = 0; i < aliases.length; i++) {
      alias = aliases[i].split('|');

      normal0 = normalizeName(alias[0]);
      normal1 = normalizeName(alias[1]);

      links[normal0] = normal1;
      names[normal0] = alias[0];

      links[normal1] = normal0;
      names[normal1] = alias[1];
    }
  }

  function loadData (data) {
    addZone(data.zones);
    addLink(data.links);
    tz.dataVersion = data.version;
  }

  function zoneExists (name) {
    if (!zoneExists.didShowError) {
      zoneExists.didShowError = true;
        logError("moment.tz.zoneExists('" + name + "') has been deprecated in favor of !moment.tz.zone('" + name + "')");
    }
    return !!getZone(name);
  }

  function needsOffset (m) {
    return !!(m._a && (m._tzm === undefined));
  }

  function logError (message) {
    if (typeof console !== 'undefined' && typeof console.error === 'function') {
      console.error(message);
    }
  }

  /************************************
    moment.tz namespace
  ************************************/

  function tz (input) {
    var args = Array.prototype.slice.call(arguments, 0, -1),
      name = arguments[arguments.length - 1],
      zone = getZone(name),
      out  = moment.utc.apply(null, args);

    if (zone && !moment.isMoment(input) && needsOffset(out)) {
      out.add(zone.parse(out), 'minutes');
    }

    out.tz(name);

    return out;
  }

  tz.version      = VERSION;
  tz.dataVersion  = '';
  tz._zones       = zones;
  tz._links       = links;
  tz._names       = names;
  tz.add          = addZone;
  tz.link         = addLink;
  tz.load         = loadData;
  tz.zone         = getZone;
  tz.zoneExists   = zoneExists; // deprecated in 0.1.0
  tz.guess        = guess;
  tz.names        = getNames;
  tz.Zone         = Zone;
  tz.unpack       = unpack;
  tz.unpackBase60 = unpackBase60;
  tz.needsOffset  = needsOffset;
  tz.moveInvalidForward   = true;
  tz.moveAmbiguousForward = false;

  /************************************
    Interface with Moment.js
  ************************************/

  var fn = moment.fn;

  moment.tz = tz;

  moment.defaultZone = null;

  moment.updateOffset = function (mom, keepTime) {
    var zone = moment.defaultZone,
      offset;

    if (mom._z === undefined) {
      if (zone && needsOffset(mom) && !mom._isUTC) {
        mom._d = moment.utc(mom._a)._d;
        mom.utc().add(zone.parse(mom), 'minutes');
      }
      mom._z = zone;
    }
    if (mom._z) {
      offset = mom._z.offset(mom);
      if (Math.abs(offset) < 16) {
        offset = offset / 60;
      }
      if (mom.utcOffset !== undefined) {
        mom.utcOffset(-offset, keepTime);
      } else {
        mom.zone(offset, keepTime);
      }
    }
  };

  fn.tz = function (name) {
    if (name) {
      this._z = getZone(name);
      if (this._z) {
        moment.updateOffset(this);
      } else {
        logError("Moment Timezone has no data for " + name + ". See http://momentjs.com/timezone/docs/#/data-loading/.");
      }
      return this;
    }
    if (this._z) { return this._z.name; }
  };

  function abbrWrap (old) {
    return function () {
      if (this._z) { return this._z.abbr(this); }
      return old.call(this);
    };
  }

  function resetZoneWrap (old) {
    return function () {
      this._z = null;
      return old.apply(this, arguments);
    };
  }

  fn.zoneName = abbrWrap(fn.zoneName);
  fn.zoneAbbr = abbrWrap(fn.zoneAbbr);
  fn.utc      = resetZoneWrap(fn.utc);

  moment.tz.setDefault = function(name) {
    if (major < 2 || (major === 2 && minor < 9)) {
      logError('Moment Timezone setDefault() requires Moment.js >= 2.9.0. You are using Moment.js ' + moment.version + '.');
    }
    moment.defaultZone = name ? getZone(name) : null;
    return moment;
  };

  // Cloning a moment should include the _z property.
  var momentProperties = moment.momentProperties;
  if (Object.prototype.toString.call(momentProperties) === '[object Array]') {
    // moment 2.8.1+
    momentProperties.push('_z');
    momentProperties.push('_a');
  } else if (momentProperties) {
    // moment 2.7.0
    momentProperties._z = null;
  }

  loadData({
    "version": "2016f",
    "zones": [
      "Africa/Abidjan|GMT|0|0||48e5",
      "Africa/Khartoum|EAT|-30|0||51e5",
      "Africa/Algiers|CET|-10|0||26e5",
      "Africa/Lagos|WAT|-10|0||17e6",
      "Africa/Maputo|CAT|-20|0||26e5",
      "Africa/Cairo|EET EEST|-20 -30|010101010|1Cby0 Fb0 c10 8n0 8Nd0 gL0 e10 mn0|15e6",
      "Africa/Casablanca|WET WEST|0 -10|01010101010101010101010101010101010101010|1Cco0 Db0 1zd0 Lz0 1Nf0 wM0 co0 go0 1o00 s00 dA0 vc0 11A0 A00 e00 y00 11A0 uM0 e00 Dc0 11A0 s00 e00 IM0 WM0 mo0 gM0 LA0 WM0 jA0 e00 Rc0 11A0 e00 e00 U00 11A0 8o0 e00 11A0|32e5",
      "Europe/Paris|CET CEST|-10 -20|01010101010101010101010|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|11e6",
      "Africa/Johannesburg|SAST|-20|0||84e5",
      "Africa/Tripoli|EET CET CEST|-20 -10 -20|0120|1IlA0 TA0 1o00|11e5",
      "Africa/Windhoek|WAST WAT|-20 -10|01010101010101010101010|1C1c0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 11B0|32e4",
      "America/Adak|HST HDT|a0 90|01010101010101010101010|1BR00 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|326",
      "America/Anchorage|AKST AKDT|90 80|01010101010101010101010|1BQX0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|30e4",
      "America/Santo_Domingo|AST|40|0||29e5",
      "America/Araguaina|BRT BRST|30 20|010|1IdD0 Lz0|14e4",
      "America/Argentina/Buenos_Aires|ART|30|0|",
      "America/Asuncion|PYST PYT|30 40|01010101010101010101010|1C430 1a10 1fz0 1a10 1fz0 1cN0 17b0 1ip0 17b0 1ip0 17b0 1ip0 19X0 1fB0 19X0 1fB0 19X0 1ip0 17b0 1ip0 17b0 1ip0|28e5",
      "America/Panama|EST|50|0||15e5",
      "America/Bahia|BRT BRST|30 20|010|1FJf0 Rb0|27e5",
      "America/Bahia_Banderas|MST CDT CST|70 50 60|01212121212121212121212|1C1l0 1nW0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0|84e3",
      "America/Fortaleza|BRT|30|0||34e5",
      "America/Managua|CST|60|0||22e5",
      "America/Manaus|AMT|40|0||19e5",
      "America/Bogota|COT|50|0||90e5",
      "America/Denver|MST MDT|70 60|01010101010101010101010|1BQV0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|26e5",
      "America/Campo_Grande|AMST AMT|30 40|01010101010101010101010|1BIr0 1zd0 On0 1zd0 Rb0 1zd0 Lz0 1C10 Lz0 1C10 On0 1zd0 On0 1zd0 On0 1zd0 On0 1C10 Lz0 1C10 Lz0 1C10|77e4",
      "America/Cancun|CST CDT EST|60 50 50|010101010102|1C1k0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0 Dd0|63e4",
      "America/Caracas|VET VET|4u 40|01|1QMT0|29e5",
      "America/Cayenne|GFT|30|0||58e3",
      "America/Chicago|CST CDT|60 50|01010101010101010101010|1BQU0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|92e5",
      "America/Chihuahua|MST MDT|70 60|01010101010101010101010|1C1l0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0|81e4",
      "America/Phoenix|MST|70|0||42e5",
      "America/Los_Angeles|PST PDT|80 70|01010101010101010101010|1BQW0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|15e6",
      "America/New_York|EST EDT|50 40|01010101010101010101010|1BQT0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|21e6",
      "America/Rio_Branco|AMT ACT|40 50|01|1KLE0|31e4",
      "America/Fort_Nelson|PST PDT MST|80 70 70|010101010102|1BQW0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0|39e2",
      "America/Halifax|AST ADT|40 30|01010101010101010101010|1BQS0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|39e4",
      "America/Godthab|WGT WGST|30 20|01010101010101010101010|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|17e3",
      "America/Goose_Bay|AST ADT|40 30|01010101010101010101010|1BQQ1 1zb0 Op0 1zcX Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|76e2",
      "America/Grand_Turk|EST EDT AST|50 40 40|0101010101012|1BQT0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|37e2",
      "America/Guayaquil|ECT|50|0||27e5",
      "America/Guyana|GYT|40|0||80e4",
      "America/Havana|CST CDT|50 40|01010101010101010101010|1BQR0 1wo0 U00 1zc0 U00 1qM0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Rc0 1zc0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0|21e5",
      "America/La_Paz|BOT|40|0||19e5",
      "America/Lima|PET|50|0||11e6",
      "America/Mexico_City|CST CDT|60 50|01010101010101010101010|1C1k0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0|20e6",
      "America/Metlakatla|PST AKST AKDT|80 90 80|012121212121|1PAa0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|14e2",
      "America/Miquelon|PMST PMDT|30 20|01010101010101010101010|1BQR0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|61e2",
      "America/Montevideo|UYST UYT|20 30|010101010101|1BQQ0 1ld0 14n0 1ld0 14n0 1o10 11z0 1o10 11z0 1o10 11z0|17e5",
      "America/Noronha|FNT|20|0||30e2",
      "America/North_Dakota/Beulah|MST MDT CST CDT|70 60 60 50|01232323232323232323232|1BQV0 1zb0 Oo0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0",
      "America/Paramaribo|SRT|30|0||24e4",
      "America/Port-au-Prince|EST EDT|50 40|010101010|1GI70 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|23e5",
      "America/Santiago|CLST CLT|30 40|010101010101010101010|1C1f0 1fB0 1nX0 G10 1EL0 Op0 1zb0 Rd0 1wn0 Rd0 46n0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1Nb0 Ap0|62e5",
      "America/Sao_Paulo|BRST BRT|20 30|01010101010101010101010|1BIq0 1zd0 On0 1zd0 Rb0 1zd0 Lz0 1C10 Lz0 1C10 On0 1zd0 On0 1zd0 On0 1zd0 On0 1C10 Lz0 1C10 Lz0 1C10|20e6",
      "America/Scoresbysund|EGT EGST|10 0|01010101010101010101010|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|452",
      "America/St_Johns|NST NDT|3u 2u|01010101010101010101010|1BQPv 1zb0 Op0 1zcX Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|11e4",
      "Antarctica/Casey|CAST AWST|-b0 -80|0101|1BN30 40P0 KL0|10",
      "Antarctica/Davis|DAVT DAVT|-50 -70|0101|1BPw0 3Wn0 KN0|70",
      "Antarctica/DumontDUrville|DDUT|-a0|0||80",
      "Antarctica/Macquarie|AEDT MIST|-b0 -b0|01|1C140|1",
      "Antarctica/Mawson|MAWT|-50|0||60",
      "Pacific/Auckland|NZDT NZST|-d0 -c0|01010101010101010101010|1C120 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00|14e5",
      "Antarctica/Rothera|ROTT|30|0||130",
      "Antarctica/Syowa|SYOT|-30|0||20",
      "Antarctica/Troll|UTC CEST|0 -20|01010101010101010101010|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|40",
      "Antarctica/Vostok|VOST|-60|0||25",
      "Asia/Baghdad|AST|-30|0||66e5",
      "Asia/Almaty|+06|-60|0||15e5",
      "Asia/Amman|EET EEST|-20 -30|010101010101010101010|1BVy0 1qM0 11A0 1o00 11A0 4bX0 Dd0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0|25e5",
      "Asia/Anadyr|ANAT ANAST ANAT|-c0 -c0 -b0|0120|1BWe0 1qN0 WM0|13e3",
      "Asia/Aqtobe|+05|-50|0||27e4",
      "Asia/Ashgabat|TMT|-50|0||41e4",
      "Asia/Baku|AZT AZST|-40 -50|0101010101010|1BWo0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00|27e5",
      "Asia/Bangkok|ICT|-70|0||15e6",
      "Asia/Barnaul|+06 +07|-60 -70|010101|1BWk0 1qM0 WM0 8Hz0 3rd0",
      "Asia/Beirut|EET EEST|-20 -30|01010101010101010101010|1BWm0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0|22e5",
      "Asia/Bishkek|KGT|-60|0||87e4",
      "Asia/Brunei|BNT|-80|0||42e4",
      "Asia/Kolkata|IST|-5u|0||15e6",
      "Asia/Chita|YAKT YAKST YAKT IRKT|-90 -a0 -a0 -80|010230|1BWh0 1qM0 WM0 8Hz0 3re0|33e4",
      "Asia/Choibalsan|CHOT CHOST|-80 -90|0101010101010|1O8G0 1cJ0 1cP0 1cJ0 1cP0 1fx0 1cP0 1cJ0 1cP0 1cJ0 1cP0 1cJ0|38e3",
      "Asia/Shanghai|CST|-80|0||23e6",
      "Asia/Dhaka|BDT|-60|0||16e6",
      "Asia/Damascus|EET EEST|-20 -30|01010101010101010101010|1C0m0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0|26e5",
      "Asia/Dili|TLT|-90|0||19e4",
      "Asia/Dubai|GST|-40|0||39e5",
      "Asia/Dushanbe|TJT|-50|0||76e4",
      "Asia/Gaza|EET EEST|-20 -30|01010101010101010101010|1BVW1 SKX 1xd1 MKX 1AN0 1a00 1fA0 1cL0 1cN0 1nX0 1210 1nz0 1220 1ny0 1220 1qm0 1220 1ny0 1220 1ny0 1220 1ny0|18e5",
      "Asia/Hebron|EET EEST|-20 -30|0101010101010101010101010|1BVy0 Tb0 1xd1 MKX bB0 cn0 1cN0 1a00 1fA0 1cL0 1cN0 1nX0 1210 1nz0 1220 1ny0 1220 1qm0 1220 1ny0 1220 1ny0 1220 1ny0|25e4",
      "Asia/Hong_Kong|HKT|-80|0||73e5",
      "Asia/Hovd|HOVT HOVST|-70 -80|0101010101010|1O8H0 1cJ0 1cP0 1cJ0 1cP0 1fx0 1cP0 1cJ0 1cP0 1cJ0 1cP0 1cJ0|81e3",
      "Asia/Irkutsk|IRKT IRKST IRKT|-80 -90 -90|01020|1BWi0 1qM0 WM0 8Hz0|60e4",
      "Europe/Istanbul|EET EEST|-20 -30|01010101010101010101010|1BWp0 1qM0 Xc0 1qo0 WM0 1qM0 11A0 1o00 1200 1nA0 11A0 1tA0 U00 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|13e6",
      "Asia/Jakarta|WIB|-70|0||31e6",
      "Asia/Jayapura|WIT|-90|0||26e4",
      "Asia/Jerusalem|IST IDT|-20 -30|01010101010101010101010|1BVA0 17X0 1kp0 1dz0 1c10 1aL0 1eN0 1oL0 10N0 1oL0 10N0 1oL0 10N0 1rz0 W10 1rz0 W10 1rz0 10N0 1oL0 10N0 1oL0|81e4",
      "Asia/Kabul|AFT|-4u|0||46e5",
      "Asia/Kamchatka|PETT PETST PETT|-c0 -c0 -b0|0120|1BWe0 1qN0 WM0|18e4",
      "Asia/Karachi|PKT|-50|0||24e6",
      "Asia/Urumqi|XJT|-60|0||32e5",
      "Asia/Kathmandu|NPT|-5J|0||12e5",
      "Asia/Khandyga|VLAT VLAST VLAT YAKT YAKT|-a0 -b0 -b0 -a0 -90|010234|1BWg0 1qM0 WM0 17V0 7zD0|66e2",
      "Asia/Krasnoyarsk|KRAT KRAST KRAT|-70 -80 -80|01020|1BWj0 1qM0 WM0 8Hz0|10e5",
      "Asia/Kuala_Lumpur|MYT|-80|0||71e5",
      "Asia/Magadan|MAGT MAGST MAGT MAGT|-b0 -c0 -c0 -a0|010230|1BWf0 1qM0 WM0 8Hz0 3Cq0|95e3",
      "Asia/Makassar|WITA|-80|0||15e5",
      "Asia/Manila|PHT|-80|0||24e6",
      "Europe/Athens|EET EEST|-20 -30|01010101010101010101010|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|35e5",
      "Asia/Novokuznetsk|+07 +06|-70 -60|010|1Dp80 WM0|55e4",
      "Asia/Novosibirsk|+06 +07|-60 -70|010101|1BWk0 1qM0 WM0 8Hz0 4eN0|15e5",
      "Asia/Omsk|OMST OMSST OMST|-60 -70 -70|01020|1BWk0 1qM0 WM0 8Hz0|12e5",
      "Asia/Pyongyang|KST KST|-90 -8u|01|1P4D0|29e5",
      "Asia/Rangoon|MMT|-6u|0||48e5",
      "Asia/Sakhalin|SAKT SAKST SAKT|-a0 -b0 -b0|010202|1BWg0 1qM0 WM0 8Hz0 3rd0|58e4",
      "Asia/Tashkent|UZT|-50|0||23e5",
      "Asia/Seoul|KST|-90|0||23e6",
      "Asia/Singapore|SGT|-80|0||56e5",
      "Asia/Srednekolymsk|MAGT MAGST MAGT SRET|-b0 -c0 -c0 -b0|01023|1BWf0 1qM0 WM0 8Hz0|35e2",
      "Asia/Tbilisi|GET|-40|0||11e5",
      "Asia/Tehran|IRST IRDT|-3u -4u|01010101010101010101010|1BTUu 1dz0 1cp0 1dz0 1cp0 1dz0 1cN0 1dz0 1cp0 1dz0 1cp0 1dz0 1cp0 1dz0 1cN0 1dz0 1cp0 1dz0 1cp0 1dz0 1cp0 1dz0|14e6",
      "Asia/Thimphu|BTT|-60|0||79e3",
      "Asia/Tokyo|JST|-90|0||38e6",
      "Asia/Tomsk|+06 +07|-60 -70|010101|1BWk0 1qM0 WM0 8Hz0 3Qp0|10e5",
      "Asia/Ulaanbaatar|ULAT ULAST|-80 -90|0101010101010|1O8G0 1cJ0 1cP0 1cJ0 1cP0 1fx0 1cP0 1cJ0 1cP0 1cJ0 1cP0 1cJ0|12e5",
      "Asia/Ust-Nera|MAGT MAGST MAGT VLAT VLAT|-b0 -c0 -c0 -b0 -a0|010234|1BWf0 1qM0 WM0 17V0 7zD0|65e2",
      "Asia/Vladivostok|VLAT VLAST VLAT|-a0 -b0 -b0|01020|1BWg0 1qM0 WM0 8Hz0|60e4",
      "Asia/Yakutsk|YAKT YAKST YAKT|-90 -a0 -a0|01020|1BWh0 1qM0 WM0 8Hz0|28e4",
      "Asia/Yekaterinburg|YEKT YEKST YEKT|-50 -60 -60|01020|1BWl0 1qM0 WM0 8Hz0|14e5",
      "Asia/Yerevan|AMT AMST|-40 -50|01010|1BWm0 1qM0 WM0 1qM0|13e5",
      "Atlantic/Azores|AZOT AZOST|10 0|01010101010101010101010|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|25e4",
      "Europe/Lisbon|WET WEST|0 -10|01010101010101010101010|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|27e5",
      "Atlantic/Cape_Verde|CVT|10|0||50e4",
      "Atlantic/South_Georgia|GST|20|0||30",
      "Atlantic/Stanley|FKST FKT|30 40|010|1C6R0 U10|21e2",
      "Australia/Sydney|AEDT AEST|-b0 -a0|01010101010101010101010|1C140 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0 1cM0|40e5",
      "Australia/Adelaide|ACDT ACST|-au -9u|01010101010101010101010|1C14u 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0 1cM0|11e5",
      "Australia/Brisbane|AEST|-a0|0||20e5",
      "Australia/Darwin|ACST|-9u|0||12e4",
      "Australia/Eucla|ACWST|-8J|0||368",
      "Australia/Lord_Howe|LHDT LHST|-b0 -au|01010101010101010101010|1C130 1cMu 1cLu 1cMu 1cLu 1fAu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1fAu 1cLu 1cMu 1cLu 1cMu|347",
      "Australia/Perth|AWST|-80|0||18e5",
      "Pacific/Easter|EASST EAST|50 60|010101010101010101010|1C1f0 1fB0 1nX0 G10 1EL0 Op0 1zb0 Rd0 1wn0 Rd0 46n0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1Nb0 Ap0|30e2",
      "Europe/Dublin|GMT IST|0 -10|01010101010101010101010|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|12e5",
      "Etc/GMT+1|GMT+1|10|0|",
      "Etc/GMT+10|GMT+10|a0|0|",
      "Etc/GMT+11|GMT+11|b0|0|",
      "Etc/GMT+12|GMT+12|c0|0|",
      "Etc/GMT+2|GMT+2|20|0|",
      "Etc/GMT+3|GMT+3|30|0|",
      "Etc/GMT+4|GMT+4|40|0|",
      "Etc/GMT+5|GMT+5|50|0|",
      "Etc/GMT+6|GMT+6|60|0|",
      "Etc/GMT+7|GMT+7|70|0|",
      "Etc/GMT+8|GMT+8|80|0|",
      "Etc/GMT+9|GMT+9|90|0|",
      "Etc/GMT-1|GMT-1|-10|0|",
      "Etc/GMT-10|GMT-10|-a0|0|",
      "Etc/GMT-11|GMT-11|-b0|0|",
      "Etc/GMT-12|GMT-12|-c0|0|",
      "Etc/GMT-13|GMT-13|-d0|0|",
      "Etc/GMT-14|GMT-14|-e0|0|",
      "Etc/GMT-2|GMT-2|-20|0|",
      "Etc/GMT-3|GMT-3|-30|0|",
      "Etc/GMT-4|GMT-4|-40|0|",
      "Etc/GMT-5|GMT-5|-50|0|",
      "Etc/GMT-6|GMT-6|-60|0|",
      "Etc/GMT-7|GMT-7|-70|0|",
      "Etc/GMT-8|GMT-8|-80|0|",
      "Etc/GMT-9|GMT-9|-90|0|",
      "Etc/UCT|UCT|0|0|",
      "Etc/UTC|UTC|0|0|",
      "Europe/Astrakhan|+03 +04|-30 -40|010101|1BWn0 1qM0 WM0 8Hz0 3rd0",
      "Europe/London|GMT BST|0 -10|01010101010101010101010|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|10e6",
      "Europe/Chisinau|EET EEST|-20 -30|01010101010101010101010|1BWo0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|67e4",
      "Europe/Kaliningrad|EET EEST FET|-20 -30 -30|01020|1BWo0 1qM0 WM0 8Hz0|44e4",
      "Europe/Kirov|+03 +04|-30 -40|01010|1BWn0 1qM0 WM0 8Hz0|48e4",
      "Europe/Minsk|EET EEST FET MSK|-20 -30 -30 -30|01023|1BWo0 1qM0 WM0 8Hy0|19e5",
      "Europe/Moscow|MSK MSD MSK|-30 -40 -40|01020|1BWn0 1qM0 WM0 8Hz0|16e6",
      "Europe/Samara|SAMT SAMST SAMT|-40 -40 -30|0120|1BWm0 1qN0 WM0|12e5",
      "Europe/Simferopol|EET EEST MSK MSK|-20 -30 -40 -30|01010101023|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11z0 1nW0|33e4",
      "Pacific/Honolulu|HST|a0|0||37e4",
      "Indian/Chagos|IOT|-60|0||30e2",
      "Indian/Christmas|CXT|-70|0||21e2",
      "Indian/Cocos|CCT|-6u|0||596",
      "Indian/Kerguelen|TFT|-50|0||130",
      "Indian/Mahe|SCT|-40|0||79e3",
      "Indian/Maldives|MVT|-50|0||35e4",
      "Indian/Mauritius|MUT|-40|0||15e4",
      "Indian/Reunion|RET|-40|0||84e4",
      "Pacific/Majuro|MHT|-c0|0||28e3",
      "MET|MET MEST|-10 -20|01010101010101010101010|1BWp0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00",
      "Pacific/Chatham|CHADT CHAST|-dJ -cJ|01010101010101010101010|1C120 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00|600",
      "Pacific/Apia|SST SDT WSDT WSST|b0 a0 -e0 -d0|01012323232323232323232|1Dbn0 1ff0 1a00 CI0 AQ0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00|37e3",
      "Pacific/Bougainville|PGT BST|-a0 -b0|01|1NwE0|18e4",
      "Pacific/Chuuk|CHUT|-a0|0||49e3",
      "Pacific/Efate|VUT|-b0|0||66e3",
      "Pacific/Enderbury|PHOT|-d0|0||1",
      "Pacific/Fakaofo|TKT TKT|b0 -d0|01|1Gfn0|483",
      "Pacific/Fiji|FJST FJT|-d0 -c0|01010101010101010101010|1BWe0 1o00 Rc0 1wo0 Ao0 1Nc0 Ao0 1Q00 xz0 1SN0 uM0 1SM0 uM0 1VA0 s00 1VA0 uM0 1SM0 uM0 1SM0 uM0 1SM0|88e4",
      "Pacific/Funafuti|TVT|-c0|0||45e2",
      "Pacific/Galapagos|GALT|60|0||25e3",
      "Pacific/Gambier|GAMT|90|0||125",
      "Pacific/Guadalcanal|SBT|-b0|0||11e4",
      "Pacific/Guam|ChST|-a0|0||17e4",
      "Pacific/Kiritimati|LINT|-e0|0||51e2",
      "Pacific/Kosrae|KOST|-b0|0||66e2",
      "Pacific/Marquesas|MART|9u|0||86e2",
      "Pacific/Pago_Pago|SST|b0|0||37e2",
      "Pacific/Nauru|NRT|-c0|0||10e3",
      "Pacific/Niue|NUT|b0|0||12e2",
      "Pacific/Norfolk|NFT NFT|-bu -b0|01|1PoCu|25e4",
      "Pacific/Noumea|NCT|-b0|0||98e3",
      "Pacific/Palau|PWT|-90|0||21e3",
      "Pacific/Pitcairn|PST|80|0||56",
      "Pacific/Pohnpei|PONT|-b0|0||34e3",
      "Pacific/Port_Moresby|PGT|-a0|0||25e4",
      "Pacific/Rarotonga|CKT|a0|0||13e3",
      "Pacific/Tahiti|TAHT|a0|0||18e4",
      "Pacific/Tarawa|GILT|-c0|0||29e3",
      "Pacific/Tongatapu|TOT|-d0|0||75e3",
      "Pacific/Wake|WAKT|-c0|0||16e3",
      "Pacific/Wallis|WFT|-c0|0||94"
    ],
    "links": [
      "Africa/Abidjan|Africa/Accra",
      "Africa/Abidjan|Africa/Bamako",
      "Africa/Abidjan|Africa/Banjul",
      "Africa/Abidjan|Africa/Bissau",
      "Africa/Abidjan|Africa/Conakry",
      "Africa/Abidjan|Africa/Dakar",
      "Africa/Abidjan|Africa/Freetown",
      "Africa/Abidjan|Africa/Lome",
      "Africa/Abidjan|Africa/Monrovia",
      "Africa/Abidjan|Africa/Nouakchott",
      "Africa/Abidjan|Africa/Ouagadougou",
      "Africa/Abidjan|Africa/Sao_Tome",
      "Africa/Abidjan|Africa/Timbuktu",
      "Africa/Abidjan|America/Danmarkshavn",
      "Africa/Abidjan|Atlantic/Reykjavik",
      "Africa/Abidjan|Atlantic/St_Helena",
      "Africa/Abidjan|Etc/GMT",
      "Africa/Abidjan|Etc/GMT+0",
      "Africa/Abidjan|Etc/GMT-0",
      "Africa/Abidjan|Etc/GMT0",
      "Africa/Abidjan|Etc/Greenwich",
      "Africa/Abidjan|GMT",
      "Africa/Abidjan|GMT+0",
      "Africa/Abidjan|GMT-0",
      "Africa/Abidjan|GMT0",
      "Africa/Abidjan|Greenwich",
      "Africa/Abidjan|Iceland",
      "Africa/Algiers|Africa/Tunis",
      "Africa/Cairo|Egypt",
      "Africa/Casablanca|Africa/El_Aaiun",
      "Africa/Johannesburg|Africa/Maseru",
      "Africa/Johannesburg|Africa/Mbabane",
      "Africa/Khartoum|Africa/Addis_Ababa",
      "Africa/Khartoum|Africa/Asmara",
      "Africa/Khartoum|Africa/Asmera",
      "Africa/Khartoum|Africa/Dar_es_Salaam",
      "Africa/Khartoum|Africa/Djibouti",
      "Africa/Khartoum|Africa/Juba",
      "Africa/Khartoum|Africa/Kampala",
      "Africa/Khartoum|Africa/Mogadishu",
      "Africa/Khartoum|Africa/Nairobi",
      "Africa/Khartoum|Indian/Antananarivo",
      "Africa/Khartoum|Indian/Comoro",
      "Africa/Khartoum|Indian/Mayotte",
      "Africa/Lagos|Africa/Bangui",
      "Africa/Lagos|Africa/Brazzaville",
      "Africa/Lagos|Africa/Douala",
      "Africa/Lagos|Africa/Kinshasa",
      "Africa/Lagos|Africa/Libreville",
      "Africa/Lagos|Africa/Luanda",
      "Africa/Lagos|Africa/Malabo",
      "Africa/Lagos|Africa/Ndjamena",
      "Africa/Lagos|Africa/Niamey",
      "Africa/Lagos|Africa/Porto-Novo",
      "Africa/Maputo|Africa/Blantyre",
      "Africa/Maputo|Africa/Bujumbura",
      "Africa/Maputo|Africa/Gaborone",
      "Africa/Maputo|Africa/Harare",
      "Africa/Maputo|Africa/Kigali",
      "Africa/Maputo|Africa/Lubumbashi",
      "Africa/Maputo|Africa/Lusaka",
      "Africa/Tripoli|Libya",
      "America/Adak|America/Atka",
      "America/Adak|US/Aleutian",
      "America/Anchorage|America/Juneau",
      "America/Anchorage|America/Nome",
      "America/Anchorage|America/Sitka",
      "America/Anchorage|America/Yakutat",
      "America/Anchorage|US/Alaska",
      "America/Argentina/Buenos_Aires|America/Argentina/Catamarca",
      "America/Argentina/Buenos_Aires|America/Argentina/ComodRivadavia",
      "America/Argentina/Buenos_Aires|America/Argentina/Cordoba",
      "America/Argentina/Buenos_Aires|America/Argentina/Jujuy",
      "America/Argentina/Buenos_Aires|America/Argentina/La_Rioja",
      "America/Argentina/Buenos_Aires|America/Argentina/Mendoza",
      "America/Argentina/Buenos_Aires|America/Argentina/Rio_Gallegos",
      "America/Argentina/Buenos_Aires|America/Argentina/Salta",
      "America/Argentina/Buenos_Aires|America/Argentina/San_Juan",
      "America/Argentina/Buenos_Aires|America/Argentina/San_Luis",
      "America/Argentina/Buenos_Aires|America/Argentina/Tucuman",
      "America/Argentina/Buenos_Aires|America/Argentina/Ushuaia",
      "America/Argentina/Buenos_Aires|America/Buenos_Aires",
      "America/Argentina/Buenos_Aires|America/Catamarca",
      "America/Argentina/Buenos_Aires|America/Cordoba",
      "America/Argentina/Buenos_Aires|America/Jujuy",
      "America/Argentina/Buenos_Aires|America/Mendoza",
      "America/Argentina/Buenos_Aires|America/Rosario",
      "America/Campo_Grande|America/Cuiaba",
      "America/Chicago|America/Indiana/Knox",
      "America/Chicago|America/Indiana/Tell_City",
      "America/Chicago|America/Knox_IN",
      "America/Chicago|America/Matamoros",
      "America/Chicago|America/Menominee",
      "America/Chicago|America/North_Dakota/Center",
      "America/Chicago|America/North_Dakota/New_Salem",
      "America/Chicago|America/Rainy_River",
      "America/Chicago|America/Rankin_Inlet",
      "America/Chicago|America/Resolute",
      "America/Chicago|America/Winnipeg",
      "America/Chicago|CST6CDT",
      "America/Chicago|Canada/Central",
      "America/Chicago|US/Central",
      "America/Chicago|US/Indiana-Starke",
      "America/Chihuahua|America/Mazatlan",
      "America/Chihuahua|Mexico/BajaSur",
      "America/Denver|America/Boise",
      "America/Denver|America/Cambridge_Bay",
      "America/Denver|America/Edmonton",
      "America/Denver|America/Inuvik",
      "America/Denver|America/Ojinaga",
      "America/Denver|America/Shiprock",
      "America/Denver|America/Yellowknife",
      "America/Denver|Canada/Mountain",
      "America/Denver|MST7MDT",
      "America/Denver|Navajo",
      "America/Denver|US/Mountain",
      "America/Fortaleza|America/Belem",
      "America/Fortaleza|America/Maceio",
      "America/Fortaleza|America/Recife",
      "America/Fortaleza|America/Santarem",
      "America/Halifax|America/Glace_Bay",
      "America/Halifax|America/Moncton",
      "America/Halifax|America/Thule",
      "America/Halifax|Atlantic/Bermuda",
      "America/Halifax|Canada/Atlantic",
      "America/Havana|Cuba",
      "America/Los_Angeles|America/Dawson",
      "America/Los_Angeles|America/Ensenada",
      "America/Los_Angeles|America/Santa_Isabel",
      "America/Los_Angeles|America/Tijuana",
      "America/Los_Angeles|America/Vancouver",
      "America/Los_Angeles|America/Whitehorse",
      "America/Los_Angeles|Canada/Pacific",
      "America/Los_Angeles|Canada/Yukon",
      "America/Los_Angeles|Mexico/BajaNorte",
      "America/Los_Angeles|PST8PDT",
      "America/Los_Angeles|US/Pacific",
      "America/Los_Angeles|US/Pacific-New",
      "America/Managua|America/Belize",
      "America/Managua|America/Costa_Rica",
      "America/Managua|America/El_Salvador",
      "America/Managua|America/Guatemala",
      "America/Managua|America/Regina",
      "America/Managua|America/Swift_Current",
      "America/Managua|America/Tegucigalpa",
      "America/Managua|Canada/East-Saskatchewan",
      "America/Managua|Canada/Saskatchewan",
      "America/Manaus|America/Boa_Vista",
      "America/Manaus|America/Porto_Velho",
      "America/Manaus|Brazil/West",
      "America/Mexico_City|America/Merida",
      "America/Mexico_City|America/Monterrey",
      "America/Mexico_City|Mexico/General",
      "America/New_York|America/Detroit",
      "America/New_York|America/Fort_Wayne",
      "America/New_York|America/Indiana/Indianapolis",
      "America/New_York|America/Indiana/Marengo",
      "America/New_York|America/Indiana/Petersburg",
      "America/New_York|America/Indiana/Vevay",
      "America/New_York|America/Indiana/Vincennes",
      "America/New_York|America/Indiana/Winamac",
      "America/New_York|America/Indianapolis",
      "America/New_York|America/Iqaluit",
      "America/New_York|America/Kentucky/Louisville",
      "America/New_York|America/Kentucky/Monticello",
      "America/New_York|America/Louisville",
      "America/New_York|America/Montreal",
      "America/New_York|America/Nassau",
      "America/New_York|America/Nipigon",
      "America/New_York|America/Pangnirtung",
      "America/New_York|America/Thunder_Bay",
      "America/New_York|America/Toronto",
      "America/New_York|Canada/Eastern",
      "America/New_York|EST5EDT",
      "America/New_York|US/East-Indiana",
      "America/New_York|US/Eastern",
      "America/New_York|US/Michigan",
      "America/Noronha|Brazil/DeNoronha",
      "America/Panama|America/Atikokan",
      "America/Panama|America/Cayman",
      "America/Panama|America/Coral_Harbour",
      "America/Panama|America/Jamaica",
      "America/Panama|EST",
      "America/Panama|Jamaica",
      "America/Phoenix|America/Creston",
      "America/Phoenix|America/Dawson_Creek",
      "America/Phoenix|America/Hermosillo",
      "America/Phoenix|MST",
      "America/Phoenix|US/Arizona",
      "America/Rio_Branco|America/Eirunepe",
      "America/Rio_Branco|America/Porto_Acre",
      "America/Rio_Branco|Brazil/Acre",
      "America/Santiago|Antarctica/Palmer",
      "America/Santiago|Chile/Continental",
      "America/Santo_Domingo|America/Anguilla",
      "America/Santo_Domingo|America/Antigua",
      "America/Santo_Domingo|America/Aruba",
      "America/Santo_Domingo|America/Barbados",
      "America/Santo_Domingo|America/Blanc-Sablon",
      "America/Santo_Domingo|America/Curacao",
      "America/Santo_Domingo|America/Dominica",
      "America/Santo_Domingo|America/Grenada",
      "America/Santo_Domingo|America/Guadeloupe",
      "America/Santo_Domingo|America/Kralendijk",
      "America/Santo_Domingo|America/Lower_Princes",
      "America/Santo_Domingo|America/Marigot",
      "America/Santo_Domingo|America/Martinique",
      "America/Santo_Domingo|America/Montserrat",
      "America/Santo_Domingo|America/Port_of_Spain",
      "America/Santo_Domingo|America/Puerto_Rico",
      "America/Santo_Domingo|America/St_Barthelemy",
      "America/Santo_Domingo|America/St_Kitts",
      "America/Santo_Domingo|America/St_Lucia",
      "America/Santo_Domingo|America/St_Thomas",
      "America/Santo_Domingo|America/St_Vincent",
      "America/Santo_Domingo|America/Tortola",
      "America/Santo_Domingo|America/Virgin",
      "America/Sao_Paulo|Brazil/East",
      "America/St_Johns|Canada/Newfoundland",
      "Asia/Almaty|Asia/Qyzylorda",
      "Asia/Aqtobe|Asia/Aqtau",
      "Asia/Aqtobe|Asia/Oral",
      "Asia/Ashgabat|Asia/Ashkhabad",
      "Asia/Baghdad|Asia/Aden",
      "Asia/Baghdad|Asia/Bahrain",
      "Asia/Baghdad|Asia/Kuwait",
      "Asia/Baghdad|Asia/Qatar",
      "Asia/Baghdad|Asia/Riyadh",
      "Asia/Bangkok|Asia/Ho_Chi_Minh",
      "Asia/Bangkok|Asia/Phnom_Penh",
      "Asia/Bangkok|Asia/Saigon",
      "Asia/Bangkok|Asia/Vientiane",
      "Asia/Dhaka|Asia/Dacca",
      "Asia/Dubai|Asia/Muscat",
      "Asia/Hong_Kong|Hongkong",
      "Asia/Jakarta|Asia/Pontianak",
      "Asia/Jerusalem|Asia/Tel_Aviv",
      "Asia/Jerusalem|Israel",
      "Asia/Kathmandu|Asia/Katmandu",
      "Asia/Kolkata|Asia/Calcutta",
      "Asia/Kolkata|Asia/Colombo",
      "Asia/Kuala_Lumpur|Asia/Kuching",
      "Asia/Makassar|Asia/Ujung_Pandang",
      "Asia/Seoul|ROK",
      "Asia/Shanghai|Asia/Chongqing",
      "Asia/Shanghai|Asia/Chungking",
      "Asia/Shanghai|Asia/Harbin",
      "Asia/Shanghai|Asia/Macao",
      "Asia/Shanghai|Asia/Macau",
      "Asia/Shanghai|Asia/Taipei",
      "Asia/Shanghai|PRC",
      "Asia/Shanghai|ROC",
      "Asia/Singapore|Singapore",
      "Asia/Tashkent|Asia/Samarkand",
      "Asia/Tehran|Iran",
      "Asia/Thimphu|Asia/Thimbu",
      "Asia/Tokyo|Japan",
      "Asia/Ulaanbaatar|Asia/Ulan_Bator",
      "Asia/Urumqi|Asia/Kashgar",
      "Australia/Adelaide|Australia/Broken_Hill",
      "Australia/Adelaide|Australia/South",
      "Australia/Adelaide|Australia/Yancowinna",
      "Australia/Brisbane|Australia/Lindeman",
      "Australia/Brisbane|Australia/Queensland",
      "Australia/Darwin|Australia/North",
      "Australia/Lord_Howe|Australia/LHI",
      "Australia/Perth|Australia/West",
      "Australia/Sydney|Australia/ACT",
      "Australia/Sydney|Australia/Canberra",
      "Australia/Sydney|Australia/Currie",
      "Australia/Sydney|Australia/Hobart",
      "Australia/Sydney|Australia/Melbourne",
      "Australia/Sydney|Australia/NSW",
      "Australia/Sydney|Australia/Tasmania",
      "Australia/Sydney|Australia/Victoria",
      "Etc/UCT|UCT",
      "Etc/UTC|Etc/Universal",
      "Etc/UTC|Etc/Zulu",
      "Etc/UTC|UTC",
      "Etc/UTC|Universal",
      "Etc/UTC|Zulu",
      "Europe/Astrakhan|Europe/Ulyanovsk",
      "Europe/Athens|Asia/Nicosia",
      "Europe/Athens|EET",
      "Europe/Athens|Europe/Bucharest",
      "Europe/Athens|Europe/Helsinki",
      "Europe/Athens|Europe/Kiev",
      "Europe/Athens|Europe/Mariehamn",
      "Europe/Athens|Europe/Nicosia",
      "Europe/Athens|Europe/Riga",
      "Europe/Athens|Europe/Sofia",
      "Europe/Athens|Europe/Tallinn",
      "Europe/Athens|Europe/Uzhgorod",
      "Europe/Athens|Europe/Vilnius",
      "Europe/Athens|Europe/Zaporozhye",
      "Europe/Chisinau|Europe/Tiraspol",
      "Europe/Dublin|Eire",
      "Europe/Istanbul|Asia/Istanbul",
      "Europe/Istanbul|Turkey",
      "Europe/Lisbon|Atlantic/Canary",
      "Europe/Lisbon|Atlantic/Faeroe",
      "Europe/Lisbon|Atlantic/Faroe",
      "Europe/Lisbon|Atlantic/Madeira",
      "Europe/Lisbon|Portugal",
      "Europe/Lisbon|WET",
      "Europe/London|Europe/Belfast",
      "Europe/London|Europe/Guernsey",
      "Europe/London|Europe/Isle_of_Man",
      "Europe/London|Europe/Jersey",
      "Europe/London|GB",
      "Europe/London|GB-Eire",
      "Europe/Moscow|Europe/Volgograd",
      "Europe/Moscow|W-SU",
      "Europe/Paris|Africa/Ceuta",
      "Europe/Paris|Arctic/Longyearbyen",
      "Europe/Paris|Atlantic/Jan_Mayen",
      "Europe/Paris|CET",
      "Europe/Paris|Europe/Amsterdam",
      "Europe/Paris|Europe/Andorra",
      "Europe/Paris|Europe/Belgrade",
      "Europe/Paris|Europe/Berlin",
      "Europe/Paris|Europe/Bratislava",
      "Europe/Paris|Europe/Brussels",
      "Europe/Paris|Europe/Budapest",
      "Europe/Paris|Europe/Busingen",
      "Europe/Paris|Europe/Copenhagen",
      "Europe/Paris|Europe/Gibraltar",
      "Europe/Paris|Europe/Ljubljana",
      "Europe/Paris|Europe/Luxembourg",
      "Europe/Paris|Europe/Madrid",
      "Europe/Paris|Europe/Malta",
      "Europe/Paris|Europe/Monaco",
      "Europe/Paris|Europe/Oslo",
      "Europe/Paris|Europe/Podgorica",
      "Europe/Paris|Europe/Prague",
      "Europe/Paris|Europe/Rome",
      "Europe/Paris|Europe/San_Marino",
      "Europe/Paris|Europe/Sarajevo",
      "Europe/Paris|Europe/Skopje",
      "Europe/Paris|Europe/Stockholm",
      "Europe/Paris|Europe/Tirane",
      "Europe/Paris|Europe/Vaduz",
      "Europe/Paris|Europe/Vatican",
      "Europe/Paris|Europe/Vienna",
      "Europe/Paris|Europe/Warsaw",
      "Europe/Paris|Europe/Zagreb",
      "Europe/Paris|Europe/Zurich",
      "Europe/Paris|Poland",
      "Pacific/Auckland|Antarctica/McMurdo",
      "Pacific/Auckland|Antarctica/South_Pole",
      "Pacific/Auckland|NZ",
      "Pacific/Chatham|NZ-CHAT",
      "Pacific/Chuuk|Pacific/Truk",
      "Pacific/Chuuk|Pacific/Yap",
      "Pacific/Easter|Chile/EasterIsland",
      "Pacific/Guam|Pacific/Saipan",
      "Pacific/Honolulu|HST",
      "Pacific/Honolulu|Pacific/Johnston",
      "Pacific/Honolulu|US/Hawaii",
      "Pacific/Majuro|Kwajalein",
      "Pacific/Majuro|Pacific/Kwajalein",
      "Pacific/Pago_Pago|Pacific/Midway",
      "Pacific/Pago_Pago|Pacific/Samoa",
      "Pacific/Pago_Pago|US/Samoa",
      "Pacific/Pohnpei|Pacific/Ponape"
    ]
  });


  return moment;
}));