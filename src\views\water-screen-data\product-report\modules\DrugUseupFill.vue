<!--
 * @Description: 水厂耗电量统计
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-31 11:00:24
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-24 17:18:44
-->
<template lang="pug">
.drug-useup-fill 
	water-row(justify='flex-end', align='center')
		.drug-useup-fill-title 月份:
		DatePicker(
			:editable='false',
			v-model='date',
			format='yyyy-MM',
			type='month',
			:clearable='false',
			style='width: 215px',
			:options='options',
			@on-change='handleQuery()',
			placement='bottom-end'
		)
		Button.water-margin-left-16(v-if='state === "fill"', type='primary', @click='handleSave()', :loading='buttonLoading') 提交
		Button.water-margin-left-16(v-if='state === "record"', type='primary', @click='handleQuery()') 查询
		Button.water-margin-left-16(v-if='state === "record"', type='primary', @click='handleExport()') 导出
	#qrcodeDowm(style='display: none')
	WaterTable.drug-useup-fill-table(border, :columns='getColumns()', :data='tableData', :loading='loading')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { EventBus } from '@/utils/eventBus.js'
import { exportFile } from '@/utils/function.js'
import { queryStatisticsData, insertStatisticsData } from '@/api/water-screen-data.js'
export default {
	components: { WaterTable, WaterRow },
	props: ['state'],
	data() {
		return {
			date: new Date(`${new Date().getFullYear()}-${new Date().getMonth() + 1}`),
			buttonLoading: false,
			loading: false,
			tableData: [],
			options: {
				disabledDate(date) {
					return date.getTime() > new Date().getTime()
				},
			},
		}
	},
	mounted() {
		this.handleQuery()
		EventBus.$on('fresh-ele-record', () => {
			this.state === 'record' && this.handleQuery()
		})
	},
	methods: {
		handleQuery() {
			queryStatisticsData({
				date: this.$moment(this.date).format('YYYY-MM'),
				type: 3,
			})
				.then(res => {
					const { result = [] } = res
					this.tableData = result.map(item => {
						return {
							...item,
							editable: this.state === 'fill',
						}
					})
				})
				.catch(() => {
					this.tableData = []
				})
		},
		//提交
		handleSave() {
			try {
				this.buttonLoading = true
				const list = []
				this.tableData.forEach(item => {
					const {
						stationId = '',
						stationName = '',
						pac = '',
						naClO = '',
						lime = '',
						naCl = '',
						ca = '',
					} = item
					list.push({
						stationId,
						stationName,
						pac,
						naClO,
						lime,
						naCl,
						ca,
					})
				})
				const params = {
					date: this.$moment(this.date).format('YYYY-MM'),
					type: 3,
					list,
				}
				insertStatisticsData(params)
					.then(() => {
						this.$Message.success('提交成功!')
						this.buttonLoading = false
						this.handleQuery()
						EventBus.$emit('fresh-ele-record')
					})
					.catch(() => {
						this.buttonLoading = false
					})
			} catch {
				this.buttonLoading = false
			}
		},
		handleExport() {
			const baseUrl = `${location.origin}/api`
			let url =
				baseUrl +
				'/waterPlat/fillData/selectPowerConsumptionStatisticsExport?type=3&date=' +
				this.$moment(this.date).format('YYYY-MM')
			exportFile(url)
		},
		//正则
		valueChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			e.target.value = e.target.value.replace(/^(([0-9]{15}\d))(.\d{1,2})?$/, '')
		},
		//输入值
		handleInputValue(index, value, key) {
			this.tableData[index][key] = value
			// const item = this.tableData[index]
			// this.tableData.splice(index, 1, {
			// 	...item,
			// 	[key]: value,
			// })
		},
		getColumns() {
			const columns = [
				{
					title: '名称',
					key: 'stationName',
					width: 120,
					align: 'center',
				},
				{
					title: 'PAC库存（kg）',
					key: 'pac',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { pac, editable } = params.row
						return editable
							? h('Input', {
									props: {
										value: pac,
										maxlength: 18,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'pac')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', pac)
					},
				},
				{
					title: '次氯酸钠库存（kg）',
					align: 'center',
					key: 'naClO',
					minWidth: 160,
					render: (h, params) => {
						const { naClO, editable } = params.row
						return editable
							? h('Input', {
									props: {
										value: naClO,
										maxlength: 18,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'naClO')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', naClO)
					},
				},
				{
					title: '碱库存（kg）',
					align: 'center',
					key: 'lime',
					minWidth: 160,
					render: (h, params) => {
						const { lime, editable } = params.row
						return editable
							? h('Input', {
									props: {
										value: lime,
										maxlength: 18,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'lime')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', lime)
					},
				},
				{
					title: '氯化钠库存（kg）',
					align: 'center',
					key: 'lime',
					minWidth: 160,
					render: (h, params) => {
						const { naCl, editable } = params.row
						return editable
							? h('Input', {
									props: {
										value: naCl,
										maxlength: 18,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'naCl')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', naCl)
					},
				},
				{
					title: '柠檬酸库存（kg）',
					align: 'center',
					key: 'ca',
					minWidth: 160,
					render: (h, params) => {
						const { ca, editable } = params.row
						return editable
							? h('Input', {
									props: {
										value: ca,
										maxlength: 18,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'ca')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', ca)
					},
				},
			]
			return columns
		},
	},
}
</script>
<style lang="less" scoped>
.drug-useup-fill {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-title {
		margin-right: 4px;
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
}
</style>
