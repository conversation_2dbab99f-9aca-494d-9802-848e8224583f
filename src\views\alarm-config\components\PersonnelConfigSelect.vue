<!--
 * @Description: 人员配置选择框
 * @Author: shenxh
 * @Date: 2022-03-23 10:15:05
 * @LastEditors: houyan
 * @LastEditTime: 2024-08-13 13:55:45
-->

<template lang="pug">
.personnel-config-select
	multifunctional-select(
		ref='multifunctionalSelect',
		:newPlaceholder='placeholder',
		:disabled='disabled',
		:selected-value='selectedValue',
		:selected-label='selectedLabel',
		:tree-data='ownershipList',
		:table-data='usersList',
		:table-loading='tableLoading',
		:total='total',
		:page-size.sync='page.pageSize',
		:current.sync='page.pageNum',
		@on-search='searchData',
		@on-select-change-tree='changeTree',
		@on-page-size-change='changePageSize',
		@on-change-page='changePage'
	)
</template>

<script>
import MultifunctionalSelect from '@/components/gc-multifunctional-select'
import { getDepartment, getUsers } from '@/api/common.js'

export default {
	name: 'personnel-config-select',
	components: {
		MultifunctionalSelect,
	},
	props: {
		// 已选择的value数组
		selectedValue: Array,
		// 已选择的label数组 (用于标签展示, 顺序要与value对应)
		selectedLabel: Array,
		// 禁用
		disabled: Boolean,
		placeholder: String,
	},
	data() {
		return {
			searchVal: '',
			tableLoading: false,
			currentNode: {},
			// selectedUser: [42497, 42499],
			// selectedLabel: ['杨燕', '陈红丽'],
			ownershipList: [],
			usersList: [],
			total: 0,
			page: {
				pageSize: 20,
				pageNum: 1,
			},
			firstTree: '',
		}
	},
	computed: {},
	watch: {},
	created() {
		this._getDepartment()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 选中树形图节点
		changeTree(selectedNodes, currentNode) {
			this.page.pageNum = 1
			this.currentNode = currentNode

			this._getUsers()
		},
		// 点击搜索或按下回车键时触发
		searchData(val) {
			this.searchVal = val

			this._getUsers()
		},

		// 切换每页条数时的回调，返回切换后的每页条数
		changePageSize() {
			this._getUsers()
		},

		// 页码改变的回调，返回改变后的页码
		changePage() {
			this._getUsers()
		},

		// 部门组织树
		_getDepartment() {
			getDepartment().then(res => {
				const data = res.result

				if (data) {
					this.firstTree = data[0].id
					this.currentNode = data[0]
					this._getUsers()
					this.ownershipList = this._filterUsers(data)
				}
			})
		},

		// 根据部门ownership查询该部门下所有员工
		_getUsers() {
			const { ownership } = this.currentNode
			let params = {}

			if (
				this.$route.name === 'alarm-scheme' ||
				this.$route.name === 'editorList' ||
				this.$route.name === 'limitManage'
			) {
				params = {
					likeOwnership: ownership,
				}
			} else {
				params = {
					ownership,
				}
			}

			this.tableLoading = true
			getUsers({
				...this.page,
				...params,
				name: this.searchVal,
			}).then(res => {
				const data = res.result

				if (data && data.list && data.list.length) {
					this.usersList = data.list.map(item => {
						return {
							...item,
							label: item.name,
							value: item.id,
						}
					})
				} else {
					this.usersList = []
				}
				this.total = data.total || 0
				this.tableLoading = false
			})
		},

		// 过滤
		_filterUsers(list) {
			if (!list || !list.length) return []

			return list.map(item => {
				return {
					...item,
					title: item.name,
					expand: true,
					selected: item.id === this.firstTree,
					children: this._filterUsers(item.nodes),
				}
			})
		},
		clearSelectData() {
			this.$refs.multifunctionalSelect.clearSelectValue()
		},
	},
}
</script>

<style lang="less" scoped></style>
