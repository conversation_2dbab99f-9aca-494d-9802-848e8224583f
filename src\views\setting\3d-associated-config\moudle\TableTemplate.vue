<!--
 * @Descripttion: 
 * @version: 
 * @Author: heliping
 * @Date: 2021-12-08 15:37:46
 * @LastEditors: heliping
 * @LastEditTime: 2022-02-28 11:10:48
-->
<template lang="pug">
div
	Spin(fix, v-if='listLoading')
		| 加载中。。。
	.flex
		template(v-for='(item, index) in searchTempalte')
			.mr10(:key='index + item.label')
				| {{ item.label }}
			.mr10(:key='item.label')
				Input(
					v-if='item.type === 1',
					clearable,
					v-model='item.value',
					:placeholder='`请输入${item.label}`',
					style='width: 200px'
				)
				Select(
					v-if='item.type === 2',
					v-model='item.value',
					clearable,
					filterable,
					style='width: 200px',
					@on-change='selestChangeHandle(item)'
				)
					Option(v-for='item in item.optionList', :value='item.value', :key='item.value')
						| {{ item.label }}
		Button(type='primary', @click='choseSearchTable')
			| 查询
		Button(type='primary', v-show='tabValue === \'siteProcess\'', @click='handleClick("optionsiteProcess")')
			| 新增
	Table.mt10(:columns='columns', :data='data', border, :max-height='tableH')
		template(slot-scope='{ row }', slot='action1')
			Button(@click='handleClick("associatedSite", row)', type='primary', size='small', v-if='row.processType === 2')
				| 关联站点
			Button(@click='handleClick("editView", row)', type='primary', size='small', v-if='row.processType === 2')
				| 编辑视角
			Button(type='primary', size='small', @click='handleClick("tagConfig", row)', v-if='row.processType === 1')
				| 标签配置
			Button(type='primary', size='small', @click='handleClick("templatePreview", row)')
				| 工艺流程模板预览
			Button(@click='handleClick("deleteModel", row)', type='error', size='small', disabled, v-if='row.processType === 2')
				| 删除
		template(slot-scope='{ row }', slot='enable')
			i-switch(v-model='row.enableValue', @on-change='swicthChange($event, row)')
		template(slot-scope='{ row }', slot='showLabelType')
			span {{ getLabelName(row.showLabelType) }}
		template(slot-scope='{ row }', slot='action2')
			Button(type='primary', @click='handleClick("edit", row)', size='small')
				| 编辑
			Button(type='primary', @click='handleClick("delete", row)', disabled, size='small')
				| 删除
			Button(type='primary', size='small', @click='handleClick("dataLabelConfig", row)')
				| 数据标签配置
			Button(type='primary', size='small', @click='handleClick("technologyPreview", row)')
				| 工艺预览
		template(slot-scope='{ row }', slot='action3')
			Button(type='primary', size='small', @click='handleClick("editProcessModelMessage", row)')
				| 编辑模型信息
			Button(type='primary', @click='handleClick("modelPresentationData", row)', size='small')
				| 模型展示数据管理
			Button(
				type='primary',
				size='small',
				v-show='tabValue === \'configuredDataLabel\'',
				@click='handleClick("setMorePointLabel", row)'
			)
				| 设置多个点标签
			Button(
				type='primary',
				size='small',
				v-show='tabValue === \'configuredVideoLabel\'',
				@click='handleClick("setMorePointVideo", row)'
			)
				| 设置多个点视频
			Button(type='error', size='small', disabled, @click='handleClick("deleteModelParts", row)')
				| 删除
			//
				<Button
				type="primary"
				@click="handleClick('editModel', row)"
				size="small"
				>
				编辑
				</Button>
		template(slot-scope='{ row }', slot='action4')
			Button(type='primary', size='small', @click='handleClick("editModel", row)')
				| 编辑模型信息
			Button(type='primary', @click='handleClick("modelPresentationData", row)', size='small')
				| 模型关联数据管理
		template(slot-scope='{ row }', slot='action5')
			Button(type='primary', size='small', @click='handleClick("editModel", row)')
				| 编辑
			//
				<Button
				type="primary"
				@click="handleClick('modelPresentationData', row)"
				size="small"
				>
				模型关联数据管理
				</Button>
	div(style='margin-top: 10px; text-align: right')
		Page(
			show-elevator,
			show-total,
			:transfer='true',
			:current='pageInfo.pageNum',
			:total='pageInfo.total',
			:page-size='pageInfo.pageSize',
			@on-change='currentFun'
		)
</template>
<script>
import {
	getTemplateList,
	getTechnologySiteList,
	getLabelSiteList,
	getLabelPublicList,
	deleteSite,
	deleteModel,
	groupList,
	setSiteEnable,
} from '@/api/setting'
import { stationList } from '@/api/editor'
// import page from './../components/page'

export default {
	components: {},
	props: {},
	computed: {},
	mounted() {},
	data() {
		return {
			listLoading: false,
			searchTempalte: [],
			columns: [],
			data: [],
			tableH: '',
			modalWidth: '',
			pageInfo: { pageSize: 10, pageNum: 1, total: 0 },
			tabValue: '',
			stationList: [],
		}
	},
	methods: {
		// 初始化入口
		async init(form) {
			this.listLoading = true
			this.tabValue = form
			this.pageInfo.pageNum = 1
			await this.getStationList()
			this.setInitData()
			this.choseTable()
		},
		getStationList() {
			return stationList({ showItemCode: false, sysCode: 'dd' }).then(res => {
				const { result = [] } = res
				this.stationList = result.map(item => {
					const { stationName } = item
					return {
						value: stationName,
						label: stationName,
					}
				})
				return
			})
		},
		// 搜索初始化
		async initSearch(form, row) {
			this.listLoading = true
			this.tabValue = form
			this.pageInfo.pageNum = 1
			this.setInitData()
			// 表格表单初始化之后设置实际的值
			if (form === 'globalProcessDataLabel') {
				const { processName } = row
				this.searchTempalte[0].value = processName
			}
			if (form === 'siteProcessDataLabel' || form === 'configuredDataLabel' || form === 'configuredVideoLabel') {
				const { stationName, stationProcessName } = row
				this.searchTempalte[0].value = stationName
				if (stationName) {
					await this.getSelectData(stationName)
				}
				this.searchTempalte[1].value = stationProcessName
			}
			this.choseSearchTable()
		},
		refreshTableData(form) {
			this.tabValue = form
			this.choseSearchTable()
		},
		swicthChange(val, row) {
			// if (!val) {
			this.$Modal.confirm({
				title: '提示',
				content: '确定要操作这条数据?',
				loading: true,
				onOk: () => {
					this.$Modal.remove()
					setSiteEnable({
						id: row.id,
						enable: val ? 1 : 0,
					}).then()
					console.log(val)
				},
			})
			// }
		},
		choseTable(params = {}) {
			// debugger
			const { pageNum, pageSize } = this.pageInfo
			const param = { pageNum, pageSize, ...params }
			switch (this.tabValue) {
				case 'modelTemplate':
					this.getTemplateList(param)
					break
				case 'siteProcess':
					this.getTechnologySiteList(param)
					break
				case 'siteProcessDataLabel':
					this.getLabelSiteList(param)
					break
				case 'configuredDataLabel':
					param.showLabelType = 1
					this.getLabelSiteList(param)
					break
				case 'configuredVideoLabel':
					param.showLabelType = 6
					this.getLabelSiteList(param)
					break
				case 'globalProcessDataLabel':
					this.getLabelPublicList(param)
					break
				case 'pipelineDataManagement':
					this.groupList(param)
					break
				default:
					break
			}
		},

		choseSearchTable() {
			let param = {}
			this.searchTempalte.forEach(item => {
				param[item.key] = item.value || ''
			})
			this.choseTable(param)
		},

		// 初始化搜索条件和表格列
		setInitData() {
			switch (this.tabValue) {
				case 'modelTemplate':
					this.columns = [
						{
							title: '工艺流程名称',
							align: 'center',
							key: 'processName',
						},
						{
							title: '工艺流程类型',
							align: 'center',
							key: 'processType',
							render: (h, params) => {
								return h('div', this.typeInit(params.row.processType))
							},
						},
						{
							title: '工艺流程模型数量',
							align: 'center',
							key: 'processNumber',
						},
						{
							title: '已关联站点数量',
							align: 'center',
							key: 'relationNumber',
						},
						{
							title: ' 操作',
							align: 'center',
							slot: 'action1',
						},
					]
					this.searchTempalte = [
						{
							label: '工艺流程名称',
							key: 'processFlowDiagramName',
							value: '',
							type: 1,
						},
					]
					break
				case 'siteProcess':
					this.columns = [
						{
							title: '站点名称',
							align: 'center',
							key: 'stationName',
						},
						{
							title: '所属项目',
							align: 'center',
							key: 'applicationName',
						},
						{
							title: '工艺流程模版名称',
							align: 'center',
							key: 'processName',
						},
						{
							title: '站点工艺流程图名称',
							align: 'center',
							key: 'stationProcessName',
						},
						{
							title: '是否启用',
							align: 'center',
							slot: 'enable',
						},
						{
							title: ' 操作',
							align: 'center',
							width: 400,
							slot: 'action2',
						},
					]
					this.searchTempalte = [
						{
							label: '站点名称',
							key: 'stationName',
							value: '',
							type: 1,
						},
						{
							label: '工艺流程模版名称',
							value: '',
							key: 'processName',
							type: 1,
						},
					]
					break
				case 'siteProcessDataLabel':
				case 'configuredDataLabel':
				case 'configuredVideoLabel':
					this.columns = [
						{
							title: '站点名称',
							align: 'center',
							key: 'stationName',
						},
						{
							title: '站点工艺流程名称',
							align: 'center',
							key: 'stationProcessName',
						},
						{
							title: '工艺流程关联模型名称',
							align: 'center',
							key: 'modelName',
						},
						{
							title: '模型数据标签缩放比例',
							align: 'center',
							key: 'labelScale',
						},
						{
							title: '是否展示',
							align: 'center',
							slot: 'showLabelType',
						},
						{
							title: '已关联设备数据项',
							align: 'center',
							key: 'associatedDevice',
						},
						{
							title: ' 操作',
							align: 'center',
							width: 650,
							slot: 'action3',
						},
					]
					this.searchTempalte = [
						{
							label: '站点名称',
							key: 'stationName',
							value: '',
							selectForm: 'station',
							type: 2,
							optionList: this.stationList,
						},
						{
							label: '站点工艺流程名称',
							key: 'stationProcessName',
							value: '',
							type: 2,
							optionList: [],
						},
						{
							label: '模型名称',
							value: '',
							key: 'modelName',
							type: 1,
						},
					]
					break
				case 'globalProcessDataLabel':
					this.columns = [
						{
							title: '全局工艺流程名称',
							align: 'center',
							key: 'processName',
						},
						{
							title: '工艺流程关联模型名称',
							align: 'center',
							key: 'modelName',
						},
						{
							title: '模型数据标签缩放比例',
							align: 'center',
							key: 'labelScale',
						},
						{
							title: '点击事件关联站点名称',
							align: 'center',
							key: 'stationName',
						},
						{
							title: '已关联设备数据项',
							align: 'center',
							key: 'deviceData',
						},
						{
							title: ' 操作',
							align: 'center',
							width: 350,
							slot: 'action4',
						},
					]
					this.searchTempalte = [
						{
							label: '全局工艺流程名称',
							key: 'processName',
							value: '',
							type: 1,
						},
						{
							label: '模型名称',
							value: '',
							key: 'modelName',
							type: 1,
						},
					]
					break
				case 'pipelineDataManagement':
					this.columns = [
						{
							title: '管道名称',
							align: 'center',
							key: 'name',
						},
						{
							title: '管道属性',
							align: 'center',
							key: 'property',
						},
						{
							title: '管道点',
							align: 'center',
							key: 'point',
						},
						{
							title: ' 操作',
							align: 'center',
							width: 350,
							slot: 'action5',
						},
					]
					this.searchTempalte = [
						{
							label: '站点名称',
							key: 'stationName',
							value: '',
							type: 1,
						},
						{
							label: '站点编码',
							value: '',
							key: 'stationCode',
							type: 1,
						},
						{
							label: '管道名称',
							value: '',
							key: 'name',
							type: 1,
						},
						{
							label: '平台',
							value: '',
							key: 'name',
							type: 1,
						},
					]
					break
				default:
					break
			}
		},

		// 下拉改变
		selestChangeHandle(item) {
			const { selectForm, value } = item
			if (
				(this.tabValue === 'siteProcessDataLabel' ||
					this.tabValue === 'configuredDataLabel' ||
					this.tabValue === 'configuredVideoLabel') &&
				selectForm === 'station'
			) {
				this.getSelectData(value)
			}
		},
		getSelectData(value) {
			getTechnologySiteList({
				pageNum: 1,
				pageSize: 20,
				stationName: value,
			}).then(res => {
				this.listLoading = false
				const arr = res.result.list
				const stationProcessList = arr.map(item => {
					const { stationProcessName } = item
					return {
						value: stationProcessName,
						label: stationProcessName,
					}
				})
				this.searchTempalte[1].optionList = stationProcessList
			})
		},

		getLabelName(type) {
			let name = ''
			switch (type) {
				case 0:
					name = '不展示标签'
					break
				case 1:
					name = '展示普通标签'
					break
				case 6:
					name = '展示视频标签'
					break
				default:
					break
			}
			return name
		},
		modelTemplate(params) {
			this.getTemplateList(params)
		},

		siteProcess(params) {
			this.getTechnologySiteList(params)
		},

		siteProcessDataLabel(params) {
			this.getLabelSiteList(params)
		},

		globalProcessDataLabel(params) {
			this.getLabelPublicList(params)
		},

		// 点击事件
		handleClick(form, row) {
			switch (form) {
				case 'delete':
					this.$Modal.confirm({
						title: '提示',
						content: '确定要删除这条数据?',
						loading: true,
						onOk: () => {
							this.$Modal.remove()
							this.deleteSite(row)
						},
					})
					break
				case 'deleteModel':
					this.$Modal.confirm({
						title: '提示',
						content: '确定要删除这条数据?',
						loading: true,
						onOk: () => {
							this.$Modal.remove()
							this.deleteModel(row)
						},
					})
					break
				default:
					this.$emit('tableClick', form, row)
					break
			}
		},
		deleteModel(row) {
			const { id } = row
			deleteModel({ id }).then(() => {
				this.$Message.success('删除成功!')
				this.pageInfo.pageNum = 1
				this.choseTable()
			})
		},
		deleteSite(row) {
			const { id } = row
			deleteSite({ id }).then(() => {
				this.$Message.success('删除成功!')
				this.pageInfo.pageNum = 1
				this.choseTable()
			})
		},
		getTemplateList(params) {
			getTemplateList(params).then(res => {
				this.handleData(res)
			})
		},
		getTechnologySiteList(params) {
			// debugger
			getTechnologySiteList(params).then(res => {
				this.handleData(res)
				this.listLoading = false
				let arr = res.result.list
				this.data = arr.map(item => {
					return {
						...item,
						enableValue: item.enable == 1 ? true : false,
					}
				})
				this.pageInfo.total = res.result.total
			})
		},
		getLabelSiteList(params) {
			getLabelSiteList(params).then(res => {
				this.listLoading = false
				const arr = res.result.list.map(item => {
					const { showLabelType } = JSON.parse(item.property)
					return {
						...item,
						showLabelType,
					}
				})
				this.data = arr
				this.pageInfo.total = res.result.total
			})
		},
		getLabelPublicList(params) {
			getLabelPublicList(params).then(res => {
				this.handleData(res)
			})
		},
		groupList(params) {
			groupList(params).then(res => {
				this.handleData(res)
			})
		},
		handleData(res) {
			this.listLoading = false
			this.data = res.result.list
			this.pageInfo.total = res.result.total
		},

		currentFun(size) {
			this.pageInfo.pageNum = size
			this.choseSearchTable()
		},
		// 类型转换
		typeInit(t) {
			if (t === 1) return '全局'
			if (t === 2) return '站点'
		},
	},
}
</script>
<style lang="less" scoped>
.flex {
	display: flex;
	align-items: center;
}
.mr10 {
	margin-right: 10px;
}
.mt10 {
	margin-top: 10px;
}
::v-deep {
	.ivu-btn {
		margin-right: 5px;
	}
}
</style>
