<!--
 * @Description: 指南管理
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-08-08 14:04:36
 * @LastEditors: houyan
 * @LastEditTime: 2024-08-12 13:55:46
-->
<template lang="pug">
.manage
	.manage-content
		Tabs(v-model='tabValue', :animated='false')
			TabPane(label='工作指南配置', name='setting')
				GuideSetting(v-if='tabValue === "setting"')
			TabPane(label='权限配置', name='permiss')
				Permiss(v-if='tabValue === "permiss"')
</template>
<script>
import GuideSetting from './modules/GuideSetting.vue'
import Permiss from './modules/Permisses.vue'
export default {
	name: '',
	components: {
		GuideSetting,
		Permiss,
	},
	data() {
		return {
			tabValue: 'setting',
		}
	},
}
</script>
<style lang="less" scoped>
.manage {
	height: 100%;
	padding: 16px;
	background-color: rgb(242 242 242) !important;
	&-content {
		height: 100%;
		padding: 16px;
		background: #fff;
	}
	::v-deep {
		.ivu-tabs {
			display: flex;
			flex-direction: column;
			height: 100%;
			.ivu-tabs-content {
				flex: 1;
			}
			.ivu-tabs-tabpane {
				height: 100%;
			}
		}
	}
}
</style>
