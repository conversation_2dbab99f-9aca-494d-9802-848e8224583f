<template lang="pug">
.create-item-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='300',
		:value='show',
		:title='title',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='80')
					FormItem(label='角色名称', prop='roleName')
						Input(v-model='formData.roleName', :style='{ width: "192px" }', placeholder='请输入角色名称')
					FormItem(label='描述')
						Input(v-model='formData.memo', type='textarea', placeholder='请输入备注')
		template(slot='footer')
			Button(@click='handleClose') 关闭
			Button(type='primary', @click='handleSubForm') 保存
</template>

<script>
// import { saveBaseDataItem, updateBaseDataItem } from '@/api/base-item.js'
// import { querySysList } from '@/api/common.js'
// import dataRepairVue from '../../../data-repair/data-repair.vue'
import { addRole, updateRole } from '@/api/promise.js'
export default {
	name: 'CreateItemPopup',
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			title: '添加',
			formData: {
				id: '',
				roleName: '',
				memo: '',
			},
			options: [],
			formRules: {
				roleName: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		// querySysList().then(res => {
		// 	this.options = res.result
		// })
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal() {
			this.$refs.form.resetFields()
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					if (this.formData.id) {
						console.log('this.formData', this.formData)
						updateRole(this.formData)
							.then(() => {
								this.$Message.success('提交成功!')
								this.handleClose()
							})
							.catch(() => {
								this.listLoading = false
							})
					} else {
						addRole(this.formData)
							.then(() => {
								this.$Message.success('新增成功!')
								this.handleClose()
							})
							.catch(() => {
								this.listLoading = false
							})
					}
				} else {
					// this.$Message.error('表单验证失败!')
				}
			})
		},

		setData(data) {
			const { id, memo, roleName } = data
			this.title = '编辑'
			// this.formData.id = id
			// this.formData.memo = memo
			// this.formData.roleName = roleName
			this.formData = { id, memo, roleName }
		},

		// 按钮-关闭
		handleClose() {
			this.$refs.form.resetFields()
			this.formData = {
				id: '',
				roleName: '',
				memo: '',
			}
			this.$emit('update:show', false)
			this.$emit('initList')
		},
	},
}
</script>

<style lang="less" scoped>
.create-item-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			padding: 0 40px;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
.es-modal.fixed-height .ivu-modal-content {
	border-radius: 0px;
}
.es-modal .ivu-modal {
	margin-left: 20px;
}
.create-item-popup .es-modal .popup-content {
	width: 100%;
	padding: 0 0px;
}
</style>
