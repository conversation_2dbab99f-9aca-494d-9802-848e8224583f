<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-09-14 10:21:58
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-09-14 10:44:42
-->
<template lang="pug">
.pipe-line-config(v-if='value.controls[index]')
	.pipe-line-config-title.water-margin-left-8 外观
	.water-margin-top-8.water-margin-left-8.water-margin-right-8
		water-row(justify='flex-start', align='center')
			//- .block
			.water-margin-right-4.text 折线类型
			Select.water-margin-top-8(
				v-model='value.controls[index].strokeDashArray',
				@on-change='handleChange("strokeDashArray")',
				style='width: 120px'
			)
				Option(label='—————', :value='1')
				Option(label='----------------', :value='2')
		water-row.water-margin-top-16(
			justify='flex-start',
			align='center',
			v-if='value.controls[index].strokeDashArray === 1'
		)
			.water-margin-right-4.text 开启动画
			i-switch(v-model='value.controls[index].animate', @on-change='handleChange("animate")')

		water-row.water-margin-top-16(justify='flex-start', align='center', v-if='value.controls[index].animate')
			.water-margin-right-4.text 动画颜色
			ColorPicker(
				v-if='value.controls[index]',
				alpha,
				v-model='value.controls[index].animateColor',
				@on-pick-clear='handleChange("animateColor")',
				@on-pick-success='handleChange("animateColor")'
			)

		water-row.water-margin-top-16(justify='flex-start', align='center')
			.water-margin-right-4.text 折线颜色
			ColorPicker(
				v-if='value.controls[index]',
				alpha,
				v-model='value.controls[index].stroke',
				@on-pick-clear='handleChange("stroke")',
				@on-pick-success='handleChange("stroke")'
			)
		//- water-row.water-margin-top-16(
		//- 	justify="space-between",
		//- 	align="center",
		//- 	v-if="value.controls[index].strokeDashArray === 2")
		//- 	.water-margin-right-4.text 线粗
		//- 	Slider(
		//- 		v-if="value.controls[index]",
		//- 		@on-input="handleChange('strokeWidth')",
		//- 		:min="1",
		//- 		:max="20",
		//- 		v-model="value.controls[index].strokeWidth",
		//- 		show-input,
		//- 		show-tip="never",
		//- 		style="width: 200px")
		//- water-row.water-margin-top-16(justify="space-between", align="center")
		//- 	.water-margin-right-4.text 层级
		//- 	Button(@click="handleChange('bottomLayer')", type="primary", size="small") 最底层
	.pipe-line-config-title.text-config-title.water-margin-left-8.water-margin-top-16 值属性
	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.water-margin-left-8.text x
		Slider(
			v-if='value.controls[index]',
			@on-input='handleChange("left")',
			v-model='value.controls[index].left',
			show-input,
			:max='value.controls[index].canvasWidth',
			show-tip='never',
			style='width: 200px'
		)
	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.water-margin-left-8.text y
		Slider(
			v-if='value.controls[index]',
			@on-input='handleChange("top")',
			:max='value.controls[index].canvasHeight',
			v-model='value.controls[index].top',
			show-input,
			show-tip='never',
			style='width: 200px'
		)
	.pipe-line-config-title.water-margin-top-8.water-margin-left-8 流向
		water-row.water-margin-top-8(justify='flex-start', align='center')
			.water-margin-left-16 始
			.water-margin-left-16.tip X
			.water-margin-left-16(v-if='value.controls[index]') {{ value.controls[index].startPoint.x }}
			.water-margin-left-16.tip Y
			.water-margin-left-16(v-if='value.controls[index]') {{ value.controls[index].startPoint.y }}
		water-row.water-margin-top-8(justify='flex-start', align='center')
			.water-margin-left-16 末
			.water-margin-left-16.tip X
			.water-margin-left-16(v-if='value.controls[index]') {{ value.controls[index].endPoint.x }}
			.water-margin-left-16.tip Y
			.water-margin-left-16(v-if='value.controls[index]') {{ value.controls[index].endPoint.y }}
		template(v-for='(item, ind) in value.controls[index].points')
			//- water-row.water-margin-top-16.water-margin-left-16(
			//- 	justify="space-between",
			//- 	align="center")
			//- 	.water-margin-right-4.text x{{ ind + 1 }}: {{ item.x }}
				//- Slider(
				//- 	v-if="item.x",
				//- 	@on-input="handleChange('points', ind)",
				//- 	v-model="item.x",
				//- 	show-input,
				//- 	:min="0",
				//- 	:max="value.controls[index].canvasWidth",
				//- 	show-tip="never",
				//- 	style="width: 200px")
			//- water-row.water-margin-top-16.water-margin-left-16(
			//- 	justify="space-between",
			//- 	align="center")
			//- 	.water-margin-right-4.text y{{ ind + 1 }}: {{ item.y }}
				//- Slider(
				//- 	v-if="item.y",
				//- 	@on-input="handleChange('points', ind)",
				//- 	v-model="item.y",
				//- 	show-input,
				//- 	:min="0",
				//- 	:max="value.controls[index].canvasHeight",
				//- 	show-tip="never",
				//- 	style="width: 200px")
</template>
<script>
import WaterRow from '@/components/gc-water-row'
export default {
	name: '',
	components: { WaterRow },
	props: {
		value: {
			type: Object,
			default: function () {
				return {}
			},
		},
		index: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			lineStyle: 'solid',
		}
	},
	methods: {
		handleChange(type) {
			// debugger
			// 开启折线动画
			if (type === 'animate' || type === 'animateColor') {
				// debugger
				this.$emit('changeControl', type, this.value.controls[this.index])
				return
			}
			if (type === 'strokeDashArray') {
				if (this.value.controls[this.index].animate) {
					this.value.controls[this.index].animate = false
					this.handleChange('animate')
				}
				this.$emit('changeControl', type, this.value.controls[this.index][type] === 1 ? [] : [5, 5])
				return
			}
			if (type === 'left' || type === 'top') {
				// this.$set(this.value.controls[this.index], 'animate', false)
				this.handleChange('animate')
			}
			this.$emit('changeControl', type, this.value.controls[this.index][type])
		},
	},
	// watch: {
	// 	'value.controls': {
	// 		handler(val) {
	// 			debugger
	// 		},
	// 	},
	// },
}
</script>
<style lang="less" scoped>
.pipe-line-config {
	padding: 8px 0;
	overflow: auto;
	height: calc(100% - 60px);

	&-title {
		color: #000;
		font-weight: bold;
	}
	.block {
		width: 18px;
		height: 18px;
		border-radius: 2px;
		background-color: #ccc;
		margin-right: 4px;
	}
	&-content {
		border-top: 1px solid #e8e8e8;
		border-bottom: 1px solid #e8e8e8;
		padding: 8px 8px 24px;
	}
	.tip {
		color: #c1c1c1;
	}
}
::v-deep {
	.ivu-input-number {
		width: 56px;
		margin-top: 0;
	}
	.ivu-input {
		border: none;
		background: transparent;
		border-radius: 0;
		cursor: pointer;
		height: 28px;
		&:focus {
			box-shadow: none;
		}
	}
}
</style>
