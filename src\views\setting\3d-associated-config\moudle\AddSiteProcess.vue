<template>
	<!-- width="480" -->
	<Modal
		class-name="custom-modal"
		:value="show"
		:transfer="false"
		:title="title"
		@on-cancel="handleCancel"
		@on-visible-change="handleVisibleChange"
	>
		<div class="water-modal-content station-modal-content">
			<Spin fix v-if="listLoading">加载中。。。</Spin>
			<Form ref="formValidate" :model="formItem" :label-width="80">
				<Form-item label="所属项目">
					<Select
						v-model="formItem.applicationName"
						filterable
						remote
						clearable
						@on-change="remoteStationsList"
						placeholder="请选择"
					>
						<Option :value="item.code" v-for="(item, key) in applicationList" :key="key">
							{{ item.name }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="站点名称">
					<Select v-model="formItem.stationId" filterable remote transfer clearable placeholder="请选择">
						<Option :value="item.stationCode" v-for="(item, key) in stationList" :key="key">
							{{ item.stationName }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="工艺流程模版名称">
					<Select v-model="formItem.processId" filterable remote transfer clearable placeholder="请选择">
						<Option :value="item.id" v-for="(item, key) in processList" :key="key">
							{{ item.processName }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="站点工艺流程名称">
					<Input v-model="formItem.stationProcessName" placeholder="请输入"></Input>
				</Form-item>
			</Form>
		</div>
		<div slot="footer">
			<Button type="primary" @click="handleCheck">确定</Button>
		</div>
	</Modal>
</template>

<script>
import { getTemplateList, getStationList, getSiteSave } from '@/api/setting'
import { querySysList } from '@/api/common.js'

export default {
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	mounted() {
		querySysList().then(res => {
			this.applicationList = res.result
		})
		this.remoteProcessList()
	},
	data() {
		return {
			title: '新增',
			listLoading: false,
			stationList: [],
			applicationList: [],
			processList: [],
			formItem: {
				stationProcessName: '',
				applicationName: '',
				stationId: '',
				processId: '',
			},
			uploadList: [],
			defaultList: [],
		}
	},
	methods: {
		async updateInit(row) {
			const { id, applicationName, processName, processId, stationId, stationName, stationProcessName } = row
			this.remoteProcessList(processName)

			this.remoteStationsList(stationName)

			this.formItem = {
				id,
				applicationName,
				processId,
				stationId,
				stationProcessName,
			}
		},
		remoteProcessList() {
			getTemplateList().then(res => {
				this.processList = res.result.list
			})
		},

		// 联想查询接口
		remoteStationsList() {
			let params = {}
			if (this.formItem.applicationName) {
				params.applicationName = this.formItem.applicationName
			}
			getStationList(params).then(res => {
				this.stationList = res.result
			})
		},

		// 弹窗显隐事件
		handleVisibleChange() {},

		handleRemove() {
			this.formItem.processImage = ''
		},
		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},
		// 重置
		handleReset() {},
		// 确定
		handleCheck() {
			const _this = this
			_this.$refs.formValidate.validate(valid => {
				if (valid) {
					getSiteSave(_this.formItem)
						.then(() => {
							this.$refs.formValidate.resetFields()
							_this.$Message.success('提交成功!')
							this.$emit('update:show', false)
							this.$emit('successCallBack')
						})
						.catch(() => {
							this.listLoading = false
						})
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
	},
}
</script>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	.ivu-modal {
		height: auto !important;
	}
	.ivu-input-number-input {
		color: #fff;
		background: #133a5e;
		border: none;
	}
	.ivu-form-item-label {
		width: 125px !important;
	}
	.ivu-form-item-content {
		margin-left: 125px !important;
	}
}
</style>
