<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-30 16:35:42
 * @LastEditors: ya<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-06-09 17:28:49
-->
<template lang="pug">
.water-management-record
	water-row(justify='flex-start', align='center')
		RadioGroup(v-model='type', type='button')
			Radio(label='month') 月漏损率
			Radio(label='year') 年漏损率
		.water-management-record-title(v-show='type===\'month\'') 年份:
		Select.water-management-record-select(v-show='type===\'month\'', v-model='yearId', placeholder='请选择')
			Option(v-for='(item, index) in yearList', :key='index', :disabled='item.disabled', :value='item.value') {{ item.label }}
		Button.water-margin-left-16(type='primary', @click='handleQuery()') 查询
		Button.water-margin-left-16(type='primary', @click='handleExport()') 导出
	#qrcodeDowm(style='display: none')
	WaterTable.water-management-record-table(
		v-show='type===\'month\'',
		border,
		:columns='columns',
		:data='tableData',
		:loading='loading'
	)
	WaterTable.water-management-record-table(
		v-show='type===\'year\'',
		border,
		:columns='yearColumns',
		:data='yearTableData',
		:loading='loading'
	)
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { yearList } from '@/utils/enums.js'
import { queryManagementData } from '@/api/water-screen-data.js'
import { exportFile } from '@/utils/function.js'
import { EventBus } from '@/utils/eventBus.js'
export default {
	components: {
		WaterTable,
		WaterRow,
	},
	data() {
		return {
			type: 'month',
			date: new Date(),
			tableData: [],
			columns: [
				{ title: '序号', align: 'center', key: 'key', minWidth: 80 },
				{
					title: '水管所名称',
					align: 'center',
					key: 'nickName',
					minWidth: 110,
				},
				{
					title: '1月',
					align: 'center',
					key: 'lossRate_1',
					minWidth: 100,
				},
				{
					title: '2月',
					align: 'center',
					key: 'lossRate_2',
					minWidth: 100,
				},
				{
					title: '3月',
					align: 'center',
					key: 'lossRate_3',
					minWidth: 100,
				},
				{
					title: '4月',
					align: 'center',
					key: 'lossRate_4',
					minWidth: 100,
				},
				{
					title: '5月',
					align: 'center',
					key: 'lossRate_5',
					minWidth: 100,
				},
				{
					title: '6月',
					align: 'center',
					key: 'lossRate_6',
					minWidth: 100,
				},
				{
					title: '7月',
					align: 'center',
					key: 'lossRate_7',
					minWidth: 100,
				},
				{
					title: '8月',
					align: 'center',
					key: 'lossRate_8',
					minWidth: 100,
				},
				{
					title: '9月',
					align: 'center',
					key: 'lossRate_9',
					minWidth: 100,
				},
				{
					title: '10月',
					align: 'center',
					key: 'lossRate_10',
					minWidth: 100,
				},
				{
					title: '11月',
					align: 'center',
					key: 'lossRate_11',
					minWidth: 100,
				},
				{
					title: '12月',
					align: 'center',
					key: 'lossRate_12',
					minWidth: 100,
				},
				{ title: '最近修改时间', key: 'lastUpdateTime', minWidth: 130 },
			],
			yearColumns: [
				{ title: '序号', align: 'center', key: 'key', minWidth: 80 },
				{
					title: '水管所名称',
					align: 'center',
					key: 'nickName',
					minWidth: 110,
				},
				{
					title: '2020',
					align: 'center',
					key: 'lossRate_2020',
					minWidth: 100,
				},
				{
					title: '2021',
					align: 'center',
					key: 'lossRate_2021',
					minWidth: 100,
				},
				{
					title: '2022',
					align: 'center',
					key: 'lossRate_2022',
					minWidth: 100,
				},
				{
					title: '2023',
					align: 'center',
					key: 'lossRate_2023',
					minWidth: 100,
				},
				{
					title: '2024',
					align: 'center',
					key: 'lossRate_2024',
					minWidth: 100,
				},
				{
					title: '2025',
					align: 'center',
					key: 'lossRate_2025',
					minWidth: 100,
				},
				{
					title: '2026',
					align: 'center',
					key: 'lossRate_2026',
					minWidth: 100,
				},
				{
					title: '2027',
					align: 'center',
					key: 'lossRate_2027',
					minWidth: 100,
				},
				{
					title: '2028',
					align: 'center',
					key: 'lossRate_2028',
					minWidth: 100,
				},
				{
					title: '2029',
					align: 'center',
					key: 'lossRate_2029',
					minWidth: 100,
				},
				{
					title: '2030',
					align: 'center',
					key: 'lossRate_2030',
					minWidth: 100,
				},
				{ title: '最近修改时间', key: 'lastUpdateTime', minWidth: 130 },
			],
			yearTableData: [],
			yearList,
			yearId: new Date().getFullYear() + '',
			loading: false,
		}
	},
	mounted() {
		this.handleMonthQuery()
		this.handleYearQuery()
		this.yearList.forEach(y => {
			if (y.value > this.yearId) {
				y.disabled = true
			}
		})
		EventBus.$on('fresh-month-record', () => {
			this.handleMonthQuery()
		})
		EventBus.$on('fresh-year-record', () => {
			this.handleYearQuery()
		})
	},
	methods: {
		handleQuery() {
			console.log(this.type)
			if (this.type === 'month') {
				this.handleMonthQuery()
			} else {
				this.handleYearQuery()
			}
		},
		handleMonthQuery() {
			queryManagementData({
				timeType: 1,
				time: this.yearId,
			})
				.then(res => {
					const { result = [] } = res
					this.tableData = result.map((item, index) => {
						const obj = {
							key: index + 1,
							...item,
						}
						if (item.list.length > 0) {
							item.list.forEach(currentMonth => {
								const key = `lossRate_${currentMonth.index}`
								obj[key] = currentMonth.lossRate ? currentMonth.lossRate : ''
							})
						}
						return obj
					})
				})
				.catch(() => {
					this.tableData = []
				})
		},
		handleYearQuery() {
			queryManagementData({
				timeType: 2,
			})
				.then(res => {
					console.log(res, 'year')
					const { result = [] } = res
					this.yearTableData = result.map((item, index) => {
						const obj = {
							key: index + 1,
							...item,
						}
						if (item.list.length > 0) {
							item.list.forEach(currentYear => {
								const key = `lossRate_${currentYear.index}`
								obj[key] = currentYear.lossRate ? currentYear.lossRate : ''
							})
						}
						console.log(obj)
						return obj
					})
				})
				.catch(() => {
					this.yearTableData = []
				})
		},
		handleExport() {
			const baseUrl = `${location.origin}/api`
			const url_month = baseUrl + '/waterPlat/fillData/lossWater/detail/export?timeType=1&time=' + this.yearId
			// this.$moment(this.date).format('YYYY')

			const url_year = baseUrl + '/waterPlat/fillData/lossWater/detail/export?timeType=2'
			const url = this.type === 'month' ? url_month : url_year
			exportFile(url)
		},
	},
}
</script>
<style scoped lang="less">
.water-management-record {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-title {
		margin-right: 4px;
		margin-left: 16px;
	}
	&-select {
		width: 150px;
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
	::v-deep {
		.ivu-radio-wrapper-checked {
			background: #1192e8;
			color: #ffffff;
		}
	}
}
</style>
