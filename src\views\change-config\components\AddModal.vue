<!--
 * @memoription: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-07-19 09:40:51
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-07-25 09:44:48
-->
<template lang="pug">
Modal(
	:value='show',
	class-name='info-modal',
	width='700',
	:title='title',
	:mask='true',
	footer-hide,
	:transfer='false',
	@on-cancel='handleCancel',
	@on-visible-change='handleVisibleChange'
)
	Form(ref='formValidate', :model='formData', :label-width='120', :rules='ruleValidate')
		water-row(justify='flex-start', align='center')
			FormItem(label='业务来源:', prop='sysCode')
				Select(v-model='formData.sysCode', placeholder='请选择', style='width: 200px', :transfer='true')
					Option(v-for='(item, index) in list', :key='index', :value='item.value') {{ item.label }}
			FormItem(label='换表时间:', prop='fixTime')
				DatePicker.water-margin-left-8(
					v-model='formData.fixTime',
					:editable='false',
					:clearable='false',
					type='datetime',
					format='yyyy-MM-dd HH:mm',
					:transfer='true',
					placement='bottom-end',
					placeholder='请选择日期和时间',
					style='width: 200px'
				)

		water-row.water-margin-top-24(justify='flex-start', align='center')
			FormItem(label='站点编码:', prop='stationCode')
				Input(
					v-model='formData.stationCode',
					placeholder='请输入站点编码',
					@on-change='changeStation',
					@on-blur='getitemCodes',
					style='width: 200px'
				)

			FormItem(label='数据项:', prop='itemCode')
				Select(
					v-model='formData.itemCode',
					placeholder='请选择',
					style='width: 200px',
					label-in-value,
					@on-change='changeItemCode',
					:transfer='true'
				)
					Option(v-for='(item, index) in dataList', :key='index', :value='item.value') {{ item.label }}
		water-row.water-margin-top-24(justify='flex-start', align='center')
			FormItem(label='原表底数:', prop='preFixData')
				Input(
					v-model='formData.preFixData',
					placeholder='请输入原表底数',
					maxlength='18',
					@on-keyup='valueChange',
					style='width: 200px'
				)
			FormItem(label='新表底数:', prop='afterFixData')
				Input(
					v-model='formData.afterFixData',
					placeholder='请输入新表底数',
					maxlength='18',
					@on-keyup='valueChange',
					style='width: 200px'
				)
		FormItem.water-margin-top-24(label='备注:', prop='memo')
			Input(v-model='formData.memo', placeholder='请输入', type='textarea', maxlength='400')
		FormItem.water-margin-top-24.water-margin-left-24
			Button.water-margin-left-24(type='primary', :loading='loading', @click='handleSave("formValidate")') 提交
			Button.water-margin-left-16(@click='handleCancel()') 取消
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import { addRecord, updateRecord, queryItemCodes } from '@/api/change-config.js'
export default {
	components: { WaterRow },
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		type: {
			type: String,
			default: 'add', // add 是新增  modify是修改
		},
	},
	computed: {
		title() {
			return `${this.type === 'add' ? '新增' : '编辑'}换表记录`
		},
	},
	data() {
		return {
			loading: false,
			list: [
				{ label: '运行调度', value: 'dd' },
				{ label: '二次供水', value: 'eg' },
				{ label: '单村供水', value: 'dc' },
			],
			dataList: [],
			formData: {
				memo: '',
				fixTime: null,
				stationCode: '',
				afterFixData: '',
				preFixData: '',
				itemCode: '',
				sysCode: '',
				itemName: '',
			},
			ruleValidate: {
				stationCode: [
					{
						required: true,
						message: '请输入站点编码',
						trigger: 'blur',
					},
				],
				afterFixData: [
					{
						required: true,
						message: '请输入新表底数',
						trigger: 'blur',
					},
					{
						type: 'string',
						pattern: /^\d+$|^\d+[.]?\d+$/,
						message: '请输入数字',
					},
				],
				preFixData: [
					{
						required: true,
						message: '请输入原表底数',
						trigger: 'blur',
					},
					{
						type: 'string',
						pattern: /^\d+$|^\d+[.]?\d+$/,
						message: '请输入数字',
					},
				],
				itemCode: [
					{
						required: true,
						message: '请选择数据项',
						trigger: 'change',
					},
				],
				fixTime: [
					{
						required: true,
						message: '请选择时间',
						type: 'date',
						trigger: 'change',
					},
				],
				sysCode: [
					{
						required: true,
						message: '请选择业务来源',
						trigger: 'change',
					},
				],
			},
		}
	},
	methods: {
		init(data) {
			const { id, fixTime, afterFixData, stationCode, sysCode, memo, itemCode, itemName, preFixData } = data
			this.formData = {
				id,
				fixTime,
				afterFixData: afterFixData + '',
				stationCode,
				sysCode,
				memo,
				itemCode,
				itemName,
				preFixData: preFixData + '',
			}
			this.getitemCodes(data.stationCode)
		},
		handleVisibleChange(value) {
			if (!value) {
				this.loading = false
				// this.formData = {
				// 	fixTime: '',
				// 	afterFixData: '',
				// 	stationCode: '',
				// 	sysCode: '',
				// 	memo: '',
				// 	itemCode: '',
				// 	itemName: '',
				// 	preFixData: '',
				// }
			}
		},
		handleCancel() {
			this.loading = false
			this.resetForm()
			this.$emit('update:show', false)
		},
		resetForm() {
			this.$refs.formValidate.resetFields()
		},
		async handleSave(name) {
			const valid = await this.$refs[name].validate()
			if (valid) {
				this.loading = true
				try {
					const {
						id,
						fixTime,
						afterFixData,
						stationCode,
						sysCode,
						memo,
						itemCode,
						itemName,
						preFixData,
					} = this.formData
					let params = {
						sysCode,
						fixTime: this.$moment(fixTime).format('YYYY-MM-DD HH:mm'),
						afterFixData,
						stationCode,
						memo,
						itemCode,
						itemName,
						preFixData,
					}
					if (this.type === 'add') {
						addRecord(params)
							.then(() => {
								this.$Message.success('新增成功')
								this.$parent.handleQuery()
								this.handleCancel()
							})
							.catch(() => {
								this.loading = false
							})
					} else {
						params.id = id
						updateRecord(params)
							.then(() => {
								this.$Message.success('修改成功')
								this.$parent.handleQuery()
								this.handleCancel()
							})
							.catch(() => {
								this.loading = false
							})
					}
				} catch {
					this.loading = false
				}
			}
		},
		getitemCodes() {
			const params = {
				baseItemCodes: ['LJLL'],
				formulaTypes: ['0', '1'],
				stationCodes: [this.formData.stationCode],
			}
			queryItemCodes(params).then(res => {
				const { result = [] } = res
				this.dataList = result.map(item => {
					return {
						label: item.itemName,
						value: item.itemRealCode,
					}
				})
			})
		},
		changeItemCode(value) {
			this.formData.itemName = value && value.label ? value.label : ''
		},
		changeStation() {
			this.formData.itemCode = ''
			this.formData.itemName = ''
		},
		//正则
		valueChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			e.target.value = e.target.value.replace(/^(([0-9]{15}\d))(.\d{1,2})?$/, '')
		},
	},
}
</script>
<style lang="less" scoped>
::v-deep {
	.ivu-modal {
		.ivu-modal-content {
			.ivu-modal-header {
				padding: 14px 16px !important;
			}
		}
	}
}
</style>
