/*
 * @Description:
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2021-07-28 10:15:49
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2023-05-31 11:24:32
 */
import { POST, GET } from '@/utils/request'

// 查询角色信息列表
export function queryUsers(params) {
	return GET({
		url: '/role/users',
		params,
	})
}
// 删除角色
export function deleteRole(params) {
	return GET({
		url: '/role/delete/' + params.id,
	})
}
// 新增角色
export function addRole(params, requestType = 'json') {
	return POST({
		url: '/role/add',
		params,
		requestType,
	})
}
// 编辑角色
export function updateRole(params, requestType = 'json') {
	return POST({
		url: '/role/update',
		params,
		requestType,
	})
}

// 用户绑定角色
export function bindUser(params, requestType = 'json') {
	return POST({
		url: '/role/bindUser',
		params,
		requestType,
	})
}

// 用户绑定角色
export function unbindUser(params, requestType = 'json') {
	return POST({
		url: '/role/unbindUser',
		params,
		requestType,
	})
}
