import { GET, POST } from '@/utils/request'

// 查询设备列表
export const apiGetCategoryAndStatus = params => {
	return GET({
		url: '/monitor/operationManagement/digitalSystemBoard/getFirstCategoryAndStatus',
		params,
	})
}

// 获取设备数量
export const apiGetDeviceNum = params => {
	return GET({
		url: '/monitor/operationManagement/digitalSystemBoard/getRunningStateCount',
		params,
	})
}

export const apiGetWaterRunningStateCount = params => {
	return GET({
		url: '/monitor/operationManagement/digitalSystemBoard/getWaterRunningStateCount',
		params,
	})
}

// 获取设备列表
export const apiGetDeviceList = params => {
	return POST({
		url: '/device/queryAlarmDevices',
		params,
		requestType: 'json',
	})
}
// 获取设备类型详情
export const apiGetDeviceDetail = params => {
	return GET({
		url: '/device/queryAlarmDevice/detail',
		params,
	})
}
// 获取视野范围内坐标
export const apiGetViewDeviceList = params => {
	return POST({
		url: '/monitor/operationManagement/digitalSystemBoard/getDeviceGisTag',
		params,
	})
}

// 获取视野范围内坐标
export const apiGetAlarmType = params => {
	return GET({
		url: '/monitor/operationManagement/digitalSystemBoard/getAlarmTypes',
		params,
	})
}

export const apiStaffsCity = (params = {}) => {
	return GET({
		url: '/monitor/ledger/getProvinceAndCity',
		params,
	})
	// return request({
	// 	// url: "/v1/utos/staffs/city",
	// 	url: '/monitor/ledger/getProvinceAndCity',
	// 	method: 'GET',
	// 	baseURL,
	// 	needJointIsolation: true,
	// 	timeout: 180000,
	// 	params,
	// })
}
