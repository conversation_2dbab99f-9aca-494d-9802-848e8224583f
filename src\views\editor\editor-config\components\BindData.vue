<template lang="pug">
.text-config(v-if='control')
	water-row(justify='space-between', align='center')
		.text-config-title 变量列表
		Button(type='primary', size='small', @click='addVarVisable = true') 添加变量
	Collapse(v-if='control.itemData.length > 0', style='margin-top: 8px')
		Panel(v-for='(item, idx) in control.itemData', :key='idx', :name='`var${idx}`')
			.collapse-head
				span {{ item.name }}
				div
					Button(
						type='primary',
						size='small',
						icon='ios-create-outline',
						style='margin-right: 4px',
						@click.stop='handleEditVar(item, idx)'
					)
					Button(type='error', size='small', icon='ios-trash-outline', @click.stop='handleDeleteVar(idx)')
			template(#content)
				water-row(justify='flex-start', align='center')
					.text 平台名称：
					.water-margin-left-4 {{ item.sysName }}
				water-row(justify='flex-start', align='center')
					.text 站点名称：
					.water-margin-left-4 {{ item.stationName }}
				water-row(justify='flex-start', align='center')
					.text 数据项：
					.water-margin-left-4 {{ item.itemName }}
	//- 添加变量弹窗
	Modal(
		v-model='addVarVisable',
		:title='`${isVarEdit ? "编辑" : "添加"}变量`',
		@on-visible-change='handleAddVarModalVisChange'
	)
		Form(:model='varForm', ref='varFormRef', :label-width='100', :rules='varRules')
			FormItem(label='变量名称', prop='name')
				Input(v-model.trim='varForm.name', :disabled='isVarEdit', placeholder='请输入变量名称')
			FormItem(label='平台选择', prop='sysCode')
				Select(v-model='varForm.sysCode', transfer, filterable, label-in-value, @on-change='handleSysChange')
					Option(v-for='(item, sysIndex) in sysList', :value='item.code', :label='item.name', :key='sysIndex') {{ item.name }}
			FormItem(label='站点选择', prop='stationCode')
				Select(v-model='varForm.stationCode', transfer, filterable, label-in-value, @on-change='handleStationChange')
					Option(
						v-for='(item, stationIndex) in stationList',
						:value='item.stationCode',
						:label='item.stationName',
						:key='stationIndex'
					) {{ item.stationName }}
			FormItem(label='数据项选择', prop='itemRealCode')
				Select(v-model='varForm.itemRealCode', filterable, transfer, label-in-value, @on-change='handleItemChange')
					Option(
						v-for='(item, dataIndex) in dataList',
						:value='item.itemRealCode',
						:label='item.itemName',
						:key='dataIndex + item.itemRealCode'
					) {{ item.itemName }}
		template(#footer)
			Button(@click='addVarVisable = false') 取消
			Button(type='primary', @click='handleAddVar') 确定
	water-row.water-margin-top-16(justify='space-between', align='center')
		.text-config-title 交互场景
		Button(type='primary', size='small', @click='addSceneBtn') 添加场景
	Collapse(v-if='control.scene.length > 0', style='margin-top: 8px')
		Panel(v-for='(item, idx) in control.scene', :key='idx', :name='`scene${idx}`')
			.collapse-head
				span {{ item.name || `场景${idx + 1}` }}
				div
					Button(
						type='primary',
						size='small',
						icon='ios-create-outline',
						style='margin-right: 4px',
						@click.stop='handleEditScene(item, idx)'
					)
					Button(type='error', size='small', icon='ios-trash-outline', @click.stop='handleDeleteScene(idx)')
			template(#content)
				water-row(justify='flex-start', align='center')
					.text 自定义条件：
					.water-margin-left-4 {{ item.condition }}
				water-row(justify='flex-start', align='center')
					.text 状态：
					//- 兼容OSS上的自定义控件
					template(v-if='control.interactive')
						img.explame-img(:src='item.color')
					template(v-else-if='control.type.indexOf("control") !== -1')
						img.explame-img(:src='`${imgOnOssBaseUrl}/${control.type}-${item.color}.gif`')
					template(v-else-if='item.switchMode === "filter"')
						Icon.icon(:class='item.color', custom='iconfont icon-record')
						span(style='margin-left: 6px') {{ colorToText[item.color] }}
					div(v-else-if='item.switchMode === "image"')
						img.explame-img(:src='require(`@/assets/images/controls/${controlTypeImgFileName}-${item.color}.png`)')
	water-row.water-margin-top-16(justify='space-between', align='center')
		.text-config-title 点击打开查看曲线
		i-switch(v-model='control.clickable', @on-change='handleChange("clickable")')
	//- 添加场景弹窗
	Modal(
		v-model='addSceneVisable',
		:title='`${isSceneEdit ? "编辑" : "添加"}场景`',
		:width='600',
		@on-visible-change='handleAddSceneModalVisChange'
	)
		Form(:model='sceneForm', ref='sceneFormRef', :rules='sceneRules', :label-width='120')
			FormItem(label='场景名称', prop='name')
				Input(v-model.trim='sceneForm.name', placeholder='请输入场景名称')
			FormItem(label='自定义条件', prop='condition', style='position: relative')
				Input(v-model.trim='sceneForm.condition', type='textarea', placeholder='请输入自定义条件')
				template(#label)
					div(style='display: inline-flex; align-items: center')
						span 自定义条件
						Tooltip(
							placement='top',
							transfer,
							:max-width='380',
							style='border: 1px solid #999; border-radius: 50%; line-height: 100%; margin-left: 4px'
						)
							Icon(type='md-help')
							template(#content)
								div 支持比较符：==、!=、>、<、>=、<=
								div 支持运算符：
									div(style='margin-left: 20px') &&：同时满足
									div(style='margin-left: 20px') ||：满足其一
								div 示例：
									div(style='margin-left: 20px') 普通条件：变量1==0
									div(style='margin-left: 20px') 复合条件：变量1==1&&变量2==2、变量1>0||变量2>0
				div
					div(v-for='item in value.controls[index].itemData', style='margin-right: 10px')
						div(v-if='typeof item.formula === "object" && item.formula !== null')
							span {{ item.name }}：
							span(v-for='(value, key) in item.formula', style='margin: 0 10px') {{ `${key} = ${value}` }}
			template(v-if='control.interactive')
				FormItem(label='交互方式', prop='switchMode')
					Select(v-model='sceneForm.switchMode', @on-change='sceneForm.color = ""', disabled)
						//- 滤镜切换
						Option(value='filter', label='滤镜切换')
						//- 图片切换
						Option(value='image', label='图片切换')
			template(v-else)
				FormItem(label='交互方式', prop='switchMode')
					Select(v-model='sceneForm.switchMode', @on-change='sceneForm.color = ""')
						//- 滤镜切换
						Option(value='filter', label='滤镜切换')
						//- 图片切换
						Option(value='image', label='图片切换')
			FormItem(v-show='sceneForm.switchMode === \'image\'', label='状态', prop='color')
				RadioGroup(v-if='controlTypeImgFileName', v-model='sceneForm.color')
					Radio(v-for='(color, index) in ["red", "yellow", "green", "gray"]', :key='index', :label='color')
						img.explame-img(:src='require(`@/assets/images/controls/${controlTypeImgFileName}-${color}.png`)')
				//- 兼容OSS上的自定义控件
				RadioGroup(v-else-if='control.type.indexOf("controls") !== -1', v-model='sceneForm.color')
					template(v-if='control.controlsDetails')
						Radio(
							v-for='(detail, index) in control.controlsDetails',
							:key='index',
							:label='`${detail.imageUrl}&index=${index}`'
						)
							img.explame-img(:src='detail.imageUrl')
					template(v-else)
						Radio(v-for='(color, index) in ["red", "yellow", "green", "gray"]', :key='index', :label='color')
							img.explame-img(:src='`${imgOnOssBaseUrl}/${control.type}-${color}.gif`')
				.warning(v-else) 未找到状态图片，请先上传状态图片或使用滤镜切换
			FormItem(v-show='sceneForm.switchMode === \'filter\'', label='状态', prop='color')
				RadioGroup(v-model='sceneForm.color')
					Radio(label='red')
						Icon.icon.red(custom='iconfont icon-record')
						span 红色
					Radio(label='yellow')
						Icon.icon.yellow(custom='iconfont icon-record')
						span 黄色
					Radio(label='green')
						Icon.icon.green(custom='iconfont icon-record')
						span 绿色
					Radio(label='gray')
						Icon.icon.gray(custom='iconfont icon-record')
						span 灰色
		template(#footer)
			Button(@click='addSceneVisable = false') 取消
			Button(type='primary', @click='handleAddScene') 确定
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import { stationList } from '@/api/editor'
import { querySysList } from '@/api/common.js'
import { controlType2FileNameMap } from '../../enum'

const pattern = /([\u4e00-\u9fa5\da-z]+)\s*(>=|>|<|<=|===|==)/gi

export default {
	name: 'BindData',
	components: { WaterRow },
	props: {
		value: {
			type: Object,
			default: function () {
				return {}
			},
		},
		index: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			// 添加变量
			addVarVisable: false,
			isVarEdit: false,
			varEditIndex: null,
			varForm: {
				name: '',
				sysCode: '',
				sysName: '',
				stationCode: '',
				stationName: '',
				itemRealCode: '',
				itemName: '',
				formula: {},
			},
			varRules: {
				name: [
					{
						required: true,
						message: '请输入变量名称',
						trigger: 'blur',
					},
					{
						pattern: /^[\u4e00-\u9fa5a-zA-Z_$][\u4e00-\u9fa5a-zA-Z0-9_$]*$/,
						message: '不能以数字开头且不能存在特殊字符',
						trigger: 'blur',
					},
				],
				sysCode: {
					required: true,
					message: '请选择平台',
					trigger: 'change',
				},
				stationCode: {
					required: true,
					message: '请选择站点',
					trigger: 'change',
				},
				itemRealCode: {
					required: true,
					message: '请选择数据项',
					trigger: 'change',
				},
			},

			// 添加场景
			isSceneEdit: false,
			sceneEditIndex: null,
			addSceneVisable: false,
			sceneForm: {
				name: '',
				switchMode: 'image',
				condition: '',
				// 滤镜或图片的别称： red、yeloow、green、gray
				color: '',
			},
			sceneRules: {
				condition: {
					required: true,
					message: '请输入自定义条件',
					trigger: 'blur',
				},
				color: {
					required: true,
					message: '请选择状态',
					trigger: 'change',
				},
			},

			// 平台列表数据
			sysList: [],
			// 站点列表数据
			stationList: [],
			// 数据项列表数据
			dataList: [],

			colorToText: {
				red: '红色',
				yellow: '黄色',
				green: '绿色',
				gray: '灰色',
			},

			imgOnOssBaseUrl: `${window.location.origin}/group1/eslink-iot`,
		}
	},
	mounted() {
		console.log('数据', this.value.controls[this.index])
		this.getSysList()
	},
	computed: {
		// 当前控件元素
		control() {
			console.log('this.value.controls[this.index]', this.value.controls[this.index])
			return this.value.controls[this.index]
		},
		// 当前控件元素类型对应的图片文件名称
		controlTypeImgFileName() {
			return controlType2FileNameMap[this.control.type] || ''
		},
	},
	methods: {
		// 删除参数
		handleDeleteVar(index) {
			let match
			let allConditions = []
			const scenes = this.control.scene
			const varItem = this.control.itemData[index].name
			for (let i = 0; i < scenes.length; i++) {
				const condition = scenes[i].condition
				while ((match = pattern.exec(condition)) !== null) {
					allConditions.push(match[1])
				}
			}
			if (allConditions.includes(varItem)) {
				this.$Message.info('变量已在交互场景中使用，无法删除！')
				return
			}

			this.$Modal.confirm({
				title: '确定要删除吗？',
				content: '',
				onOk: () => {
					this.control.itemData.splice(index, 1)
					this.$forceUpdate()
				},
			})
		},
		// 获取平台下拉列表
		getSysList() {
			return querySysList().then(res => {
				const { result = [] } = res
				this.sysList = result
			})
		},
		// 平台下拉框改变事件
		handleSysChange(data) {
			if (data) {
				const { label = '', value = '' } = data
				this.varForm.sysName = label
				this.getStationList(value)
			}
		},

		// 获取站点下拉列表
		getStationList(sysCode) {
			return stationList({
				showItemCode: false,
				sysCode,
			}).then(res => {
				const { result = [] } = res
				this.stationList = result
			})
		},
		// 站点下拉框改变事件
		handleStationChange(data) {
			if (data) {
				const { label = '', value: stationCode } = data
				this.varForm.stationName = label
				stationList({
					showItemCode: true,
					sysCode: this.varForm.sysCode,
					stationCode,
					formulaTypes: ['3'],
				}).then(res => {
					const { result = [] } = res
					this.dataList = result[0].stationDataItem || []
				})
			}
		},
		// 数据项下拉改变事件
		handleItemChange(data) {
			if (data) {
				const { label = '', value } = data
				this.varForm.itemName = label
				const target = this.dataList.find(item => {
					return item.itemRealCode === value
				})
				if (target) {
					if (target.formula) {
						this.varForm.formula = target.formula
					}
				}
			}
		},

		handleAddVar() {
			this.$refs.varFormRef.validate(valid => {
				if (valid) {
					// 新增
					if (!this.isVarEdit) {
						if (!this.control.itemData) {
							this.control.itemData = []
						}
						this.control.itemData.push({
							...this.varForm,
						})
					} else {
						this.control.itemData.splice(this.varEditIndex, 1, {
							...this.varForm,
						})
					}
					this.addVarVisable = false
				}
			})
		},
		// 添加变量弹窗显隐改变事件
		handleAddVarModalVisChange(value) {
			if (!value) {
				this.isVarEdit = false
				this.varEditIndex = null
				this.$refs.varFormRef.resetFields()
			}
		},
		// 编辑变量弹窗
		async handleEditVar(data, index) {
			this.isVarEdit = true
			this.varEditIndex = index
			this.varForm = JSON.parse(JSON.stringify(data))
			this.addVarVisable = true
			const { sysCode, sysName, stationCode, stationName, itemRealCode, itemName } = data

			if (sysCode) {
				await this.handleSysChange({ value: sysCode, label: sysName })
			}
			if (stationCode) {
				this.handleStationChange({
					value: stationCode,
					label: stationName,
				})
			}
			if (itemRealCode) {
				this.handleItemChange({ value: itemRealCode, label: itemName })
			}
		},

		// 添加新场景
		handleAddScene() {
			this.$refs.sceneFormRef.validate(valid => {
				if (valid) {
					const filter = {
						red: [60, 1, 1],
						yellow: [60, 60, 1],
						green: [1, 60, 1],
						gray: 'Grayscale',
					}
					// 新增
					if (!this.isSceneEdit) {
						if (!this.control.scene) {
							this.control.scene = []
						}

						this.control.scene.push({
							...this.sceneForm,
							filter: filter[this.sceneForm.color],
							auto: true,
						})
					} else {
						// 编辑
						this.control.scene.splice(this.sceneEditIndex, 1, {
							...this.sceneForm,
							filter: filter[this.sceneForm.color],
							auto: true,
						})
					}
					this.addSceneVisable = false
				}
			})
		},
		// 添加场景弹窗显隐改变事件
		handleAddSceneModalVisChange(value) {
			if (!value) {
				this.isSceneEdit = false
				this.sceneEditIndex = null
				this.$refs.sceneFormRef.resetFields()
			}
		},
		addSceneBtn() {
			// debugger
			this.addSceneVisable = true
			if (this.control.interactive) {
				this.sceneForm.switchMode = this.control.interactive
			}
		},

		// 编辑场景
		async handleEditScene(data, index) {
			this.isSceneEdit = true
			this.sceneEditIndex = index
			this.sceneForm = {
				switchMode: 'filter',
				...JSON.parse(JSON.stringify(data)),
			}

			this.addSceneVisable = true
		},
		// 删除场景
		handleDeleteScene(index) {
			this.$Modal.confirm({
				title: '确定要删除吗？',
				content: '',
				onOk: () => {
					this.control.scene.splice(index, 1)
					this.$forceUpdate()
				},
			})
		},
		handleChange(type) {
			this.$emit('changeControl', type, this.control[type])
		},
	},
}
</script>
<style lang="less" scoped>
.text-config {
	padding: 8px;
	&-title {
		color: #000;
		font-weight: bold;
	}
}
::v-deep {
	.ivu-modal {
		.ivu-modal-content {
			.ivu-modal-header {
				padding: 14px 16px !important;
			}
		}
	}
	.ivu-collapse > .ivu-collapse-item {
		& > .ivu-collapse-header {
			display: flex;
			align-items: center;
			height: 30px;
			line-height: 30px;
		}
	}
	.ivu-collapse-content {
		padding: 0 10px;
		& > .ivu-collapse-content-box {
			padding: 10px 0;
		}
	}
}
.collapse-head {
	flex: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-right: 10px;
}
.red {
	color: #f93b3b;
}
.yellow {
	color: #f9f93b;
}
.green {
	color: #3bf93b;
}
.gray {
	color: gray;
}
.warning {
	color: #ff9900;
}
::v-deep {
	.ivu-form-item-error-tip {
		white-space: nowrap;
	}
	.ivu-radio-wrapper {
		display: inline-flex;
		align-items: center;
		margin-right: 16px;
	}
}
.explame-img {
	display: block;
	height: 40px;
}
.ivu-form {
	width: 100%;
}
</style>
