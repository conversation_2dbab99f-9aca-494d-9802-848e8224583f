<!--
 * @Description: 视频绑定弹窗
 * @Author: shenxh
 * @Date: 2022-04-06 11:31:41
 * @LastEditors: shenxh
 * @LastEditTime: 2022-06-23 11:29:19
-->

<template lang="pug">
.bind-video
	es-modal.es-modal(
		v-model='showModal',
		:transfer='false',
		:is-direct-close-modal='false',
		width='1000',
		title='绑定视频',
		@on-cancel='handleClose'
	)
		template(slot='body')
			.body-wrap
				Spin(v-show='tagLoading', fix)
				Tabs(v-model='tabsVal')
					TabPane(v-for='item in platformList', :key='item.id', :label='item.name', :name='item.id + ""')
					//- TabPane(label="海康安防管理平台", name="1")
					//- TabPane(label="大华安防管理平台", name="2")

				multifunctional-table(
					v-model='selectedEqu',
					:show-modal='showModal',
					:load='loadNode',
					:tree-data='treeData',
					:table-loading='tableLoading',
					:columns='columns',
					:total='page.total',
					:table-data='tableData',
					@node-click='nodeClick',
					@search='searchNode',
					@on-page-size-change='onPageSizeChange',
					@on-change='changePage'
				)
					template(slot='form')
						.form-wrap
							Form(ref='form', :model='bindFormData', inline='', :label-width='100')
								FormItem(label='原设备名称', prop='oldName')
									Input(type='text', v-model='bindFormData.oldName', placeholder='请输入')
								Button(type='primary', @click='handleBindSubForm') 查询
							.select-wrap
								Checkbox(v-model='checkboxVal', @on-change='changeBindCheckbox') 包含下级区域
								Button(type='primary', :loading='suncBtnLoading', @click='handleSync') 数据同步
		template(slot='footer')
			Button(@click='handleClose') 取消
			Button(type='primary', :disabled='bindBtnDisabled', @click='handleSubForm') 确定
</template>

<script>
import MultifunctionalTable from '@/components/gc-multifunctional-table'
import {
	getThirdplatAreaList,
	getThirdplatEquInfo,
	bindVideoEqu,
	getSecurityPlatform,
	getThirdplatEquInfoUpdate,
	getBindEquipmentAll,
} from '@/api/security-manage'

export default {
	name: 'bind-video',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		MultifunctionalTable,
	},
	props: {
		showModal: Boolean,
		selectedNode: Object,
		equipmentFormData: Object,
	},
	data() {
		return {
			tagLoading: false,
			suncBtnLoading: false,
			searchVal: '',
			selectedNodeData: {},
			bindBtnDisabled: true,
			tableLoading: false,
			selectedEqu: [],
			tabsVal: '1',
			checkboxVal: true,
			bindFormData: {},
			treeData: [],
			platformList: [],
			columns: [
				{
					type: 'selection',
					width: 60,
					align: 'center',
				},
				{
					title: '序号',
					type: 'index',
					align: 'center',
					width: 65,
				},
				{
					title: '原始设备名称',
					key: 'name',
				},
				{
					title: '所属区域',
					key: 'areaName',
				},
			],
			tableData: [],
			page: {
				current: 1,
				pageSize: 20,
				total: 0,
			},
		}
	},
	computed: {},
	watch: {
		tabsVal() {
			this.selectedNodeData = {}

			this._getTreeData()
			this._getTableData()
		},
		showModal(val) {
			if (val) {
				this._getSelectedEqu()
			}
		},
	},
	created() {
		this._getTableData()
		this._getSecurityPlatform()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 按钮-确定
		handleSubForm() {
			this._bindVideoEqu()
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
			this.$emit('handle-cancel')
		},

		// 绑定视频弹窗-查询按钮
		handleBindSubForm() {
			this.page.current = 1

			this._getTableData()
		},

		// 绑定视频弹窗-包含下级区域复选框
		changeBindCheckbox() {
			this._getTableData()
		},

		// 切换每页条数时的回调，返回切换后的每页条数
		onPageSizeChange(quantity) {
			this.page.current = 1
			this.page.pageSize = quantity

			this._getTableData()
		},

		// 页码改变的回调，返回改变后的页码
		changePage(pageNo) {
			this.page.current = pageNo

			this._getTableData()
		},

		// 节点被点击时的回调
		nodeClick(data) {
			this.page.current = 1
			this.selectedNodeData = data || {}
			if (data) {
				this.bindBtnDisabled = false
			} else {
				this.bindBtnDisabled = true
			}

			this._getTableData()
		},

		// 加载节点
		loadNode(node, resolve) {
			const { code } = node.data

			getThirdplatAreaList({
				// parentCode: code || '-1',
				parentCode: code,
				name: this.searchVal,
				platformId: this.tabsVal,
			})
				.then(res => {
					resolve(this._setTreeData(res))
				})
				.catch(() => {
					resolve([])
				})
		},

		// 搜索节点
		searchNode(val) {
			this.searchVal = val

			this._getTreeData(val)
		},

		// 信息同步
		handleSync() {
			this.suncBtnLoading = true
			getThirdplatEquInfoUpdate()
				.then(res => {
					const data = res.result

					if (data === 0) {
						this.$Message.success('同步成功 ')
					} else {
						this.$Message.error('同步失败 ')
					}

					this.selectedNodeData = {}
					this.suncBtnLoading = false

					this._getTreeData()
					this._getTableData()
				})
				.catch(() => {
					this.suncBtnLoading = false
				})
		},

		// 获取已选数据
		_getSelectedEqu() {
			this.tagLoading = true

			getBindEquipmentAll({
				...this.equipmentFormData,
				// areaCode: this.selectedNode.code,
				areaId: this.selectedNode.id,
			}).then(res => {
				const data = res.result

				if (data) {
					this.selectedEqu = data
				}
				this.tagLoading = false
			})
		},

		// 视频设备绑定
		_bindVideoEqu() {
			const platIdList = this.selectedEqu.map(item => {
				return item.code
			})

			bindVideoEqu({
				// areaCode: this.selectedNode.code,
				areaId: this.selectedNode.id,
				platId: this.tabsVal,
				deviceList: String(platIdList),
			}).then(res => {
				this.$Message.success('操作成功')
				this.handleClose()
				this.$emit('bind-video', res)
			})
		},

		// 获取表格数据
		_getTableData() {
			const { current: pageNum, pageSize } = this.page
			const formData = {
				...this.bindFormData,
				containSub: this.checkboxVal ? 1 : 0,
				platformId: this.tabsVal,
				areaCode: this.selectedNodeData.code,
				pageNum,
				pageSize,
			}

			if (!this.selectedNodeData.code) {
				this.tableData = []
				this.page.total = 0
				this.tableLoading = false
				return
			}

			this.tableLoading = true
			getThirdplatEquInfo(formData)
				.then(res => {
					const data = res.result

					if (data) {
						this.tableData = data.list
						this.page.total = data.total
					}
					this.tableLoading = false
				})
				.catch(() => {
					this.tableData = []
					this.page.total = 0
					this.tableLoading = false
				})
		},

		// 获取树形图数据
		_getTreeData(name) {
			getThirdplatAreaList({
				// parentCode: name ? 'root000000' : '-1',
				name,
				platformId: this.tabsVal,
			})
				.then(res => {
					this.treeData = this._setTreeData(res) || []
				})
				.catch(() => {
					this.treeData = []
				})
		},

		// 设置树形图数据
		_setTreeData(res) {
			let data = res.result.map(item => {
				return {
					...item,
					leaf: !item.hasChild,
				}
			})
			data.sort((a, b) => {
				return a.sort - b.sort
			})

			return data
		},

		// 获取平台数据
		_getSecurityPlatform() {
			getSecurityPlatform({
				pageNum: 1,
				pageSize: 0,
			}).then(res => {
				const data = res.result

				if (data && data.list) {
					this.platformList = data.list || []
					this.tabsVal = data.list[0].id + ''
				}
			})
		},
	},
}
</script>

<style lang="less" scoped>
.bind-video {
	.body-wrap {
		position: relative;
		.form-wrap {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 10px;
			.ivu-form-item {
				margin-bottom: 0;
			}
			.select-wrap {
				/deep/ .ivu-checkbox {
					margin-right: 5px;
				}
			}
		}
	}
}
</style>
