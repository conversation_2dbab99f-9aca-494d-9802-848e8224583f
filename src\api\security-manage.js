/*
 * @Description: 安防管理
 * @Author: shenxh
 * @Date: 2022-04-07 14:09:22
 * @LastEditors: shenxh
 * @LastEditTime: 2023-08-29 17:33:13
 */

import { GET, POST } from '@/utils/request'

/* 安防平台管理 start */
// 添加及编辑安防平台
export function updSecurityPlatform(params) {
	return POST({
		url: '/security/platform/saveOrUpdate',
		params,
		requestType: 'json',
	})
}

// 删除安防平台
export function delSecurityPlatform(params) {
	return POST({
		url: '/security/platform/delete',
		params,
	})
}

// 查询安防平台
export function getSecurityPlatform(params) {
	return POST({
		url: '/security/platform/query',
		params,
	})
}

// 连接信息测试
export function checkData(params) {
	return POST({
		url: '/security/platform/checkData',
		params,
		requestType: 'json',
	})
}
/* 安防平台管理 end */

/* 安保区域管理 start */
// 添加或编辑安防区域
export function updSecurityArea(params) {
	return POST({
		url: '/security/area/saveOrUpdate',
		params,
	})
}

// 删除安防区域
export function delSecurityArea(params) {
	return POST({
		url: '/security/area/delete/' + params.id,
	})
}

// 安防区域排序
export function sortSecurityArea(params) {
	return POST({
		url: '/security/area/sort',
		params,
	})
}

// 安防区域查询
export function getSecurityArea(params) {
	return POST({
		url: '/security/area/child',
		params,
	})
}
/* 安保区域管理 end */

/* 视频绑定 start */
// 已绑定设备查询
export function getBindEquipment(params) {
	return POST({
		url: '/security/devicebinded/query',
		params,
	})
}
// 已绑定设备查询(全部)
export function getBindEquipmentAll(params) {
	return POST({
		// url: '/security/devicebinded/queryForAreaCode',
		url: '/security/devicebinded/queryForAreaId',
		params,
	})
}

// 第三方平台下的区域列表查询
export function getThirdplatAreaList(params) {
	return POST({
		url: '/security/thirdplat/areaQueryChild',
		params,
	})
}

// 第三方平台下的设备信息查询
export function getThirdplatEquInfo(params) {
	return POST({
		url: '/security/thirdplat/deviceQuery',
		params,
	})
}

// 第三方平台区域信息与设备信息同步接口
export function getThirdplatEquInfoUpdate(params) {
	return POST({
		url: '/security/thirdplat/resources/update',
		params,
	})
}

// 视频设备绑定
export function bindVideoEqu(params) {
	return POST({
		url: '/security/devicebinded/bind',
		params,
	})
}

// 视频设备取消绑定
export function cancelBindVideoEqu(params) {
	return POST({
		url: '/security/devicebinded/debind',
		params,
	})
}

// 编辑保存
export function updEquipment(params) {
	return POST({
		url: '/security/devicebinded/saveOrUpdate',
		params,
		requestType: 'json',
	})
}
/* 视频绑定 end */

/* 视频监控 start */
// 视频播放资源树查询
export function getVideoTree(params) {
	return POST({
		url: '/security/devicebinded/videoresources/query',
		params,
	})
}
// 视频播放资源树查询-云睿
export function getVideoTreeYR(params) {
	return GET({
		url: '/security/platform/videoWebsocketUrl',
		params,
	})
}
export function getVideoLocalRecords(params) {
	return POST({
		url: '/security/platform/getVideoLocalRecords',
		params,
	})
}
// 视频播放资源树查询-第三方
export function integratresources(params) {
	return GET({
		url: '/security/devicebinded/integratresources/query',
		params,
	})
}
// 视频对讲-开始
export function getAudioTalk(params) {
	return POST({
		url: '/security/platform/getAudioTalk',
		params,
	})
}
// 视频对讲-结束
export function stopAudioTalk(params) {
	return POST({
		url: '/security/platform/stopAudioTalk',
		params,
	})
}
/* 视频监控 end */

/* 安防平台类型管理 start */
// 查询字典表-获取平台类型
export function getPlatDic(params) {
	return POST({
		url: '/security/PlatDic/query',
		params,
	})
}
/* 安防平台类型管理 end */

// 视频镜头控制
export function videoLensControl(params) {
	return POST({
		url: '/waterPlat/videoPlatform/videoLensControl',
		params,
	})
}
// 视频方向控制
export function videoDirectionControl(params) {
	return POST({
		url: '/waterPlat/videoPlatform/videoDirectionControl',
		params,
	})
}
// 移动到预制点
export function toPrecastPoint(params) {
	return POST({
		url: '/waterPlat/videoPlatform/toPrecastPoint',
		params,
	})
}
// 查询预置点信息
export function queryPrecastPoint(params) {
	return POST({
		url: '/waterPlat/videoPlatform/queryPrecastPoint',
		params,
	})
}
// 保存预制点
export function savePrecastPoint(params) {
	return POST({
		url: '/waterPlat/videoPlatform/savePrecastPoint',
		params,
	})
}
// 删除预置点信息
export function deletePrecastPoint(params) {
	return POST({
		url: '/waterPlat/videoPlatform/deletePrecastPoint',
		params,
	})
}

// 获取视频url
export function getVideoUrl(params) {
	return POST({
		url: '/security/platform/getVideoToken',
		params,
	})
}

// 获取视频url-历史回放
export function getVideoUrlHis(params) {
	return POST({
		url: '/security/platform/getPlayBackRecords',
		params,
	})
}

// 获取用户配置
export function getUserConfig(params) {
	return GET({
		url: '/sysConfig/get/byCode',
		params,
	})
}
