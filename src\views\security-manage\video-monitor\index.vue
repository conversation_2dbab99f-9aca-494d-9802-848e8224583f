<!--
 * @Description: 视频监控
 * @Author: shenxh
 * @Date: 2022-04-01 09:50:38
 * @LastEditors: shenxh
 * @LastEditTime: 2023-12-14 13:55:04
-->

<template lang="pug">
.video-monitor
	.header-wrap
		es-header.header(title='视频监控')
	.video-monitor-wrap
		.video-monitor-tree
			tree.tree-box(
				ref='tree',
				:data='treeData',
				:load='loadNode',
				prefix-icon,
				lazy,
				highlight-current,
				search-type='lazy',
				@node-click='nodeClick',
				@search='searchNode'
			)
			platform-control(v-if='videoType == "1"', :id='cameraIndexCode')

		.video-monitor-content
			video-native-hk(v-if='videoType == "1"', ref='HKRef', :list='list', @cb-integration='handleCb')
			video-native-dh(v-if='videoType == "2"', ref='DHRef', :data='selectedNode', :pathList='pathList')
			VideoDemoMul(v-if='videoType == "3"', ref='YRRef', :data='selectedNode')
</template>

<script>
import Tree from '@/components/gc-tree'
import PlatformControl from './components/platform-control'
import VideoNativeHk from '@/components/gc-video-native-hk/VideoNativeHk.vue'
import VideoNativeDh from '@/components/gc-video-native-dh/DHPlayer.vue'
import VideoDemoMul from '@/components/gc-video-player/VideoDemoMul.vue'

import { getVideoTree, integratresources, getVideoUrl, getUserConfig } from '@/api/security-manage'

export default {
	name: 'video-monitor',
	components: {
		Tree,
		PlatformControl,
		VideoNativeHk,
		VideoNativeDh,
		VideoDemoMul,
	},
	props: {},
	data() {
		return {
			videoType: 0,
			firstSearch: false,
			searchVal: '',
			selectedNode: {},
			treeData: [],
			// 当前选中的视频code
			cameraIndexCode: '',
			list: [],
			pathList: [],
		}
	},
	computed: {},
	watch: {},
	created() {
		this.getUserConfig()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 加载节点
		loadNode(node, resolve) {
			const { cameraList, id } = node.data

			// 第三方请求
			if (this.$route.query.thirdParty && !this.firstSearch) {
				integratresources({
					id: id || id === 0 ? id : '',
					name: this.searchVal,
					type: this.$route.query.type,
				}).then(res => {
					this.firstSearch = true
					resolve(this._setTreeData(res, cameraList))
				})
			} else {
				getVideoTree({
					// parentCode: code,
					id,
					name: this.searchVal,
					type: id || id === 0 ? undefined : this.$route.query.type,
				}).then(res => {
					resolve(this._setTreeData(res, cameraList))
				})
			}
		},

		// 搜索节点
		searchNode(val) {
			this.searchVal = val

			this._getTreeData(val)
		},

		// 节点被点击时的回调
		nodeClick(data) {
			if (data) {
				const { cameraIndexCode, cameraList, type, platformId } = data

				this.selectedNode = data
				if (this.videoType == 1) {
					// 点击区域
					if (type === 'area') {
						this.$refs.HKRef.stopAllPreview()
						if (cameraList && cameraList.length) {
							this.list = cameraList.slice(0, 9)
							this.list.forEach((item, index) => {
								this.$refs.HKRef.playVideo(index + 1, item.cameraIndexCode)
								this.$refs.HKRef.resizeVideo()
								this.$refs.HKRef.oWebControl.JS_CuttingPartWindow(372, 200) // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
							})
						}
					} else {
						// 点击设备
						this.$refs.HKRef.playVideo(0, cameraIndexCode)
						this.$refs.HKRef.resizeVideo()
						this.$refs.HKRef.oWebControl.JS_CuttingPartWindow(372, 200) // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
					}
				} else {
					if (type === 'equipment') {
						this.getVideoUrl(cameraIndexCode, platformId, type, data)
					} else {
						if (cameraList && cameraList.length) {
							let codes = cameraList.map(item => {
								return item && item.cameraIndexCode ? item.cameraIndexCode : ''
							})

							codes.length = 9 // 最多显示9个

							this.getVideoUrl(codes.toString(), cameraList[0].platformId, type)
						}
					}
				}
			}

			// 选中状态
			this.$refs.tree.$refs['el-tree'].setCurrentKey(this.selectedNode.id)
		},

		// 获取树形图数据
		_getTreeData(name) {
			// 第三方请求
			if (this.$route.query.thirdParty) {
				integratresources({
					name,
					type: this.$route.query.type,
				}).then(res => {
					this.treeData = this._setTreeData(res) || []
				})
			} else {
				getVideoTree({
					name,
					type: this.$route.query.type,
				}).then(res => {
					this.treeData = this._setTreeData(res) || []
				})
			}
		},

		// 设置树形图数据
		_setTreeData(res, cameraList) {
			let data = res.result.map(item => {
				return {
					...item,
					type: 'area',
					leaf: item.cameraList && item.cameraList.length ? false : !item.hasChild,
				}
			})
			data.sort((a, b) => {
				return a.sort - b.sort
			})
			if (cameraList && cameraList.length) {
				data.push(
					...cameraList.map(item => {
						return {
							...item,
							type: 'equipment',
							name: item.cameraName,
							leaf: true,
						}
					}),
				)
			}

			return data
		},

		// 获取视频url-实时监控
		getVideoUrl(code, platformId, type, data = {}) {
			if (this.videoType == '3') {
				setTimeout(() => {
					this.$refs.YRRef &&
						this.$refs.YRRef.dblclickChannel({
							channelAbility:
								'RemoteControl,AudioTalkV1,FrameReverse,AudioEncode,AudioEncodeControl,AlarmMD,MDW,MDS,HeaderDetect,RtFaceDetect,RtFaceCompa,SMDAI,SMDH,ChnLocalStorage,LEDS,MDMD,VC,WDRV2,WLTM,VQS,NVM',
							channelId: data.id,
							channelName: data.cameraName,
							deviceAbility:
								'Auth,DHP2P,HSEncrypt,CloudStorage,LocalStorage,PlaybackByFilename,MT,CK,RD,LocalRecord,XUpgrade,TimeSync,ModifyPassword,LocalStorageEnable,RTSV2,PBSV2,ESV1,DaySummerTime,WeekSummerTime,TAP,TimeFormat,InstantDisAlarm,IDAP,DevReset,TLSEnable,DHCP,Reboot,NEC,CCD,ME,DHPenetrate,MesTrans,PicTrans,DataTrans,LRRF,IOTTUNNEL,CDD-OSS,CDD-OBS,CDD-US3,CDD-BOS,CDD-COS,CDDV2,CDD,AudioTalk',
							deviceId: data.cameraIndexCode,
							deviceModel: 'DH-NVR4408-HDS2/I',
							deviceType: 'channel',
							onlineStatus: data.status || 1,
							protoType: 'rtsv',
							recordProtoType: 'rtsv',
							talkProtoType: 'rtsp',
							code,
							platformId,
						})
				}, 50)

				return
			}

			getVideoUrl({
				code,
				platformId,
			}).then(res => {
				const { result } = res
				let arr = []

				if (result && result.length) {
					arr = result.map(item => {
						const { url = '' } = item || {}

						return {
							path: url,
							redirect: false,
						}
					})
				}

				this.pathList = arr
				setTimeout(() => {
					this.$refs.DHRef && this.$refs.DHRef.loadVideo(type)
				}, 50)
			})
		},

		// 获取用户配置
		getUserConfig() {
			getUserConfig({
				code: 'video.type',
			}).then(res => {
				const { result = [] } = res

				if (result.length) {
					this.videoType = result[0].val
				}
			})
		},
		handleCb(code) {
			this.cameraIndexCode = code
		},
	},
}
</script>

<style lang="less" scoped>
.video-monitor {
	width: 100%;
	height: 100%;
	.header-wrap {
		padding: 0 16px;
		.header {
			border-bottom: 1px solid #efefef;
		}
	}
	.video-monitor-wrap {
		display: flex;
		width: 100%;
		height: calc(100% - 40px);
		.video-monitor-tree {
			display: flex;
			flex-direction: column;
			flex-shrink: 0;
			width: 200px;
			padding-top: 16px;
			.tree-box {
				padding: 0 16px;
				flex: 1;
			}
		}
		.video-monitor-content {
			flex-grow: 1;
			border-left: 1px solid #efefef;
			height: 100%;
			background-color: #333;
		}
	}
}
</style>
