<!--
 * @Description: 多功能表格
 * @Author: shenxh
 * @Date: 2022-04-06 13:34:39
 * @LastEditors: shenxh
 * @LastEditTime: 2022-04-15 18:17:07
-->

<template>
	<div class="multifunctional-table">
		<!-- 抽屉 -->
		<div class="multifunctional-drawer">
			<drop-down v-model="isFold">
				<Tag
					v-for="(item, index) in selectedList"
					:key="item.sourceCode"
					closable
					@on-close="handleCloseTag(item, index)"
				>
					{{ item[labelKey] }}
				</Tag>

				<Button
					v-show="selectedList && selectedList.length"
					type="primary"
					size="small"
					@click="handleClearBtn"
				>
					清除
				</Button>
			</drop-down>
		</div>
		<!-- 内容 -->
		<div class="multifunctional-content">
			<div class="multifunctional-content-tree" :style="{ width: treeWidth + 'px' }">
				<tree
					ref="tree"
					:data="treeData"
					:load="load"
					lazy
					highlight-current
					search-type="lazy"
					@node-click="nodeClick"
					@search="searchNode"
				></tree>
			</div>
			<div class="multifunctional-content-table">
				<div class="search-wrap">
					<slot name="form"></slot>
				</div>
				<div class="table-wrap" :style="{ width: tableWidth + 'px' }">
					<Table
						:key="tableKey"
						ref="selection"
						border
						:height="255"
						:loading="tableLoading"
						:columns="columns"
						:data="tableData"
						@on-selection-change="onSelectionChange"
						@on-select="onSelect"
						@on-select-cancel="onSelectCancel"
						@on-select-all="onSelectAll"
						@on-select-all-cancel="onSelectAllCancel"
					></Table>
				</div>
				<div class="page-wrap">
					<Page
						:total="total"
						:current="current"
						:page-size="pageSize"
						size="small"
						show-elevator
						show-sizer
						show-total
						:page-size-opts="pageSizeOpts"
						@on-page-size-change="onPageSizeChange"
						@on-change="changePage"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import Tree from '@/components/gc-tree'
import DropDown from '@/components/gc-drop-down'

export default {
	name: 'multifunctional-table',
	components: {
		Tree,
		DropDown,
	},
	model: {
		prop: 'selectedList',
		event: 'setSelectedList',
	},
	props: {
		labelKey: {
			type: String,
			default: 'name',
		},
		valueKey: {
			type: String,
			default: 'code',
		},
		showModal: {
			type: Boolean,
			default: false,
		},
		treeLoading: Boolean,
		// 已选列表
		selectedList: {
			type: Array,
			default() {
				return []
			},
		},
		// 表格加载中
		tableLoading: Boolean,
		// 树形图宽度
		treeWidth: {
			type: Number,
			default: 200,
		},
		// 表格宽度
		tableWidth: {
			type: Number,
			default: 760,
		},
		load: Function,
		// 左侧树形图数据
		treeData: {
			type: Array,
			default() {
				return [
					{
						title: '1',
						expand: true,
						children: [
							{
								title: '1-1',
								expand: true,
							},
						],
					},
				]
			},
		},
		// 右侧表格 columns
		columns: {
			type: Array,
			default() {
				return [
					{
						type: 'selection',
						width: 60,
						align: 'center',
					},
					{
						title: '序号',
						type: 'index',
						align: 'center',
						width: 65,
					},
					{
						title: '名称',
						key: 'label',
					},
				]
			},
		},
		// 右侧表格数据 (label/value 必传)
		tableData: {
			type: Array,
			default() {
				return [
					{
						label: '张三',
						value: '1',
					},
					{
						label: '李四',
						value: '2',
					},
				]
			},
		},
		// 分页-总数
		total: {
			type: Number,
			default: 0,
		},
		// 分页-每页数量
		pageSize: {
			type: Number,
			default: 20,
		},
		// 分页-当前页码
		current: {
			type: Number,
			default: 1,
		},
		// 分页-每页条数切换的配置
		pageSizeOpts: {
			type: Array,
			default() {
				return [10, 20, 50, 100]
			},
		},
	},
	data() {
		return {
			tableKey: 0,
			isFold: this.isFold,
		}
	},
	computed: {},
	watch: {
		tableData: {
			handler() {
				this._setTableData()
			},
			deep: true,
		},
		showModal(val) {
			this.isFold = true
			if (val) {
				setTimeout(() => {
					this._setTableData()

					// 重新渲染表格数据
					this.tableKey++
				}, 200)
			}
		},
	},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 关闭tag
		handleCloseTag(itm, idx) {
			this.$emit('setSelectedList', this._setSelectedList(itm))
			this.$emit('on-close', itm, idx)

			setTimeout(() => {
				this._setTableData()

				// 重新渲染表格数据
				this.tableKey++
			}, 200)
		},

		// tag 清除按钮
		handleClearBtn() {
			this.$emit('setSelectedList', [])
			setTimeout(() => {
				this._setTableData()

				// 重新渲染表格数据
				this.tableKey++
			}, 200)
		},

		// 搜索节点
		searchNode(val) {
			this.$emit('search', val)
		},

		// 节点被点击时的回调
		nodeClick(data, node, nodeComponent) {
			this.$emit('node-click', data, node, nodeComponent)
		},

		// 节点选中状态发生变化时的回调
		checkChange(data, hasSelected, sasSelectedChild) {
			this.$emit('check-change', data, hasSelected, sasSelectedChild)
		},

		// 当前选中节点变化时触发的事件
		currentChange(data, node) {
			this.$emit('current-change', data, node)
		},

		// 选中某一项时触发
		onSelect(selection, row) {
			let tmpSelectedList = JSON.parse(JSON.stringify(this.selectedList))

			tmpSelectedList.push({
				...row,
				sourceCode: row.code,
			})

			this.$emit('setSelectedList', tmpSelectedList)
			this.$emit('on-select', row, tmpSelectedList)
		},

		// 取消选中某一项时触发
		onSelectCancel(selection, row) {
			this.$emit('setSelectedList', this._setSelectedList(row))
			this.$emit('on-select', row, this._setSelectedList(row))
		},

		// 点击全选时触发
		onSelectAll(selection) {
			let tmpSelectedList = JSON.parse(JSON.stringify(this.selectedList))

			selection.forEach(item => {
				if (
					tmpSelectedList.every(item1 => {
						return item[this.valueKey] !== item1.sourceCode
					})
				) {
					tmpSelectedList.push({
						...item,
						sourceCode: item.code,
					})
				}
			})

			this.$emit('setSelectedList', tmpSelectedList)
			this.$emit('on-select-all', selection)
		},

		// 点击取消全选时触发
		onSelectAllCancel(selection) {
			let tmpSelectedList = JSON.parse(JSON.stringify(this.selectedList))
			let arr = []

			arr = tmpSelectedList.filter(item => {
				let hasItem = false

				this.tableData.forEach(item1 => {
					if (item[this.valueKey] === item1[this.valueKey]) {
						hasItem = true
					}
				})

				return !hasItem
			})

			this.$emit('setSelectedList', arr)
			this.$emit('on-select-all-cancel', selection)
		},

		// 只要选中项发生变化时就会触发
		onSelectionChange(selection) {
			this.$emit('on-selection-change', selection)
		},

		// 切换每页条数时的回调，返回切换后的每页条数
		onPageSizeChange(quantity) {
			this.$emit('update:current', 1)
			this.$emit('update:pageSize', quantity)
			this.$emit('on-page-size-change', quantity)
		},

		// 页码改变的回调，返回改变后的页码
		changePage(pageNo) {
			this.$emit('update:current', pageNo)
			this.$emit('on-change', pageNo)
		},

		// 修改列表
		_setSelectedList(item) {
			let tmpSelectedList = JSON.parse(JSON.stringify(this.selectedList))

			for (let i = 0; i < tmpSelectedList.length; i++) {
				if (tmpSelectedList[i].sourceCode === item[this.valueKey]) {
					tmpSelectedList.splice(i, 1)

					break
				}
			}

			return tmpSelectedList
		},

		// 设置表格数据
		_setTableData() {
			if (this.tableData && this.tableData.length) {
				this.tableData.forEach(item => {
					item._checked = false
					this.selectedList.forEach(item1 => {
						if (item[this.valueKey] == item1.sourceCode) {
							item._checked = true
						}
					})
				})
			}
		},
	},
}
</script>

<style lang="less" scoped>
.multifunctional-table {
	position: relative;
	width: 100%;
	height: 100%;
	padding-top: 52px;
	.multifunctional-drawer {
		position: absolute;
		top: 0;
		width: 100%;
		z-index: 9999;
	}
	.multifunctional-content {
		display: flex;
		width: 100%;
		height: 100%;
		.multifunctional-content-tree {
			flex-shrink: 0;
			max-height: 348px;
			overflow: auto;
			padding-right: 10px;
			padding-top: 8px;
			border-right: 1px solid #efefef;
		}
		.multifunctional-content-table {
			flex-grow: 1;
			height: 100%;
			padding: 8px;
			.page-wrap {
				white-space: nowrap;
				.ivu-page {
					margin-bottom: 0;
					margin-top: 8px;
				}
			}
		}
	}
}
</style>
