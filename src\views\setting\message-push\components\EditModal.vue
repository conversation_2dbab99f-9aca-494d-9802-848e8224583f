<template lang="pug">
.create-system-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='450',
		v-model='showModal',
		:title='type === 1 ? "编辑" : "新增"',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='90')
					FormItem(label='名称', prop='name')
						Input(v-model='formData.name')
					FormItem(label='IP', prop='ip')
						Input(v-model='formData.ip')
					FormItem(label='端口', prop='port')
						Input(v-model='formData.port')
					FormItem(label='用户名', prop='user')
						Input(v-model='formData.user')
					FormItem(label='密码', prop='password')
						Input(v-model='formData.password', type='password', password)
					FormItem(label='转发topic', prop='topic')
						Input(v-model='formData.topic', placeholder='请输入topic')
					FormItem(label='是否启用', prop='enable')
						RadioGroup(v-model='formData.enable')
							Radio(:label='1') 启用
							Radio(:label='0') 禁用
		template(slot='footer')
			Button(@click='handleClose') 取消
			Button(:loading='btnLoading', type='primary', @click='handleSubForm') 保存
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import { getConnects } from '@/api/data-acquisition-config'
import { addMqttNotifyConfig, updateMqttNotifyConfig } from '@/api/setting'

export default {
	name: 'create-system-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		WaterRow,
	},
	props: {
		showModal: Boolean,
		data: Object,
		type: Number, // 0-添加; 1-编辑
	},
	data() {
		return {
			sysList: [],
			btnLoading: false,
			formData: {
				id: '',
				enable: 1,
				port: '',
				ip: '',
				name: '',
				password: '',
				topic: '',
				user: '',
			},
			formRules: {
				ip: [
					{
						required: true,
						trigger: 'blur',
						message: '必填项',
					},
				],
				name: [
					{
						required: true,
						trigger: 'blur',
						message: '必填项',
					},
				],
				password: [
					{
						required: true,
						trigger: 'blur',
						message: '必填项',
					},
				],
				port: [
					{
						required: true,
						trigger: 'blur',
						message: '必填项',
					},
				],
				topic: [
					{
						required: true,
						trigger: 'blur',
						message: '必填项',
					},
				],
				user: [
					{
						required: true,
						trigger: 'blur',
						message: '必填项',
					},
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				getConnects().then(res => {
					const { result = [] } = res
					this.sysList = result
				})
				// this.formData = { ...this.data }
				for (const key in this.formData) {
					if (Object.hasOwnProperty.call(this.formData, key)) {
						if (this.data[key] || this.data[key] === 0) {
							if (key === 'port') {
								this.formData[key] = this.data[key] + ''
							} else {
								this.formData[key] = this.data[key]
							}
						}
					}
				}
			} else {
				setTimeout(() => {
					this.$refs.form.resetFields()
				}, 200)
			}
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.btnLoading = true
					if (this.type === 0) {
						this.formData.stationWord = this.$route.query.sysCode

						this.addTransChannel(this.formData)
					} else {
						this.updateTransChannel(this.formData)
					}
				}
			})
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		// 新增设备原始档案
		addTransChannel(params) {
			addMqttNotifyConfig(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		// 编辑
		updateTransChannel(params) {
			updateMqttNotifyConfig(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		setResData(res) {
			const { responseCode } = res || {}

			if (responseCode === '100000') {
				this.$Message.success('操作成功')

				this.handleClose()
			}

			this.btnLoading = false
			this.$emit('submit-form', res)
		},
	},
}
</script>

<style lang="less" scoped>
.create-system-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
