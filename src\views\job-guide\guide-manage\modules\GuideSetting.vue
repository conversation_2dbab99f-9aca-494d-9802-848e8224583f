<!--
 * @Description: 指南配置
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-03-14 16:32:31
 * @LastEditors: houyan
 * @LastEditTime: 2024-08-12 14:05:52
-->
<template lang="pug">
.setting
	water-row(justify='space-between', align='center')
		water-row(justify='flex-start', align='center')
			.query-title 指南标题:
			Input.mr10(v-model='formData.title', clearable, placeholder='请输入', style='width: 200px')
			.query-title 启用状态:
			Select.mr10(v-model='formData.enable', clearable, style='width: 200px')
				Option(:value='0') 关闭
				Option(:value='1') 启用
			.query-title 所属分类:
			Select(v-model='formData.typeId', clearable, style='width: 200px')
				Option(v-for='item in typeList', :key='item.id', :value='item.id') {{ item.name }}
			Button.water-margin-left-8(type='primary', @click='handleQuery') 查询
		div
			Button.mr10(type='primary', @click='classifyShow = true') 管理指南分类
			Button(type='primary', @click='handleSet({})') 添加指南
	WaterTable.table-container.mt10(border, :columns='columns', :data='tableData', :loading='loading')
	Page(
		:total='pageTotal',
		show-elevator,
		show-sizer,
		@on-page-size-change='handleChangeSize',
		@on-change='handleChangePage'
	)
	GuideSettingPopup(
		v-model='guideSettingShow',
		:currentRow='currentRow',
		:typeList='typeList',
		@submit-form='handleQuery'
	)
	ManageClassifyPopup(v-model='classifyShow', @close='handleQuery')
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import { queryGuideInfoPage, updateGuideInfo, deleteGuideInfo, getGuideTypeList } from '@/api/other'
import WaterTable from '@/components/gc-water-table'
import GuideSettingPopup from '../components/GuideSettingPopup.vue'
import ClassifyPopup from '../components/ClassifyPopup.vue'
import ManageClassifyPopup from '../components/ManageClassifyPopup.vue'
import { Poptip, Button } from '@eslink/esvcp-pc-ui'
export default {
	components: {
		WaterRow,
		WaterTable,
		GuideSettingPopup,
		ClassifyPopup,
		ManageClassifyPopup,
	},
	data() {
		return {
			formData: {
				title: '',
				enable: '',
				typeId: '',
			},
			columns: [
				{
					title: 'id',
					key: 'id',
					align: 'center',
				},
				{
					title: '所属分类',
					key: 'typeId',
					align: 'center',
					render: (h, params) => {
						const typeName = this.typeList.find(item => item.id === params.row.typeId)?.name
						return h('span', {}, typeName)
					},
				},
				{
					title: '指南标题',
					key: 'title',
					align: 'center',
				},
				{
					title: '排序号',
					key: 'sort',
					align: 'center',
				},

				{
					title: '启用状态',
					key: 'enable',
					align: 'center',
					render: (h, params) => {
						return h('i-switch', {
							props: {
								value: params.row.enable,
								trueValue: 1,
								falseValue: 0,
								size: 'large',
							},
							on: {
								'on-change': value => {
									this.handleEnableChange(params.row, value)
								},
							},
							style: {
								cursor: 'pointer',
							},
							scopedSlots: {
								open: () => h('span', {}, '启用'),
								close: () => h('span', '关闭'),
							},
						})
					},
				},
				{
					title: '操作',
					key: 'deal',
					align: 'center',
					render: (h, params) => {
						const arr = [
							h(
								Poptip,
								{
									props: {
										placement: 'bottom-end',
										title: '确定要删除吗？',
										confirm: true,
										transfer: true,
									},
									on: {
										'on-ok': () => {
											const { id } = params.row
											this._deleteGuideInfo(id)
										},
									},
								},
								[
									h(
										Button,
										{
											class: 'mr10',
											props: {
												type: 'error',
												size: 'small',
											},
										},
										'删除',
									),
								],
							),
							h(
								Button,
								{
									props: {
										type: 'primary',
										size: 'small',
									},
									on: {
										click: () => {
											this.handleSet(params.row)
										},
									},
								},
								'修改',
							),
						]
						return h('div', arr)
					},
				},
			],
			tableData: [],
			loading: false,
			pageSize: 10,
			pageNum: 1,
			pageTotal: 0,
			guideSettingShow: false,
			classifyShow: false,
			currentRow: {},
			typeList: [],
		}
	},
	methods: {
		handleQuery() {
			this._queryGuideInfoPage()
			this._getGuideTypeList()
		},
		_getGuideTypeList() {
			getGuideTypeList().then(res => {
				this.typeList = res.result || []
			})
		},
		_queryGuideInfoPage() {
			const { title, enable, typeId } = this.formData
			const params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				enable,
				title,
				typeId,
			}
			this.loading = true

			queryGuideInfoPage(params)
				.then(res => {
					const { result } = res
					const { list, total } = result

					this.loading = false
					this.tableData = list
					this.pageTotal = total
				})
				.catch(error => {
					console.error(error)
					this.loading = false
				})
		},
		handleChangePage(val) {
			this.pageNum = val
			this._queryGuideInfoPage()
		},
		handleChangeSize(val) {
			this.pageSize = val
			this._queryGuideInfoPage()
		},
		handleEnableChange(row, value) {
			updateGuideInfo({
				...row,
				enable: value,
			}).then(() => {
				this.$Message.success('操作成功')
				this.handleQuery()
			})
		},
		_deleteGuideInfo(id) {
			deleteGuideInfo({
				id,
			}).then(() => {
				this.$Message.success('删除成功')
				this.handleQuery()
			})
		},
		handleSet(row) {
			this.currentRow = row
			this.guideSettingShow = true
		},
	},
	mounted() {
		this.handleQuery()
	},
}
</script>
<style lang="less" scoped>
.setting {
	height: 100%;
	padding: 16px 16px 0 16px;
	display: flex;
	flex-direction: column;
	.query-title {
		white-space: nowrap;
		margin-right: 4px;
	}
	.table-container {
		flex: 1;
	}
	::v-deep {
		.mr10 {
			margin-right: 10px;
		}
	}
}
</style>
