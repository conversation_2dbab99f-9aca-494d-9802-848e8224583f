<template>
	<!-- width="480" -->
	<Modal
		class-name="custom-modal"
		:value="show"
		:transfer="false"
		:title="title"
		@on-cancel="handleCancel"
		@on-visible-change="handleVisibleChange"
	>
		<div class="water-modal-content station-modal-content">
			<Spin fix v-if="listLoading">加载中。。。</Spin>
			<Form ref="formValidate" :model="formItem" :label-width="80" :rules="formRules">
				<Form-item label="基础数据项" prop="baseDataItemId">
					<Select transfer v-model="formItem.baseDataItemId" filterable remote clearable placeholder="请选择">
						<Option v-for="(item, key) in baseDataItems" :value="item.id" :key="key">
							{{ item.baseItemName }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="数据项编号" prop="itemRealCode">
					<Input v-model="formItem.itemRealCode" placeholder="请输入"></Input>
				</Form-item>
				<Form-item label="数据项名称" prop="itemName">
					<Input v-model="formItem.itemName" placeholder="请输入"></Input>
				</Form-item>
				<Form-item label="公式类型" prop="formulaType">
					<Select transfer v-model="formItem.formulaType" remote clearable placeholder="请选择">
						<Option v-for="item in formulaTypes" :key="item.value" :value="item.value">
							{{ item.label }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="公式" prop="formula">
					<Input v-model="formItem.formula" placeholder="请输入"></Input>
				</Form-item>
				<Form-item label="序号" prop="orderBy">
					<Input style="width: 200px" type="number" v-model="formItem.orderBy"></Input>
				</Form-item>
			</Form>
		</div>
		<div slot="footer">
			<Button type="primary" @click="handleCheck">确定</Button>
		</div>
	</Modal>
</template>

<script>
import { saveBaseTemplateDetail, queryPage } from '@/api/base-item'

export default {
	name: 'AddItem',
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		type: {
			type: String,
		},
		id: {
			type: Number,
		},
	},
	created() {},
	data() {
		return {
			title: '新增',
			listLoading: false,
			baseDataItems: [],
			formItem: {
				baseDataItemId: null,
				itemRealCode: '',
				itemName: '',
				formulaType: null,
				formula: '',
				orderBy: null,
			},
			formulaTypes: [
				{
					value: '0',
					label: '原始数据',
				},
				{
					value: '1',
					label: '单位换算',
				},
				{
					value: '2',
					label: '公式计算',
				},
				{
					value: '3',
					label: '状态数据',
				},
			],
			formRules: {
				baseDataItemId: [
					{
						required: true,
						message: '请输入数据项名称',
						trigger: 'change',
						type: 'number',
					},
				],
				itemName: [
					{
						required: true,
						message: '请输入数据项名称',
						trigger: 'change',
						type: 'string',
					},
				],
				itemRealCode: [
					{
						required: true,
						message: '请输入数据项编号',
						trigger: 'change',
						type: 'string',
					},
				],
				formulaType: [
					{
						required: true,
						message: '请选择公式类型',
						trigger: 'change',
					},
				],
			},
		}
	},
	methods: {
		getBaseDataItems(val) {
			queryPage({ type: val, needPage: false }).then(res => {
				this.baseDataItems = res.result.list
			})
		},

		// 弹窗显隐事件
		handleVisibleChange() {},

		handleRemove() {
			this.formItem.processImage = ''
		},
		// 弹窗取消事件
		handleCancel() {
			this.formItem.baseDataItemId = null
			this.formItem.itemRealCode = ''
			this.formItem.itemName = ''
			this.formItem.formulaType = null
			this.formItem.formula = ''
			this.formItem.orderBy = null
			this.baseDataItems = []
			this.$emit('update:show', false)
		},
		// 确定
		handleCheck() {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					this.formItem.type = this.type
					this.formItem.templateId = this.id
					saveBaseTemplateDetail(this.formItem)
						.then(() => {
							this.$Message.success('提交成功!')
							this.handleCancel()
							this.$emit('init')
						})
						.catch(() => {
							this.listLoading = false
						})
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
	},
}
</script>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	.ivu-modal {
		height: auto !important;
	}
	.ivu-input-number-input {
		color: #fff;
		background: #133a5e;
		border: none;
	}
	.ivu-form-item-label {
		width: 125px !important;
	}
	.ivu-form-item-content {
		margin-left: 125px !important;
	}
}
</style>
