/*
 * @Description: 数据采集配置
 * @Author: shenxh
 * @Date: 2023-04-03 09:31:38
 * @LastEditors: shenxh
 * @LastEditTime: 2024-10-09 11:29:15
 */

export default [
	{
		path: '/data-acquisition-config/collect-road-config',
		name: 'collect-road-config',
		component: (/* webpackChunkName: 'collect-road-config' */) =>
			import('@/views/data-acquisition-config/collect-road-config/CollectRoadConfig'),
	},
	{
		path: '/data-acquisition-config/equipment-files',
		name: 'equipment-files',
		component: (/* webpackChunkName: 'equipment-files' */) =>
			import('@/views/data-acquisition-config/equipment-files/equipment-files'),
	},
	{
		path: '/data-acquisition-config/collect-rules-config',
		name: 'collect-rules-config',
		component: (/* webpackChunkName: 'collect-rules-config' */) =>
			import('@/views/data-acquisition-config/collect-rules-config/CollectRulesConfig'),
	},
	{
		path: '/data-acquisition-config/down-control-configuration',
		name: 'down-control-configuration',
		component: (/* webpackChunkName: 'down-control-configuration' */) =>
			import('@/views/data-acquisition-config/down-control-configuration/down-control-configuration'),
	},
	{
		path: '/data-acquisition-config/collect-rules-proofread',
		name: 'collect-rules-proofread',
		component: (/* webpackChunkName: 'collect-rules-proofread' */) =>
			import('@/views/data-acquisition-config/collect-rules-proofread/collect-rules-proofread'),
	},
]
