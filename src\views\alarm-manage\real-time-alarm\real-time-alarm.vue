<!--
 * @Description: 实时报警
 * @Author: shenxh
 * @Date: 2022-03-03 14:42:44
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-28 15:33:33
-->

<template lang="pug">
.real-time-alarm
	warn-list(:hideRight='true')
</template>

<script>
import WarnList from './modules/WarnList.vue'

export default {
	name: 'real-time-alarm',
	components: {
		WarnList,
	},
	props: {},
	data() {
		return {}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {},
}
</script>

<style lang="less" scoped>
.real-time-alarm {
	width: 100%;
	padding: 12px 16px;
}
</style>
