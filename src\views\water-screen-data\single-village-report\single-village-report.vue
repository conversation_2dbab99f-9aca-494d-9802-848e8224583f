<!--
 * @Description: 大屏单村填报
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-30 14:34:21
 * @LastEditors: ya<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-06-07 15:00:59
-->
<template lang="pug">
.single-village-report
	.single-village-report-content
		Tabs(v-model='tabValue', :animated='false')
			TabPane(label='单村数据填报', name='fill')
				fill-list
			TabPane(label='填报记录', name='record')
				record-list
</template>
<script>
import FillList from './modules/SingleVillageFill.vue'
import RecordList from './modules/SingleVillageRecord.vue'
export default {
	name: 'data-fill-management',
	components: {
		FillList,
		RecordList,
	},
	data() {
		return {
			tabValue: 'fill',
		}
	},
}
</script>
<style lang="less" scoped>
.single-village-report {
	height: 100%;
	padding: 16px;
	background-color: rgb(242 242 242) !important;
	&-content {
		height: 100%;
		padding: 16px;
		background: #fff;
	}
	::v-deep {
		.ivu-tabs {
			display: flex;
			flex-direction: column;
			height: 100%;
			.ivu-tabs-content {
				flex: 1;
			}
			.ivu-tabs-tabpane {
				height: 100%;
			}
		}
	}
}
</style>
