<template lang="pug">
Modal(:value='show', class-name='custom-modal', title='视频选择', width='900', @on-visible-change='handleVisibleChange')
	.cont-box 
		.tree
			tree(
				v-if='show',
				ref='tree',
				:data='treeData',
				:load='loadNode',
				lazy,
				highlight-current,
				search-type='lazy',
				@node-click='nodeClick',
				@search='searchNode'
			)
		.table
			.search 
				.form
					.label 设备名称：
					Input(v-model='searchForm.name', placeholder='请输入设备名称', clearable)
				Button(type='primary', @click='_getTableData(1)') 搜索
			.table-box
				es-table(
					v-if='show',
					:columns='columns',
					:data='tableData',
					:loading='tableLoading',
					border,
					showPage,
					:pageData='pageData',
					style='height: calc(100% -100px)',
					@on-page-num-change='handlePageNum',
					@on-page-size-change='handlePageSize'
				)
					template(slot-scope='{ row, index }', slot='radio')
						Radio.radio(v-model='row.radio', @on-change='handleRadioChange(index)', style='margin: 0')
	template(#footer)
		.btns-box 
			Button(@click='handleCancel') 取消
			Button(type='primary', @click='handleCheck') 确定
</template>

<script>
import Tree from '@/components/gc-tree'
import { getSecurityArea, getBindEquipment } from '@/api/security-manage'

export default {
	components: {
		Tree,
	},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		videoCode: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			treeData: [],
			selectedNode: {},

			// 表格
			searchForm: {
				name: '',
			},
			tableLoading: false,
			tableData: [],
			columns: [
				{
					title: ' ',
					width: 60,
					align: 'center',
					slot: 'radio',
				},
				{
					title: '设备名称',
					key: 'name',
				},
				{
					title: '设备编码',
					key: 'code',
				},
				{
					title: '所属平台',
					key: 'platformName',
				},
				{
					title: '原始名称',
					key: 'sourceName',
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 50, 100],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},

			// 选中表格的index
			selectedIndex: null,
		}
	},
	methods: {
		// 获取区域树
		async getAreaTree() {
			try {
				await getSecurityArea()
			} catch (error) {
				//
			}
		},
		// 加载节点
		async loadNode(node, resolve) {
			try {
				const { id } = node.data
				const res = await getSecurityArea({
					id,
					name: this.searchVal,
				})
				resolve(this._setTreeData(res))
			} catch (error) {
				//
				resolve([])
			}
		},
		// 节点被点击时的回调
		nodeClick(data) {
			this.pageData.current = 1
			this.selectedNode = data || {}
			if (data && data.id) {
				this.bindBtnDisabled = false
			} else {
				this.bindBtnDisabled = true
			}
			this._getTableData()
		},
		// 设置树形图数据
		_setTreeData(res) {
			let data = res.result.map(item => {
				return {
					...item,
					leaf: !item.hasChild,
				}
			})
			data.sort((a, b) => {
				return a.sort - b.sort
			})
			return data
		},
		// 搜索节点
		searchNode(val) {
			this.searchVal = val

			this._getTreeData(val)
		},
		// 获取表格数据
		_getTableData(num) {
			const { current: pageNum, pageSize } = this.pageData
			const formData = {
				...this.searchForm,
				areaId: this.selectedNode.id,
				pageNum: num ? num : pageNum,
				pageSize,
			}

			this.tableLoading = true
			getBindEquipment(formData)
				.then(res => {
					const data = res.result

					this.tableData = []
					this.pageData.total = 0
					this.tableLoading = false
					if (data) {
						this.tableData = data.list.map(item => {
							return {
								...item,
								radio: item.code === this.videoCode,
							}
						})
						// 初始化之前选中的视频

						this.pageData.total = data.total
					}
				})
				.catch(() => {
					this.tableData = []
					this.pageData.total = 0
					this.tableLoading = false
				})
		},

		// 表格单选事件
		handleRadioChange(index) {
			this.tableData.forEach(item => {
				this.$set(item, 'radio', false)
			})
			this.tableData[index].radio = true
			this.selectedIndex = index
		},
		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum

			this._getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.current = 1
			this.pageData.pageSize = pageSize

			this._getTableData()
		},

		handleCancel() {
			this.$emit('update:show', false)
		},
		handleCheck() {
			if (!this.selectedIndex && this.selectedIndex !== 0) {
				this.$Message.warning('请先选择视频')
				return
			}
			this.$emit('ok', this.tableData[this.selectedIndex])
			this.handleCancel()
		},
		handleVisibleChange() {
			this.tableData = []
			this.pageData.total = 0
			this.pageData.current = 1
		},
	},
}
</script>

<style lang="less" scoped>
.cont-box {
	display: flex;
	align-items: center;
	height: 400px;
	.tree {
		flex-shrink: 0;
		width: 200px;
		height: 100%;
	}
	.table {
		display: flex;
		flex-direction: column;
		flex: 1;
		height: 400px;
		margin-left: 12px;
		.table-box {
			height: 100%;
		}
	}
}
.search {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
	.form {
		display: flex;
		align-items: center;
		.label {
			flex-shrink: 0;
		}
	}
}
.btns-box {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}
::v-deep {
	.ivu-modal {
		.ivu-modal-content {
			.ivu-modal-header {
				padding: 14px 16px !important;
			}
		}
	}
}
.radio {
	margin-right: 0;
	::v-deep {
		.ivu-radio {
			margin-right: 0;
		}
	}
}
</style>
