import { GET, POST } from '@/utils/request'

// 修复类型列表
export function getStationItemDataRepairTypes(params) {
	return GET({
		url: '/stationItemDataRepair/types',
		params,
	})
}

// 数据修复分页查询
export function getStationItemDataRepair(params) {
	return POST({
		url: '/stationItemDataRepair/page',
		params,
		requestType: 'json',
	})
}

// 数据修复新增
export function createStationItemDataRepair(params) {
	return POST({
		url: '/stationItemDataRepair/add',
		params,
		requestType: 'json',
	})
}

// 图例管理
// 查询全部图例
export function queryMapConfigList(params) {
	return POST({
		url: '/waterPlat/mapConfig/list',
		params,
		requestType: 'json',
	})
}

// 新增图例
export function addMapConfig(params) {
	return POST({
		url: '/waterPlat/mapConfig/add',
		params,
		requestType: 'json',
	})
}

// 删除图例
export function deleteMapConfig(params) {
	const { id } = params
	return GET({
		url: `/waterPlat/mapConfig/delete/${id}`,
		params,
	})
}

// 修改图例
export function updateMapConfig(params) {
	return POST({
		url: '/waterPlat/mapConfig/update',
		params,
		requestType: 'json',
	})
}

// 工作指南分类

// 查询工作指南分类信息
export function getGuideTypeList(params) {
	return POST({
		url: '/waterPlat/guideType/list',
		params,
		requestType: 'json',
	})
}

// 分页查询工作指南分类
export function getGuideTypePage(params) {
	return POST({
		url: '/waterPlat/guideType/page',
		params,
		requestType: 'json',
	})
}

// 新增工作指南分类
export function addGuideType(params) {
	return POST({
		url: '/waterPlat/guideType/add',
		params,
		requestType: 'json',
	})
}
// 更新工作指南分类
export function updateGuideType(params) {
	return POST({
		url: '/waterPlat/guideType/update',
		params,
		requestType: 'json',
	})
}

// 删除工作指南分类
export function deleteGuideType(params) {
	const { id } = params
	return GET({
		url: `/waterPlat/guideType/delete/${id}`,
		params,
	})
}

// 工作指南具体信息
// 新增工作指南具体信息
export function addGuideInfo(params) {
	return POST({
		url: '/waterPlat/guideInfo/add',
		params,
		requestType: 'json',
	})
}
// 工作指南信息修改
export function updateGuideInfo(params) {
	return POST({
		url: '/waterPlat/guideInfo/update',
		params,
		requestType: 'json',
	})
}
// 工作指南信息分页查询
export function queryGuideInfoPage(params) {
	return POST({
		url: '/waterPlat/guideInfo/page',
		params,
		requestType: 'json',
	})
}
// 删除工作指南信息
export function deleteGuideInfo(params) {
	const { id } = params
	return GET({
		url: `/waterPlat/guideInfo/delete/${id}`,
		params,
	})
}

// 权限配置
// 角色列表查询
export function queryRoles(params) {
	return GET({
		url: `/smart/report/configuration/queryRoles`,
		params,
	})
}
// 工作指南权限配置
export function setRoleGuideConfig(params) {
	return POST({
		url: `/waterPlat/roleGuide/config`,
		params,
		requestType: 'json',
	})
}

// 用户工作指南权限查询
export function queryRoleGuide(params) {
	return GET({
		url: `/waterPlat/roleGuide/query`,
		params,
	})
}
