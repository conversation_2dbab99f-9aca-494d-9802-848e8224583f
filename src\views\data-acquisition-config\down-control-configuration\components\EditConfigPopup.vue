<!--
 * @Description: 创建系统弹窗
 * @Author: shenxh
 * @Date: 2022-04-01 15:54:51
 * @LastEditors: hmt
 * @LastEditTime: 2025-01-10 08:55:01
-->
<template lang="pug">
.create-system-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='650',
		v-model='showModal',
		:title='type === 1 ? "编辑" : "新增"',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :label-width='100', :rules='formRules', :key='formKey')
					FormItem(label='设备/对象', prop='stationName')
						Input(v-model='equipmentName', disabled, clearable)
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='原始位号', prop='originalCode')
							AutoComplete(
								v-model='formData.originalCode',
								:data='originalCodeList',
								transfer,
								clearable,
								disabled,
								placeholder='请输入并选择原始位号',
								style='width: 200px',
								@on-search='queryTransOriginalCode',
								@on-select='handleOriginalCode'
							)
						FormItem(label='原始描述', prop='originalName')
							Input(v-model='formData.originalName', clearable, style='width: 200px', disabled)
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='变量类型', prop='baseItemCode')
							Select(
								v-model='formData.baseItemCode',
								filterable,
								transfer,
								clearable,
								:loading='baseItemCodeCodeLoading',
								style='width: 200px',
								disabled
							)
								Option(
									v-for='(item, index) in baseItemCodeList',
									:value='item.baseItemCode',
									:key='index',
									@click.native='handleBaseItemCode(item, index)'
								) {{ item.baseItemName }}
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='平台变量', prop='itemCode')
							Input(v-model='formData.itemCode', placeholder='请输入平台变量', clearable, style='width: 200px', disabled)
						FormItem(label='数据项名称', prop='itemName')
							Input(v-model='formData.itemName', placeholder='请输入数据项名称', clearable, style='width: 200px', disabled)
					FormItem(label='备注', prop='memo')
						Input(v-model='formData.memo', type='textarea', placeholder='请输入', disabled)
					FormItem(label='数据格式', prop='formulaType')
						Radio-group(v-model='formData.formulaType')
							Radio(label='0', disabled) 普通数据
							Radio(label='3', disabled) 状态数据
					FormItem(label='状态选项', prop='', v-if='formData.formulaType == "3"', disabled)
						.self-box
							.head
								.head-item 状态
								.head-item 描述语
							.main
								.main-line(v-for='(item, index) in formData.formulas')
									.main-item
										Input(placeholder='请输入...', style='width: 80%', disabled, v-model='item.state')
									.main-item
										Input(placeholder='请输入...', style='width: 80%', disabled, v-model='item.desc')
					water-row.table-btn-wrap(justify='space-between')
						FormItem(label='读写状态', prop='readWriteFlag')
							Radio-group(v-model='formData.readWriteFlag')
								Radio(label='0') 只读
								Radio(label='1') 可读写
						FormItem(label='开启平台下控', prop='downControlFlag')
							i-switch(v-model='formData.downControlFlag')
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(
							label='下控小数位数',
							prop='writePrecision',
							:label-width='120',
							:rules='{ required: formData.formulaType == "0", validator: zsLimit10, trigger: "change" }'
						)
							Input(v-model='formData.writePrecision', style='width: 180px')
						FormItem(
							label='下控最大值',
							prop='writeMax',
							:rules='{ required: formData.formulaType == "0", validator: validateTriggerNumCustom, trigger: "change" }'
						)
							Input(v-model='formData.writeMax', style='width: 200px', placeholder='请输入最大值')

					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(
							label='下控最小值',
							prop='writeMin',
							:rules='{ required: formData.formulaType == "0", validator: validateTriggerNumCustom, trigger: "change" }'
						)
							Input(v-model='formData.writeMin', style='width: 200px', placeholder='请输入最小值')
						FormItem(label='下控类型', prop='writeType')
							Select(v-model='formData.writeType', clearable, style='width: 200px', transfer)
								Option(v-for='(item, index) in typeList', :value='item.value', :key='index') {{ item.name }}
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='排序号', prop='order')
							Input(v-model='formData.order', style='width: 200px', placeholder='请输入排序号')

						FormItem(label='下控显示数据项', prop='showItemCode', :label-width='120')
							Select(v-model='formData.showItemCode', filterable, transfer, clearable, style='width: 180px')
								Option(v-for='(item, index) in itemList', :value='item.value', :key='index') {{ item.lable }}
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='数值类型', prop='showItemCode', :label-width='120')
							Select(v-model='formData.valueType', filterable, transfer, clearable, style='width: 180px')
								Option(v-for='(item, index) in dataTypeList', :value='item.value', :key='index') {{ item.name }}

		template(slot='footer')
			Button(@click='handleClose') 取消
			Button(:loading='btnLoading', type='primary', @click='handleSubForm') 保存
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import { addTransRule, updateTransRule, queryTransOriginalCode, showItem } from '@/api/data-acquisition-config'
import { queryPage } from '@/api/base-item'

export default {
	name: 'edit-config-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		WaterRow,
	},
	props: {
		showModal: Boolean,
		equipmentData: Object,
		collectRoadData: Object,
		data: Object,
		transChannelList: Array,
		type: Number, // 0-添加; 1-编辑
	},
	data() {
		// let self = this
		const validateCode = (rule, value, callback) => {
			if (!value || !value.trim()) {
				callback(new Error('请输入'))
			} else {
				callback()
			}
		}
		const zsLimit = (rule, value, callback) => {
			const reg = /^[1-9]\d*$/
			if (reg.test(value) && value) {
				// const num = Number(value)
				callback()
			} else {
				callback(new Error('请输入正整数'))
			}
		}
		return {
			formKey: 0,
			btnLoading: false,
			originalCodeLoading: false,
			baseItemCodeCodeLoading: false,
			formData: {
				downControlFlag: '0',
				formulaType: '',
			},
			baseItemCodeList: [],
			originalCodeList: [],
			originalCodeDataList: [],
			formRules: {
				originalCode: [
					{
						required: true,
						validator: validateCode,
						trigger: 'blur',
					},
				],
				baseItemCode: [
					{
						required: true,
						message: '请输入',
						trigger: 'change',
					},
				],
				itemCode: [
					{
						required: true,
						validator: validateCode,
						trigger: 'blur',
					},
				],
				itemName: [
					{
						required: true,
						validator: validateCode,
						trigger: 'blur',
					},
				],
				order: [
					{
						required: false,
						validator: zsLimit,
						trigger: 'change',
					},
				],
			},
			testArr: [],
			typeList: [
				{ name: '文本', value: 'text' },
				{ name: '单选按钮', value: 'radio' },
				// { name: '下拉框', value: 'select' },
				// { name: '复选框', value: 'checkbox' },
				// { name: '密码', value: 'password' },
				{ name: '开关', value: 'switch' },
			],
			itemList: [],
			dataTypeList: [
				{ name: '字符串', value: 'STRING' },
				{ name: '浮点型', value: 'FLOAT' },
				{ name: '布尔型', value: 'BOOLEAN' },
				{ name: '整型', value: 'INT' },
			],
		}
	},
	computed: {
		equipmentName() {
			let str = ''

			if (this.type === 0) {
				const { stationName = '', stationCode = '' } = this.equipmentData

				if (stationCode) {
					str = `${stationName} (${stationCode})`
				}
			} else {
				const { stationName = '', stationCode = '' } = this.data

				if (stationCode) {
					str = `${stationName} (${stationCode})`
				}
			}

			return str
		},
	},
	watch: {
		showModal(val) {
			if (val) {
				this.getItemList()
			}
		},
		collectRoadData() {
			this.getBaseDataItems()
		},
		'formData.formulaType': {
			handler(val) {
				if (val == '0') {
					this.$set(this.formData, 'writeType', this.formData.writeType ? this.formData.writeType : 'text')
				} else {
					this.$set(this.formData, 'writeType', this.formData.writeType ? this.formData.writeType : 'radio')
				}
			},
		},
	},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		zsLimit10(rule, value, callback) {
			if (this.formData.formulaType == '0') {
				if (!value) {
					callback(new Error('请输入'))
				} else {
					const reg = /^[1-9]\d*$/
					if (reg.test(value) && value) {
						const num = Number(value)
						if (num > 10) {
							callback(new Error('小数位数最多为10位'))
						} else {
							callback()
						}
					} else {
						callback(new Error('请输入正整数'))
					}
				}
			} else {
				callback()
			}
		},
		validateTriggerNumCustom(rule, value, callback) {
			if (this.formData.formulaType == '0') {
				if (value) {
					const num = Number(value)
					if (isNaN(num)) {
						callback(new Error('请输入有效数字'))
					} else {
						callback()
					}
				} else {
					callback(new Error('请输入'))
				}
			} else {
				callback()
			}
		},

		getItemList() {
			const stationCodeRef = this.type === 0 ? this.equipmentData?.stationCode : this.data?.stationCode

			showItem({
				stationCodeRef,
			}).then(res => {
				this.itemList = res.result
			})
		},
		// 新增数据状态列
		addLine() {
			this.testArr.push(6)
		},
		removeLine(index) {
			this.testArr.splice(index, 1)
		},
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				this.formData = { ...this.data }
			} else {
				setTimeout(() => {
					this.$refs.form.resetFields()
					this.formKey++
				}, 200)
			}
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					Object.assign(this.formData, {
						sysCode: this.collectRoadData.sysCode,
						transChannelCode: this.collectRoadData.channelCode,
						stationCode: this.equipmentData.stationCode || '',
					})
					this.btnLoading = true
					if (this.type === 0) {
						this.addTransRule(this.formData)
					} else {
						this.updateTransRule(this.formData)
					}
				}
			})
		},

		// 按钮-关闭
		handleClose() {
			this.formData.formulaType = '0'
			this.$nextTick(() => {
				this.$emit('set-value', false)
			})
		},

		// 点击原始位号
		handleOriginalCode(val) {
			const data = this.originalCodeDataList.find(item => item.originalCode === val) || {}
			const { originalName = '' } = data

			this.formData.originalName = originalName
		},

		// 点击变量类型
		handleBaseItemCode(data = {}) {
			const { id, baseItemName, baseItemCode } = data
			this.formData = {
				...this.formData,
				baseItemId: id,
				baseItemName,
				itemCode: baseItemCode + '_',
			}
		},

		// 新增采集规则
		addTransRule(params) {
			addTransRule(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		// 编辑采集规则
		updateTransRule(params) {
			updateTransRule(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		setResData(res) {
			const { responseCode } = res || {}

			if (responseCode === '100000') {
				this.$Message.success('操作成功')

				this.handleClose()
			}

			this.btnLoading = false
			this.$emit('submit-form', res)
		},

		// 原始位号下拉框查询
		queryTransOriginalCode(val = '') {
			this.originalCodeLoading = true
			queryTransOriginalCode({
				transChannelCode: this.collectRoadData.transChannelCode,
				originalCode: val.trim(),
				pageNum: 1,
				pageSize: 10,
			}).then(res => {
				const { result = {} } = res || {}
				const { list = [] } = result

				this.originalCodeList = list.map(item => item.originalCode)
				this.originalCodeDataList = list
				this.originalCodeLoading = false
			})
		},

		// 变量类型下拉框
		getBaseDataItems(val) {
			this.baseItemCodeCodeLoading = true
			queryPage({
				baseItemName: val,
				type: this.collectRoadData.sysCode,
				needPage: false,
				// pageNum: 1,
				// pageSize: 10,
			}).then(res => {
				const { result = {} } = res || {}
				const { list = [] } = result
				this.baseItemCodeList = list
				this.baseItemCodeCodeLoading = false
			})
		},
	},
}
</script>

<style lang="less" scoped>
.create-system-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
	.self-box {
		width: 100%;
		.head {
			width: 100%;
			display: flex;
			.head-item {
				width: 33.33%;
				text-align: center;
			}
		}
		.main {
			width: 100%;
			.main-line {
				width: 100%;
				display: flex;
				align-items: center;
				margin-bottom: 8px;
				.main-item {
					width: 33.33%;
					display: flex;
					align-items: center;
					justify-content: center;
					.icon {
						width: 20px;
					}
				}
			}
		}
		.addbtn {
			width: 61%;
			margin-left: 3%;
			height: 32px;
			line-height: 32px;
			text-align: center;
			border-radius: 10px;
			border: 1px solid #5cadff;
			font-size: 28px;
		}
	}
}

/deep/ .es-modal {
	.ivu-modal {
		max-height: 100% !important;
	}
}
</style>
