<!--
 * @Description: RjghtWarnAnalysis.vue
 * @Version: 2.0
 * @Autor: hanmengtian
 * @Date: 2022-06-23 10:34:47
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-23 15:13:42
-->
<template>
	<div class="right-aside">
		<div class="right-aside--child">
			<div class="sum-up">{{ analysisObj.totalNumber }}条</div>
			<div class="right-aside--child--sub f-fontSize--12">本月累计告警统计</div>
		</div>
		<div class="right-aside--child f-mt--10" :class="[analysisObj.areaList.length > 4 ? 'scroll' : '']">
			<div class="sum-up">本月区域报警统计</div>
			<div class="f-flex--start" v-for="(item, index) in analysisObj.areaList" :key="index">
				<Tooltip placement="top" :content="item.areaName" class="right-aside--child--text" transfer>
					<span class="right-aside--child--sub f-fontSize--12 long-text">
						{{ item.areaName }}
					</span>
				</Tooltip>
				<span
					class="right-aside--child--progress"
					:style="`width: ${dealDataWidth(item.thisTypeNum, analysisObj.maxAreaNumber)}px`"
				></span>
				<span class="right-aside--child--sub f-fontSize--12">
					{{ item.thisTypeNum }}
				</span>
			</div>
		</div>
		<div class="right-aside--child f-mt--10">
			<div class="sum-up">本月泵房报警次数统计top5</div>
			<div class="f-flex--start" v-for="(item, index) in analysisObj.weekList" :key="index">
				<Tooltip placement="top" :content="item.stationName" class="right-aside--child--text" transfer>
					<span class="right-aside--child--sub f-fontSize--12 long-text">
						{{ item.stationName }}
					</span>
				</Tooltip>
				<span
					class="right-aside--child--progress"
					:style="`width: ${dealDataWidth(item.thisTypeNum, analysisObj.maxWeekNumber)}px`"
				></span>
				<span class="right-aside--child--sub f-fontSize--12">
					{{ item.thisTypeNum }}
				</span>
			</div>
		</div>
		<!-- <div class="right-aside--child f-mt--10 echart-box">
			<div class="sum-up">7天内告警类型统计对比</div>
			<div id="echart" style="width: 100%; height: 100%"></div>
		</div> -->
	</div>
</template>

<script>
export default {
	props: {
		analysisObj: {
			type: Object,
			default: () => {
				let obj = {
					totalNumber: 0,
					maxAreaNumber: 0,
					maxWeekNumber: 0,
					areaList: [],
					weekList: [],
				}
				return obj
			},
		},
	},
	methods: {
		dealDataWidth(num, maxNumber) {
			const width = (num / maxNumber) * this.getWithNumber(maxNumber)
			return parseInt(width)
		},
		//动态获取宽带
		getWithNumber(num) {
			const width = num < 10 ? 30 : num < 20 ? 150 : 185
			return Number(width)
		},
	},
}
</script>

<style lang="less" scoped>
.flex_init_func(@ai: stretch, @jc: flex-start) {
	display: flex;
	justify-content: @jc;
	align-items: @ai;
}
.f-fontSize--12 {
	font-size: 12px;
}
.f-flex--start {
	.flex_init_func(center; flex-start);
}
.f-mt--10 {
	margin-top: 10px;
}

.right-aside {
	width: 100%;
	height: 100%;
	border-left: 1px dashed #d1d3e0;
	padding: 0 12px;
	&--child {
		padding: 8px;
		background: #f9f9fc;
		border-radius: 4px;
		border: 1px solid #e6e7f0;
		max-height: 180px;
		&.echart-box {
			height: 200px;
		}
		&.scroll {
			overflow-y: scroll;
			&::-webkit-scrollbar {
				width: 4px;
			}
		}
		.sum-up {
			color: #25273d;
			font-weight: bold;
		}
		&--sub {
			color: #c9c9c9;
			line-height: 20px;
			&.long-text {
				width: 52px;
				box-sizing: border-box;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				display: inline-block;
				cursor: pointer;
			}
		}
		&--progress {
			display: inline-block;
			height: 6px;
			background: #149be6;
			border-radius: 3px;
			margin: 0 4px;
			max-width: 185px;
		}
	}
}
</style>
