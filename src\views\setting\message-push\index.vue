<template lang="pug">
.collect-road-config
	.header-wrap
		es-header.header(title='消息推送管理')

	water-row.table-btn-wrap(justify='space-between', align='center')
		i
		Button(type='primary', @click='handleCreate') 新增
	.table-wrap
		es-table(
			:columns='columns',
			:data='tableData',
			:loading='loading',
			border,
			showPage,
			:pageData='pageData',
			@on-page-num-change='handlePageNum',
			@on-page-size-change='handlePageSize'
		)
			template(slot-scope='{ row }', slot='enable')
				span {{ row.enable === 1 ? `启用` : `禁用` }}
			template(slot-scope='{ row }', slot='action')
				Button.water-margin-right-8(
					:type='row.enable === 1 ? "error" : "primary"',
					@click='handleEnable(row)',
					size='small'
				) {{ row.enable === 1 ? `禁用` : `启用` }}
				Button(type='primary', @click='handleRowUpd(row)', size='small') 编辑
	create-system-popup(v-model='showModal', :data='currentRow', :type='popupType', @submit-form='handleSubForm')
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import CreateSystemPopup from './components/EditModal.vue'
import { getTopicDel } from '@/api/data-acquisition-config'
import { queryMqttNotifyConfig } from '@/api/setting'
import { updateMqttNotifyConfig } from '@/api/setting'

export default {
	name: 'collect-road-config',
	components: {
		WaterRow,
		CreateSystemPopup,
	},
	props: {},
	data() {
		return {
			popupType: 0,
			showModal: false,
			loading: false,
			currentRow: {},
			tableData: [],
			columns: [
				{
					title: '名称',
					key: 'name',
				},
				{
					title: 'IP',
					key: 'ip',
				},
				{
					title: '端口',
					key: 'port',
				},
				{
					title: '用户名',
					key: 'user',
				},
				{
					title: '转发topic',
					key: 'topic',
				},
				{
					title: '是否启用',
					slot: 'enable',
				},
				{
					title: '创建时间',
					key: 'createTime',
				},
				{
					title: '操作',
					slot: 'action',
					align: 'center',
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 50, 100, 200],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		this.getTableData()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 搜索按钮
		handleSearchBtn(params) {
			this.form = params

			this.getTableData()
		},

		// 重置按钮
		handleResetBtn() {
			this.form = {}
			this.pageData.current = 1

			this.getTableData()
		},

		// 添加按钮
		handleCreate() {
			this.currentRow = {}
			this.popupType = 0
			this.showModal = true
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum

			this.getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize

			this.getTableData()
		},

		// 行-编辑
		handleRowUpd(row) {
			this.currentRow = row
			this.popupType = 1
			this.showModal = true
		},
		handleEnable(row) {
			this.$Modal.confirm({
				title: `确定执行${row.enable === 1 ? `禁用` : `启用`}吗？`,
				content: '',
				onOk: () => {
					this.dealEnable(row)
				},
			})
		},
		dealEnable(row) {
			const { id, enable } = row
			let params = {
				id,
			}
			if (enable == 1) {
				params.enable = 0
			} else {
				params.enable = 1
			}
			updateMqttNotifyConfig(params).then(res => {
				const { responseCode } = res || {}

				if (responseCode === '100000') {
					this.$Message.success('操作成功')
					this.getTableData()
				}
			})
		},

		// 行-删除
		handleRowDel(row) {
			let pam = { topicId: row.topicId }
			this.getTopicDelFn(pam)
		},

		// 弹窗按钮-保存
		handleSubForm() {
			this.getTableData()
		},

		getText(str = '') {
			return str.replace(/\n/g, '<br>')
		},

		// 获取表格数据
		getTableData() {
			this.loading = true
			queryMqttNotifyConfig({
				pageNum: this.pageData.current,
				pageSize: this.pageData.pageSize,
				// needPage: true,
			}).then(res => {
				const { result = {} } = res || {}
				const { list = [], total } = result

				this.tableData = list
				this.pageData.total = total
				this.loading = false
			})
		},

		// 删除采集渠道
		getTopicDelFn(params) {
			getTopicDel(params).then(res => {
				const { responseCode } = res || {}

				if (responseCode === '100000') {
					this.$Message.success('操作成功')

					this.getTableData()
				}
			})
		},
	},
}
</script>

<style lang="less" scoped>
.collect-road-config {
	padding: 0 16px;
	/deep/ .es-search {
		height: inherit;
		padding: 8px 0;
		margin-bottom: 8px;
		.prefix {
			max-width: inherit;
			.ivu-tooltip-rel {
				max-width: inherit;
				.prefix-title {
					max-width: inherit;
				}
			}
		}
	}
	.table-btn-wrap {
		margin-bottom: 8px;
	}
	.table-wrap {
		width: 100%;
		height: calc(100vh - 150px);
	}
}
</style>
