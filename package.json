{"name": "water-plat", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode dev", "dev": "vue-cli-service serve --mode dev", "build": "vue-cli-service build --mode test", "build:pro": "vue-cli-service build --mode pro", "build:pre": "vue-cli-service build --mode pre", "build:js": "vue-cli-service build --mode js-local", "build:yqLocal": "vue-cli-service build --mode yq-local", "build:yqsw": "vue-cli-service build --mode yq-sw", "build:wzzlsLocal": "vue-cli-service build --mode wzzls-local", "build:wzzls": "vue-cli-service build --mode wzzls", "build:pysw-local": "vue-cli-service build --mode pysw-local", "build:ya-local": "vue-cli-service build --mode ya-local", "build:xtsy-local": "vue-cli-service build --mode xtsy-local", "analyz": "vue-cli-service build --mode analyz", "lint": "vue-cli-service lint", "commit": "git add . && git cz"}, "dependencies": {"@easydarwin/easyplayer": "^5.0.7", "@eslink/esvcp-pc-ui": "^2.1.19-beta.121", "core-js": "^3.6.5", "drag-arc": "^1.0.8", "echarts": "^5.4.2", "esvcp-pc": "^0.1.88", "fabric": "^5.2.1", "gifuct-js": "^2.1.2", "md5": "^2.2.1", "moment": "^2.29.1", "proj4": "^2.8.0", "three": "^0.115.0", "tweenjs": "^1.0.2", "vue": "^2.6.11", "vue-element-tree": "1.0.11", "vue-router": "^3.4.3", "vue-sketch-ruler": "^1.0.3", "vue-ueditor-wrap": "^2.5.6", "vuex": "^3.4.0"}, "devDependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@commitlint/cli": "^9.1.2", "@commitlint/config-conventional": "^9.1.2", "@e-cloud/es-commitlint": "0.0.2", "@prettier/plugin-pug": "^1.20.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "compression-webpack-plugin": "^5.0.2", "eslint": "^6.8.0", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^6.2.2", "husky": "^4.3.8", "less": "^3.9.0", "less-loader": "^5.0.0", "lint-staged": "^10.5.4", "lodash-es": "^4.17.21", "node-sass": "^6.0.1", "prettier": "^2.2.1", "pug": "^3.0.0", "pug-plain-loader": "^1.0.0", "sass-loader": "^10.2.0", "vue-template-compiler": "^2.6.11", "vuedraggable": "^2.24.3"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx,html,css,vue,less,scss}": "prettier  --plugin-search-dir ./node_modules --write"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "_customHasUsedEsStandard": true}