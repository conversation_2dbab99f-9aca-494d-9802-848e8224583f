<!--
 * @Description: 热线填报页面
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-30 16:35:42
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-10 15:28:26
-->
<template lang="pug">
.hotline-fill-list
	water-row(justify='space-between', align='center')
		water-row(justify='flex-start', align='center')
			.hotline-fill-list-form-title 日期选择:
			DatePicker.water-margin-right-16(
				v-model='date',
				format='yyyy-MM-dd',
				:clearable='false',
				type='date',
				style='width: 215px',
				:options='options',
				@on-change='handleQuery()',
				placement='bottom-end'
			)
		Button.water-margin-left-16(type='primary', @click='handleSave()', :loading='buttonLoading') 提交
	WaterTable.hotline-fill-list-table(border, :columns='getColumns()', :data='tableData', :loading='loading')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { queryHotlineData, insertHotlineFillData } from '@/api/water-screen-data.js'
import { EventBus } from '@/utils/eventBus.js'
export default {
	components: {
		WaterTable,
		WaterRow,
	},
	data() {
		return {
			date: this.$moment(new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).format('YYYY-MM-DD'), //默认选择前一天
			tableData: [],
			itemValueObj: {
				callInNum: {
					content: '昨日呼入次数',
					desc: '次',
					editable: true,
				},
				giveUpCallNum: {
					content: '未接次数（放弃呼叫次数）',
					desc: '次',
					editable: true,
				},
				callOutNum: {
					content: '呼出次数',
					desc: '次',
					editable: true,
				},
				callInTime: {
					content: '呼入时长',
					desc: '分钟（不足一分钟按一分钟算）',
					editable: true,
				},
				callOutTime: {
					content: '呼出时长',
					desc: '分钟（不足一分钟按一分钟算）',
					editable: true,
				},
				callTotalTime: {
					content: '服务时长',
					desc: '呼入时长+呼出时长',
					editable: false,
				},
				callLoseRate: {
					content: '呼损率',
					desc: '昨日未接次数/昨日话务量',
					editable: false,
				},
				callTotalNum: {
					content: '昨日话务量',
					desc: '(次)昨日呼入+呼出',
					editable: false,
				},
			},
			columns: [],
			loading: false,
			buttonLoading: false,
			options: {
				disabledDate(date) {
					return date.getTime() > new Date(new Date().getTime() - 24 * 60 * 60 * 1000).getTime()
				},
			},
			callTotalNum: 0,
			callTotalTime: 0,
		}
	},
	mounted() {
		this.handleQuery()
	},
	methods: {
		handleQuery() {
			queryHotlineData({
				date: this.$moment(this.date).format('YYYY-MM-DD'),
			})
				.then(res => {
					const { result = [] } = res
					const tableData = []
					let callTotalNum = 0
					let callTotalTime = 0
					if (result.length > 0) {
						result.forEach(item => {
							if (item.itemCode === 'callInNum' || item.itemCode === 'callOutNum') {
								const value = item.value ? item.value : 0
								callTotalNum = callTotalNum + Number(value)
							}
							if (item.itemCode === 'callInTime' || item.itemCode === 'callOutTime') {
								const value = item.value ? item.value : 0
								callTotalTime = callTotalTime + Number(value)
							}
							if (this.itemValueObj[item.itemCode]) {
								tableData.push({
									content: this.itemValueObj[item.itemCode].content,
									desc: this.itemValueObj[item.itemCode].desc,
									itemCode: item.itemCode,
									editable: this.itemValueObj[item.itemCode].editable,
									itemValue: this.getValue(item, callTotalNum, callTotalTime),
								})
							}
						})
						this.tableData = tableData
						this.getCallLoseRate()
					} else {
						this.handleDealData()
					}
				})
				.catch(() => {
					this.handleDealData()
				})
		},
		handleDealData() {
			const keys = Object.keys(this.itemValueObj)
			this.tableData = keys.map(item => {
				return {
					content: this.itemValueObj[item].content,
					desc: this.itemValueObj[item].desc,
					itemCode: item.itemCode,
					editable: this.itemValueObj[item.itemCode].editable,
					itemValue: item.value,
				}
			})
		},
		getValue(item, callTotalNum, callTotalTime) {
			if (item.itemCode === 'callTotalNum') {
				return callTotalNum
			} else if (item.itemCode === 'callTotalTime') {
				return callTotalTime
			} else {
				return item.value
			}
		},
		getCallLoseRate() {
			const n = this.tableData.findIndex(item => item.itemCode === 'giveUpCallNum')
			const k = this.tableData.findIndex(item => item.itemCode === 'callTotalNum')
			const m = this.tableData.findIndex(item => item.itemCode === 'callLoseRate')
			const itemValue = this.tableData[n].itemValue ? this.tableData[n].itemValue : ''
			const value = this.tableData[k].itemValue ? this.tableData[k].itemValue : ''
			let newVal
			if (itemValue && value) {
				newVal = ((Number(itemValue) / Number(value)) * 100).toFixed(2)
			} else {
				newVal = 0
			}

			this.tableData[m].itemValue = newVal
		},
		//保存数据项
		handleSave() {
			try {
				this.buttonLoading = true
				const list = []
				this.tableData.forEach(item => {
					const { itemCode = '', itemValue = '' } = item
					list.push({
						itemCode,
						value: Number(itemValue),
					})
				})

				const params = {
					date: this.$moment(this.date).format('YYYY-MM-DD'),
					list,
				}
				insertHotlineFillData(params)
					.then(() => {
						this.$Message.success('提交成功!')
						this.buttonLoading = false
						this.handleQuery()
						EventBus.$emit('fresh-hotline-record')
					})
					.catch(() => {
						this.buttonLoading = false
					})
			} catch {
				this.buttonLoading = false
			}
		},
		getColumns() {
			const columns = [
				{
					title: '内容明细',
					key: 'content',
					minWidth: 160,
					align: 'center',
				},
				{
					title: '申报值',
					key: 'itemValue',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { itemValue, editable, itemCode } = params.row
						return h('Input', {
							props: {
								value: itemValue,
								maxlength: 18,
								disabled: !editable,
							},
							on: {
								'on-change': e => {
									this.valueChange(e)
									const value = e.target.value

									if (itemCode === 'callInNum' || itemCode === 'callOutNum') {
										const key = itemCode === 'callInNum' ? 'callOutNum' : 'callInNum'
										this.handleInputTotal(value, key, 'callTotalNum')
										this.getCallLoseRate()
									}
									if (itemCode === 'callInTime' || itemCode === 'callOutTime') {
										const key = itemCode === 'callInTime' ? 'callOutTime' : 'callInTime'
										this.handleInputTotal(value, key, 'callTotalTime')
									}
									this.handleInputValue(params.index, value, 'itemValue')
									if (itemCode === 'giveUpCallNum') {
										this.getCallLoseRate()
									}
								},
								'on-keyup': e => {
									this.valueChange(e)
								},
							},
						})
					},
				},
				{
					title: '统计说明',
					key: 'desc',
					minWidth: 160,
					align: 'center',
				},
			]
			return columns
		},
		//正则
		valueChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			e.target.value = e.target.value.replace(/^(([0-9]{15}\d))(.\d{1,2})?$/, '')
		},
		handleInputTotal(value, key, itemCode) {
			const n = this.tableData.findIndex(item => item.itemCode === key)
			const k = this.tableData.findIndex(item => item.itemCode === itemCode)
			const itemValue = this.tableData[n].itemValue ? this.tableData[n].itemValue : 0
			const newVal = Number(itemValue) + Number(value)
			this.tableData[k].itemValue = newVal
		},
		//输入值
		handleInputValue(index, value, key) {
			this.tableData[index][key] = value
			// const item = this.tableData[index]
			// this.tableData.splice(index, 1, {
			// 	...item,
			// 	[key]: value,
			// })
		},
	},
}
</script>
<style scoped lang="less">
.hotline-fill-list {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-form {
		margin-right: 24px;
		&-title {
			margin-right: 4px;
		}
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
}
</style>
