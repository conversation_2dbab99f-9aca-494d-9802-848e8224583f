/*
 * @Descripttion:
 * @version:
 * @Author: heliping
 * @Date: 2022-03-18 10:00:59
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2025-01-03 14:26:41
 */
const setting = [
	{
		path: '/setting/2d-index',
		name: '2d-index',
		component: (/* webpackChunkName: 'static-page' */) => import('@/views/setting/2d-config'),
	},
	{
		path: '/setting/item-config',
		name: '2d-index',
		component: (/* webpackChunkName: 'static-page' */) => import('@/views/setting/item-config'),
	},
	{
		path: '/setting/2d-set-label',
		name: '2d-set-label',
		component: (/* webpackChunkName: 'static-page' */) => import('@/views/setting/2d-config/set-label'),
	},
	{
		path: '/setting/3d-index',
		name: '3d-index',
		component: (/* webpackChunkName: 'static-page' */) => import('@/views/setting/3d-index.vue'),
	},
	{
		path: '/data-deal',
		name: 'data-deal',
		component: (/* webpackChunkName: 'static-page' */) => import('@/views/setting/datadeal.vue'),
	},
	{
		path: '/setting/3d-index-simple',
		name: '3d-index-simple',
		component: (/* webpackChunkName: 'static-page' */) => import('@/views/setting/3d-index-simple.vue'),
	},
	{
		path: '/setting/3d-associated-config',
		name: '3d-associated-config',
		component: (/* webpackChunkName: 'static-page' */) => import('@/views/setting/3d-associated-config'),
	},
	{
		path: '/setting/tp-setting',
		name: 'topic-setting',
		component: (/* webpackChunkName: 'static-page' */) => import('@/views/setting/topic/topic-setting'),
	},
	// 消息推送管理
	{
		path: '/message-push',
		name: 'message-push',
		component: (/* webpackChunkName: 'message-push' */) => import('@/views/setting/message-push/index'),
	},
	// 短信配置
	{
		path: '/setting/SMS-setting',
		name: 'SMS-setting',
		component: (/* webpackChunkName: 'SMS-setting' */) => import('@/views/setting/SMS-setting/index'),
	},
]
export default setting
