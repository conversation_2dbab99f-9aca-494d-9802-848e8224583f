<!--
 * @Description: 告警记录详情
 * @Autor: Fengjialing
 * @Date: 2025-05-13
 * @LastEditors: Fengjialing
 * @LastEditTime: 2025-05-13
-->
<template>
	<div class="container">
		<div class="container-header">
			<Button type="text" @click="handleBack" class="back-button">返回</Button>
		</div>
		<DetailGroup :detail="deviceInfo"></DetailGroup>
		<DetailGroup :detail="alarmInfo"></DetailGroup>
		<DetailGroup :detail="userInfo"></DetailGroup>
		<DetailGroup :detail="handleInfo"></DetailGroup>
	</div>
</template>
<script>
import DetailGroup from './DetailGroup'
import { apiGetAlarmRecordDetail } from '@/api/alarm-config.js'

export default {
	components: { DetailGroup },
	data() {
		return {
			deviceInfo: {
				title: '设备信息',
				data: [
					{ label: '设备类型', key: 'nodeType', type: 'text' },
					{ label: '设备编号', key: 'nodeCode', type: 'text' },
					{ label: '安装时间', key: 'installTime', type: 'text' },
					{ label: '安装地址', key: 'address', type: 'text' },
				],
			},
			alarmInfo: {
				title: '告警信息',
				data: [
					{ label: '告警类型', key: 'alarmTypeName', type: 'text' },
					{ label: '告警等级', key: 'alarmLevelName', type: 'text' },
					{ label: '告警时间', key: 'lastActiveTime', type: 'text' },
					{ label: '告警值', key: 'alarmValue', type: 'text' },
					{ label: '设定值', key: 'alarmLimit', type: 'text' },
					{ label: '最后告警时间', key: 'lastActiveTime', type: 'text' },
					{ label: '告警说明', key: 'alarmDetail', col: '100', type: 'text' },
				],
			},
			userInfo: {
				title: '用户信息',
				data: [
					{ label: '用户姓名', key: 'userName', type: 'text' },
					{ label: '身份证号', key: 'identityNum', type: 'text' },
					{ label: '联系电话', key: 'contactPhone', type: 'text' },
					{ label: '联系地址', key: 'contactAddress', type: 'text' },
				],
			},
			handleInfo: {
				title: '处理信息',
				data: [
					{ label: '处理状态', key: 'disposeStatusDesc', type: 'text' },
					{ label: '处理方式', key: 'processWay', type: 'text' },
					{ label: '处理时间', key: 'processTime', type: 'text' },
					{ label: '处理人', key: 'processPersonId', type: 'text' },
					{ label: '处理内容', key: 'processContent', col: '100', type: 'text' },
					{ label: '处理图片', key: 'processPictures', col: '100', type: 'image' },
					{ label: '附件', key: 'processAttachments', col: '100', type: 'attachment' },
				],
			},
		}
	},
	methods: {
		extractFileName(url) {
			// 使用正则表达式匹配最后一个斜杠后的部分
			return decodeURIComponent(new URL(url).pathname).split('/').pop()
		},
		// 获取详情信息
		getRecordDetail() {
			const params = {
				id: this.$route?.query?.id,
			}
			apiGetAlarmRecordDetail(params).then(res => {
				// 读取图片和附件的文件名称
				res.result.processPictures = res.result?.processPictures
					? res.result?.processPictures.map(item => {
							return {
								name: this.extractFileName(item),
								url: item,
							}
					  })
					: []
				res.result.processAttachments = res.result?.processAttachments
					? res.result?.processAttachments.map(item => {
							return {
								name: this.extractFileName(item),
								url: item,
							}
					  })
					: []
				this.deviceInfo.data = this.getGroupDetail(this.deviceInfo.data, res.result)
				this.alarmInfo.data = this.getGroupDetail(this.alarmInfo.data, res.result)
				this.userInfo.data = this.getGroupDetail(this.userInfo.data, res.result)
				this.handleInfo.data = this.getGroupDetail(this.handleInfo.data, res.result)
				console.log('this.handleInfo.data', this.handleInfo.data)
			})
		},
		getGroupDetail(source, data) {
			source = source.map(item => {
				const value = item.key && data[item.key] !== undefined ? data[item.key] : '--'
				return {
					...item,
					value,
				}
			})
			console.log('source', source)
			return source
		},
		handleBack() {
			this.$router.back()
		},
	},
	mounted() {
		this.getRecordDetail()
	},
}
</script>
<style lang="less" scoped>
.container {
	display: flex;
	flex-direction: column;
}
.back-button {
	color: blue;
	font-size: 20px;
}
</style>