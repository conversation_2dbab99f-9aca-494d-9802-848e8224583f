<template>
	<div class="gc-detail-group">
		<span class="title1">
			{{ detail.title || '--' }}
		</span>
		<ul v-if="detail.data" v-show="showDetail" class="content">
			<li
				v-for="(item, index) in detail.data"
				:key="index"
				:style="{ width: `${(item.col || 1) * 33}%` }"
				class="item"
			>
				<div v-if="item.type === 'text'">
					<span class="label">{{ item.label || '--' }}:</span>
					<span class="value">
						{{ item.value || '--' }}
						<span v-if="item.unit">{{ item.unit }}</span>
					</span>
				</div>
				<div v-else-if="item.type === 'image'">
					<GcUploadImgNew
						v-model="item.value"
						:maxNum="0"
						:canEdit="false"
						:width="160"
						:height="160"
					></GcUploadImgNew>
				</div>
				<div v-else>
					<span class="label">附件:</span>
					<span
						v-if="item.value"
						class="file-name"
						:href="item.value[0].url"
						@click="download(item.value[0].url, item.value[0].name)"
					>
						{{ item.value[0].name }}(点击下载)
					</span>
				</div>
			</li>
		</ul>
		<div v-show="showDetail" class="slot-content">
			<slot></slot>
		</div>
	</div>
</template>

<script>
import GcUploadImgNew from '@/components/gc-upload-img-new/index'
export default {
	name: 'GcDetailGroup',
	components: {
		GcUploadImgNew,
	},
	props: {
		detail: {
			type: Object,
			default: () => ({
				title: '',
				data: [],
			}),
		},
	},
	data() {
		return {
			showDetail: true,
		}
	},
	methods: {
		download(url, fileName) {
			const link = document.createElement('a')
			link.href = url
			link.download = fileName
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)
		},
	},
}
</script>
  
<style lang="less" scoped>
.gc-detail-group {
	padding: 18px 0 12px;
	word-break: break-all;
	display: flex;
	flex-direction: column;
	&:last-of-type {
		border-bottom: none;
	}
	.title1 {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		font-size: 18px;
		padding-bottom: 10px;
		color: #282c42;
		cursor: pointer;
		user-select: none;
		border-bottom: 1px solid rgb(218, 218, 226);
	}
	.content {
		display: flex;
		flex-wrap: wrap;
		.item {
			width: 33%;
			padding: 11px 8px 11px 0;
			display: flex;
			.label {
				color: #7b7e97;
				white-space: nowrap;
			}
			.value {
				margin-left: 12px;
				color: #3f435e;
				word-break: break-all;
			}
		}
	}
	.slot-content {
		color: #5f627d;
		line-height: 32px;
		flex: 1;
		overflow: auto;
	}
	.file-name {
		margin-left: 20px;
		font-family: 'Source Han Sans CN';
		font-style: normal;
		font-weight: 400;
		font-size: 10px;
		line-height: 15px;
		text-decoration-line: underline;
		color: #2b57e8;
		border: none;
		cursor: pointer;
	}
}
</style>