<!--
 * @Description: tabs
 * @Author: shenxh
 * @Date: 2022-03-07 09:21:18
 * @LastEditors: shenxh
 * @LastEditTime: 2022-11-07 10:53:09
-->

<template lang="pug">
.water-tabs
	Tabs.tabs(:value='value', @on-click='handleTab')
		TabPane(v-for='item in dataList', :key='item.code', :label='getTabTag(item.label, item.value)', :name='item.code')
</template>

<script>
import { querySysList } from '@/api/common.js'
import { queryAlarmConfigCount } from '@/api/alarm-config.js'
import { queryAlarmInfoCount } from '@/api/alarm-manage.js'

export default {
	name: 'water-tabs',
	components: {},
	model: {
		prop: 'value',
		event: 'set-value',
	},
	props: {
		value: String,
		formData: Object,
		isSearch: Boolean, // 点击搜索按钮
		hideAll: <PERSON><PERSON><PERSON>,
	},
	data() {
		return {
			sysList: [],
			dataList: [],
		}
	},
	computed: {},
	watch: {
		isSearch(val) {
			if (val) {
				this._querySysList()
			}
		},
	},
	created() {},
	mounted() {
		this._querySysList()
	},
	beforeDestroy() {},
	methods: {
		// 点击 tab
		handleTab(code) {
			let currentTabObj = this._getTabItemData(code, 'code')

			this.$emit('set-value', code)
			console.log('{ code, currentTabObj }', { code, currentTabObj })
			this.$emit('handle-tab', { code, currentTabObj })
		},

		// 获取 tab 标签
		getTabTag(label, value) {
			const render = h => {
				return h('div', [
					h('span', `${label}（${value}）`),
					// h('Badge', {
					// 	props: {
					// 		count: value,
					// 		'overflow-count': 999,
					// 	},
					// }),
				])
			}

			return render
		},

		//查询【系统列表】
		_querySysList() {
			querySysList({ subSystem: true }).then(res => {
				console.log('查询系统列表this.hideAll', this.hideAll)
				const data = res.result
				let all = {
					label: '全部',
					value: 0,
					id: '-1',
					code: '_all',
				}
				let arr = []

				if (data && data.length) {
					data.forEach(item => {
						arr.push({
							label: item.name,
							value: 0,
							id: item.id + '',
							code: item.subCode ? item.code + '&&' + item.subCode : item.code,
						})
					})
				}

				if (!this.hideAll) {
					this.sysList = [all, ...arr]
				} else {
					this.sysList = arr
					// this.$emit('set-value', 'dc')
				}

				this.$emit('get-sys-list', this.sysList)

				if (this.$route.name === 'real-time-alarm' || this.$route.name === 'history-alarm') {
					this._queryAlarmInfoCount()
				} else {
					this._queryAlarmConfigCount()
				}
			})
		},

		// 获取对应 tab
		_getTabItemData(tabVal, prop) {
			let data = {}

			this.dataList.forEach(item => {
				if (item[prop] == tabVal) {
					data = item
				}
			})

			return data
		},

		// 报警统计（数量）
		_queryAlarmInfoCount() {
			const {
				alarmLevelId,
				disposeStatus,
				nodeName,
				startTime,
				endTime,
				lastActiveDate,
				alarmTypeName,
				nodeCodes,
			} = this.formData

			const queryParams = {
				alarmLevelId,
				disposeStatus,
				nodeName,
				startTime,
				endTime,
				lastActiveDate,
				subSysCode: this.$route.query.subSysCode || '',
			}
			if (this.$route.query.sysCode === 'eg') {
				queryParams['alarmTypeName'] = alarmTypeName
				nodeCodes && (queryParams['nodeCodes'] = nodeCodes)
			}
			queryAlarmInfoCount(queryParams).then(res => {
				const data = res.result

				this._getTabQua(data)
			})
		},

		// 报警方案统计
		_queryAlarmConfigCount() {
			const { scenarioName: name, alarmLevelId } = this.formData

			queryAlarmConfigCount({
				alarmConfigType: this.$route.name === 'special-use-alarm' ? 2 : 1,
				name,
				needPage: true,
				alarmLevelId,
			}).then(res => {
				const data = res.result

				this._getTabQua(data)
			})
		},

		// 获取tab数量
		_getTabQua(data) {
			if (data) {
				if (!this.hideAll) {
					this.sysList[0].value = 0
				}
				this.sysList.forEach(item => {
					item.value = 0
					data.forEach(item1 => {
						const { subSysCode } = item1
						const { code } = item
						if (subSysCode) {
							if (code.split('&&')[1] === subSysCode) {
								item.value = item1.totalCount
								if (!this.hideAll) {
									this.sysList[0].value += item.value
								}
							}
						} else {
							if (item.code == item1.sysCode) {
								item.value = item1.totalCount
								if (!this.hideAll) {
									this.sysList[0].value += item.value
								}
							}
						}
					})
				})
				this.sysList[0].value = Number(this.sysList[0].value.toFixed(0))
			}

			this.dataList = this.sysList
			this.$emit('update:isSearch', false)
			this.$emit('get-tab-data', data)
		},
	},
}
</script>

<style lang="less" scoped>
.water-tabs {
	.tabs {
		/deep/ .ivu-tabs-bar {
			margin: 0;
			border: none;
			.ivu-tabs-ink-bar {
				height: 3px;
			}
		}
	}
}
</style>
