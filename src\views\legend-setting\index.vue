<template lang="pug">
.legend-setting
	.header-wrap
		es-header.header(title='图列配置')
	water-row.table-btn-wrap(justify='space-between', align='center')
		i
		Button(type='primary', @click='handleCreate') 添加
	.legend-setting-content
		Table.legend-setting-table(:columns='columns', :data='tableData', :loading='loading', :height='700', border)
			template(slot-scope='{ row }', slot='mapUrl')
				img(:src='row.mapUrl', :style='{ height: "30px", cursor: "pointer" }', @click='handlePreview(row)')
			template(slot-scope='{ row }', slot='action')
				Button(type='text', :style='{ color: "#3AA7D8" }', @click='handleUpd(row)', size='small') 编辑
				Poptip(transfer, confirm='', title='确定删除吗？', @on-ok='handleDel(row)')
					But<PERSON>(type='text', :style='{ color: "#EC5151" }', size='small') 删除

	legend-popup(v-model='showModal', :data='currentRow', @submit-form='handleSubForm')
	image-viewer(v-if='previewVisible', :imgUrlList='previewList', @closeImageViewer='previewVisible = false')
</template>

<script>
import ImageViewer from '@eslink/esvcp-pc-ui/packages/es-components/imageViewer/imageViewer'
import WaterRow from '@/components/gc-water-row'
import LegendPopup from './components/LegendPopup.vue'
import { queryMapConfigList, deleteMapConfig } from '@/api/other'

export default {
	name: 'legend-setting',
	components: {
		WaterRow,
		LegendPopup,
		ImageViewer,
	},
	props: {},
	data() {
		return {
			currentRow: {},
			showModal: false,
			loading: false,
			tableData: [],
			columns: [
				{
					title: '序号',
					type: 'index',
					align: 'center',
					width: 65,
				},
				{
					title: '图例名称',
					key: 'name',
				},
				{
					title: '图例',
					slot: 'mapUrl',
				},

				{
					title: '操作',
					align: 'center',
					slot: 'action',
					width: 130,
				},
			],
			previewList: [],
			previewVisible: false,
			currentIndex: 1,
		}
	},
	computed: {},
	watch: {},
	created() {
		this.getTableData()
	},
	mounted() {
		console.log(this.$ImagePreview)
	},
	beforeDestroy() {},
	methods: {
		// 添加按钮
		handleCreate() {
			this.currentRow = {}
			this.showModal = true
		},
		// 编辑按钮
		handleUpd(row) {
			this.currentRow = row
			this.showModal = true
		},
		// 删除按钮
		handleDel(row) {
			this.currentRow = row
			this._deleteMapConfig()
		},
		// 弹窗按钮-保存
		handleSubForm() {
			this.getTableData()
		},
		// 获取表格数据
		getTableData() {
			this.loading = true
			queryMapConfigList({})
				.then(res => {
					const data = res.result

					this.loading = false
					if (data) {
						this.tableData = data || []
					}
				})
				.catch(() => {
					this.loading = false
				})
		},
		// 删除
		_deleteMapConfig() {
			deleteMapConfig({
				id: this.currentRow.id,
			}).then(() => {
				this.$Message.success('删除成功')
				this.getTableData()
			})
		},
		// 预览
		handlePreview(row) {
			this.previewList = [row.mapUrl]
			this.previewVisible = true
		},
	},
}
</script>

<style lang="less" scoped>
.legend-setting {
	width: 100%;
	height: 100%;
	padding: 0 16px;
	.table-btn-wrap {
		margin: 10px 0;
	}
	.legend-setting-content {
		width: 100%;
		height: calc(100vh - 160px);
		.icon-eye {
			margin-left: 5px;
			cursor: pointer;
			color: #41a8ed;
		}
	}
}
</style>
