<template lang="pug">
.container
	es-header(title='工艺图列表')
		template(v-if='filterAuth === "false"')
			.right-content(slot='right-content', v-show='status === 2')
				Button.right-content-mr(@click='btnClick("allocation")', type='primary', size='small') 权限分配
				Button(@click='btnClick("create")', type='primary', size='small') 新建工艺图
			.right-content(slot='right-content', v-show='status === 1')
				Button.right-content-mr(@click='btnClick("confirmChoose")', type='primary', size='small') 确认选择
	.search
		Input(
			v-model='value4',
			icon='ios-search',
			placeholder='请输入工艺图名称',
			style='width: 400px',
			@on-enter='handleSearchEnter'
		)
	Tabs(v-model='tabValue', @on-click='handleTabClick')
		Tab-pane(:label='item.label', :name='item.id + ""', v-for='(item, index) in tabList', :key='index')
	.content
		.card(
			v-for='item in proList',
			:class='{ "card-checkabled": status === 1, "card-active": status === 1 && item.checkbox }'
		)
			Row.top-title(type='flex', justify='space-between')
				Tooltip.title-tooltip(placement='top')
					.title {{ (item.stationBinds && item.stationBinds[0] && item.stationBinds[0].stationName) || '站点名称' }}
					div(slot='content') {{ (item.stationBinds && item.stationBinds[0] && item.stationBinds[0].stationName) || '站点名称' }}
				Tooltip(v-show='status === 2', placement='bottom', content='可以禁用文字提示')
					Icon.icon(type='md-more')
					div(slot='content')
						template(v-if='filterAuth === "false"')
							//- .button(@click="btnClick('copy', item)") 复制链接
							.button(@click='btnClick("rename", item)') 重命名
							.button(@click='btnClick("associate", item)', v-show='item.state === 1') 关联站点
							.button(@click='btnClick("removebind", item)', v-show='item.state === 2') 解除关联
							.button(@click='btnClick("edit", item)') 编辑
							.button(@click='btnClick("priview", item)') 预览
							.button(@click='btnClick("delete", item)') 删除
						template(v-if='filterAuth === "true"')
							template(v-if='item.authType === "READ"')
								.button(@click='btnClick("priview", item)') 预览
							template(v-if='item.authType === "WRITE"')
								.button(@click='btnClick("rename", item)') 重命名
								.button(@click='btnClick("associate", item)', v-show='item.state === 1') 关联站点
								.button(@click='btnClick("removebind", item)', v-show='item.state === 2') 解除关联
								.button(@click='btnClick("edit", item)') 编辑
								.button(@click='btnClick("priview", item)') 预览
								.button(@click='btnClick("delete", item)') 删除
				Checkbox(v-show='status === 1', v-model='item.checkbox')
			img.img(:src='item.thumbnail', :style='{ "background-color": item.backgroundColor }')
			.process-title 
				Tooltip.title-tooltip(v-if='!item.rename', placement='top')
					.title-box {{ item.name || '暂无' }}
					div(slot='content') {{ item.name || '暂无' }}
				//- icon="md-checkmark"
				Input(
					v-else,
					v-model='item.name',
					placeholder='工艺图',
					size='small',
					@on-blur='handleEnter',
					@on-enter='handleEnter'
				)
	relevance-modal(v-if='showTag', @close='handleClose', @bindSuccess='handleSearchEnter', :bindId='bindId')
	//- 权限详情
	permission-allocation(
		v-if='showAllocation',
		ref='allocation',
		@close='handleCloseAuth',
		@changeStatue='handleChangeStatus'
	)
	//- 权限表单
	permission-form(v-show='showForm', ref='form', @close='handleCloseAuth')
</template>

<script>
import { queryPage, deleteProcess, rename, cancelBind } from '@/api/editor'
import relevanceModal from './components/RelevanceModal.vue'
import permissionAllocation from './components/PermissionAllocation.vue'
import permissionForm from './components/PermissionForm.vue'
export default {
	components: { relevanceModal, permissionAllocation, permissionForm },
	data() {
		return {
			tabValue: '-1',
			tabList: [
				{
					id: '-1',
					label: '全部',
				},
				{
					id: 1,
					label: '待关联',
				},
				{
					id: 2,
					label: '已关联',
				},
				// {
				// 	id: 0,
				// 	label: '已删除',
				// },
			],
			proList: [],
			value4: '',
			showTag: false,
			showAllocation: false,
			currentRow: {},
			// 展示状态 1 编辑状态 2 展示状态
			status: 2,
			showForm: false,
			authObj: '',
			// false是管理员  true 非管理员
			filterAuth: '',
		}
	},
	created() {
		this.width = document.body.offsetWidth - 600
		this.height = document.body.offsetHeight - 60
		this.filterAuth = this.$route.query.filterAuth
		// debugger
		// console.log('$route.query.filterAuth11', this.$route.query.filterAuth)
		// console.log('$route.query.filterAuth11', this.$route)
	},
	mounted() {
		this.getData()
	},
	methods: {
		handleTabClick(value) {
			if (value === '-1') {
				this.getData()
			} else {
				this.getData(value)
			}
		},

		handleClose() {
			this.showTag = false
		},
		// //  删除权限详情  做判断  普通删除和赋值删除
		// handleAllocationClose() {

		// },
		// 关闭表单
		handleCloseAuth(form, item) {
			switch (form) {
				// 保存成功
				case 'saveSuc':
					this.showAllocation = true
					this.showForm = false
					this.proList.forEach(item => {
						item.checkbox = false
					})
					break
				// 编辑表单
				case 'edit':
					this.showAllocation = false
					this.showForm = false
					this.status = 1
					this.authObj = item
					this.$nextTick(() => {
						this.$refs.form.initData(item)
						const { processFlowCharts = [] } = item
						this.proList.forEach(pro => {
							processFlowCharts.forEach(flow => {
								if (pro.id === flow.id) {
									pro.checkbox = true
								}
							})
						})
					})
					break
				case 'add':
					this.showAllocation = false
					this.showForm = false
					this.status = 1
					this.$nextTick(() => {
						this.authObj = ''
						this.$refs.form.initData()
					})
					break
				// 关闭权限详情
				case 'closeInfo':
					this.showAllocation = false
					break
				// 关闭表单弹框
				case 'closeForm':
					this.showForm = false
					break
				default:
					break
			}
		},
		handleSearchEnter() {
			this.handleTabClick(this.tabValue)
		},
		// 更改状态
		handleChangeStatus(value) {
			this.status = value
		},
		// 获取列表信息
		getData(state) {
			queryPage({
				state: state,
				pageNum: 1,
				pageSize: 999,
				name: this.value4,
				filterAuth: this.filterAuth,
			}).then(res => {
				const { result } = res
				if (result) {
					const { list } = result

					this.proList = list.map(item => {
						const { backgroundColor } = JSON.parse(item.canvasStyle)
						return {
							...item,
							rename: false,
							checkbox: false,
							backgroundColor: backgroundColor || '#ddd',
						}
					})
				} else {
					this.proList = []
				}
			})
		},
		btnClick(from, item) {
			switch (from) {
				// 新建
				case 'create':
					this.$router.push({
						path: '/editorConfig',
						query: { filterAuth: this.filterAuth },
					})
					break
				case 'edit':
					this.$router.push({
						path: '/editorConfig',
						query: { id: item.id, filterAuth: this.filterAuth },
					})
					break
				// 重命名
				case 'rename':
					this.currentRow = item
					this.proList.forEach(pro => {
						pro.rename = false
					})
					item.rename = true
					break
				// 删除
				case 'delete':
					this.$Modal.confirm({
						title: '提示',
						content: '将对该工艺图进行删除，删除后将无法显示。请确认是否删除?',
						loading: true,
						onOk: () => {
							this.$Modal.remove()

							deleteProcess({ id: item.id }).then(() => {
								this.$Message.info('删除成功！')
								this.handleTabClick(this.tabValue)
							})
						},
					})
					break
				// 关联
				case 'associate':
					this.bindId = item.id
					this.showTag = true

					break
				// 权限
				case 'allocation':
					this.showAllocation = true
					break
				// 确认选择
				case 'confirmChoose':
					{
						this.showForm = true
						let processFlowCharts = []
						this.proList.forEach(item => {
							if (item.checkbox) {
								processFlowCharts.push(item)
							}
						})
						if (this.authObj) {
							this.authObj.processFlowCharts = processFlowCharts
							console.log(this.authObj)
							this.$refs.form.initData(this.authObj)
						} else {
							this.$refs.form.initData({ processFlowCharts })
						}

						this.status = 2
					}
					break
				// 解除绑定
				case 'removebind':
					{
						if (!item.stationBinds) {
							this.$Message.info('未关联站点，请先关联站点！')
							return
						}
						let ids = []
						item.stationBinds.forEach(item => {
							ids.push(item.id)
						})
						this.$Modal.confirm({
							title: '提示',
							content: '将对该工艺图进行解除关联。请确认是否解除?',
							loading: true,
							onOk: () => {
								this.$Modal.remove()

								cancelBind(ids).then(() => {
									this.$Message.info('解除绑定成功！')
									this.handleTabClick(this.tabValue)
								})
							},
						})
					}
					break
				// 发布
				case 'priview':
					this.$router.push({
						path: '/priview',
						query: {
							id: item.id,
							back: true,
							filterAuth: this.$route.query.filterAuth,
						},
					})
					break
				// 拷贝
				case 'copy':
					this.copy()
					break

				default:
					break
			}
		},
		handleEnter() {
			const { id, name } = this.currentRow
			rename({ id, name }).then(() => {
				this.currentRow.rename = false
				this.$Message.info('操作成功！')
			})
		},
		handleCreate() {},
		// 复制
		copy(str = '复制') {
			var oInput = document.createElement('input') //创建一个input标签
			oInput.value = str //将要复制的值赋值给input
			document.body.appendChild(oInput) //在页面中插入
			oInput.select() // 模拟鼠标选中
			document.execCommand('Copy') // 执行浏览器复制命令（相当于ctrl+c）
			oInput.style.display = 'none' //只是用一下input标签的特性，实际并不需要显示，所以这里要隐藏掉
			this.$Message.success('复制成功')
		},
	},
}
</script>

<style lang="less" scoped>
.container {
	width: 100%;
	height: 100%;
	padding: 8px;
}
.content {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	margin: 0 auto;
}
.right-content-mr {
	margin-right: 8px;
}
.card {
	position: relative;
	display: flex;
	flex-direction: column;
	width: 160px;
	background-color: #edededcc;
	margin-right: 5px;
	margin-bottom: 5px;
	border: 1px solid #ffffff;
	border-radius: 4px;
	.top-title {
		display: flex;
		align-items: center;
		padding: 0 8px;
		.title-tooltip {
			width: 100%;
			overflow: hidden;
			flex: 1;
			height: 27px;
			line-height: 27px;
		}
	}
	.img {
		display: block;
		width: 100%;
		height: 100px;
	}
	.process-title {
		line-height: 32px;
		height: 32px;
		padding: 0 8px;
		text-align: center;
		overflow: hidden;
		.title-box {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
		.title-tooltip {
			overflow: hidden;
			width: 100%;
		}
	}
	.title {
		color: #147bb5;
		font-weight: 700;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		text-align: center;
	}
	.icon {
		font-size: 20px;
		color: #414141;
		vertical-align: middle;
	}
	.button {
		cursor: pointer;
		white-space: nowrap;
	}
	::v-deep {
		.ivu-tooltip-inner {
			white-space: pre-wrap;
		}
	}
}
.card-checkabled {
	border: 1px solid #eae5e5;
}
.card-active {
	border: 1px solid #149be6;
}
.search {
	width: 100%;
	text-align: center;
	// transform: scale(1.5);
	::v-deep {
		.ivu-input-icon {
			left: 6px;
			height: 40px;
			line-height: 40px;
			font-size: 24px;
		}
		.ivu-input-icon-normal + .ivu-input {
			padding-left: 40px;
		}
		.ivu-input {
			height: 40px;
			line-height: 40px;
			font-size: 18px;
		}
	}
}
.content {
	.ivu-checkbox-wrapper {
		margin-right: 0px;
	}
}
::v-deep {
	.ivu-input-icon {
		font-size: 20px;
		cursor: pointer;
	}
}
</style>
