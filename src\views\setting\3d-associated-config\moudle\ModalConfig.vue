<template>
	<Modal
		class-name="custom-modal"
		width="680"
		:value="show"
		:transfer="false"
		:title="title"
		@on-cancel="handleCancel"
		@on-visible-change="handleVisibleChange"
	>
		<div class="water-modal-content station-modal-content">
			<Spin fix v-if="listLoading">加载中。。。</Spin>
			<Table :columns="columns" :data="data">
				<template slot-scope="{ row, index }" slot="name">
					<!-- :remote-method="remoteMethod2" -->
					<Select
						v-if="tab3EditIndex === index"
						v-model="editName"
						filterable
						clearable
						:transfer="true"
						remote
						@on-change="noFun"
						:loading="loading"
					>
						<Option
							v-for="(option, index) in options"
							:value="option.equipmentUniqueRepresentation"
							:key="index"
						>
							{{ option.equipmentUniqueRepresentation }}
						</Option>
					</Select>
					<span v-else>{{ row.equipmentName }}</span>
				</template>
				<!-- <template slot-scope="{ row, index }" slot="no">
					<Input
						type="text"
						v-model="editNo"
						disabled
						v-if="tab3EditIndex === index"
					/>
					<span v-else>{{ row.equipmentNum }}</span>
				</template> -->
				<template slot-scope="{ row, index }" slot="type">
					<Select v-model="editType" filterable clearable :transfer="true" v-if="tab3EditIndex === index">
						<Option v-for="item in typeList" :value="item.itemRealCode" :key="item.id">
							{{ item.itemName }}
						</Option>
					</Select>
					<span v-else>{{ row.monitorTypeName }}</span>
				</template>
				<template slot-scope="{ row, index }" slot="action">
					<div v-if="tab3EditIndex === index">
						<Button type="primary" @click="saveLabelFun" size="small">保存</Button>
						<!-- <Button
							type="primary"
							@click="unEditFun(row)"
							size="small"
						>
							取消
						</Button> -->
					</div>
					<div v-else>
						<!-- <Button
							type="primary"
							@click="handleEdit(row, index)"
							size="small"
							style="margin-right: 5px"
						>
							编辑
						</Button> -->
						<Button type="primary" @click="deleteLable(row)" size="small">删除</Button>
					</div>
				</template>
			</Table>
		</div>
		<div slot="footer">
			<!-- <Button type="primary" @click="handleCheck">确定</Button> -->
		</div>
	</Modal>
</template>

<script>
import {
	getSiteLabelList,
	getEquipmentList,
	getEquipmentTypeList,
	// getModelEquipmentList,
	labelSave,
	deleteLabel,
} from '@/api/setting'

export default {
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '配置',
		},
	},
	mounted() {},
	data() {
		return {
			listLoading: false,
			defaultList: [],
			columns: [
				{
					title: '关联设备名称',
					align: 'center',
					slot: 'name',
				},
				// {
				// 	title: '关联设备编号',
				// 	align: 'center',
				// 	slot: 'no',
				// },
				{
					title: '关联数据类型',
					align: 'center',
					slot: 'type',
				},
				{
					title: '操作',
					slot: 'action',
					align: 'center',
					renderHeader: h => {
						return h('div', [
							h('span', '操作('),
							h(
								'span',
								{
									style: {
										color: '#459DEE',
										cursor: 'pointer',
									},
									on: {
										click: () => {
											if (this.isAddStatus) {
												this.$Message.warning('你已有新增数据，请完成新增后再操作！！')
												return
											}
											if (this.isEditStatus) {
												this.$Message.warning('你有数据在编辑，请完成编辑后再操作！！')
												return
											}
											this.isAddStatus = true
											this.data.push({})
											;(this.editName = ''),
												(this.editNo = ''),
												(this.editType = ''),
												(this.tab3EditIndex = this.data.length - 1)
											this.editLabelID = ''
											// this.options = []
											this.typeList = []
										},
									},
								},
								'+',
							),
							h('span', ')'),
						])
					},
				},
			],
			data: [],
			tab3EditIndex: '',
			options: [],
			typeList: [],
			loading: false,
			stationId: '',
			modelId: '',
			editNo: '',
		}
	},
	methods: {
		init(row) {
			const { stationId, modelId, applicationName } = row
			this.stationId = stationId
			this.modelId = modelId
			this.applicationName = applicationName
			this.getLabelList()
			// 查询设备
			this.remoteMethod2()
		},
		getLabelList() {
			getSiteLabelList({
				stationId: this.stationId,
				modelId: this.modelId,
			}).then(res => {
				this.data = res.result
			})
		},
		remoteMethod2() {
			this.loading = true
			getEquipmentList({
				stationCode: this.stationId,
				applicationName: this.applicationName,
			}).then(res => {
				this.loading = false
				this.options = res.result
			})
		},
		noFun(val) {
			// this.options.forEach(i => {
			// 	if (i.equipmentId === v) {
			// 		debugger
			// 		this.editNo = i.equipmentNum
			// 		this.typeList = i.typeName
			// 	}
			// })
			getEquipmentTypeList({
				stationCodes: val,
			}).then(res => {
				this.typeList = res.result
			})
		},
		async handleEdit(row, i) {
			if (this.isAddStatus) {
				this.$Message.warning('你已有新增数据，请完成新增后再操作！！')
				return
			}
			this.isEditStatus = true
			this.options = [
				{
					equipmentId: row.equipmentId,
					equipmentName: row.equipmentName,
				},
			]
			let params = {
				equipmentName: row.equipmentName,
			}
			const res = await getEquipmentList(params)
			this.typeList = res.result[0].typeName
			this.$nextTick(() => {
				this.editName = row.equipmentId
				this.editType = row.monitorTypeId
			})
			this.tab3EditIndex = i
			this.editNo = row.equipmentNum
			this.editLabelID = row.id
		},
		// 保存
		saveLabelFun() {
			if (this.editName === '') {
				this.$Message.warning('关联设备名称不能为空！')
				return
			}
			if (this.editType === '') {
				this.$Message.warning('关联数据类型不能为空！')
				return
			}
			let params = {
				id: this.editLabelID,
				modelId: this.modelId,
				stationId: this.stationId,
				equipmentId: this.editName,
				monitorTypeId: this.editType,
			}
			labelSave(params).then(() => {
				this.tab3EditIndex = -1
				this.getLabelList()
				this.$Message.success('保存成功！')
				this.isAddStatus = false
				this.isEditStatus = false
				// 刷新页面
			})
		},
		deleteLable(row) {
			this.$Modal.confirm({
				title: '提示',
				content: '确定要删除这条数据?',
				loading: true,
				onOk: () => {
					this.$Modal.remove()
					this.deleteLabel(row)
				},
			})
		},
		deleteLabel(row) {
			deleteLabel({ id: row.id }).then(() => {
				this.$Message.success('操作成功')
				this.getLabelList()
			})
		},
		// 弹窗显隐事件
		handleVisibleChange() {},

		// 弹窗取消事件
		handleCancel() {
			this.data = []
			this.isAddStatus = false
			this.isEditStatus = false
			this.$emit('update:show', false)
		},
		// 重置
		handleReset() {},
		// 确定
		handleCheck() {
			this.$emit('update:show', false)
		},
	},
}
</script>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	.ivu-table-header thead tr th {
		padding: 0;
	}
	.ivu-modal {
		height: auto !important;
	}
	.ivu-input-number-input {
		color: #fff;
		background: #133a5e;
		border: none;
	}
	.ivu-form-item-label {
		width: 125px !important;
	}
	.ivu-form-item-content {
		margin-left: 125px !important;
	}
	.ivu-btn {
		margin-right: 5px;
	}
}
</style>
