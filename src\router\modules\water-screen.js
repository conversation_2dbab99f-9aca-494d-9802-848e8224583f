/*
 * @Description:
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-30 15:22:31
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-05-31 10:43:18
 */
export default [
	{
		path: '/single-village-report',
		name: 'singleVillageReport',
		meta: {
			title: '单村数据填报',
		},
		component: (/* webpackChunkName: 'water-screen-data' */) =>
			import('@/views/water-screen-data/single-village-report/single-village-report.vue'),
	},
	{
		path: '/second-supply-report',
		name: 'secondSupplyReport',
		meta: {
			title: '二供数据填报',
		},
		component: (/* webpackChunkName: 'water-screen-data' */) =>
			import('@/views/water-screen-data/second-supply-report/SecondSupplyReport.vue'),
	},
	{
		path: '/hotline-report',
		name: 'hotlineReport',
		meta: {
			title: '热线数据填报',
		},
		component: (/* webpackChunkName: 'water-screen-data' */) =>
			import('@/views/water-screen-data/hotline-report/hotline-report.vue'),
	},
	{
		path: '/water-management-report',
		name: 'waterManagementReport',
		meta: {
			title: '经营数据填报',
		},
		component: (/* webpackChunkName: 'water-screen-data' */) =>
			import('@/views/water-screen-data/water-management-report/WaterManagementReport.vue'),
	},
	{
		path: '/product-report',
		name: 'productReport',
		meta: {
			title: '生产调度填报',
		},
		component: (/* webpackChunkName: 'water-screen-data' */) =>
			import('@/views/water-screen-data/product-report/ProductReport.vue'),
	},
]
