<template>
	<div
		class="flow-tip-box"
		:style="{
			left: left + 'px',
			top: top + 'px',
			right: right + 'px',
			bottom: bottom + 'px',
		}"
	>
		<div class="adjust-offset">
			<slot></slot>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		// tip-box的宽高
		width: {
			type: [Number, String],
			default: 'auto',
		},
		height: {
			type: [Number, String],
			default: 'auto',
		},
		left: {
			type: [Number, String],
		},
		top: {
			type: [Number, String],
		},
		right: {
			type: [Number, String],
		},
		bottom: {
			type: [Number, String],
		},
	},
	data() {
		return {}
	},
	methods: {},
}
</script>

<style lang="less" scoped>
.flow-tip-box {
	position: absolute;
	// border: 1px solid #3ce8ff;
	border-radius: 4px;
	// background: rgba(0, 16, 31, 0.8);
	box-sizing: border-box;
	color: rgba(255, 255, 255, 0.75);
	font-size: 14px;
	.adjust-offset {
		// position: relative;
		// left: -50%;
		// margin-right: 50%;
		// margin-top: -50%;
		position: absolute;
		// left: -50%;
		// top: 50%;
		transform: translate(-8%, 0);
		z-index: 9;
	}
	.tips {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		margin-bottom: 4px;

		.title {
			flex-grow: 1;
			flex-shrink: 0;
			text-align: right;
			color: rgba(255, 255, 255, 0.75);
		}
		.value {
			flex-shrink: 0;
			width: 72px;
			min-height: 24px;
			line-height: 24px;
			padding: 0 4px;
			margin-left: 4px;
			font-size: 14px;
			background-color: #081a33;
			color: rgba(255, 255, 255, 0.75);
			border: 1px solid #639fc0;
			border-radius: 4px;
			box-sizing: content-box;
		}
		.error {
			border-color: #e5615b;
			background-color: #471c15;
		}
	}
}
</style>
