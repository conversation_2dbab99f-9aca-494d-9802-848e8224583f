<template lang="pug">
.text-config(v-if='value.controls[index]')
	.text-config-title 值属性
	water-row.water-margin-top-16(justify='flex-start', align='center')
		.water-margin-right-4.text 文本值
		Input(v-model='value.controls[index].text', style='width: 100%', @on-change='handleChange("text")')
	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.text x
		InputNumber(
			v-if='value.controls[index]',
			v-model='value.controls[index].left',
			@on-change='handleChange("left")',
			style='width: 200px'
		)
		.water-margin-right-4.text(style='margin-left: 10px') y
		InputNumber(
			v-if='value.controls[index]',
			v-model='value.controls[index].top',
			@on-change='handleChange("top")',
			style='width: 200px'
		)
	water-row.water-margin-top-16(justify='flex-start', align='center')
		.water-margin-right-4.text 宽度
		InputNumber(v-model.number='value.controls[index].width', style='width: 100%', @on-change='handleChange("width")')
		.water-margin-right-4.water-margin-left-8.text 高度
		InputNumber(v-model.number='value.controls[index].height', style='width: 100%', @on-change='handleChange("height")')
		//- Slider(
		//- 	v-if="value.controls[index]",
		//- 	@on-input="handleChange('left')",
		//- 	v-model="value.controls[index].left",
		//- 	show-input,
		//- 	:max="value.controls[index].canvasWidth",
		//- 	show-tip="never",
		//- 	style="width: 200px")
	//- water-row.water-margin-top-8(justify="space-between", align="center")
	//- 	.water-margin-right-4.text y
	//- 	Slider(
	//- 		v-if="value.controls[index]",
	//- 		@on-input="handleChange('top')",
	//- 		:max="value.controls[index].canvasHeight",
	//- 		v-model="value.controls[index].top",
	//- 		show-input,
	//- 		show-tip="never",
	//- 		style="width: 200px")
	.text-config-title.water-margin-top-16 字体
	water-row.water-margin-top-8(justify='space-between', align='center')
		.water-margin-right-4.text 大小
		Slider(
			v-if='value.controls[index]',
			:max='40',
			@on-input='handleChange("fontSize")',
			v-model='value.controls[index].fontSize',
			show-input,
			show-tip='never',
			style='width: 200px'
		)
	water-row(justify='space-between', align='center')
		.water-margin-right-4.text 旋转角度
		Slider(
			v-if='value.controls[index]',
			@on-input='handleChange("angle")',
			v-model='value.controls[index].angle',
			show-input,
			:min='0',
			:max='360',
			show-tip='never',
			style='width: 100%; margin-left: 10px'
		)
	water-row.water-margin-top-8(justify='flex-start', align='center')
		.water-margin-right-4.text 颜色
		ColorPicker(
			v-if='value.controls[index]',
			alpha,
			v-model='value.controls[index].fill',
			@on-pick-clear='handleChange("fill")',
			@on-pick-success='handleChange("fill")'
		)
	water-row.water-margin-top-16(justify='flex-start', align='center')
		.water-margin-right-4.text 加粗
		i-switch.water-margin-right-8(
			v-model='value.controls[index].fontWeight',
			true-value='bold',
			false-value='normal',
			size='small',
			@on-change='handleChange("fontWeight")'
		)
		.water-margin-right-4.text 斜体
		i-switch(
			v-model='value.controls[index].fontStyle',
			true-value='italic',
			false-value='normal',
			size='small',
			@on-change='handleChange("fontStyle")'
		)
	water-row.water-margin-top-16(justify='flex-start', align='center')
		.water-margin-right-4.text 下划
		i-switch.water-margin-right-8(
			v-model='value.controls[index].underline',
			size='small',
			@on-change='handleChange("underline")'
		)
		.water-margin-right-4.text 中划
		i-switch.water-margin-right-8(
			v-model='value.controls[index].linethrough',
			size='small',
			@on-change='handleChange("linethrough")'
		)
		.water-margin-right-4.text 上划
		i-switch.water-margin-right-8(
			v-model='value.controls[index].overline',
			size='small',
			@on-change='handleChange("overline")'
		)
	water-row.water-margin-top-8(justify='space-between', align='center')
		.water-margin-right-4.text 间距
		Slider(
			v-if='value.controls[index]',
			@on-input='handleChange("charSpacing")',
			v-model='value.controls[index].charSpacing',
			show-input,
			:max='200',
			:min='0',
			show-tip='never',
			style='width: 200px'
		)
	water-row.water-margin-top-8(justify='space-between', align='center')
		.water-margin-right-4.text 对齐
		RadioGroup(
			v-model='value.controls[index].textAlign',
			@on-change='handleChange("textAlign")',
			size='small',
			type='button'
		)
			Radio(v-for='(item, i) in textAlignList', :label='item', :key='item')
				span(v-html='textAlignListSvg[i]')
	.text-config-title.water-margin-top-16 背景
	water-row.water-margin-top-16(justify='flex-start', align='center')
		.water-margin-right-4.text 颜色
		ColorPicker(
			v-if='value.controls[index]',
			alpha,
			v-model='value.controls[index].backgroundColor',
			@on-pick-clear='handleChange("backgroundColor")',
			@on-pick-success='handleChange("backgroundColor")'
		)
	//- water-row.water-margin-top-8(justify="space-between", align="center")
	//- 	.water-margin-right-4.text 宽度
	//- 	Slider(
	//- 		v-if="value.controls[index]",
	//- 		@on-input="handleChange('width')",
	//- 		v-model="value.controls[index].width",
	//- 		show-input,
	//- 		:max="value.controls[index].width + 100",
	//- 		:min="0",
	//- 		show-tip="never",
	//- 		style="width: 200px")
	.text-config-title.water-margin-top-16 边距
	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.text 宽度
		Slider(
			v-if='value.controls[index]',
			@on-input='handleChange("strokeWidth")',
			v-model='value.controls[index].strokeWidth',
			show-input,
			:min='0',
			:max='20',
			show-tip='never',
			style='width: 200px'
		)
	//- water-row.water-margin-top-16(justify="flex-start", align="center")
	//- 	.water-margin-right-4.text 颜色
	//- 	ColorPicker(
	//- 		v-if="value.controls[index]",
	//- 		alpha,
	//- 		v-model="value.controls[index].stroke",
	//- 		@on-pick-clear="handleChange('stroke')",
	//- 		@on-pick-success="handleChange('stroke')")
	//- water-row.water-margin-top-16(justify="flex-start", align="center")
	//- 	.water-margin-right-4.text 边距
	//- 	Slider(
	//- 		v-if="value.controls[index]",
	//- 		@on-input="handleChange('padding')",
	//- 		v-model="value.controls[index].padding",
	//- 		show-input,
	//- 		:min="0",
	//- 		:max="10",
	//- 		show-tip="never",
	//- 		style="width: 200px")
</template>
<script>
import WaterRow from '@/components/gc-water-row'
export default {
	name: '',
	components: { WaterRow },
	props: {
		value: {
			type: Object,
			default: function () {
				return {}
			},
		},
		index: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			backgroundColor: '#19be6b',
			// value: 60,
			showType: '',
			itemValue: '',
			dateTime: new Date(),
			formatText: 'yyyy-MM-dd',
			// 字体对齐方式
			textAlignList: ['left', 'center', 'right'],
			list: [
				{ text: 'yyyy-MM-dd HH:mm:ss', value: 'yyyy-MM-dd HH:mm:ss' },
				{ text: 'yyyy/MM/dd HH:mm:ss', value: 'yyyy/MM/dd HH:mm:ss' },
				{ text: 'yyyy-M-d H:mm', value: 'yyyy-M-d H:mm' },
				{ text: 'yyyy/M/d HH:mm', value: 'yyyy/M/d HH:mm' },
				{ text: 'yyyy-MM-dd', value: 'yyyy-MM-dd' },
				{ text: 'yyyy/MM/dd', value: 'yyyy/MM/dd' },
				{ text: 'HH:mm:ss', value: 'HH:mm:ss' },
				{ text: 'yyyy/MM/dd', value: 'yyyy/MM/dd' },
				{ text: 'yyyy年MM月dd日', value: 'yyyy年MM月dd日' },
				{ text: 'yyyy年M月dd日', value: 'yyyy年M月d日' },
			],
			textAlignListSvg: [
				'<svg t="1650441458823" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3554" width="14" height="14"><path d="M198.4 198.4h341.333333c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533334 19.2v57.6c0 8.533333-2.133333 14.933333-8.533334 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-341.333333c-8.533333 0-14.933333-2.133333-19.2-8.533334-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533333z m0 170.666667h569.6c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333h-569.6c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533333z m0 170.666666h454.4c8.533333 0 14.933333 2.133333 19.2 8.533334 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333h-454.4c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533334z m0 170.666667h625.066667c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-625.066667c-8.533333 0-14.933333-2.133333-19.2-8.533334-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533333z" p-id="3555"></path></svg>',
				'<svg t="1650441512015" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3704" width="14" height="14"><path d="M313.6 198.4h398.933333c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533334 19.2v57.6c0 8.533333-2.133333 14.933333-8.533334 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-398.933333c-8.533333 0-14.933333-2.133333-19.2-8.533334-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 10.666667-8.533333 19.2-8.533333z m-115.2 170.666667h625.066667c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333h-625.066667c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533333z m115.2 170.666666h398.933333c8.533333 0 14.933333 2.133333 19.2 8.533334 6.4 6.4 8.533333 12.8 8.533334 19.2v57.6c0 8.533333-2.133333 14.933333-8.533334 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333h-398.933333c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 10.666667-8.533333 19.2-8.533334z m-115.2 170.666667h625.066667c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-625.066667c-8.533333 0-14.933333-2.133333-19.2-8.533334-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533333z" p-id="3705"></path></svg>',
				'<svg t="1650441519862" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3854" width="14" height="14"><path d="M454.4 283.733333v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 6.4-6.4 12.8-8.533333 19.2-8.533333h341.333334c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-341.333334c-8.533333 0-14.933333-2.133333-19.2-8.533334-4.266667-4.266667-8.533333-10.666667-8.533333-19.2z m-226.133333 170.666667v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 6.4-6.4 12.8-8.533333 19.2-8.533333h569.6c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333H256c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-4.266667-8.533333-10.666667-8.533333-19.2z m113.066666 170.666667v-57.6c0-8.533333 2.133333-14.933333 8.533334-19.2 6.4-6.4 12.8-8.533333 19.2-8.533334h454.4c8.533333 0 14.933333 2.133333 19.2 8.533334 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333h-454.4c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-4.266667-8.533333-10.666667-8.533334-19.2z m-170.666666 170.666666v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 6.4-6.4 12.8-8.533333 19.2-8.533333h625.066667c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-625.066667c-8.533333 0-14.933333-2.133333-19.2-8.533334-6.4-4.266667-8.533333-10.666667-8.533333-19.2z" p-id="3855"></path></svg>',
			],
		}
	},
	methods: {
		handleChange(type) {
			// debugger
			// console.log(type)
			this.$emit('changeControl', type, this.value.controls[this.index][type])
		},
	},
}
</script>
<style lang="less" scoped>
.text-config {
	padding: 8px;
	&-title {
		color: #000;
		font-weight: bold;
	}
}
.text {
	flex-shrink: 0;
}
::v-deep {
	.ivu-input-number {
		width: 56px;
		margin-top: 0;
	}
	.ivu-input {
		// border: none;
		// border-radius: 0;
		background: transparent;
		cursor: pointer;
		height: 28px;
		&:focus {
			box-shadow: none;
		}
	}
	.ivu-color-picker-rel .ivu-input {
		border: none;
		border-radius: 0;
	}
}
</style>
