<!--
 * @Description: 报表管理
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-03-14 16:32:31
 * @LastEditors: houyan
 * @LastEditTime: 2024-03-19 16:23:24
-->
<template lang="pug">
WaterRow
	.role-list
		.role(
			v-for='(item, index) in roleList',
			:key='index',
			:class='{ active: activeIndex === index }',
			@click='handleChangeRole(index)'
		) {{ item.name }}
	.report-list
		Button.save-button(type='primary', @click='handleSave') 保存
		.report-container
			Tree(ref='treeRef', :data='reportList', show-checkbox)
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import { getRolesList, getReportConfigList, saveOrUpdateRoleReport } from '@/api/report.js'
export default {
	components: {
		WaterRow,
	},
	data() {
		return {
			value: '',
			activeIndex: 0,
			roleList: [],
			reportIds: [],
			reportList: [],
		}
	},
	methods: {
		getTreeData(data) {
			return data.map(item => {
				const { name, id, reports } = item
				let obj = {
					title: name,
					id,
					expand: true,
					checked: false,
				}

				if (this.reportIds.includes(id)) {
					obj.checked = true
				}

				if (reports && reports.length > 0) {
					obj.children = this.getTreeData(reports)
				}
				return obj
			})
		},
		setTreeChecked(data) {
			return data.map(item => {
				const { id, children } = item
				item.checked = false

				if (this.reportIds.includes(id)) {
					item.checked = true
				}

				if (children && children.length > 0) {
					item.children = this.setTreeChecked(children)
				}
				return item
			})
		},
		_getRolesList() {
			getRolesList().then(res => {
				const { result } = res

				if (result && result.length) {
					this.roleList = result
					this.reportIds = this.roleList[this.activeIndex].reportIds
				}
			})
		},
		_getReportConfigList() {
			getReportConfigList().then(res => {
				const { result } = res

				if (result && result.length) {
					this.reportList = this.getTreeData(result)
				}
			})
		},
		async _saveOrUpdateRoleReport() {
			const params = {
				roleId: this.roleList[this.activeIndex].id,
				reportIds: this.reportIds,
			}
			await saveOrUpdateRoleReport(params)
			this.$Message.success('修改成功')
			this._getRolesList()
		},
		handleChangeRole(index) {
			this.activeIndex = index
			this.reportIds = this.roleList[this.activeIndex].reportIds
			let copyData = this.$common.deepCopy(this.reportList)
			this.reportList = this.setTreeChecked(copyData)
		},
		handleSave() {
			const arr = this.$refs.treeRef.getCheckedNodes()
			const ids = arr.map(item => item.id)

			this.reportIds = ids
			this._saveOrUpdateRoleReport()
		},
	},
	mounted() {
		this._getRolesList()
		this._getReportConfigList()
	},
}
</script>
<style lang="less" scoped>
.water-layout-flex {
	height: 100%;
}
.role-list {
	margin-right: 10px;
	padding: 10px;
	width: 240px;
	height: 100%;
	overflow: auto;
	border: 1px solid #e4e7ed;
	.role {
		padding: 4px 10px;
		cursor: pointer;
		font-size: 14px;
	}
	.active {
		background-color: #ecf5ff;
	}
}
.report-list {
	display: flex;
	flex-direction: column;
	flex: 1;
	padding: 10px;
	height: 100%;
	overflow: auto;
	border: 1px solid #e4e7ed;
	.save-button {
		margin-left: auto;
		margin-bottom: 10px;
	}
	.report-container {
		flex: 1;
		overflow: auto;
	}
}
</style>
