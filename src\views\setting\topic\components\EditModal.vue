<template lang="pug">
.create-system-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='450',
		v-model='showModal',
		:title='type === 1 ? "编辑" : "新增"',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='110')
					FormItem(label='数据源名称', prop='connectId')
						Select.select(v-model='formData.connectId', transfer, filterable, placeholder='请选择')
							Option(v-for='(item, index) in sysList', :key='index', :value='item.connectId') {{ item.sourceName }}
					FormItem(label='数据库访问URL', prop='url')
						Input(v-model='formData.url', disabled)
					FormItem(label='数据库名称', prop='dbName')
						Input(v-model='formData.dbName', disabled)
					FormItem(label='topic', prop='topic')
						Input(v-model='formData.topic', placeholder='请输入topic')
		template(slot='footer')
			Button(@click='handleClose') 取消
			Button(:loading='btnLoading', type='primary', @click='handleSubForm') 保存
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import { getConnects, updateTopic, addTopic } from '@/api/data-acquisition-config'

export default {
	name: 'create-system-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		WaterRow,
	},
	props: {
		showModal: Boolean,
		data: Object,
		type: Number, // 0-添加; 1-编辑
	},
	data() {
		// const validateCode = (rule, value, callback) => {
		// 	if (!value || !value.trim()) {
		// 		callback(new Error('请输入'))
		// 	} else {
		// 		callback()
		// 	}
		// }

		return {
			sysList: [],
			btnLoading: false,
			formData: {},
			formRules: {
				memo: [
					{
						max: 512,
						trigger: 'blur',
						message: '不能超过 512 个字符',
					},
				],
			},
		}
	},
	computed: {},
	watch: {
		'formData.connectId': {
			handler(val) {
				if (val) {
					const obj = this.sysList.find(item => item.connectId === val)
					this.formData.url = obj.url
					this.formData.dbName = obj.dbName
				} else {
					this.formData.url = ''
					this.formData.dbName = ''
				}
			},
		},
	},
	created() {
		this.getConnectsFn()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				getConnects().then(res => {
					console.log('res', res)
					const { result = [] } = res
					this.sysList = result
				})
				this.formData = { ...this.data }
				if (this.formData.enableRule || this.formData.enableRule === 0) {
					this.formData.enableRule += ''
				}
			} else {
				setTimeout(() => {
					this.$refs.form.resetFields()
				}, 200)
			}
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.btnLoading = true
					if (this.type === 0) {
						this.formData.stationWord = this.$route.query.sysCode

						this.addTransChannel(this.formData)
					} else {
						this.updateTransChannel(this.formData)
					}
				}
			})
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		// 新增设备原始档案
		addTransChannel(params) {
			addTopic(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		// 编辑
		updateTransChannel(params) {
			updateTopic(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		setResData(res) {
			const { responseCode } = res || {}

			if (responseCode === '100000') {
				this.$Message.success('操作成功')

				this.handleClose()
			}

			this.btnLoading = false
			this.$emit('submit-form', res)
		},
	},
}
</script>

<style lang="less" scoped>
.create-system-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
