import { POST, GET } from '@/utils/request'

// 查询采集渠道
export function queryTransChannelNoEnableRule(params) {
	return POST({
		url: '/waterPlat/transChannel/queryTransChannelNoEnableRule',
		params,
	})
}

// 设备原始档案分页列表查询
export function queryStationInfo(params) {
	return POST({
		url: '/waterPlat/transStationInfo/queryStationInfo',
		params,
	})
}

// 站点规则信息查询
export function queryRules(params) {
	return POST({
		url: '/waterPlat/transDataCheck/queryRules',
		params,
		requestType: 'json',
	})
}

// 站点规则信息导出
export function exportRules(params) {
	return GET({
		url: '/waterPlat/transDataCheck/exportRules',
		params,
		// responseType: 'blob',
	})
}
// 历史数据查询
export function queryDataHistory(params) {
	return POST({
		url: '/waterPlat/transDataCheck/queryDataHistory',
		params,
		requestType: 'json',
	})
}
