<template lang="pug">
.create-item-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='600',
		:value='show',
		:title='title',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='95')
					FormItem(label='类别描述', prop='categoryName')
						Input(v-model='formData.categoryName', placeholder='请输入类别描述')
		template(slot='footer')
			But<PERSON>(@click='handleClose') 取消
			Button(type='primary', @click='handleSubForm') 确认
</template>

<script>
import { saveCategory } from '@/api/base-item.js'
import { querySysList } from '@/api/common.js'
// import dataRepairVue from '../../../data-repair/data-repair.vue'
export default {
	name: 'CreateItemPopup',
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			title: '添加',
			formData: {
				categoryName: '',
			},
			options: [],
			formRules: {
				categoryName: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		querySysList().then(res => {
			this.options = res.result
		})
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal() {
			this.$refs.form.resetFields()
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					if (this.formData.id) {
						// updateBaseDataItem(this.formData)
						// 	.then(() => {
						// 		this.$Message.success('提交成功!')
						// 		this.$refs.form.resetFields()
						// 		this.$emit('update:show', false)
						// 		this.$emit('initList')
						// 	})
						// 	.catch(() => {
						// 		this.listLoading = false
						// 	})
					} else {
						saveCategory(this.formData)
							.then(() => {
								this.$Message.success('提交成功!')
								this.$refs.form.resetFields()
								this.$emit('update:show', false)
								this.$emit('initList')
							})
							.catch(() => {
								this.listLoading = false
							})
					}
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},

		setData(data) {
			console.log(data)
			this.title = '编辑'
			this.formData = data
		},

		// 按钮-关闭
		handleClose() {
			this.formData = {
				categoryName: '',
			}
			this.$emit('update:show', false)
		},
	},
}
</script>

<style lang="less" scoped>
.create-item-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			padding: 0 40px;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
