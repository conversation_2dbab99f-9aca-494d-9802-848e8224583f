/*
 * @Description: 推送管理
 * @Author: shenxh
 * @Date: 2022-03-09 10:17:23
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-01-06 14:08:14
 */

import { POST, GET } from '@/utils/request'

// 消息手动推送
export function pushMessage(params) {
	const { id } = params
	return GET({
		url: `/waterPlat/alarmNotifyInfo/push/${id}`,
	})
}
// 消息推送记录分页查询
export function queryPushPage(params) {
	return POST({
		url: '/waterPlat/alarmNotifyInfo/page',
		params,
		requestType: 'json',
	})
}
// 消息推送统计
export function queryPushTotal(params) {
	return GET({
		url: '/waterPlat/alarmNotifyInfo/total',
		params,
	})
}
// 获取推送类型
export function alarmNotifyInfoTypes(params) {
	return GET({
		url: '/waterPlat/alarmNotifyInfo/types',
		params,
	})
}
