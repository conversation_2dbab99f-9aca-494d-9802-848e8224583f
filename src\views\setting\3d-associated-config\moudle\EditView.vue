<template>
	<Modal
		class-name="custom-modal"
		:value="show"
		width="480"
		:transfer="false"
		:title="title"
		@on-cancel="handleCancel"
		@on-visible-change="handleVisibleChange"
	>
		<div class="water-modal-content station-modal-content">
			<!-- <Button type="primary" @click="add">新增</Button>
			<Spin fix v-if="listLoading">加载中。。。</Spin> -->
			<Form ref="formValidate" :model="formItem" :label-width="80">
				<Form-item label="相机视角点(position)">
					<Input v-model="formItem.endPositionX" placeholder="x"></Input>
					,
					<Input v-model="formItem.endPositionY" placeholder="y"></Input>
					,
					<Input v-model="formItem.endPositionZ" placeholder="z"></Input>
				</Form-item>
				<Form-item label="物体中心点(target)">
					<Input v-model="formItem.endTargetX" placeholder="x"></Input>
					,
					<Input v-model="formItem.endTargetY" placeholder="y"></Input>
					,
					<Input v-model="formItem.endTargetZ" placeholder="z"></Input>
				</Form-item>
			</Form>
		</div>
		<div slot="footer">
			<Button type="primary" @click="handleCheck">确定</Button>
		</div>
	</Modal>
</template>

<script>
import { diagramEdit } from '@/api/setting'

export default {
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	mounted() {},
	data() {
		return {
			title: '编辑模型视角',
			listLoading: false,
			formItem: {
				id: '',
			},
		}
	},
	methods: {
		async init(row) {
			const { id } = row
			// console.log(row)
			this.formItem.id = id
		},
		add() {
			this.formItem.extras.push({
				key: '',
				value: '',
			})
		},
		// 弹窗显隐事件
		handleVisibleChange() {},

		handleRemove() {
			this.formItem.processImage = ''
		},
		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},
		// 重置
		handleReset() {},
		// 确定
		handleCheck() {
			const _this = this
			_this.$refs.formValidate.validate(valid => {
				if (valid) {
					debugger
					diagramEdit(_this.formItem)
						.then(() => {
							_this.$Message.success('提交成功!')
							this.$emit('update:show', false)
							this.$emit('successCallBack')
							_this.formItem.extras = [
								{
									key: '',
									value: '',
								},
							]
						})
						.catch(() => {
							this.listLoading = false
						})
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
	},
}
</script>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	.ivu-modal {
		height: auto !important;
	}
	.ivu-input-number-input {
		color: #fff;
		background: #133a5e;
		border: none;
	}
	.ivu-form-item-label {
		width: 125px !important;
	}
	.ivu-form-item-content {
		margin-left: 125px !important;
	}
	.ivu-input-wrapper {
		width: 48%;
	}
}
</style>
