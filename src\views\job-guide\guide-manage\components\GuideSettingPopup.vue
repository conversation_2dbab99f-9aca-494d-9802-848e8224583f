<template lang="pug">
es-modal.es-modal(
	:transfer='false',
	:is-direct-close-modal='false',
	width='500',
	v-model='showModal',
	:title='currentRow && currentRow.id ? "编辑" : "添加"',
	@on-cancel='handleClose',
	@on-visible-change='changeModal'
)
	template(slot='body')
		.popup-content
			Form.form(ref='form', :model='formData', :rules='formRules', :label-width='80')
				FormItem(label='指南标题', prop='title')
					Input(v-model='formData.title', placeholder='请输入', maxlength='16')
				FormItem(label='所属分类', prop='typeId')
					Select(v-model='formData.typeId', clearable)
						Option(v-for='item in typeList', :key='item.id', :value='item.id') {{ item.name }}
				FormItem(label='排序号', prop='sort')
					Input(v-model='formData.sort', placeholder='请输入', maxlength='4')
				FormItem(label='是否启用', prop='enable')
					i-switch(v-model='formData.enable', :true-value='1', :false-value='0', size='large')
						template(#open) 
							span 启用
						template(#close)
							span 关闭

				FormItem(label='指南文件', prop='filePath')
					UploadFile(v-model='formData.filePath', @set-value='validateField')
				div(style='width: 58px; height: 58px; line-height: 58px')
					Icon(type='camera', size='20')
	template(slot='footer')
		Button(@click='handleClose') 关闭
		Button(type='primary', @click='handleSubForm') 保存
</template>

<script>
import { addGuideInfo, updateGuideInfo } from '@/api/other'
import UploadFile from '@/components/gc-upload-file/upload-file'
import { ruleOnlyInteger } from '@/utils/formRules.js'

export default {
	name: 'create-control-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		UploadFile,
	},
	props: {
		showModal: Boolean,
		currentRow: Object,
		typeList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			formData: {
				title: '',
				typeId: '',
				sort: '',
				enable: 0,
				filePath: [],
			},
			formRules: {
				title: [
					{
						required: true,
						message: '请输入',
						trigger: 'change',
					},
				],
				typeId: [
					{
						required: true,
						message: '请选择',
						type: 'number',
						trigger: 'change',
					},
				],
				sort: [
					{
						required: true,
						validator: (rule, value, callback) => {
							if (value === '' || value === null) {
								callback(new Error('请输入排序值'))
							} else if (typeof value !== 'number' && typeof value !== 'string') {
								callback(new Error('排序值必须是数字或字符串'))
							} else {
								callback()
							}
						},
						message: '请输入',
						trigger: 'change',
					},
					ruleOnlyInteger,
				],
				enable: [
					{
						required: true,
					},
				],
				filePath: [
					{
						required: true,
						validator: (rule, value, callback) => {
							if (Array.isArray(value) && value.length > 0) {
								callback()
							} else {
								callback(new Error('请选择'))
							}
						},
						trigger: 'blur',
					},
				],
			},
		}
	},
	methods: {
		changeModal(isShow) {
			this.$refs.form.resetFields()
			if (isShow) {
				this._getGuideTypeList()
				if (this.currentRow.filePath) {
					const newFilePath = JSON.parse(this.currentRow.filePath)

					this.formData = { ...this.currentRow }
					this.formData.filePath = newFilePath
				}
			}
		},
		validateField() {
			this.$refs.form.validateField('filePath')
		},
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this._submitForm()
				}
			})
		},
		handleClose() {
			this.$emit('set-value', false)
		},
		_submitForm() {
			const { title, typeId, filePath, sort, enable } = this.formData
			const newFilePath = JSON.stringify(filePath)
			const guideInfo = {
				title,
				typeId,
				filePath: newFilePath,
				sort,
				enable,
			}

			const action =
				this.currentRow && this.currentRow.id
					? updateGuideInfo({ ...guideInfo, id: this.currentRow.id })
					: addGuideInfo(guideInfo)

			action.then(() => {
				this.$Message.success('操作成功')
				this.handleClose()
				this.$emit('submit-form', this.formData)
			})
		},
	},
}
</script>

<style lang="less" scoped>
.es-modal {
	.popup-content {
		width: 100%;
		padding: 0 40px;
		.form {
			width: 100%;
			/deep/ .ivu-form-item {
				margin-bottom: 20px;
			}
		}
	}
}
</style>
