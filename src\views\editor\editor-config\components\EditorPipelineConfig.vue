<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: z<PERSON>yi
 * @Date: 2022-09-14 10:21:58
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-09-14 10:44:42
-->
<template lang="pug">
.pipe-line-config(v-if='value.points')
	.pipe-line-config-title.water-margin-top-8.water-margin-left-8 流向
		water-row.water-margin-top-8(justify='flex-start', align='center')
			.water-margin-left-16 始
			.water-margin-left-16.tip X
			.water-margin-left-16(v-if='value.points.startPoint') {{ value.points.startPoint.x }}
			.water-margin-left-16.tip Y
			.water-margin-left-16(v-if='value.points.startPoint') {{ value.points.startPoint.y }}
		water-row.water-margin-top-8(justify='flex-start', align='center')
			.water-margin-left-16 末
			.water-margin-left-16.tip X
			.water-margin-left-16(v-if='value.points.endPoint') {{ value.points.endPoint.x }}
			.water-margin-left-16.tip Y
			.water-margin-left-16(v-if='value.points.endPoint') {{ value.points.endPoint.y }}
		template(v-for='(item, index) in value.points.linePath')
			water-row.water-margin-top-16(justify='space-between', align='center')
				.water-margin-right-4.text x{{ index + 1 }}
				Slider(
					v-if='item',
					:max='value.points.canvasWidth',
					@on-input='handleChange($event, "left", index)',
					v-model='item.x',
					show-input,
					show-tip='never',
					style='width: 200px'
				)
			water-row.water-margin-top-16(justify='space-between', align='center')
				.water-margin-right-4.text y{{ index + 1 }}
				Slider(
					v-if='item',
					:max='value.points.canvasHeight',
					@on-input='handleChange($event, "top", index)',
					v-model='item.y',
					show-input,
					show-tip='never',
					style='width: 200px'
				)
</template>
<script>
import WaterRow from '@/components/gc-water-row'
export default {
	name: '',
	components: { WaterRow },
	props: {
		value: {
			type: Object,
			default: function () {
				return {}
			},
		},
		index: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			lineStyle: 'solid',
		}
	},
	methods: {
		handleChange(val, type, index) {
			this.$emit('changeControl', type, val, { index })
		},
	},
}
</script>
<style lang="less" scoped>
.pipe-line-config {
	padding: 8px 0;
	overflow: auto;
	height: calc(100% - 60px);

	&-title {
		color: #000;
		font-weight: bold;
	}
	.block {
		width: 18px;
		height: 18px;
		border-radius: 2px;
		background-color: #ccc;
		margin-right: 4px;
	}
	&-content {
		border-top: 1px solid #e8e8e8;
		border-bottom: 1px solid #e8e8e8;
		padding: 8px 8px 24px;
	}
	.tip {
		color: #c1c1c1;
	}
}
::v-deep {
	.ivu-input-number {
		width: 56px;
		margin-top: 0;
	}
	.ivu-input {
		border: none;
		background: transparent;
		border-radius: 0;
		cursor: pointer;
		height: 28px;
		&:focus {
			box-shadow: none;
		}
	}
}
</style>
