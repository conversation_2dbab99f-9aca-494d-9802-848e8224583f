<!--
 * @Description: 水厂耗电量统计
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-31 11:00:24
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-24 17:18:44
-->
<template lang="pug">
.electricity-fill 
	water-row(justify='flex-end', align='center')
		.electricity-fill-title 月份:
		DatePicker(
			:editable='false',
			v-model='date',
			format='yyyy-MM',
			type='month',
			:clearable='false',
			style='width: 215px',
			:options='options',
			@on-change='handleQuery()',
			placement='bottom-end'
		)
		Button.water-margin-left-16(v-if='state === "fill"', type='primary', @click='handleSave()', :loading='buttonLoading') 提交
		Button.water-margin-left-16(v-if='state === "record"', type='primary', @click='handleQuery()') 查询
		Button.water-margin-left-16(v-if='state === "record"', type='primary', @click='handleExport()') 导出
	#qrcodeDowm(style='display: none')
	WaterTable.electricity-fill-table(border, :columns='getColumns()', :data='tableData', :loading='loading')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { EventBus } from '@/utils/eventBus.js'
import { exportFile } from '@/utils/function.js'
import { queryStatisticsData, insertStatisticsData } from '@/api/water-screen-data.js'
export default {
	components: { WaterTable, WaterRow },
	props: ['state'],
	data() {
		return {
			date: new Date(`${new Date().getFullYear()}-${new Date().getMonth() + 1}`),
			buttonLoading: false,
			loading: false,
			tableData: [],
			options: {
				disabledDate(date) {
					return date.getTime() > new Date().getTime()
				},
			},
		}
	},
	mounted() {
		this.handleQuery()
		EventBus.$on('fresh-ele-record', () => {
			this.state === 'record' && this.handleQuery()
		})
	},
	methods: {
		handleQuery() {
			queryStatisticsData({
				date: this.$moment(this.date).format('YYYY-MM'),
				type: 2,
			})
				.then(res => {
					const { result = [] } = res
					this.tableData = result.map(item => {
						return {
							...item,
							editable: this.state === 'fill',
						}
					})
				})
				.catch(() => {
					this.tableData = []
				})
		},
		//提交
		handleSave() {
			try {
				this.buttonLoading = true
				const list = []
				this.tableData.forEach(item => {
					const {
						stationName = '',
						totalElectricity = '',
						waterConsumeElectricity = '',
						stationId = '',
					} = item
					list.push({
						stationName,
						stationId,
						waterConsumeElectricity,
						totalElectricity,
					})
				})
				const params = {
					date: this.$moment(this.date).format('YYYY-MM'),
					type: 2,
					list,
				}
				insertStatisticsData(params)
					.then(() => {
						this.$Message.success('提交成功!')
						this.buttonLoading = false
						this.handleQuery()
						EventBus.$emit('fresh-ele-record')
					})
					.catch(() => {
						this.buttonLoading = false
					})
			} catch {
				this.buttonLoading = false
			}
		},
		handleExport() {
			const baseUrl = `${location.origin}/api`
			let url =
				baseUrl +
				'/waterPlat/fillData/selectPowerConsumptionStatisticsExport?type=2&date=' +
				this.$moment(this.date).format('YYYY-MM')
			exportFile(url)
		},
		//正则
		valueChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
			// 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			e.target.value = e.target.value.replace(/^(([0-9]{15}\d))(.\d{1,2})?$/, '')
		},
		//输入值
		handleInputValue(index, value, key) {
			this.tableData[index][key] = value
			// const item = this.tableData[index]
			// this.tableData.splice(index, 1, {
			// 	...item,
			// 	[key]: value,
			// })
		},
		getColumns() {
			const columns = [
				{
					title: '名称',
					key: 'stationName',
					width: 120,
					align: 'center',
				},
				{
					title: '本月用电量（kW·h）',
					key: 'totalElectricity',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { totalElectricity, editable } = params.row
						return editable
							? h('Input', {
									props: {
										value: totalElectricity,
										maxlength: 18,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'totalElectricity')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', totalElectricity)
					},
				},
				{
					title: '千吨水耗电量（kW·h/ km³）',
					align: 'center',
					key: 'waterConsumeElectricity',
					minWidth: 160,
					render: (h, params) => {
						const { waterConsumeElectricity, editable } = params.row
						return editable
							? h('Input', {
									props: {
										value: waterConsumeElectricity,
										maxlength: 18,
									},
									on: {
										'on-change': e => {
											this.valueChange(e)
											const value = e.target.value
											this.handleInputValue(params.index, value, 'waterConsumeElectricity')
										},
										'on-keyup': e => {
											this.valueChange(e)
										},
									},
							  })
							: h('span', waterConsumeElectricity)
					},
				},
			]
			return columns
		},
	},
}
</script>
<style lang="less" scoped>
.electricity-fill {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-title {
		margin-right: 4px;
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
}
</style>
