<!--
 * @Description: 采集渠道配置
 * @Author: shenxh
 * @Date: 2023-04-03 09:22:40
 * @LastEditors: shenxh
 * @LastEditTime: 2023-04-12 09:17:45
-->

<template lang="pug">
.collect-road-config
	.header-wrap
		es-header.header(title='采集渠道配置')
	es-search.es-search(
		col='4',
		:show-collapse='false',
		:modules='moduleList',
		@on-search='handleSearchBtn',
		@on-reset='handleResetBtn'
	)
	water-row.table-btn-wrap(justify='space-between', align='center')
		i
		Button(type='primary', @click='handleCreate') 新增
	.table-wrap
		es-table(
			:columns='columns',
			:data='tableData',
			:loading='loading',
			border,
			showPage,
			:pageData='pageData',
			@on-page-num-change='handlePageNum',
			@on-page-size-change='handlePageSize'
		)
			template(slot-scope='{ row }', slot='params')
				.ellipsis-text(v-html='getText(row.params)', :title='getText(row.params)')
			template(slot-scope='{ row }', slot='memo')
				span(v-html='getText(row.memo)')
			template(slot-scope='{ row }', slot='action')
				Button(type='text', :style='{ color: "#3AA7D8" }', @click='handleRowUpd(row)', size='small') 编辑
				Poptip(transfer, confirm='', title='确定删除吗？', @on-ok='handleRowDel(row)')
					Button(type='text', :style='{ color: "#EC5151" }', size='small') 删除

	create-system-popup(
		v-model='showModal',
		:data='currentRow',
		:type='popupType',
		:trans-channel-list='moduleList[0].data[0].dataSourceList',
		@submit-form='handleSubForm'
	)
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import CreateSystemPopup from './components/CreateCollectRoadConfig.vue'
import { queryTransChannel, deleteTransChannel } from '@/api/data-acquisition-config'

export default {
	name: 'collect-road-config',
	components: {
		WaterRow,
		CreateSystemPopup,
	},
	props: {},
	data() {
		return {
			popupType: 0,
			showModal: false,
			loading: false,
			form: {},
			currentRow: {},
			tableData: [],
			moduleList: [
				{
					label: '',
					value: '1',
					isDefault: true,
					keys: ['channelCode', 'xxx', 'xxx', 'xxx'],
					model: {
						channelCode: '',
					},
					data: [
						{
							type: 1,
							key: 'channelCode',
							formItemProps: {
								label: '渠道名称/编码',
								prop: 'channelCode',
								labelWidth: 110,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
					],
				},
			],
			columns: [
				{
					type: 'index',
					title: '序号',
					align: 'center',
					width: 80,
				},
				{
					title: '渠道编码',
					key: 'channelCode',
				},
				{
					title: '采集渠道名称',
					key: 'name',
				},
				{
					title: '业务类型',
					key: 'sysName',
				},
				{
					title: '渠道参数',
					slot: 'params',
				},
				{
					title: '备注',
					slot: 'memo',
				},
				{
					title: '采集规则配置',
					key: 'enableRuleStr',
				},
				{
					title: '最后更新时间',
					key: 'updateTime',
				},
				{
					title: '操作人',
					key: 'createUser',
				},
				{
					title: '操作',
					slot: 'action',
					align: 'center',
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 50, 100, 200],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		this.getTableData()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 搜索按钮
		handleSearchBtn(params) {
			this.form = params

			this.getTableData()
		},

		// 重置按钮
		handleResetBtn() {
			this.form = {}
			this.pageData.current = 1

			this.getTableData()
		},

		// 添加按钮
		handleCreate() {
			this.currentRow = {}
			this.popupType = 0
			this.showModal = true
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum

			this.getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize

			this.getTableData()
		},

		// 行-编辑
		handleRowUpd(row) {
			this.currentRow = row
			this.popupType = 1
			this.showModal = true
		},

		// 行-删除
		handleRowDel(row) {
			this.deleteTransChannel(row)
		},

		// 弹窗按钮-保存
		handleSubForm() {
			this.getTableData()
		},

		getText(str = '') {
			return str.replace(/\n/g, '<br>')
		},

		// 获取表格数据
		getTableData() {
			this.loading = true
			queryTransChannel({
				...this.form,
				pageNum: this.pageData.current,
				pageSize: this.pageData.pageSize,
			}).then(res => {
				const { result = {} } = res || {}
				const { list = [], total } = result

				this.tableData = list
				this.pageData.total = total
				this.loading = false
			})
		},

		// 删除采集渠道
		deleteTransChannel(params) {
			deleteTransChannel(params).then(res => {
				const { responseCode } = res || {}

				if (responseCode === '100000') {
					this.$Message.success('操作成功')

					this.getTableData()
				}
			})
		},
	},
}
</script>

<style lang="less" scoped>
.collect-road-config {
	padding: 0 16px;
	/deep/ .es-search {
		height: inherit;
		padding: 8px 0;
		margin-bottom: 8px;
		.prefix {
			max-width: inherit;
			.ivu-tooltip-rel {
				max-width: inherit;
				.prefix-title {
					max-width: inherit;
				}
			}
		}
	}
	.table-btn-wrap {
		margin-bottom: 8px;
	}
	.table-wrap {
		width: 100%;
		height: calc(100vh - 150px);
	}
}
.ellipsis-text {
	max-height: 120px;
	padding: 10px 0;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: pre;
}
</style>
