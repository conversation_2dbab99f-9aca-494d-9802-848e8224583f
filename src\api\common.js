/*
 * @Description: 公共分类
 * @Author: shenxh
 * @Date: 2022-03-03 14:34:24
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-09-27 09:56:02
 */

import { GET } from '@/utils/request'

// 查询【系统列表】
export function querySysList(params) {
	return GET({
		url: '/waterPlat/common/querySysList',
		params,
	})
}
// 查询系统【站点树】
export function queryStationsBySysCode(params) {
	return GET({
		url: '/waterPlat/common/queryStationsBySysCode',
		params,
	})
}
// 查询系统【站点列表】
export function querySysConfigTree(params) {
	return GET({
		url: '/waterPlat/common/querySysConfigTree',
		params,
	})
}

// 查询系统-报警类型（通用）
export function querySysCommonAlarmTypeDTO(params) {
	return GET({
		url: '/waterPlat/common/querySysCommonAlarmTypeDTO',
		params,
	})
}

// 查询系统-报警类型-设备类型（专用）
export function querySysSpecialAlarmTypeDTO(params) {
	return GET({
		url: '/waterPlat/common/querySysSpecialAlarmTypeDTO',
		params,
	})
}
// 查询系统-报警类型-设备,一个设备类型对多个设备（鸡泽专用）
export function querySysSpecialAlarmTypeNewDTO(params) {
	return GET({
		url: '/waterPlat/common/queryDeviceTypeAlarmType',
		params,
	})
}

// 查询【报警等级列表】
export function queryAlarmLevelList(params) {
	return GET({
		url: '/waterPlat/common/queryAlarmLevelList',
		params,
	})
}

// 查询报警配置【规则类型】
export function queryAlarmConfigRuleType(params) {
	return GET({
		url: '/waterPlat/common/queryAlarmConfigRuleType',
		params,
	})
}

// 查询系统【报警来源信息】
export function querySysNodeInfo(params) {
	return GET({
		url: '/waterPlat/common/querySysNodeInfo',
		params,
	})
}
// 查询通知模板
export function queryNotificationTemplate(params) {
	return GET({
		url: '/sms/templateAttribute/queryAll',
		params,
	})
}

// 部门组织数
export function getDepartment(params) {
	return GET({
		url: '/waterPlat/common/dept',
		params,
	})
}

// 根据部门ownership查询该部门下所有员工
export function getUsers(params) {
	return GET({
		url: '/waterPlat/common/dept/users',
		params,
	})
}

// 查询二供系统的区域和区域下的站点编号
export function queryAreaAndStationCodes(params) {
	return GET({
		url: '/waterPlat/common/queryAreaAndStationCodes?sysCode=eg',
		params,
	})
}

// 获取当前用户
export function currentUser(params) {
	return GET({
		url: '/sysConfig/currentUser',
		params,
		hideLoading: true,
	})
}

// 【最小流量】-左侧分区树
export function queryAreaTree(params) {
	return GET({
		url: '/dma/minFlow/areaTree',
		params,
	})
}

// 查询站点树
export function queryTypeTree(params) {
	return GET({
		url: '/dma/station/type/queryTypeTree',
		params,
	})
}
