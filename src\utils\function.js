/*
 * @Description:
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-31 14:03:00
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-05-31 14:03:02
 */
export function exportFile(url) {
	document.getElementById('tableDown') &&
		document.getElementById('qrcodeDowm').removeChild(document.getElementById('tableDown'))
	let iframe = document.createElement('iframe')
	iframe.id = 'tableDown'
	iframe.src = url
	iframe.style.display = 'none'
	document.getElementById('qrcodeDowm').appendChild(iframe)
}
export function getVal(val) {
	if (val || val === 0) {
		return val
	}
	return '--'
}
