<template>
	<div class="device-account-detail">
		<Spin :spinning="loading" tip="加载中...">
			<div class="device-info">
				<gc-header :header-data="headerData">
					<!-- <template #status>
						<span :class="['status', status.class]">{{ status.text }}</span>
					</template> -->
				</gc-header>
				<div class="tab-box">
					<gc-detail-tab
						:common="deviceId"
						:tab-list="tabList"
						:default-active-name.sync="defaultActiveName"
					></gc-detail-tab>
				</div>
			</div>
		</Spin>
	</div>
</template>

<script>
import AlarmRecord from './tabs/alarmRecord/index.vue'
import BasicInfo from './tabs/basicInfo/index.vue'
import { apiGetDeviceDetail } from '@/api/iotOnePicture.js'
import { isBlank } from '@/utils/util.js'
import GcHeader from '@/components/gc-header/index.vue'
import GcDetailTab from '@/components/gc-detail-tab/index.vue'
// import { mapGetters } from 'vuex'

export default {
	name: 'deviceDetail',
	components: { GcHeader, GcDetailTab },
	props: {
		deviceId: {
			type: [String, Number],
			required: true,
		},
	},
	data() {
		return {
			// dynomicKeys: [],
			title: '设备新增',
			detail: {},
			// alarmCount: 0,
			loading: false, //加载loading
			defaultActiveName: 'BasicInfo',
		}
	},
	computed: {
		// ...mapGetters({
		// 	userInfo: 'userInfo',
		// 	dataList: 'dataList',
		// }),
		headerData() {
			// let title = this.detail.deviceTypeName
			let title = this.title
			// if (this.userInfo.tenantType == 1) {
			// 	title =
			// 		(this.dataList.userType.find(item => item.defaultValue == this.detail.userType) || {}).name || '--'
			// }
			return {
				pic: 'account-detail', //url
				title, //标题
				desc: [
					{
						icon: 'icon-bianhao',
						text: this.detail.deviceCode,
					},
					{
						icon: 'icon-daohangdizhi',
						text: this.detail.address,
						maxWidth: '40%',
					},
				],
			}
		},
		// status() {
		// 	const map = {
		// 		0: {
		// 			text: '待安装',
		// 			class: 'wait-installed',
		// 		},
		// 		1: {
		// 			text: '已安装',
		// 			class: 'installed',
		// 		},
		// 		2: {
		// 			text: '已拆除',
		// 			class: 'had-uninstalled',
		// 		},
		// 	}
		// 	return {
		// 		text: map[this.detail.deviceStatus]?.text || '',
		// 		class: map[this.detail.deviceStatus]?.class || '',
		// 	}
		// },
		tabList() {
			let tabArr = [
				{
					name: 'BasicInfo',
					label: '基本信息',
					component: BasicInfo,
					disabled: false,
					tabData: this.detail,
				},
				{
					name: 'AlarmRecord',
					label: '告警记录',
					component: AlarmRecord,
					disabled: false,
					// keyWord: 'monitor:device:account:record:alarm',
					needTag: true,
					// tagNum: this.alarmCount,
				},
			]
			return tabArr
		},
	},
	watch: {
		deviceId: {
			immediate: true,
			handler(val) {
				if (val) {
					this.getDeviceDetail(val)
				}
			},
		},
	},
	created() {},
	methods: {
		// 获取设备详情
		getDeviceDetail(deviceId) {
			console.log('getDeviceDetail', deviceId)
			this.loading = true
			apiGetDeviceDetail({
				deviceId: deviceId,
			})
				.then(res => {
					console.log('res', res)
					if (res.responseCode == '100000') {
						this.detail = res.result
						this.title = this.detail.deviceTypeName
					} else {
						alert(res.message)
					}
				})
				.finally(() => {
					this.loading = false
				})
		},
		dealReRenderData(row) {
			let obj = {}
			for (var key in row) {
				let val = row[key]
				if (!isBlank(val)) {
					if (key === 'installDate') {
						obj[key] = new Date(val)
					} else if (key === 'latitude') {
						obj['lngLat'] = `经度${row.longitude || ''}度，纬度${row.latitude || ''}度`
						obj[key] = val
					} else {
						obj[key] = val
					}
				}
			}
			return obj
		},
		// getDynomicKeys(list) {
		// 	this.dynomicKeys = list
		// },
	},
}
</script>
<style lang="scss" scoped>
.device-account-detail {
	@include base-button(100px);
	height: 100%;
	display: flex;
	.customer-info {
		height: 100%;
		padding: 12px;
	}
	.device-info {
		flex: 1;
		width: 0;
		display: flex;
		flex-direction: column;
	}
	.tab-box {
		flex: 1;
		height: 0;
	}
	.installed {
		background: #e4f5ec;
		color: #16a65d;
		&::before {
			position: absolute;
			top: 8px;
			left: 10px;
			display: block;
			content: '';
			width: 4px;
			height: 4px;
			border-radius: 50%;
			background-color: #16a65d;
		}
	}
	.wait-installed {
		background: #f5e9e4;
		color: #e5662e;
		&::before {
			position: absolute;
			top: 8px;
			left: 10px;
			display: block;
			content: '';
			width: 4px;
			height: 4px;
			border-radius: 50%;
			background-color: #e5662e;
		}
	}
	.had-uninstalled {
		background: #dbdbdb;
		color: #8f8f8f;
		&::before {
			position: absolute;
			top: 8px;
			left: 10px;
			display: block;
			content: '';
			width: 4px;
			height: 4px;
			border-radius: 50%;
			background-color: #8f8f8f;
		}
	}
}
</style>
