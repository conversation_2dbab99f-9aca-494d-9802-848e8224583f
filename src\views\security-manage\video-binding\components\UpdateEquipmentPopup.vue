<!--
 * @Description: 编辑设备弹窗
 * @Author: shenxh
 * @Date: 2022-04-01 15:54:51
 * @LastEditors: shenxh
 * @LastEditTime: 2024-03-12 16:23:36
-->

<template lang="pug">
.update-equipment-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='600',
		v-model='showModal',
		:title='formData && formData.id ? "编辑" : "添加"',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='80')
					FormItem(label='设备名称', prop='name')
						Input(v-model='formData.name', placeholder='请输入设备名称')
					FormItem(label='设备编码', prop='code')
						Input(v-model='formData.code', placeholder='请输入设备编码', disabled)
					FormItem(label='所属平台', prop='platformId')
						Select(v-model='formData.platformId', transfer, filterable, placeholder='请选择', disabled)
							Option(v-for='item in options', :key='item.value', :value='item.value') {{ item.label }}
					FormItem(label='原始名称', prop='sourceCode')
						Input(v-model='formData.sourceCode', placeholder='请输入原始名称', disabled)
					FormItem(label='元数据', prop='metaData')
						Input(v-model='formData.metaData', placeholder='请输入元数据', disabled)
					FormItem(label='视频画面', prop='previewImageList')
						.previewImageList
							upload-img(v-model='formData.previewImageList')
							.tip 限制200KB以内
		template(slot='footer')
			Button(@click='handleClose') 取消
			Button(type='primary', @click='handleSubForm') 确定
</template>

<script>
import UploadImg from '@/components/gc-upload-img/UploadImg'
import { updEquipment } from '@/api/security-manage'

export default {
	name: 'update-equipment-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		UploadImg,
	},
	props: {
		showModal: Boolean,
		data: Object,
	},
	data() {
		return {
			formData: {},
			options: [
				{
					label: '海康安防管理平台',
					value: 1,
				},
				{
					label: '大华安防管理平台',
					value: 2,
				},
			],
			formRules: {
				name: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				const previewImageList = []

				if (this.data.previewImageUrl) {
					previewImageList.push({
						url: this.data.previewImageUrl,
					})
				}
				this.formData = {
					...this.data,
					previewImageList,
				}
			} else {
				this.$refs.form.resetFields()
			}
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this._updEquipment()
				}
			})
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		// 添加及编辑安防平台
		_updEquipment() {
			const { id, name, previewImageList = [] } = this.formData

			updEquipment({
				id,
				name,
				previewImageUrl: previewImageList[0]?.url,
			}).then(() => {
				this.$Message.success('操作成功')

				this.handleClose()
				this.$emit('submit-form', this.formData)
			})
		},
	},
}
</script>

<style lang="less" scoped>
.update-equipment-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			padding: 0 40px;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
				.previewImageList {
					display: flex;
					align-items: flex-end;
					.tip {
						margin-left: 20px;
						margin-bottom: 4px;
					}
				}
			}
		}
	}
}
</style>
