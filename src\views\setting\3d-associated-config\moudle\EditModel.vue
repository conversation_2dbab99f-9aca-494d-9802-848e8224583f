<template>
	<Modal
		class-name="custom-modal"
		width="460"
		:value="show"
		:transfer="false"
		:title="title"
		@on-cancel="handleCancel"
		@on-visible-change="handleVisibleChange"
	>
		<div class="water-modal-content station-modal-content">
			<!-- <Button type="primary" @click="add">新增</Button> -->
			<Spin fix v-if="listLoading">加载中。。。</Spin>
			<Form ref="formValidate" :model="formItem" :label-width="80">
				<Form-item label="名称">
					<Input v-model="formItem.name" placeholder="请输入对象名"></Input>
				</Form-item>
				<Form-item label="管径">
					<Input v-model="formItem.pipeDiameter" placeholder="请输入对象名"></Input>
				</Form-item>
				<Form-item label="管材">
					<Input v-model="formItem.pipeMaterial" placeholder="请输入对象名"></Input>
				</Form-item>
				<Form-item label="管龄">
					<Input v-model="formItem.pipeAge" placeholder="请输入对象名"></Input>
				</Form-item>
				<Form-item label="标签展示点">
					<!-- <Input
						v-model="formItem.selectPoint"
						placeholder="请输入对象名"
					></Input> -->
					<Select transfer v-model="formItem.selectPoint" filterable clearable placeholder="请选择">
						<Option v-for="(item, index) in pointList" :value="index" :key="index">
							{{ index + ':' }}{{ item }}
						</Option>
					</Select>
				</Form-item>
			</Form>
		</div>
		<div slot="footer">
			<Button type="primary" @click="handleCheck">确定</Button>
		</div>
	</Modal>
</template>

<script>
import { groupUpdate } from '@/api/setting'

export default {
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	mounted() {},
	data() {
		return {
			title: '编辑',
			listLoading: false,
			formItem: {
				id: '',
				name: '',
				selectPoint: '',
				pipeDiameter: '',
				pipeMaterial: '',
				pipeAge: '',
			},
			pointList: [],
			currentRow: {},
		}
	},
	methods: {
		init(row) {
			this.currentRow = row
			const { id, property, point, name } = row
			this.pointList = point
			const obj = JSON.parse(property)
			const { pipeDiameter, pipeMaterial, pipeAge, selectPoint } = obj
			this.formItem.id = id
			this.formItem.pipeDiameter = pipeDiameter
			this.formItem.pipeMaterial = pipeMaterial
			this.formItem.selectPoint = selectPoint
			this.formItem.pipeAge = pipeAge
			this.formItem.name = name
		},
		add() {
			this.formItem.extras.push({
				key: '',
				value: '',
			})
		},
		// 弹窗显隐事件
		handleVisibleChange() {},

		handleRemove() {
			this.formItem.processImage = ''
		},
		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},
		// 重置
		handleReset() {},
		// 确定
		handleCheck() {
			const _this = this
			_this.$refs.formValidate.validate(valid => {
				if (valid) {
					const { name, id, pipeDiameter, pipeMaterial, pipeAge, selectPoint } = _this.formItem
					let property = JSON.parse(this.currentRow.property)
					property.pipeDiameter = pipeDiameter
					property.pipeMaterial = pipeMaterial
					property.selectPoint = selectPoint
					property.pipeAge = pipeAge
					let params = {
						name,
						id,
						property: JSON.stringify(property),
					}
					groupUpdate(params)
						.then(() => {
							_this.$Message.success('提交成功!')
							this.$emit('update:show', false)
							this.$emit('successCallBack')
							_this.formItem.extras = [
								{
									key: '',
									value: '',
								},
							]
						})
						.catch(() => {
							this.listLoading = false
						})
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
	},
}
</script>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	.ivu-modal {
		height: auto !important;
	}
	.ivu-input-number-input {
		color: #fff;
		background: #133a5e;
		border: none;
	}
	.ivu-form-item-label {
		width: 125px !important;
	}
	.ivu-form-item-content {
		margin-left: 125px !important;
	}
	.ivu-input-wrapper {
		width: 280px;
	}
}
</style>
