<template lang="pug">
.text-config(v-if='value.controls[index]')
	water-row(justify='space-between', align='center')
		.text-config-title 点击打开查看曲线
		i-switch(v-model='value.controls[index].clickable', @on-change='handleChange("clickable")')
</template>
<script>
import WaterRow from '@/components/gc-water-row'
export default {
	name: 'InteractionConfig',
	components: { WaterRow },
	props: {
		value: {
			type: Object,
			default: function () {
				return {}
			},
		},
		index: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {}
	},
	methods: {
		handleChange(type) {
			this.$emit('changeControl', type, this.value.controls[this.index][type])
		},
	},
}
</script>
<style lang="less" scoped>
.text-config {
	padding: 8px;
	&-title {
		color: #000;
		font-weight: bold;
	}
}
::v-deep {
	.ivu-input-number {
		width: 56px;
		margin-top: 0;
	}
	.ivu-input {
		border: none;
		background: transparent;
		border-radius: 0;
		cursor: pointer;
		height: 28px;
		&:focus {
			box-shadow: none;
		}
	}
}
</style>
