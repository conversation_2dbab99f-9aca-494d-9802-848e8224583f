<template lang="pug">
.create-system-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='500',
		v-model='showModal',
		:title='formData && formData.id ? "编辑" : "添加"',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='80')
					FormItem(label='配置编码', prop='code')
						Input(v-model='formData.code', placeholder='请输入配置编码')
					FormItem(label='配置类型', prop='type')
						Select(v-model='formData.type', transfer, filterable, placeholder='请选择')
							Option(v-for='item in platformList', :key='item.value', :value='item.value') {{ item.label }}
					FormItem(label='值', prop='val')
						//- i-switch(v-model="formData.val")
						Input(v-model='formData.val', placeholder='请输入值')
					FormItem(label='备注', prop='memo')
						Input(v-model='formData.memo', type='textarea', maxlength='100', show-word-limit, placeholder='请输入备注')
		template(slot='footer')
			Button(@click='handleClose') 关闭
			Button(type='primary', @click='handleSubForm') 保存
</template>

<script>
import { addSysConfig, updateSysConfig } from '@/api/base-item'

export default {
	name: 'create-system-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {},
	props: {
		showModal: Boolean,
		data: Object,
	},
	data() {
		return {
			formData: {},
			platformList: [
				{ value: '0', label: '全局' },
				{ value: '1', label: '租户' },
			],
			formRules: {
				code: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
				type: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
					},
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				this.formData = { ...this.data }
				// this.formData.val = this.data.val === 'true' ? true : false
			} else {
				this.$refs.form.resetFields()
			}
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this._updSecurityPlatform()
				}
			})
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		// 添加及编辑安防平台
		_updSecurityPlatform() {
			const { id, code, val, type, memo } = this.formData
			if (id) {
				updateSysConfig({
					id,
					code,
					val,
					type,
					memo,
				}).then(res => {
					console.log(res)
					this.$Message.success('操作成功')

					this.handleClose()
					this.$emit('submit-form', this.formData)
				})
			} else {
				addSysConfig({
					code,
					val,
					type,
					memo,
				}).then(res => {
					console.log(res)
					this.$Message.success('操作成功')

					this.handleClose()
					this.$emit('submit-form', this.formData)
				})
			}
		},
	},
}
</script>

<style lang="less" scoped>
.create-system-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			padding: 0 40px;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
