<template>
	<div class="wrap">
		<Modal
			:value="show"
			width="860"
			class-name="vertical-center "
			@on-visible-change="modalStateChange"
			:mask-closable="false"
		>
			<div slot="header">
				<p class="header-title">压力分区设备列表</p>
			</div>
			<div class="body">
				<div class="body-left">
					<table-content
						title="已关联"
						ref="leftTableRef"
						:data="associatedData.data"
						:pageData="associatedData.pageData"
						@search="handleSearch($event, 'associated')"
						@on-select="handleSelect($event, 'associated')"
						@on-change="handlePageChange($event, 'associated')"
						@on-page-size-change="handlePageSizeChange($event, 'associated')"
					></table-content>
				</div>
				<div class="body-center">
					<Icon
						:class="{ active: notAssociatedSelectList.length }"
						type="ios-arrow-dropleft-circle"
						@click="toLeft"
					/>
				</div>
				<div class="body-right">
					<table-content
						title="未关联"
						ref="rightTableRef"
						showSelectation
						:data="notAssociatedData.data"
						:pageData="notAssociatedData.pageData"
						@search="handleSearch($event, 'notAssociated')"
						@on-select="handleSelect($event, 'notAssociated')"
						@on-change="handlePageChange($event, 'notAssociated')"
						@on-page-size-change="handlePageSizeChange($event, 'notAssociated')"
					></table-content>
				</div>
			</div>
			<div slot="footer">
				<!-- <Button @click.native="clickCancel">关闭</Button> -->
			</div>
		</Modal>
	</div>
</template>

<script>
import tableContent from './tableContent.vue'
import {
	// getTemplateRelationList,
	templateRelation,
	notAssociatedList,
	associatedList,
} from '@/api/setting'
export default {
	name: 'add-model',

	components: {
		tableContent,
	},

	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},

	data() {
		return {
			loading: false,
			associatedData: { data: [], pageData: {} },
			notAssociatedData: { data: [], pageData: {} },
			rowData: {},
			notAssociatedSelectList: [],
		}
	},

	methods: {
		init(row) {
			this.rowData = row
			this.$refs.leftTableRef.message = ''
			this.$refs.rightTableRef.message = ''
			this.initData(row)
			this.getTemplateRelationList(row, { status: 1 })
			this.getTemplateRelationList(row, { status: 0 })
		},
		// notAssociatedList(row, option) {
		// 	const parmas = this.getParams(row, option)
		// 	notAssociatedList(parmas).then(res => {
		// 		console.log(res)
		// 	})
		// },

		// associatedList(row, option) {
		// 	const parmas = this.getParams(row, option)
		// 	associatedList(parmas).then(res => {
		// 		console.log(res)
		// 	})
		// },

		initData() {
			this.notAssociatedSelectList = []
		},

		handleSearch(message, from) {
			let option = {}
			message && (option = { stationName: message })
			switch (from) {
				case 'associated':
					this.getTemplateRelationList(this.rowData, {
						...option,
						status: 1,
					})
					break
				case 'notAssociated':
					this.getTemplateRelationList(this.rowData, {
						...option,
						status: 0,
					})
					break
				default:
					break
			}
		},

		handlePageChange(pageNum, from) {
			switch (from) {
				case 'associated':
					this.getTemplateRelationList(this.rowData, {
						pageNum,
						status: 1,
					})
					break
				case 'notAssociated':
					this.getTemplateRelationList(this.rowData, {
						pageNum,
						status: 0,
					})
					break
				default:
					break
			}
		},

		handlePageSizeChange(pageSize, from) {
			switch (from) {
				case 'associated':
					this.getTemplateRelationList(this.rowData, {
						pageSize,
						status: 1,
					})
					break
				case 'notAssociated':
					this.getTemplateRelationList(this.rowData, {
						pageSize,
						status: 0,
					})
					break
				default:
					break
			}
		},

		handleSelect(selectObj, from) {
			const { selection } = selectObj
			switch (from) {
				case 'notAssociated':
					this.notAssociatedSelectList = selection
					break
				default:
					break
			}
		},

		getTemplateRelationList(row, option) {
			const parmas = this.getParams(row, option)
			if (parmas.status === 1) {
				associatedList(parmas).then(data => {
					let form = 'associatedData'

					this.dealData(data, form)
				})
			} else {
				notAssociatedList(parmas).then(data => {
					let form = 'notAssociatedData'

					this.dealData(data, form)
				})
			}
		},
		// 获取参数
		getParams(row, option) {
			let parmas = {
				pageNum: 1,
				pageSize: 10,
				id: row.id,
				stationName: '',
				stationType: '',
			}
			return { ...parmas, ...option }
		},
		// 处理数据
		dealData(data, key) {
			const { list = [], total = 0, pageNum = 1 } = data.result
			this.$set(this[key], 'data', list)
			this.$set(this[key], 'pageData', {
				total: total,
				current: pageNum || 1,
			})
		},
		// 模态窗口状态变化事件
		modalStateChange(val) {
			if (val === false) {
				this.$emit('update:show', false)
			}
		},

		clickSubmit() {
			this.$refs.formAddRef.validate(valid => {
				if (valid) {
					this.loading = true
					const formTemp = { ...this.form }

					this.sendData(formTemp)
				}
			})
		},
		async toLeft() {
			if (this.notAssociatedSelectList.length === 0) {
				return
			}
			let ids = []
			this.notAssociatedSelectList.forEach(item => {
				const { applicationName, stationId } = item
				// debugger
				ids.push({
					applicationName,
					stationId,
					id: this.rowData.id,
				})
			})
			// const url = '/api/iwater/pressureAreaEquipment/batch/save'
			// await this.sendData(url, ids)
			// debugger
			// console.log(this.rowData)
			templateRelation(ids).then(() => {
				this.$Message.info('操作成功')
				this.notAssociatedSelectList = []
				this.getTemplateRelationList(this.rowData, {
					status: 1,
				})
				this.getTemplateRelationList(this.rowData, {
					status: 0,
				})
				this.$refs.leftTableRef.message = ''
				this.$refs.rightTableRef.message = ''
			})
		},
	},
}
</script>

<style lang="less" scoped>
::v-deep {
	.ivu-modal-footer {
		padding: 0;
	}
}
.body {
	display: flex;
	.body-left,
	.body-right {
		width: calc(50% - 12px);
	}
	.body-center {
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
		color: #cccc;
		font-size: 24px;
		.active {
			cursor: pointer;
			color: #459dee;
		}
	}
}
.header-title {
	position: relative;
	padding: 10px 14px;
	font-size: 14px;
	color: #666;
	font-weight: normal;
	height: auto;
	width: auto;
	vertical-align: middle;
	line-height: 1;
	text-align: left;
}
.header-title::before {
	position: absolute;
	top: 13px;
	left: 0;
	content: '';
	width: 8px;
	height: 8px;
	background: #3b95e9;
}
</style>
