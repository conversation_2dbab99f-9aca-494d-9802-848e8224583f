<!--
 * @Description: 巡检任务详情
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-02-22 15:09:03
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-05-20 08:37:33
-->
<template lang="pug">
Modal(
	:value='show',
	class-name='info-modal',
	width='65',
	title='巡检任务详情',
	:mask='true',
	footer-hide,
	:transfer='false',
	@on-cancel='handleCancel',
	@on-visible-change='handleVisibleChange'
)
	water-row(justify='flex-start', align='center')
		es-header.header(title='巡检任务名称：')
		span {{ taskName }}

	water-row(justify='flex-start', align='center')
		es-header.header(title='巡检人：')
		span {{ executor }}
	es-header.header.water-margin-top-4(title='巡检明细')
	es-table.water-margin-top-4.water-table(
		border,
		:columns='columns',
		:data='tableData',
		height='400',
		:loading='loading'
	)
		template(slot-scope='{ row }', slot='image')
			img.device-image(
				v-for='(item, index) in row.fileList',
				:key='index',
				@click='handleView(row.fileList, index)',
				:src='item.url'
			)
		template(slot-scope='{ row }', slot='action')
			span {{ row.PatrolState }}
			Button(
				v-if='row.PatrolState === "未巡查"',
				type='primary',
				size='small',
				style='margin-left: 8px',
				@click='handleConfirm(row)'
			) 手动确认
	check-task(ref='checkModal', :show.sync='showModal', @fresh-detail='freshDetail()')
</template>
<script>
import { getPatrolTaskList, getUId } from '@/api/inspection-management.js'
import WaterRow from '@/components/gc-water-row'
import CheckTask from './CheckTask.vue'
import { EventBus } from '@/utils/eventBus.js'
import { init } from '@eslink/esvcp-pc-ui/packages/es-components/imageViewer/index.js'
export default {
	name: 'task-detail',
	components: { WaterRow, CheckTask },
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		sysCode() {
			return this.$route.query.sysCode || 'dc'
		},
	},
	data() {
		return {
			loading: false,
			tableData: [],
			taskName: '',
			executor: '',
			columns: [
				{
					title: '所属片区',
					key: 'Area',
				},
				{
					title: this.sysCode === 'dc' ? '供水站' : '泵房名称',
					key: 'Name',
				},
				{
					title: '巡检计划时段',
					key: 'Execution_Time',
				},
				{
					title: '巡检内容描述',
					key: 'Description',
				},
				{
					title: '巡检照片',
					slot: 'image',
				},
				{
					title: '巡检时间',
					key: 'LastAuditTime',
				},
				{ title: '操作', slot: 'action', minWidth: 100 },
			],
			showModal: false,
			Id: null,
			uid: null,
		}
	},
	mounted() {
		this.getMessages()
	},
	methods: {
		getMessages() {
			window.addEventListener('message', messageEvent => {
				const data = messageEvent.data
				const params = data.params
				this.indentyDisplay = this.loginName = params && params.loginName ? params.loginName : 'yueqingTest'
				if (this.indentyDisplay) {
					this.getUid(this.indentyDisplay)
				}
			})
		},
		getUid(UserName = 'yueqingTest') {
			getUId({ UserName }).then(res => {
				this.uid = res.result
			})
		},
		handleVisibleChange(value) {
			if (!value) {
				// this.currentIndex = 0
			}
		},
		handleCancel() {
			this.$emit('update:show', false)
		},
		freshDetail() {
			this.Id && this.getTaskDetail(this.Id)
			EventBus.$emit('fresh-task-list')
		},
		//查看任务详情
		getTaskDetail(Id) {
			this.Id = Id
			getPatrolTaskList({ Id })
				.then(res => {
					const { result = '' } = res
					const { Name = '', Executor = '', PatrolExes = [] } = result
					this.taskName = Name
					this.executor = Executor
					const online = process.env.VUE_APP_ENV === 'online'
					const imageurl = online ? process.env.VUE_PLAT_IMAGE_URL : window.VUE_PLAT_IMAGE_URL
					this.tableData = PatrolExes.map(item => {
						return {
							...item,
							fileList: item.DeviceImages.map(imageitem => {
								return {
									url: `${imageurl}/FileCenter/IMG/Read/${imageitem.MediaId}/para?uid=${this.uid}`,
								}
							}),
						}
					})
				})
				.catch(() => {
					this.tableData = []
					this.taskName = ''
					this.executor = ''
				})
		},
		handleConfirm(row) {
			this.showModal = true
			this.$refs.checkModal.initTask(this.Id, row)
		},
		handleView(imgUrlList = [], idx) {
			imgUrlList = imgUrlList.map(item => {
				return item.url
			})
			init({
				imgUrlList,
				index: idx,
				title: '巡检图片',
			})
		},
	},
}
</script>
<style lang="less" scoped>
.device-image {
	width: 42px;
	height: 42px;
	&:not(:first-child) {
		margin-left: 4px;
	}
}
::v-deep {
	.ivu-modal {
		.ivu-modal-content {
			.ivu-modal-header {
				padding: 14px 16px !important;
			}
		}
	}
}
</style>
