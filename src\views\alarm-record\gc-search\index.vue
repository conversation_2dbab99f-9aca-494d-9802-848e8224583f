<template>
	<div class="gc-search">
		<div class="gc-search-input" v-for="item in searchConfig" :key="item.prop">
			<template v-if="item.type === 'input'">
				<Input v-model="searchForm.nodeCode" placeholder="请输入" style="width: 300px">
					<span slot="prepend">告警设备</span>
					<Icon type="ios-search" slot="suffix" @click="handleSearch" />
				</Input>
			</template>
		</div>
		<div class="gc-search-advanced" v-if="advancedSearch" @click="emitAdvanceQuery">
			<img src="@/assets/images/search.png" />
		</div>
		<div class="gc-search-reset">
			<img src="@/assets/images/reset.png" @click="handleReset" v-if="needReset" />
		</div>
		<Drawer
			title="高级筛选"
			:closable="true"
			v-model="showAdvancedSearch"
			:mask="false"
			@on-close="handleAdvanceCancel"
		>
			<div class="advanced-search-content">
				<!-- 告警类型、设备类型、告警等级、告警事件和处理状态 -->
				<Form ref="formDataRef" :model="formData">
					<FormItem label="设备类型" prop="nodeTypes">
						<Select v-model="formData.nodeTypes" placeholder="请选择" clearable>
							<Option v-for="item in deviceTypeList" :value="item.value" :key="item.value">
								{{ item.label }}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="告警类型" prop="alarmTypeCode">
						<Select v-model="formData.alarmTypeCode" placeholder="请选择" clearable>
							<Option v-for="item in alarmTypeList" :value="item.value" :key="item.value">
								{{ item.label }}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="告警等级" prop="alarmLevelCode">
						<Select v-model="formData.alarmLevelCode" placeholder="请选择" clearable>
							<Option v-for="item in alarmLevelList" :value="item.value" :key="item.value">
								{{ item.label }}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="告警时间" prop="lastActiveTime">
						<DatePicker
							type="datetime"
							placeholder="请选择"
							clearable
							v-model="formData.lastActiveTime"
							format="yyyy-MM-dd HH:mm:ss"
						></DatePicker>
					</FormItem>
					<FormItem label="处理状态" prop="disposeStatus">
						<Select v-model="formData.disposeStatus" placeholder="请选择" clearable>
							<Option v-for="item in handleStatusList" :value="item.value" :key="item.value">
								{{ item.label }}
							</Option>
						</Select>
					</FormItem>
				</Form>
			</div>
			<div class="advanced-search-footer">
				<Button @click="handleAdvanceCancel">取消</Button>
				<Button @click="handleAdvanceSearch" type="primary" style="margin-left: 12px">搜索</Button>
			</div>
		</Drawer>
	</div>
</template>
<script>
import { apiDeviceTyleList, apiGetAlarmLevelList, apiGetAlarmTypeList } from '@/api/alarm-config.js'
export default {
	components: {},
	emits: ['search', 'reset', 'confirm', 'cancel'],
	props: {
		searchConfig: {
			type: Array,
			default: () => [
				{
					prop: 'pointName',
					type: 'input',
					label: '风险点名称',
					placeholder: '请输入',
					defaultValue: '',
				},
			],
		},
		advancedSearch: {
			type: Boolean,
			default: true,
		},
		needReset: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			showAdvancedSearch: false,
			searchForm: {}, //外部搜索
			formData: {
				nodeTypes: '',
				alarmTypeCode: '',
				alarmLevelCode: '',
				lastActiveTime: '',
				disposeStatus: '',
			}, //高级搜索
			deviceTypeList: [], // 设备类型字典
			alarmTypeList: [], // 告警类型字典
			alarmLevelList: [], // 告警等级字典
			handleStatusList: [
				{
					label: '未处理',
					value: 0,
				},
				{
					label: '已处理',
					value: 10,
				},
				{
					label: '已建单',
					value: 20,
				},
			], // 处理状态字典
		}
	},
	computed: {},
	watch: {},
	methods: {
		emitAdvanceQuery() {
			this.showAdvancedSearch = !this.showAdvancedSearch
		},
		handleSearch() {
			const { nodeTypes, ...others } = this.formData
			this.$emit('search', {
				...this.searchForm,
				...others,
				nodeTypes,
			})
		},
		handleReset() {
			this.resetForm()
			this.showAdvancedSearch = false
			this.$emit('reset')
		},
		handleAdvanceSearch() {
			this.formData.lastActiveTime = this.formData?.lastActiveTime
				? this.$moment(this.formData?.lastActiveTime).format('YYYY-MM-DD HH:mm:ss')
				: null
			this.formData.nodeTypes = this.formData.nodeTypes ? [this.formData.nodeTypes] : []
			this.handleSearch()
			this.showAdvancedSearch = false
		},
		handleAdvanceCancel() {
			this.resetForm()
			this.showAdvancedSearch = false
		},
		resetForm() {
			console.log('gcsearch resetForm')
			this.searchForm = {}
			this.$refs.formDataRef?.resetFields()
		},
	},
	created() {},
	mounted() {
		apiDeviceTyleList({ name: '' }).then(res => {
			// this.deviceTypeList = res
			const { result } = res
			if (result.length) {
				this.deviceTypeList =
					result.length > 0
						? result?.map(item => {
								return {
									label: item?.name,
									value: item?.deviceTypeCode,
								}
						  })
						: []
			}
		})
		apiGetAlarmTypeList({ sysCode: 'jz' }).then(res => {
			const { result } = res
			if (result) {
				this.alarmTypeList = Object.keys(result).map(key => ({
					label: key,
					value: result[key],
				}))
			} else {
				this.alarmTypeList = []
			}
		})
		apiGetAlarmLevelList().then(res => {
			const { result } = res
			if (result.length) {
				this.alarmLevelList =
					result.length > 0
						? result?.map(item => {
								return {
									label: item?.name,
									value: item?.code,
								}
						  })
						: []
			}
		})
	},
	beforeDestroy() {},
}
</script>
<style lang="less" scoped>
.gc-search {
	min-height: 48px;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	.gc-search-advanced {
		margin-left: 12px;
		font-size: 14px;
		height: 32px;
		color: #3f435e;
		cursor: pointer;
		user-select: none;
		@include flex-center;
		.gc-search-advanced.active {
			height: 30px;
			color: #dd0ea5;
			border-radius: 4px;
			position: relative;
		}
	}
	.gc-search-reset {
		width: 32px;
		height: 32px;
		margin-left: 10px;
		cursor: pointer;
	}
	/deep/ .ivu-input-suffix {
		z-index: 5;
		cursor: pointer;
	}
}
/deep/ .ivu-drawer-body {
	display: flex;
	flex-direction: column;
	padding: 0;
}
/deep/ .ivu-input-group-prepend {
	border: none;
	background: #fff;
	border-bottom: 1px solid #b4c8fd;
	border-radius: 0;
}
/deep/ .ivu-input {
	border: none;
	border-bottom: 1px solid #b4c8fd;
	border-radius: 0;
}
/deep/ .ivu-drawer-right {
	width: 350px !important;
}

/deep/ .ivu-form-item {
	width: 100%;
	padding: 0 5px;
	.ivu-form-item-label {
		margin-bottom: 10px;
	}
}
/deep/ .ivu-form {
	width: 100%;
}
.advanced-search-content {
	flex: 1;
	padding: 12px;
	overflow: auto;
}
.advanced-search-footer {
	display: flex;
	flex-shrink: 0;
	align-items: center;
	justify-content: flex-end;
	height: 50px;
	padding: 0 16px;
	margin-bottom: 15px;
}
</style>