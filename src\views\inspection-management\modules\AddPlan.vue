<!--
 * @Description: 创建巡检计划和修改巡检计划的modal
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-02-22 10:57:32
 * @LastEditors: hanmengtian
 * @LastEditTime: 2022-11-21 10:04:09
-->
<template lang="pug">
Modal(
	v-if='show',
	:value='show',
	class-name='info-modal',
	width='880',
	:title='title',
	:mask='true',
	footer-hide,
	:transfer='false',
	@on-cancel='handleCancel',
	@on-visible-change='handleVisibleChange'
)
	Form(ref='formValidate', :model='formData', :label-width='120', :rules='ruleValidate')
		FormItem(label='巡检计划名称:', prop='patrolName')
			Input(
				v-model='formData.patrolName',
				:readonly='type === "check" ? true : false',
				placeholder='请输入巡检计划名称',
				style='width: 240px'
			)
		FormItem.water-margin-top-24(label='巡检人:', prop='notifyUserIdsArr')
			div(style='width: 420px')
				personnel-config-select(
					ref='configSelect',
					placeholder='请选择巡检人',
					:disabled='type === "check"',
					:selected-value='formData.notifyUserIdsArr',
					:selected-label='formData.notifyUserNamesArr'
				)
		water-row.water-margin-top-24(justify='flex-start', align='center')
			FormItem(label='自动创建频率:', prop='frequency')
				Select(
					v-model='formData.frequency',
					placeholder='请选择',
					style='width: 100px',
					:transfer='true',
					:disabled='type === "check" ? true : false',
					@on-change='changeFrequency'
				)
					Option(v-for='(item, index) in list1', :key='index', :value='item.value') {{ item.label }}
			FormItem(:label='formData.frequency === "月" ? "每月日期:" : "每周日期"', prop='day')
				Select(
					v-model='formData.day',
					placeholder='请选择',
					style='width: 120px',
					:transfer='true',
					:disabled='type === "check" ? true : false'
				)
					Option(v-for='(item, index) in list2', :key='index', :value='item.value') {{ item.label }}
		FormItem.special-item.water-margin-top-24(label='巡检站点:', prop='models')
			div
				water-row(justify='flex-start', align='center')
					div(style='width: 108px') {{ sysCode === 'dc' ? '所属片区' : '泵房选择' }}
					div(style='width: 108px') {{ sysCode === 'dc' ? '供水站' : '' }}
					div(style='width: 208px') 计划巡检时段
					div(style='width: 210px') 巡检内容描述
				water-row.water-margin-top-4(
					justify='flex-start',
					align='center',
					v-for='(item, index) in formData.models',
					:key='index'
				)
					FormItem(
						:prop='`models.${index}.areaId`',
						:rules='{ required: true, type: "string", message: "", trigger: "change" }'
					)
						Select(
							v-if='sysCode === "dc"',
							v-model='item.areaId',
							placeholder='请选择',
							:label-in-value='true',
							style='width: 100px',
							:disabled='type === "check" ? true : false',
							@on-change='value => { changeArea(value, index); }'
						)
							Option(v-for='(item, key) in groupList', :key='key', :value='item.value') {{ item.label }}
					//- 二供片区和泵房
					FormItem(
						:prop='`models.${index}.areaIdAndStationId`',
						:rules='{ required: true, type: "array", message: "", trigger: "change" }'
					)
						Cascader(
							v-if='sysCode === "eg"',
							v-model='item.areaIdAndStationId',
							:data='cascaderData',
							:disabled='type === "check" ? true : false',
							style='width: 200px',
							@on-change='(value, selectedData) => { changeEgAreaId(value, selectedData, index, item); }'
						)
					FormItem(
						:prop='`models.${index}.stationCode`',
						v-if='sysCode === "dc"',
						:rules='{ required: true, message: "", trigger: "change" }'
					)
						Select.water-margin-left-8(
							v-model='item.stationCode',
							:label-in-value='true',
							:transfer='true',
							:disabled='type === "check" ? true : false',
							placeholder='请选择',
							style='width: 100px',
							@on-change='value => { changeStation(value, index, item.stationList); }'
						)
							Option(v-for='(s, j) in item.stationList', :key='j', :value='s.value') {{ s.label }}
					FormItem(
						:prop='`models.${index}.dateRange`',
						:rules='{ required: true, type: "array", message: "", fields: { 0: { type: "date", required: true, message: "" }, 1: { type: "date", required: true, message: "" } } }'
					)
						DatePicker.water-margin-left-8(
							v-model='item.dateRange',
							format='yyyy/MM/dd',
							type='daterange',
							:options='options',
							placement='bottom-end',
							placeholder='请选择时间段',
							style='width: 200px',
							:disabled='type === "check" ? true : false'
						)
					FormItem(:prop='`models.${index}.desc`', :rules='{ required: true, message: "", trigger: "blur" }')
						Input.water-margin-left-8(
							type='text',
							v-model='item.desc',
							maxlength='300',
							:readonly='type === "check" ? true : false',
							placeholder='请输入描述内容',
							style='width: 240px'
						)

					Button.water-margin-left-8.btn(
						style='width: 20px; height: 20px; line-height: 20px',
						v-if='type !== "check" && formData.models && formData.models.length > 1',
						type='primary',
						shape='circle',
						icon='md-remove',
						@click='handleDelete(index, "models")'
					)

		FormItem.water-margin-top-24(v-if='type !== "check"')
			Button(type='dashed', @click='handleAddItem', icon='md-add') 添加巡检点
		FormItem.water-margin-top-24(label='提前通知时间:', prop='time')
			water-row(justify='flex-start', align='center')
				Input(
					v-model='formData.time',
					maxlength='5',
					placeholder='请输入时间',
					style='width: 240px',
					:readonly='type === "check" ? true : false'
				) 
				.water-margin-left-8 小时
		//- 查看详情不能更改
		FormItem.water-margin-top-24(v-if='type !== "check"')
			Button(type='primary', :loading='loading', @click='handleSave("formValidate")') 提交
			Button.water-margin-left-16(@click='handleCancel()') 取消
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import {
	getStationList,
	getTownShipList,
	updateSupplyPlan,
	addSupplyPlan,
	getSupplyPlanDetail,
	getVillagePlanDetail,
	// getAreaList,
	// getPumpList,
	updateVillagePlan,
	addVillagePlan,
	getParentAreaId,
	getAreaAndStationMap,
} from '@/api/inspection-management.js'
import PersonnelConfigSelect from '@/views/alarm-config/components/PersonnelConfigSelect.vue'
import { weekArr, monthArr } from '@/utils/enums.js'
import { EventBus } from '@/utils/eventBus.js'
export default {
	name: 'add-plan',
	components: {
		WaterRow,
		PersonnelConfigSelect,
	},
	computed: {
		title() {
			return `${this.typeEnums[this.type]}巡检计划`
		},
		sysCode() {
			return this.$route.query.sysCode || 'dc'
		},
	},

	props: {
		show: {
			type: Boolean,
			default: false,
		},
		type: {
			type: String,
			default: 'add', // add 是新增  modify是修改 check 是查看
		},
	},
	data() {
		const validateMultiple = (rule, value, callback) => {
			if (value && value.length) {
				callback()
			} else {
				callback(new Error('请选择'))
			}
		}
		const validateDateRange = (rule, value, callback) => {
			if (value && value[0] && value[1]) {
				callback()
			} else {
				callback(new Error('请选择'))
			}
		}
		return {
			loading: false,
			typeEnums: {
				add: '创建',
				modify: '修改',
				check: '查看',
			},
			formData: {
				patrolName: '',
				notifyUserIdsArr: [],
				notifyUserNamesArr: [],
				time: '',
				models: [
					{
						areaId: this.sysCode === 'dc' ? '' : [],
						areaIdAndStationId: [],
						stationCode: '',
						dateRange: ['', ''],
						desc: '',
						stationList: [],
					},
				],
				frequency: '月',
				day: '',
			},
			ruleValidate: {
				patrolName: [
					{
						required: true,
						message: '请输入巡检计划名称',
						trigger: 'blur',
					},
				],
				notifyUserIdsArr: [
					{ required: true, message: '请选择巡检人' },
					{ validator: validateMultiple, trigger: 'change' },
				],
				frequency: [
					{
						required: true,
						message: '请选择创建频率',
						trigger: 'change',
					},
				],
				day: [
					{
						required: true,
						message: '请选择日期',
						trigger: 'change',
					},
				],

				time: [
					{
						required: true,
						message: '请输入时间',
						trigger: 'blur',
					},
					{
						type: 'string',
						pattern: /^\d+$|^\d+[.]?\d+$/,
						message: '请输入数字',
					},
				],
				dateRange: [
					{ required: true, message: '请选择' },
					{ validator: validateDateRange, trigger: 'change' },
				],
			},
			list1: [
				{ label: '月', value: '月' },
				{ label: '周', value: '周' },
			],
			list2: monthArr,
			groupList: [],
			cascaderData: [],
			stationList: [],
			Id: null,
			options: {
				disabledDate(date) {
					const year = new Date().getFullYear()
					const month = new Date().getMonth()
					const firstDay = `${year}-${month + 1}-01`
					const endDate = new Date(year, month + 1, 0).getDate()
					return (
						date.getTime() < new Date(new Date(firstDay)).getTime() ||
						date.getTime() > new Date(year, month, endDate).getTime()
					)
				},
			},
			loginName: '110',
		}
	},
	created() {
		this.getTownShipList()
	},
	mounted() {
		this.getMessages()
	},
	methods: {
		init(row) {
			const { Id } = row
			new Promise(resolve => {
				if (this.sysCode === 'eg') {
					getSupplyPlanDetail({ Id }).then(res => {
						resolve(res.result)
					})
				} else {
					getVillagePlanDetail({ Id }).then(res => {
						resolve(res.result)
					})
				}
			}).then(result => {
				const {
					Id,
					Name,
					ExecutorID = '',
					Executor = '',
					PeriodUnit,
					PlanPeriod = 0,
					AdvanceNoticeDate = 0,
					Devices = [],
				} = result
				this.Id = Id
				const models = []
				Devices.forEach(async (item, index) => {
					let egAreaId = []
					if (this.sysCode === 'eg') {
						egAreaId = await this.getParentAreaId(item.AreaID, index) // 二供片区id
					}
					models.push({
						...item,
						dateRange: [item.StartTime, item.EndTime],
						areaIdAndStationId: [...egAreaId, item.StationID + ''], // 二供泵房和片区id
						areaId: this.sysCode === 'dc' ? item.AreaID : egAreaId,
						stationCode: item.StationID,
						desc: item.Describe,
						areaName: item.Area,
						stationName: item.WaterStop,
						stationList: this.getStationList(item.AreaID, index),
						longitude: item.X,
						latitude: item.Y,
						shape: item.Shape,
					})
				})
				this.formData = {
					patrolName: Name,
					notifyUserNamesArr: Executor.split(',') || [],
					notifyUserIdsArr: ExecutorID.split(',') || [],
					day: PlanPeriod + '',
					frequency: PeriodUnit,
					time: AdvanceNoticeDate + '',
					models,
				}
				this.list2 = PeriodUnit === '月' ? monthArr : weekArr
			})
		},
		getMessages() {
			window.addEventListener('message', messageEvent => {
				const data = messageEvent.data || ''
				const params = data.params
				this.loginName = params && params.loginName ? params.loginName : '110'
			})
		},
		handleVisibleChange(value) {
			if (!value) {
				this.loading = false
				//this.resetForm()
			}
		},
		handleCancel() {
			this.loading = false
			this.resetForm()
			this.$emit('update:show', false)
		},
		//重置参数
		resetForm() {
			// this.$refs.formValidate.resetFields()
			// this.formData.models = [
			// 	{
			// 		areaId: '',
			// 		stationCode: '',
			// 		dateRange: ['', ''],
			// 		desc: '',
			// 		stationList: [],
			// 	},
			// ]
			// this.formData.notifyUserIdsArr = []
			// this.formData.notifyUserNamesArr = []
			this.formData = {
				patrolName: '',
				notifyUserIdsArr: [],
				notifyUserNamesArr: [],
				time: '',
				models: [
					{
						areaId: this.sysCode === 'dc' ? '' : [],
						areaIdAndStationId: [],
						stationCode: '',
						dateRange: ['', ''],
						desc: '',
						stationList: [],
					},
				],
				frequency: '月',
				day: '',
			}
			this.$refs.configSelect.clearSelectData()
			this.list2 = monthArr
		},
		async handleSave(name) {
			const valid = await this.$refs[name].validate()
			if (valid) {
				const { patrolName, notifyUserIdsArr, notifyUserNamesArr, frequency, day, time, models } = this.formData
				let tip = 0
				models.forEach(item => {
					for (let i in item) {
						if (!item[i] && i !== 'dateRange') {
							tip++
						}
						if (i === 'dateRange' && !item[i][0]) {
							tip++
						}
					}
				})
				if (tip > 0) {
					tip = 0
					this.$Message.error('请填写巡检站点信息！')
					return false
				}
				const list = models.map(item => {
					const n = item.areaId.length
					return {
						StartTime: this.$moment(item.dateRange[0]).format('YYYY/MM/DD'),
						EndTime: `${this.$moment(item.dateRange[1]).format('YYYY/MM/DD')}`,
						Describe: item.desc,
						AreaID: this.sysCode === 'dc' ? item.areaId : item.areaId[n - 1],
						Area: item.areaName,
						StationID: item.stationCode,
						WaterStop: item.stationName,
						Shape: '',
						X: item.longitude,
						Y: item.latitude,
					}
				})
				const params = {
					PatrolName: patrolName,
					CheckingPeople: `${notifyUserNamesArr}`,
					CheckingPeople_ID: `${notifyUserIdsArr}`,
					State: false,
					Frequency: frequency,
					Frequency_Date: day,
					CreateName: this.loginName,
					AdvanceNoticeDate: time,
					Models: list,
				}
				//保存
				this.loading = true
				try {
					this.handleSubmit(params)
				} catch {
					this.loading = false
				}
			} else {
				this.$Message.error('请正确填写')
				this.loading = false
			}
		},
		//切换频率
		changeFrequency(value) {
			this.list2 = value === '月' ? monthArr : weekArr
		},
		handleAddItem() {
			const index = this.formData.models.length
			const obj = {
				areaId: this.sysCode === 'dc' ? '' : [],
				stationCode: '',
				dateRange: ['', ''],
				desc: '',
				stationList: [],
			}
			this.formData['models'].splice(index, 0, obj)
		},
		handleDelete(index, prop) {
			this.formData[prop].splice(index, 1)
		},
		handleSubmit(params) {
			if (this.sysCode === 'dc') {
				this.handleConfirmVillage(params)
			} else if (this.sysCode === 'eg') {
				this.handleConfirmSupply(params)
			}
		},
		handleConfirmVillage(params) {
			if (this.type === 'add') {
				addVillagePlan(params).then(res => {
					if (typeof res.result === 'boolean' && res.result) {
						this.$Message.success('创建计划成功')
						this.handleCancel()
						EventBus.$emit('fresh-list')
					} else {
						this.$Message.error(`${res.result && res.result.Message ? res.result.Message : '创建失败'}`)
						this.loading = false
					}
				})
			} else {
				updateVillagePlan(this.Id, params).then(res => {
					if (typeof res.result === 'boolean' && res.result) {
						this.$Message.success('修改成功')
						this.handleCancel()
						EventBus.$emit('fresh-list')
					} else {
						this.$Message.error(`${res.result && res.result.Message ? res.result.Message : '修改失败'}`)
						this.loading = false
					}
				})
			}
		},
		handleConfirmSupply(params) {
			if (this.type === 'add') {
				addSupplyPlan(params).then(res => {
					if (typeof res.result === 'boolean' && res.result) {
						this.$Message.success('创建计划成功')
						this.handleCancel()
						EventBus.$emit('fresh-list')
					} else {
						this.$Message.error(`${res.result && res.result.Message ? res.result.Message : '创建失败'}`)
						this.loading = false
					}
				})
			} else {
				updateSupplyPlan(this.Id, params).then(res => {
					if (typeof res.result === 'boolean' && res.result) {
						this.$Message.success('修改成功')
						this.handleCancel()
						EventBus.$emit('fresh-list')
					} else {
						this.$Message.error(`${res.result && res.result.Message ? res.result.Message : '操作失败'}`)
						this.loading = false
					}
				})
			}
		},
		//获取所属乡镇
		getTownShipList() {
			if (this.sysCode === 'dc') {
				getTownShipList().then(res => {
					const { result = [] } = res
					const list = []
					if (result.length > 0) {
						result.forEach(item => {
							list.push({
								label: item.name,
								value: item.id + '',
							})
						})
					}
					this.groupList = list
				})
			} else {
				// getAreaList().then(res => {
				// 	const { result = [] } = res
				// 	const list = []
				// 	if (result.length > 0) {
				// 		result.forEach(item => {
				// 			const obj = {
				// 				label: item.name,
				// 				value: item.id + '',
				// 			}
				// 			if (item.list && item.list.length > 0) {
				// 				const children = item.list.map(item => {
				// 					return {
				// 						label: item.name,
				// 						value: item.id + '',
				// 					}
				// 				})
				// 				obj.children = children
				// 			}
				// 			list.push(obj)
				// 		})
				// 	}
				// 	this.cascaderData = list
				// })
				// 筛选出有泵房的区域
				getAreaAndStationMap().then(res => {
					const { result = [] } = res
					const list = []
					if (result.length > 0) {
						result.forEach(resultItem => {
							const obj = {}
							// 有二级片区
							if (resultItem.list && resultItem.list.length) {
								const listArr = []
								resultItem.list.map(subItem => {
									// 二级片区有泵房
									if (subItem.stationList && subItem.stationList.length) {
										const children = subItem.stationList.map(pumpItem => {
											return {
												label: pumpItem.stationName,
												value: pumpItem.id + '',
												longitude: pumpItem.longitude,
												latitude: pumpItem.latitude,
											}
										})
										obj.label = resultItem.name
										obj.value = resultItem.id + ''
										listArr.push({
											label: subItem.name,
											value: subItem.id + '',
											children,
										})
									}
								})
								obj.children = listArr
							} else if (resultItem.stationList && resultItem.stationList.length) {
								// 一级片区有泵房
								const children = resultItem.stationList.map(pumpItem => {
									return {
										label: pumpItem.stationName,
										value: pumpItem.id + '',
										longitude: pumpItem.longitude,
										latitude: pumpItem.latitude,
									}
								})
								obj.label = resultItem.name
								obj.value = resultItem.id + ''
								obj.children = children
							}
							obj.label && list.push(obj)
						})
					}
					this.cascaderData = list
				})
			}
		},
		// 二供所选片区父id
		getParentAreaId(areaId) {
			let ids = []
			return new Promise((resolve, reject) => {
				getParentAreaId({ areaId })
					.then(res => {
						const { result = '' } = res
						const { parentId = '' } = result
						if (Object.keys(result).length) {
							ids = [parentId + '', areaId + '']
						} else {
							ids = [areaId + '']
						}
						resolve(ids)
					})
					.catch(error => {
						reject(error)
					})
			})
		},
		//站点列表
		getStationList(areaId, index) {
			const params = areaId ? { areaId } : {}
			const stationList = []
			if (this.sysCode === 'dc') {
				getStationList(params).then(res => {
					const { result = [] } = res

					result.forEach(item => {
						const coordinate = item.coordinate ? item.coordinate.split(',') : []
						const obj = {
							label: item.stationName,
							value: item.id + '',
							latitude: coordinate && coordinate.length > 0 ? coordinate[1] : '',
							longitude: coordinate && coordinate.length > 0 ? coordinate[0] : '',
						}

						stationList.push(obj)
					})
					this.formData['models'][index].stationList = stationList
				})
			}
			return stationList
		},
		changeArea(value, index) {
			if (value) {
				this.handleInputLabel(value.label, index, 'areaName')
				this.getStationList(value.value, index)
			}
		},
		changeStation(value, index, stationList) {
			if (value) {
				this.handleInputLabel(value.label, index, 'stationName')
				const item = stationList.find(station => station.value === value.value)
				this.handleInputLabel(item.longitude, index, 'longitude')
				this.handleInputLabel(item.latitude, index, 'latitude')
			}
		},
		changeEgAreaId(value, selectedData, index) {
			if (value.length) {
				const n = value.length
				this.handleInputLabel(selectedData[n - 2].label, index, 'areaName')
				this.handleInputLabel([selectedData[n - 2].value], index, 'areaId')
				// 泵房label
				this.handleInputLabel(selectedData[n - 1].label, index, 'stationName')
				// 泵房value
				this.handleInputLabel(selectedData[n - 1].value, index, 'stationCode')
				// 泵房经度
				this.handleInputLabel(selectedData[n - 1].longitude, index, 'longitude')
				// 泵房维度
				this.handleInputLabel(selectedData[n - 1].latitude, index, 'latitude')
			} else {
				this.handleInputLabel('', index, 'areaName')
				this.handleInputLabel([], index, 'areaId')
				// 泵房label
				this.handleInputLabel('', index, 'stationName')
				// 泵房value
				this.handleInputLabel('', index, 'stationCode')
				// 泵房经度
				this.handleInputLabel('', index, 'longitude')
				// 泵房维度
				this.handleInputLabel('', index, 'latitude')
			}
			console.log('models', this.formData['models'][index])
		},
		handleInputLabel(value, index, key) {
			const item = this.formData['models'][index]

			this.formData['models'].splice(index, 1, {
				...item,
				[key]: value,
			})
		},
	},
}
</script>
<style lang="less" scoped>
.special-item {
	::v-deep {
		.ivu-form-item {
			z-index: initial;
		}
		.ivu-form-item-label:before {
			content: '*';
			display: inline-block;
			margin-right: 4px;
			line-height: 1;
			font-family: SimSun;
			font-size: 14px;
			color: #ed4014;
		}
		.ivu-btn > .ivu-icon {
			line-height: 1;
		}
	}
}
::v-deep {
	.ivu-modal {
		.ivu-modal-content {
			.ivu-modal-header {
				padding: 14px 16px !important;
			}
		}
	}
	.personnel-config-select .select-dropdown {
		top: 390px !important;
	}
	.select-selection-disabled,
	.ivu-select-disabled .ivu-select-selection,
	.ivu-input[disabled] {
		background-color: #fff !important;
		color: #535568;
	}
}
</style>
