<template lang="pug">
.text-config
	.text-config-title 值属性
	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.text x
		InputNumber(v-if='control', v-model='control.left', @on-change='handleChange($event, "left")', style='width: 200px')
		.water-margin-right-4.text(style='margin-left: 10px') y
		InputNumber(v-if='control', v-model='control.top', @on-change='handleChange($event, "top")', style='width: 200px')
	//- water-row.water-margin-top-16(justify="flex-start", align="center")
	//- 	.water-margin-right-4.text 缩放比例
	//- water-row.water-margin-top-16(justify="space-between", align="center")
	//- 	.water-margin-right-4.text 水平缩放
	//- 	InputNumber(
	//- 		v-if="control",
	//- 		:value="control.scaleX",
	//- 		@on-change="handleChange($event, 'scaleX')",
	//- 		style="width: 200px")
	//- 	.water-margin-right-4.text(style="margin-left: 10px") 垂直比例
	//- 	InputNumber(
	//- 		v-if="control",
	//- 		:value="control.scaleY",
	//- 		@on-change="handleChange($event, 'scaleY')",
	//- 		style="width: 200px")
	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.text 旋转角度
		Slider(
			v-if='control',
			@on-input='handleChange($event, "angle")',
			v-model='control.angle',
			show-input,
			:min='0',
			:max='360',
			show-tip='never',
			style='width: 100%; margin-left: 10px'
		)
	water-row.water-margin-top-16(justify='flex-start', align='center')
		.water-margin-right-4.text 锁定纵横比
		Checkbox(v-model='lockRate')
	water-row.water-margin-top-8(justify='space-between', align='center')
		.water-margin-right-4.text 宽度
		InputNumber(
			v-if='control',
			:value='parseFloat((control.width * control.scaleX).toFixed(2))',
			@on-change='handleChange($event, "width")',
			style='width: 200px'
		)
		.water-margin-right-4.text(style='margin-left: 10px') 高度
		InputNumber(
			v-if='control',
			:value='parseFloat((control.height * control.scaleY).toFixed(2))',
			@on-change='handleChange($event, "height")',
			style='width: 200px'
		)
	//- water-row.water-margin-top-16(justify="space-between", align="center")
	//- 	.water-margin-right-4.text y
	//- 	Slider(
	//- 		v-if="control",
	//- 		@on-input="handleChange('top')",
	//- 		:max="control.canvasHeight",
	//- 		v-model="control.top",
	//- 		show-input,
	//- 		show-tip="never",
	//- 		style="width: 200px")
	//- .text-config-title.water-margin-top-16 值绑定
	//- Row.water-margin-top-8(type="flex", justify="space-between", align="middle")
	//- 	.water-margin-right-4.text 平台选择
	//- 	Select(
	//- 		v-model="control.sysCode",
	//- 		@on-change="sysChangeHandle",
	//- 		filterable,
	//- 		transfer,
	//- 		style="width: 120px")
	//- 		Option(
	//- 			v-for="(item, sysIndex) in sysList",
	//- 			:value="item.code",
	//- 			:key="sysIndex") {{ item.name }}
	//- Row.water-margin-top-8(type="flex", justify="space-between", align="middle")
	//- 	.water-margin-right-4.text 站点选择
	//- 	Select(
	//- 		v-model="control.stationCode",
	//- 		@on-change="stationChangeHandle",
	//- 		filterable,
	//- 		transfer,
	//- 		style="width: 120px")
	//- 		Option(
	//- 			v-for="(item, stationIndex) in stationList",
	//- 			:value="item.stationCode",
	//- 			:key="stationIndex") {{ item.stationName }}
	//- Row.water-margin-top-8(type="flex", justify="space-between", align="middle")
	//- 	.water-margin-right-4.text 数据项选择
	//- 	Select(
	//- 		v-model="control.itemRealCode",
	//- 		@on-change="itemChangeHandle",
	//- 		filterable,
	//- 		transfer,
	//- 		style="width: 120px")
	//- 		Option(
	//- 			v-for="(item, dataIndex) in dataList",
	//- 			:value="item.itemRealCode",
	//- 			:key="dataIndex + item.itemRealCode") {{ item.itemName }}
	//- Row.water-margin-top-8(
	//- 	type="flex",
	//- 	justify="space-between",
	//- 	align="middle",
	//- 	v-if="showValueSelect")
	//- 	.water-margin-right-4.text 值选择
	//- 	Select(
	//- 		v-model="control.itemValue",
	//- 		@on-change="valueChangeHandle",
	//- 		filterable,
	//- 		style="width: 120px")
	//- 		Option(
	//- 			v-for="(item, valueIndex) in valueList",
	//- 			:value="item.value",
	//- 			:key="valueIndex") {{ item.label }}
	//- Radio-group.water-margin-left-8.water-margin-top-8(
	//- 	v-model="control.colorObj[control.itemValue].vertical",
	//- 	@on-change="colorChangeHandle",
	//- 	v-if="showColorGroup && (control.itemValue || control.itemValue === 0)")
	//- 	Radio(label="red")
	//- 		Icon.icon.red(custom="iconfont icon-record")
	//- 		span 红色
	//- 	Radio(label="yellow")
	//- 		Icon.icon.yellow(custom="iconfont icon-record")
	//- 		span 黄色
	//- 	Radio(label="green")
	//- 		Icon.icon.green(custom="iconfont icon-record")
	//- 		span 绿色
	.text-config-title.water-margin-top-16 镜像
	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.text 水平镜像
		Checkbox(v-model='control.flipX', @on-change='handleChange($event, "flipX")')
	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.text 垂直镜像
		Checkbox(v-model='control.flipY', @on-change='handleChange($event, "flipY")')
	//- .text-config-title.water-margin-top-16 描边
	//- water-row.water-margin-top-16(justify="space-between", align="center")
	//- 	.water-margin-right-4.text 宽度
	//- 	Slider(
	//- 		v-if="control",
	//- 		@on-input="handleChange($event, 'strokeWidth')",
	//- 		v-model="control.strokeWidth",
	//- 		show-input,
	//- 		:min="0",
	//- 		:max="10",
	//- 		show-tip="never",
	//- 		style="width: 200px")
	//- water-row.water-margin-top-16(justify="flex-start", align="center")
	//- 	.water-margin-right-4.text 颜色
	//- 	ColorPicker(
	//- 		v-if="control",
	//- 		alpha,
	//- 		v-model="control.stroke",
	//- 		@on-pick-clear="handleChange($event, 'stroke')",
	//- 		@on-pick-success="handleChange($event, 'stroke')")
	div(v-if='control && ["VIDEO", "VIDEO_PANEL"].includes(control.type)')
		water-row.water-margin-top-16(justify='space-between', align='center')
			.text-config-title 视频
			Button(type='primary', size='small', @click='handleChoseVedio') 选择视频
		div
			water-row.form-item(justify='space-between', align='center')
				.label 安防平台：
				Input(v-model='control.video.platformName', disabled, placeholder='请选择视频')
			water-row.form-item(justify='space-between', align='center')
				.label 设备名称：
				Input(v-model='control.video.name', disabled, placeholder='请选择视频')
			water-row.form-item(justify='space-between', align='center')
				.label 设备编码：
				Input(v-model='control.video.code', disabled, placeholder='请选择视频')
			water-row.form-item(justify='space-between', align='center')
				.label 原始名称：
				Input(v-model='control.video.sourceName', disabled, placeholder='请选择视频')
	//- 选择视频弹窗
	video-modal(:show.sync='showVedioModal', :video-code='code', @ok='handleOk')
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import VideoModal from '../../components/VideoModal.vue'
import { stationList } from '@/api/editor'
import { querySysList } from '@/api/common.js'
export default {
	name: '',
	components: { WaterRow, VideoModal },
	props: {
		value: {
			type: Object,
			default: function () {
				return {}
			},
		},
		index: {
			type: Number,
			default: 0,
		},
	},
	computed: {
		// 当前控件元素
		control() {
			return this.value.controls[this.index]
		},
		code() {
			return this.control.video ? this.control.video.code : ''
		},
	},
	created() {
		this.initPage()
	},
	data() {
		return {
			single: true,
			backgroundColor: '#19be6b',
			// value: 60,
			showType: '',
			itemValue: '',
			sysList: [],
			stationList: [],
			dataList: [],
			valueList: [
				{ value: 0, label: 0 },
				{ value: 1, label: 1 },
			],
			isFirst: true,
			showColorGroup: true,
			showValueSelect: true,

			// 锁定纵横比
			lockRate: true,

			// 视频
			showVedioModal: false,
		}
	},
	methods: {
		// 页面初始化
		async initPage() {
			await this.querySysList()
			const { sysCode, stationCode, itemRealCode } = this.value.controls[this.index]
			if (sysCode) {
				await this.sysChangeHandle(sysCode)
			}
			if (stationCode) {
				this.stationChangeHandle(stationCode)
			}
			if (itemRealCode) {
				this.itemChangeHandle(itemRealCode, true)
			}
		},
		// API 查询平台
		querySysList() {
			return querySysList().then(res => {
				const { result = [] } = res

				this.sysList = result
			})
		},
		// select 框输入事件
		sysChangeHandle(val) {
			return this.getStationList({ sysCode: val })
		},
		stationChangeHandle(val) {
			this.showColorGroup = false
			for (let index = 0; index < this.stationList.length; index++) {
				const element = this.stationList[index]
				if (element.stationCode === val) {
					this.dataList = element.stationDataItem
					break
				}
			}
		},
		/**
		 * 数据项切换方法
		 * @param {*} val 数据项传输的值
		 * @param {*} flag 数据项是否改变触发  true：赋值  false:改变触发
		 */
		itemChangeHandle(val, flag) {
			this.showColorGroup = false
			this.showValueSelect = false
			setTimeout(() => {
				for (let index = 0; index < this.dataList.length; index++) {
					const element = this.dataList[index]
					if (val === element.itemRealCode) {
						let arr = []
						// this.valueList = []
						this.control.formula = []
						let colorObj = {}
						if (element.formula) {
							this.showValueSelect = true
							this.showColorGroup = true
							for (const key in element.formula) {
								if (Object.hasOwnProperty.call(element.formula, key)) {
									if (this.$common.isEmptyObject(this.control.colorObj) || !flag) {
										colorObj[key] = {
											red: 1,
											green: 1,
											blue: 1,
											vertical: 'red',
										}
									}
									arr.push({
										label: element.formula[key],
										value: key,
									})
									// this.valueList.push({
									// 	label: element.formula[key],
									// 	value: key,
									// })
									this.value.controls[this.index].formula.push(key)
								}
							}
							if (this.$common.isEmptyObject(this.control.colorObj) || !flag) {
								// debugger
								this.$set(this.control, 'colorObj', colorObj)
							}
							if (!flag) {
								if (arr.length) {
									this.$set(this.control, 'itemValue', arr[0].value)
									this.dealValue()
								}
							}
							this.$set(this, 'valueList', arr)
						} else {
							this.$set(this, 'valueList', [])
							this.$set(this.control, 'colorObj', {})
							this.$set(this.control, 'itemValue', null)
						}
						break
					}
				}
			}, 100)
		},
		getStationList(params) {
			return stationList({ showItemCode: true, ...params }).then(res => {
				const { result = [] } = res
				this.stationList = result
			})
		},
		valueChangeHandle() {
			this.dealValue()
		},
		colorChangeHandle() {
			this.dealValue()
		},
		dealValue() {
			let current = this.control
			let key = this.control.itemValue
			const vertical = current.colorObj[key].vertical

			if (vertical == 'red') {
				current.colorObj[key].red = 60
				current.colorObj[key].green = 1
				current.colorObj[key].blue = 1
			}
			if (vertical == 'yellow') {
				current.colorObj[key].red = 60
				current.colorObj[key].green = 60
				current.colorObj[key].blue = 1
			}
			if (vertical == 'green') {
				current.colorObj[key].red = 1
				current.colorObj[key].green = 60
				current.colorObj[key].blue = 1
			}
			if (this.isFirst) {
				// 构造image filter
				this.$emit('changeImageColor', true, [
					this.getVal(current.colorObj[key].red),
					this.getVal(current.colorObj[key].green),
					this.getVal(current.colorObj[key].blue),
				])
				this.isFirst = false
			} else {
				this.$emit('changeControl', 'red', [
					this.getVal(current.colorObj[key].red),
					this.getVal(current.colorObj[key].green),
					this.getVal(current.colorObj[key].blue),
				])
			}
		},
		// 属性选择
		handleChange(value, type) {
			const wRate = value / this.control.width
			const hRate = value / this.control.height
			switch (type) {
				case 'red':
				case 'green':
				case 'blue':
					this.$emit('changeControl', type, [
						this.getVal(this.control.colorObj[this.control.itemValue].red),
						this.getVal(this.control.colorObj[this.control.itemValue].green),
						this.getVal(this.control.colorObj[this.control.itemValue].blue),
					])
					break
				case 'width':
					if (this.lockRate) {
						this.$emit('changeControl', 'scaleY', wRate, {
							type: 'controls',
							index: this.index,
							key: 'scaleY',
							value: wRate,
						})
					}
					this.$emit('changeControl', 'scaleX', value / this.control[type], {
						type: 'controls',
						index: this.index,
						key: 'scaleX',
						value: value / this.control[type],
					})
					break
				case 'height':
					if (this.lockRate) {
						this.$emit('changeControl', 'scaleX', hRate, {
							type: 'controls',
							index: this.index,
							key: 'scaleX',
							value: hRate,
						})
					}
					this.$emit(
						'changeControl',
						'scaleY',
						value / this.control[type],

						{
							type: 'controls',
							index: this.index,
							key: 'scaleY',
							value: value / this.control[type],
						},
					)
					break
				default:
					this.$emit('changeControl', type, this.control[type])
					break
			}
		},
		getVal(val) {
			if (val === 0 || val) {
				return val
			}
			return 1
		},

		// 选择视频
		handleChoseVedio() {
			this.showVedioModal = true
		},
		// 弹窗确定事件
		handleOk(data) {
			const { url = '', platformId = '', platformName = '', name = '', code = '', sourceName = '' } = data
			this.$emit('changeControl', 'video', {
				url,
				platformId,
				platformName,
				name,
				code,
				sourceName,
			})
		},
	},
	watch: {
		index() {
			this.isFirst = true
		},
	},
}
</script>
<style lang="less" scoped>
.text-config {
	padding: 8px;
	&-title {
		color: #000;
		font-weight: bold;
	}
}
.red {
	color: #f93b3b;
}
.yellow {
	color: #f9f93b;
}
.green {
	color: #3bf93b;
}
.text {
	flex-shrink: 0;
}
::v-deep {
	.ivu-input-number {
		width: 56px;
		margin-top: 0;
	}
}

.form-item {
	margin-top: 8px;
	.label {
		flex-shrink: 0;
	}
}
</style>
