<template lang="pug">
.no-data(:style='{ height, background }')
	img(src='https://eslink-iot.oss-cn-beijing.aliyuncs.com/WDS-nodata.png')
	span {{ title }}
</template>
<script>
export default {
	props: {
		height: {
			type: String,
			default: '100%',
		},
		background: {
			type: String,
			default: '#161515f7',
		},
		title: {
			type: String,
			default: '暂无数据',
		},
	},
}
</script>
<style lang="less" scoped>
.no-data {
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	color: #fff;
	// background: #161515f7;
	img {
		height: 35px;
		margin-bottom: 8px;
	}
}
</style>
