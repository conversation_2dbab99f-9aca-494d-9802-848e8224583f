<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-09-15 11:30:55
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-09-15 11:33:22
-->
<template lang="pug">
.config-header
	.actions-box.actions-left-box
		Icon.icon(custom='iconfont icon-zhexiantu', @click='drawShape("line")', size='24', title='绘线')
		Icon.icon(custom='iconfont icon-fangxingweixuanzhong', @click='drawShape("rect")', size='24', title='方形')
		Icon.icon(custom='iconfont icon-round1', @click='drawShape("circle")', size='32', title='圆形')
		Icon.icon(custom='iconfont icon-text', @click='drawShape("text")', size='32', title='文本')
		Icon.icon(custom='iconfont icon-jichu_lianjie', @click='drawShape("link")', size='32', title='链接区域')
	.title-box
		Input.text-input(v-model='title', @on-blur='handleEnter', @on-enter='handleEnter')
	.actions-box.actions-right-box
		span.box--item(@click='btnClick("back")')
			Icon(type='ios-arrow-round-back', size='30')
			span 返回列表
		span.box--item(@click='btnClick("save")')
			Icon(custom='iconfont icon-save1', size='30') 
			span 保存
		span.box--item(@click='btnClick("priview")')
			Icon(type='ios-aperture-outline', size='30') 
			span 预览
		span.box--item(@click='btnClick("pub")')
			Icon(type='ios-paper-plane-outline', size='30') 
			span 发布
</template>
<script>
export default {
	name: '',
	data() {
		return {
			title: '未命名工艺图',
			isReadonly: true,
			drawLineFlag: false,
		}
	},
	watch: {},
	mounted() {},
	methods: {
		// doubleClick() {
		// 	this.isReadonly = false
		// },
		drawShape(type) {
			this.$emit('drawShape', type)
		},
		btnClick(from) {
			this.$emit('handleClick', from)
		},
		handleEnter() {
			this.isReadonly = true
			this.$emit('rename', this.title)
		},
	},
}
</script>
<style lang="less" scoped>
.config-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	height: 48px;
	border-bottom: 1px solid #e8e8e8;
	.actions-box {
		display: flex;
		justify-content: space-around;
		align-items: center;
		width: 255px;
		i {
			display: inline-block;
			// margin-right: 16px;
			cursor: pointer;
			font-size: 26px !important;
		}
		.icon-text,
		.icon-round1 {
			font-size: 28px !important;
		}
		.box--item {
			display: flex;
			align-items: center;
			cursor: pointer;
		}
	}
	.actions-right-box {
		width: 320px;
	}
	.title-box {
		height: 100%;
		line-height: 48px;
		text-align: center;
		cursor: pointer;
	}
}

::v-deep {
	.text-input .ivu-input {
		text-align: center;
		font-size: 18px;
	}
}
</style>
