// 固定控件类型对应控件属性Map
export const controlTypes2AttrMap = {
	custom: {},
	PUMP: {
		name: '水泵',
		url: require('@/assets/images/controls/waterPump.png'),
		type: 'PUMP',
	},
	VALVE: {
		name: '阀门',
		url: require('@/assets/images/controls/valve.png'),
		type: 'VALVE',
	},
	// ELECTRIC_VALVE: {
	// 	name: '电动阀',
	// 	url: require('@/assets/images/controls/electricValve.png'),
	// 	type: 'ELECTRIC_VALVE',
	// },
	// JY_PUMP: {
	// 	name: '加药泵',
	// 	url: require('@/assets/images/controls/jyPump.png'),
	// 	type: 'JY_PUMP',
	// },
	// kyj: {
	// 	name: '空压机',
	// 	url: require('@/assets/images/controls/kyj.png'),
	// 	type: 'kyj',
	// },
	// ylj: {
	// 	name: '压力计',
	// 	url: require('@/assets/images/controls/ylj.png'),
	// 	type: 'ylj',
	// },
	// FCX_PUMP: {
	// 	name: '反冲洗泵',
	// 	url: require('@/assets/images/controls/fcxPump.png'),
	// 	type: 'FCX_PUMP',
	// },
	// METER_PUMP: {
	// 	name: '计量泵',
	// 	url: require('@/assets/images/controls/meterPump.png'),
	// 	type: 'METER_PUMP',
	// },
	// DIVE_PUMP: {
	// 	name: '潜水泵',
	// 	url: require('@/assets/images/controls/divePump.png'),
	// 	type: 'DIVE_PUMP',
	// },
	// LIFT_PUMP: {
	// 	name: '提升泵',
	// 	url: require('@/assets/images/controls/liftPump.png'),
	// 	type: 'LIFT_PUMP',
	// },
	// STATION_PUMP: {
	// 	name: '泵站泵',
	// 	url: require('@/assets/images/controls/stationPump.png'),
	// 	type: 'STATION_PUMP',
	// },
	// WATERWORKS_PUMP: {
	// 	name: '水厂泵',
	// 	url: require('@/assets/images/controls/waterworksPump.png'),
	// 	type: 'WATERWORKS_PUMP',
	// },
	// BOOSTER_PUMP: {
	// 	name: '增压泵',
	// 	url: require('@/assets/images/controls/boosterPump.png'),
	// 	type: 'BOOSTER_PUMP',
	// },
	// PUMP_ROOM: {
	// 	name: '进水泵房',
	// 	url: require('@/assets/images/controls/waterPumpRoom.png'),
	// 	type: 'PUMP_ROOM',
	// },
	// WATER_TANK: {
	// 	name: '清水池',
	// 	url: require('@/assets/images/controls/clearWater.png'),
	// 	type: 'WATER_TANK',
	// },
	// ALUM_ROOM: {
	// 	name: '加矾间',
	// 	url: require('@/assets/images/controls/ALUM_ROOM.png'),
	// 	type: 'ALUM_ROOM',
	// },
	// CHLORINE_ROOM: {
	// 	name: '加氯间',
	// 	url: require('@/assets/images/controls/CHLORINE_ROOM.png'),
	// 	type: 'CHLORINE_ROOM',
	// },
	// SEDIMENTATION_TANK: {
	// 	name: '沉淀池',
	// 	url: require('@/assets/images/controls/SEDIMENTATION_TANK.png'),
	// 	type: 'SEDIMENTATION_TANK',
	// },
	// WATER_INTAKE: {
	// 	name: '取水池',
	// 	url: require('@/assets/images/controls/WATER_INTAKE.png'),
	// 	type: 'WATER_INTAKE',
	// },
	// FILTER_TANK: {
	// 	name: '过滤池',
	// 	url: require('@/assets/images/controls/FILTER_TANK.png'),
	// 	type: 'FILTER_TANK',
	// },
	// REACTION_TANK: {
	// 	name: '反应池',
	// 	url: require('@/assets/images/controls/REACTION_TANK.png'),
	// 	type: 'REACTION_TANK',
	// },
	// BLOWER: {
	// 	name: '鼓风机',
	// 	url: require('@/assets/images/controls/BLOWER.png'),
	// 	type: 'BLOWER',
	// },
	// SUCTION_WELL: {
	// 	name: '吸水井',
	// 	url: require('@/assets/images/controls/SUCTION_WELL.png'),
	// 	type: 'SUCTION_WELL',
	// },

	// FAN: {
	// 	name: '风机',
	// 	url: require('@/assets/images/controls/fan.png'),
	// 	type: 'FAN',
	// },
	VIDEO: {
		name: '视频',
		url: require('@/assets/images/controls/VIDEO.svg'),
		type: 'VIDEO',
	},
	VIDEO_PANEL: {
		name: '视频面板',
		url: require('@/assets/images/controls/VIDEO_PANEL.svg'),
		type: 'VIDEO_PANEL',
	},
}
export const controlTypes2AttrtempMap = {
	custom: {},
	PUMP: {
		name: '水泵',
		url: require('@/assets/images/controls/waterPump.png'),
		type: 'PUMP',
	},
	VALVE: {
		name: '阀门',
		url: require('@/assets/images/controls/valve.png'),
		type: 'VALVE',
	},
	ELECTRIC_VALVE: {
		name: '电动阀',
		url: require('@/assets/images/controls/electricValve.png'),
		type: 'ELECTRIC_VALVE',
	},
	JY_PUMP: {
		name: '加药泵',
		url: require('@/assets/images/controls/jyPump.png'),
		type: 'JY_PUMP',
	},
	kyj: {
		name: '空压机',
		url: require('@/assets/images/controls/kyj.png'),
		type: 'kyj',
	},
	ylj: {
		name: '压力计',
		url: require('@/assets/images/controls/ylj.png'),
		type: 'ylj',
	},
	FCX_PUMP: {
		name: '反冲洗泵',
		url: require('@/assets/images/controls/fcxPump.png'),
		type: 'FCX_PUMP',
	},
	METER_PUMP: {
		name: '计量泵',
		url: require('@/assets/images/controls/meterPump.png'),
		type: 'METER_PUMP',
	},
	DIVE_PUMP: {
		name: '潜水泵',
		url: require('@/assets/images/controls/divePump.png'),
		type: 'DIVE_PUMP',
	},
	LIFT_PUMP: {
		name: '提升泵',
		url: require('@/assets/images/controls/liftPump.png'),
		type: 'LIFT_PUMP',
	},
	STATION_PUMP: {
		name: '泵站泵',
		url: require('@/assets/images/controls/stationPump.png'),
		type: 'STATION_PUMP',
	},
	WATERWORKS_PUMP: {
		name: '水厂泵',
		url: require('@/assets/images/controls/waterworksPump.png'),
		type: 'WATERWORKS_PUMP',
	},
	BOOSTER_PUMP: {
		name: '增压泵',
		url: require('@/assets/images/controls/boosterPump.png'),
		type: 'BOOSTER_PUMP',
	},
	PUMP_ROOM: {
		name: '进水泵房',
		url: require('@/assets/images/controls/waterPumpRoom.png'),
		type: 'PUMP_ROOM',
	},
	WATER_TANK: {
		name: '清水池',
		url: require('@/assets/images/controls/clearWater.png'),
		type: 'WATER_TANK',
	},
	ALUM_ROOM: {
		name: '加矾间',
		url: require('@/assets/images/controls/ALUM_ROOM.png'),
		type: 'ALUM_ROOM',
	},
	CHLORINE_ROOM: {
		name: '加氯间',
		url: require('@/assets/images/controls/CHLORINE_ROOM.png'),
		type: 'CHLORINE_ROOM',
	},
	SEDIMENTATION_TANK: {
		name: '沉淀池',
		url: require('@/assets/images/controls/SEDIMENTATION_TANK.png'),
		type: 'SEDIMENTATION_TANK',
	},
	WATER_INTAKE: {
		name: '取水池',
		url: require('@/assets/images/controls/WATER_INTAKE.png'),
		type: 'WATER_INTAKE',
	},
	FILTER_TANK: {
		name: '过滤池',
		url: require('@/assets/images/controls/FILTER_TANK.png'),
		type: 'FILTER_TANK',
	},
	REACTION_TANK: {
		name: '反应池',
		url: require('@/assets/images/controls/REACTION_TANK.png'),
		type: 'REACTION_TANK',
	},
	BLOWER: {
		name: '鼓风机',
		url: require('@/assets/images/controls/BLOWER.png'),
		type: 'BLOWER',
	},
	SUCTION_WELL: {
		name: '吸水井',
		url: require('@/assets/images/controls/SUCTION_WELL.png'),
		type: 'SUCTION_WELL',
	},

	FAN: {
		name: '风机',
		url: require('@/assets/images/controls/fan.png'),
		type: 'FAN',
	},
	VIDEO: {
		name: '视频',
		url: require('@/assets/images/controls/VIDEO.svg'),
		type: 'VIDEO',
	},
	VIDEO_PANEL: {
		name: '视频面板',
		url: require('@/assets/images/controls/VIDEO_PANEL.svg'),
		type: 'VIDEO_PANEL',
	},
}

// 控件类型 和 控件原始状态图片文件名称 映射对象
// 该map列举了 UI制作了状态图的控件类型
// 其他未制作状态图的 默认使用滤镜切换状态
export const controlType2FileNameMap = {
	BLOWER: 'BLOWER',
	BOOSTER_PUMP: 'boosterPump',
	DIVE_PUMP: 'divePump',
	ELECTRIC_VALVE: 'electricValve',
	FCX_PUMP: 'fcxPump',
	LIFT_PUMP: 'liftPump',
	METER_PUMP: 'meterPump',
	STATION_PUMP: 'stationPump',
	VALVE: 'valve',
	WATERWORKS_PUMP: 'waterworksPump',
}
