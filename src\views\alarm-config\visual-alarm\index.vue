<template lang="pug">
.visual-alarm 
	es-search.es-search(
		col='4',
		:show-collapse='false',
		:modules='moduleList',
		@on-search='handleSearchBtn',
		@on-reset='handleResetBtn'
	)
	.table-wrap
		es-table.water-table(
			stripe,
			:columns='columns',
			:data='tableData',
			:loading='loading',
			border,
			showPage,
			:pageData='pageData',
			@on-page-num-change='handlePageNum',
			@on-page-size-change='handlePageSize'
		)
			template(slot-scope='{ row }', slot='count')
				span {{ `${row.allAlarmCount || '--'} / ${row.commonAlarmCount || '--'} / ${row.specialAlarmCount || '--'}` }}
			template(slot-scope='{ row }', slot='filter')
				i-switch(v-model='row.filterAlarm === 1', @on-change='changeFilter($event, row.id)')
			template(slot-scope='{ row }', slot='deal')
				Button(type='primary', @click='handleConfig(row)') 配置
	config-modal(v-model='showConfigModal', :code='currentCode', @save='_getTableData')
</template>

<script>
import { getVisualAlarm, setFilter } from '@/api/alarm-config'
import ConfigModal from './components/ConfigModal.vue'

export default {
	components: { ConfigModal },
	data() {
		return {
			moduleList: [
				{
					keys: ['type', 'condition', '', ''],
					model: {
						type: 'all',
						condition: '',
					},
					data: [
						{
							type: 2,
							key: 'type',
							formItemProps: {
								label: '查询方式',
								prop: 'type',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [
								{
									label: '系统代码',
									value: 'code',
								},
								{
									label: '系统名称',
									value: 'name',
								},
								{
									label: '综合查询',
									value: 'all',
								},
							],
						},
						{
							type: 1,
							key: 'condition',
							formItemProps: {
								label: '查询内容',
								prop: 'condition',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
						},
					],
				},
			],
			columns: [
				{
					type: 'index',
					width: 65,
					align: 'center',
					title: '序号',
				},
				{
					title: '系统代码',
					width: 160,
					key: 'code',
					align: 'center',
				},
				{
					title: '系统名称',
					width: 160,
					key: 'name',
					align: 'center',
				},
				{
					title: '是否开启过滤',
					width: 180,
					slot: 'filter',
					align: 'center',
				},
				{
					title: '有效规则数据量（总/通用/专用）',
					width: 260,
					slot: 'count',
					align: 'center',
				},
				{
					title: '备注',
					key: 'memo',
				},
				{
					title: '最后更新时间',
					width: 200,
					key: 'lastUpdateTime',
					align: 'center',
				},
				{
					title: '操作人',
					width: 180,
					key: 'updateUser',
					align: 'center',
				},
				{
					title: '操作',
					width: 120,
					slot: 'deal',
					align: 'center',
				},
			],
			tableData: [{}],
			loading: false,
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 30, 40, 50],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
			// 配置弹窗
			showConfigModal: false,
			currentCode: '',
		}
	},
	created() {
		this._getTableData()
	},
	methods: {
		// 获取表格数据
		_getTableData() {
			const formData = this.moduleList[0].model
			const { type, condition } = formData
			const { current: pageNum, pageSize } = this.pageData
			this.loading = true
			getVisualAlarm({
				type,
				condition,
				pageNum,
				pageSize,
			}).then(res => {
				const { list = [], total = 0 } = res.result
				this.tableData = list
				this.pageData.total = total
				this.loading = false
			})
		},
		// 是否开启过滤
		async changeFilter(filterAlarm, id) {
			try {
				await setFilter({ id, filterAlarm })
				this.$Message.success('操作成功')
				this._getTableData()
			} catch (error) {
				this.$Message.error('操作失败')
			}
		},
		// 配置
		handleConfig(data) {
			this.showConfigModal = true
			this.currentCode = data.code || ''
		},
		// 搜索按钮
		handleSearchBtn() {
			this.pageData.current = 1
			this._getTableData()
		},

		// 重置按钮
		handleResetBtn() {
			this.pageData.current = 1
			this._getTableData()
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum
			this._getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize
			this._getTableData()
		},
	},
}
</script>

<style lang="less" scoped>
.visual-alarm {
	padding: 12px 16px;
	.es-search {
		padding-top: 4px;
		/deep/ .ivu-input-wrapper {
			overflow: hidden;
		}
		/deep/ .ivu-btn {
			background-color: #57a3f3;
			border-color: #57a3f3;
			color: #fff;
			padding: 0 10px;
			margin-left: 10px;
			&:last-child {
				color: #515a6e;
				background-color: #fff;
				border-color: #dcdee2;
			}
			.ivu-icon {
				display: none;
			}
			span {
				margin: 0;
			}
		}
	}
	.table-wrap {
		padding-top: 8px;
		height: calc(100vh - 72px);
	}
}
</style>
