<!--
 * @Description: 站点选择框
 * @Author: shenxh
 * @Date: 2022-03-23 10:15:05
 * @LastEditors: shenxh
 * @LastEditTime: 2023-01-03 19:27:16
-->

<template lang="pug">
.personnel-config-select
	multifunctional-select(
		ref='multifunctionalSelect',
		:newPlaceholder='placeholder',
		:disabled='disabled',
		:selected-value='selectedValue',
		:selected-label='selectedLabel',
		:tree-data='ownershipList',
		:table-data='tableData',
		dropdown-position='bottom',
		:table-loading='tableLoading',
		:total='total',
		:tree-total='treeTotal',
		:page-size.sync='tablePage.pageSize',
		:current.sync='tablePage.pageNum',
		:tree-page-size.sync='treePage.pageSize',
		:tree-current.sync='treePage.pageNum',
		@on-current-change='currentChange',
		@on-search='searchData',
		@on-tree-search='searchTreeData',
		@on-select-change-tree='changeTree',
		@on-page-size-change='changePageSize',
		@on-tree-page-size-change='changeTreePageSize',
		@on-change-page='changePage',
		@on-tree-change-page='changeTreePage',
		@close-tag='closeTag',
		@handle-select='handleSelect'
	)
</template>

<script>
import MultifunctionalSelect from '@/components/gc-site-data/multifunctional-select.vue'
import { getStationTemp, queryStationPage } from '@/api/base-item'

export default {
	name: 'personnel-config-select',
	components: {
		MultifunctionalSelect,
	},
	props: {
		// 已选择的value数组
		selectedValue: Array,
		// 已选择的label数组 (用于标签展示, 顺序要与value对应)
		selectedLabel: Array,
		selectedType: String,
		// 禁用
		disabled: Boolean,
		placeholder: String,
	},
	data() {
		return {
			stationCode: '',
			searchVal: '',
			tableLoading: false,
			currentNode: {},
			// selectedUser: [42497, 42499],
			// selectedLabel: ['杨燕', '陈红丽'],
			ownershipList: [],
			tableData: [],
			total: 0,
			treeTotal: 0,
			treePage: {
				pageSize: 20,
				pageNum: 1,
			},
			tablePage: {
				pageSize: 20,
				pageNum: 1,
			},
			firstTree: '',
		}
	},
	computed: {},
	watch: {},
	created() {
		this.queryStations()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 关闭tag
		closeTag(selectedValue, selectedLabel) {
			this.$emit('close-tag', selectedValue, selectedLabel)
		},

		// 点击选择框
		handleSelect(show) {
			show && this.queryStations()
		},

		// 选中树形图节点
		changeTree(selectedNodes, currentNode) {
			this.tablePage.pageNum = 1
			this.currentNode = currentNode

			this.getStationTemp()
		},
		// 点击搜索或按下回车键时触发
		searchData(val) {
			this.searchVal = val

			// 123
		},
		searchTreeData(val) {
			this.stationCode = val

			this.queryStations()
		},

		// 切换每页条数时的回调，返回切换后的每页条数
		changePageSize() {
			this.getStationTemp()
		},
		changeTreePageSize() {
			this.queryStations()
		},

		// 页码改变的回调，返回改变后的页码
		changePage() {
			this.getStationTemp()
		},
		changeTreePage() {
			this.queryStations()
		},

		currentChange(val, oldVal) {
			this.$emit('on-current-change', val, oldVal)
		},

		queryStations() {
			let params = {
				stationCode: this.stationCode,
				needPage: true,
				pageSize: this.treePage.pageSize,
				pageNum: this.treePage.pageNum,
				type: this.selectedType,
			}
			queryStationPage(params).then(res => {
				this.ownershipList = res.result.list.map(item => {
					return {
						...item,
						title: item.stationCode,
					}
				})
				this.treeTotal = res.result.total
			})
		},

		getStationTemp() {
			this.tableLoading = true
			getStationTemp({
				stationCode: this.currentNode.stationCode,
				needPage: true,
				pageSize: this.tablePage.pageSize,
				pageNum: this.tablePage.pageNum,
			}).then(res => {
				const { list = [], total } = res.result

				this.tableData = list.map(item => {
					return {
						...item,
						label: item.itemName,
						value: item.itemRealCode,
					}
				})
				this.total = total
				this.tableLoading = false
			})
		},
	},
}
</script>

<style lang="less" scoped></style>
