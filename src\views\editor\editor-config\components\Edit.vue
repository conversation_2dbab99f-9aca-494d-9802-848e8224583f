<template lang="pug">
//- 内容
.content-wrapper
	Button.button(v-show='isDrawingMode && showButton', @click='endDrawPolyline', size='small') 结束绘制
	Button.button(v-show='type && showButton', @click='handleDelete', size='small', type='error') 删除
	SketchRule(
		:lang='lang',
		:thick='thick',
		:scale='scale',
		:width='1385',
		:height='889',
		:startX='startX',
		:startY='startY',
		:shadow='shadow',
		:isShowReferLine='false',
		:horLineArr='lines.h',
		:verLineArr='lines.v',
		:cornerActive='false',
		:palette='palette'
	)
	fabric#canvas(
		@dragover.prevent,
		@click.stop,
		@drop.native='dropControl($event)',
		ref='canvas',
		:width='width',
		:height='height',
		@handleDelete='handleDelete',
		@mouse:up='handleMouseUp',
		@mouse:down='handleMouseDown',
		@object:moving='handleMoving',
		@selection:created='selected',
		@selection:cleared='cleared',
		@object:modified='modified'
	)
	svg.flow-svg(
		xmlns='http://www.w3.org/2000/svg',
		version='1.1',
		:style='`width: ${width}px; height: ${height}px`',
		v-if='svgFlag'
	)
		template(v-for='(line, index) in animateLines')
			polyline.polyline(:key='index', :style='{ stroke: line.animateColor }', :points='getPoints(line)')
</template>

<script>
import { controlTypes2AttrtempMap } from '../../enum'
import fabricC from '@/components/gc-fabric'
import { itemDate } from '@/api/editor'
import { getVal } from '@/utils/function.js'
import SketchRule from 'vue-sketch-ruler'
const rectWidth = 160
const rectHeight = 200

let linePath = []
let editLineObject = []
let circleObject = []
// 绘制的线段集合
// let polyLines = []
let controls = []
let selectObj = []
export default {
	components: {
		fabric: fabricC,
		SketchRule,
	},
	props: {
		showButton: {
			type: Boolean,
			default: true,
		},
	},
	computed: {
		shadow() {
			return {
				x: 0,
				y: 0,
				width: rectWidth,
				height: rectHeight,
			}
		},
		canvasStyle() {
			return {
				width: rectWidth + 'px',
				height: rectHeight + 'px',
				transform: `scale(${this.scale})`,
			}
		},
	},
	data() {
		return {
			imgUrl: '',
			width: 1385,
			height: 889,
			// 绘制线段模式开关
			isDrawingMode: false,
			// 前一个circle实例 为了放置后面线段的实例
			prevCircleInstance: {},
			type: null,
			animateLines: [],
			svgFlag: true,
			//标尺
			scale: 1, //658813476562495, //1,
			startX: 0,
			startY: 0,
			lines: {
				h: [0],
				v: [0],
			},
			thick: 20,
			lang: 'zh-CN',
			isShowRuler: true,
			isShowReferLine: true,
			palette: {
				bgColor: 'rgba(225,225,225, 0)',
				longfgColor: '#BABBBC',
				shortfgColor: '#C8CDD0',
				fontColor: '#7D8694',
				shadowColor: '#E8E8E8',
				lineColor: '#51d6a9',
				borderColor: '#DADADC',
				cornerActiveColor: 'rgb(235, 86, 72, 0.6)',
			},
		}
	},
	mounted() {
		// this.width = document.body.offsetWidth - 535
		// this.height = document.body.offsetHeight - 48
		this.$nextTick(() => {
			setTimeout(() => {
				let left = (document.querySelector('.canvas-wrapper').clientWidth - 1385) / 2
				if (document.querySelector('.canvas-wrapper').clientWidth - 1385 < 0) {
					left = 0
				}
				var svgPanel = document.querySelector('.flow-svg')
				svgPanel.style.left = left + 'px'
			}, 200)
		})

		window.addEventListener('resize', () => {
			// this.autoScale(canvasWidth, canvasHeight)
			let left = (document.querySelector('.canvas-wrapper').clientWidth - 1385) / 2
			if (document.querySelector('.canvas-wrapper').clientWidth - 1385 < 0) {
				left = 0
			}
			var svgPanel = document.querySelector('.flow-svg')
			svgPanel.style.left = left + 'px'
		})
	},
	methods: {
		getCanvas() {
			return this.$refs.canvas.canvas
		},
		setBackImage(item, flag) {
			const { imageUrl, id, canvasScale } = item
			let img = new Image()
			img.crossOrigin = 'anonymous'
			img.src = imageUrl
			img.onload = () => {
				let width = img.naturalWidth
				let height = img.naturalHeight
				let xS = this.width / width
				let yS = this.height / height
				let scale = xS > yS ? yS : xS
				let options = {
					imgUrl: imageUrl,
					imgId: id,
					width: width,
					height: height,
					opacity: 1,
					scaleX: flag ? canvasScale : scale,
					scaleY: flag ? canvasScale : scale,
				}
				// 可跨域设置
				this.$refs.canvas.test(img, options)

				this.$refs.canvas.setCanvas(width * scale, height * scale)
				if (!flag) {
					this.$emit('showLevel', {
						type: 1,
						canvasWidth: width * scale,
						canvasHeight: height * scale,
					})
					this.type = null
				}
			}
		},
		getBackgroundImage() {
			return this.$refs.canvas.canvas.backgroundImage
		},
		setBackColor(item) {
			this.$refs.canvas.setBackgroundColor(item)
		},

		setImageGamma(val, gamma) {
			this.$refs.canvas.setImageGamma(val, gamma)
		},

		// 放置控件或者放置文本
		async dropControl(e) {
			if (this.isDrawingMode === true) {
				return
			}
			const widgetConfig = e.dataTransfer.getData('widget-config')
			const textConfig = e.dataTransfer.getData('text-config')
			let control = null

			const { width, height } = this.$refs.canvas.getCanvasSize()
			// 控件
			if (widgetConfig) {
				const { url, type, controlsDetails, interactive, imageStandards, id } = JSON.parse(widgetConfig)
				let imgUrl = url
				// if (type === 'custom') {
				// 	imgUrl = await this.drawBase64Image(url)
				// }
				control = await this.$refs.canvas.createImage(imgUrl, {
					top: e.offsetY,
					left: e.offsetX,
					canvasWidth: width,
					canvasHeight: height,
					iconUrl: url,
					type,
					controlsDetails,
					interactive,
					imageStandards,
					index: controls.length,
					name: '控件' + (controls.length + 1),
					code: controls.length + 1,
					video: {
						url: '',
						platformId: '',
						platformName: '',
						name: '',
						code: '',
						sourceName: '',
					},
					customControlId: id,
				})
				this.type = type
			}

			// 标签绑定
			if (textConfig) {
				const { itemRealCode, sysCode, stationCode: code } = JSON.parse(textConfig)
				let params = [
					{
						itemCodes: [itemRealCode],
						stationCode: code,
						sysCode,
					},
				]
				const obj = await this.itemData(params, JSON.parse(textConfig))

				const { itemName, value, unit, stationCode } = obj
				// 默认只展示数值
				const text = `${getVal(value)}`
				control = this.$refs.canvas.createTextbox(text, {
					top: e.offsetY,
					left: e.offsetX,
					itemName,
					itemRealCode,
					unit,
					value,
					sysCode,
					stationCode,
					canvasWidth: width,
					canvasHeight: height,
					// width: 1000,
					textAlign: 'center',
					type: 'TEXT',
					fill: '#ffffff',
					index: controls.length,
					backgroundColor: '#0b162c',
					// lockScalingX: true,
					// lockScalingY: true,
					name: '控件' + (controls.length + 1),
					code: controls.length + 1,
					fontSize: 12,
					content: text,
					editable: false,
					hideTitle: true,
					hideUnit: true,
					angle: 0,
					originX: 'left',
					originY: 'center',
				})

				this.type = 'TEXT'
			}

			this.$refs.canvas.canvas.setActiveObject(control)
			this.$emit('showLevel', control)
			controls.push(control)
		},
		drawBase64Image(img) {
			return new Promise(resolve => {
				let image = new Image()
				image.src = img + '?v=' + Math.random() // 防止缓存，必填
				image.crossOrigin = '*' // 解决前端跨域问题
				image.onload = function () {
					var canvas = document.createElement('canvas')
					canvas.width = image.width
					canvas.height = image.height
					var ctx = canvas.getContext('2d')
					ctx.drawImage(image, 0, 0, image.width, image.height)
					var dataURL = canvas.toDataURL('image/')
					resolve(dataURL)
					// return dataURL
				}
			})
		},

		// 预览方法
		async priviewRender(item, selectable = false, evented = false) {
			const { controlsType, baseStyle, canvasWidth, canvasHeight, iconUrl } = item
			const style = JSON.parse(baseStyle)
			const { top, left, flipX, flipY, stroke, strokeWidth } = style

			let control = null
			if (controlTypes2AttrtempMap[controlsType] || `${controlsType}`.indexOf('controls-') > -1) {
				const {
					color,
					red,
					green,
					blue,
					scaleX,
					scaleY,
					stationCode,
					itemRealCode,
					sysCode,
					type,
					colorObj,
					angle,
					video,
					interactive,
					controlsDetails,
				} = style
				let imgUrl = controlTypes2AttrtempMap[controlsType]?.url || iconUrl
				control = await this.$refs.canvas.createImage(imgUrl, {
					top,
					left,
					angle,
					color,
					red,
					green,
					blue,
					scaleX,
					scaleY,
					index: controls.length,
					stationCode,
					itemRealCode,
					sysCode,
					type,
					colorObj,
					canvasWidth,
					canvasHeight,
					selectable,
					evented,
					flipX,
					flipY,
					stroke,
					strokeWidth,
					iconUrl,
					video,
					interactive,
					controlsDetails,
				})
			} else if (['TEXT', 'PLAINTEXT'].includes(controlsType)) {
				const {
					backgroundColor,
					fontSize,
					clickable,
					fill,
					itemName,
					stationCode,
					itemRealCode,
					sysCode,
					type,
					text,
					unit,
					value,
					hideTitle,
					hideUnit,
					strokeWidth,
					textAlign,
					fontWeight,
					fontStyle,
					underline,
					linethrough,
					overline,
					charSpacing,
					width,
					scaleX,
					scaleY,
					angle,
					originX = 'left',
					originY = 'center',
				} = style
				if (clickable) {
					evented = true
				}
				control = this.$refs.canvas.createTextbox(text, {
					width,
					top,
					left: left,
					type,
					backgroundColor,
					fill,
					unit,
					value,
					fontSize,
					selectable,
					evented,
					scaleX,
					scaleY,
					// lockScalingX: true,
					// lockScalingY: true,
					stationCode,
					canvasWidth,
					canvasHeight,
					itemRealCode,
					sysCode,
					itemName,
					index: controls.length,
					hideTitle,
					hideUnit,
					editable: controlsType === 'PLAINTEXT',
					textAlign,
					// width,
					strokeWidth,
					// stroke,
					fontWeight,
					fontStyle,
					underline,
					linethrough,
					overline,
					charSpacing,
					angle,
					originX,
					originY,
				})
				this.$refs.canvas.toTopLayer(control)
			} else if (controlsType === 'LINE_SEGMENT') {
				const {
					points,
					stroke,
					strokeWidth,
					strokeDashArray,
					animate,
					left,
					top,
					initValue,
					animateColor,
				} = style
				if (animate) {
					this.animateLines.push({ ...style, index: controls.length })
				}
				control = this.$refs.canvas.createPolyline({
					xyData: points,
					opt: {
						type: 'LINE_SEGMENT',
						stroke: stroke || 'red',
						strokeWidth,
						strokeDashArray,
						index: controls.length,
						left,
						top,
						// index: style.index,
						selectable,
						canvasWidth,
						canvasHeight,
						evented,
						initValue,
						animate,
						animateColor,
					},
				})
				this.$refs.canvas.toBottomLayer(control)
			} else if (controlsType === 'SQUARE') {
				const { fill, scaleX, scaleY } = style
				control = this.$refs.canvas.createRect({
					type: 'SQUARE',
					fill,
					top,
					left,
					scaleX,
					scaleY,
					selectable,
					canvasWidth,
					canvasHeight,
					index: controls.length,
					evented,
					stroke: stroke || 'red',
					strokeWidth: strokeWidth || 1,
				})
			} else if (controlsType === 'LINK') {
				const { fill, scaleX, scaleY, interactionType, jumpType, link } = style
				control = this.$refs.canvas.createRect({
					type: 'LINK',
					fill,
					top,
					left,
					scaleX,
					scaleY,
					opacity: 0.5,
					selectable,
					canvasWidth,
					canvasHeight,
					index: controls.length,
					evented,
					stroke: stroke || 'red',
					strokeWidth: strokeWidth || 1,
					interactionType,
					jumpType,
					link,
				})
			} else if (controlsType === 'ROUND') {
				const { fill, scaleX, scaleY } = style
				control = this.$refs.canvas.createCircle({
					type: 'ROUND',
					fill,
					top,
					left,
					scaleX,
					scaleY,
					canvasWidth,
					canvasHeight,
					selectable,
					index: controls.length,
					evented,
				})
			}
			controls.push(control)
			return control
		},
		// api 查询数据
		itemData(params, item) {
			return itemDate(params).then(res => {
				const { result } = res
				let obj = {}

				result.forEach(it => {
					const { showItemCode, stationCode, sysCode } = it
					it.stationDataItem.forEach(data => {
						obj = {
							...item,
							...data,
							showItemCode,
							stationCode,
							sysCode,
							isShow: true,
						}
					})
				})
				return obj
			})
		},
		/**
		 * @description h5 原生拖拽事件
		 */
		dragstart(e, item) {
			// console.log(e.offsetX, e.offsetY)
			e.dataTransfer.setData(
				'widget-config',
				JSON.stringify({
					startX: e.offsetX,
					startY: e.offsetY,
					url: item.url,
				}),
			)
			// setTimeout(() => {
			// 	this.$parent.widgetShow = false
			// }, 300)
		},
		createRect(option) {
			const { width, height } = this.$refs.canvas.getCanvasSize()
			const rect = this.$refs.canvas.createRect({
				type: 'SQUARE',
				index: controls.length,
				canvasWidth: width,
				canvasHeight: height,
				name: '折线' + (controls.length + 1),
				code: controls.length + 1,
				...option,
			})
			this.type = 'SQUARE'
			controls.push(rect)
			this.$refs.canvas.canvas.setActiveObject(rect)
			this.$emit('showLevel', rect)
			return rect
		},
		createCircle() {
			const { width, height } = this.$refs.canvas.getCanvasSize()
			const circle = this.$refs.canvas.createCircle({
				type: 'ROUND',
				index: controls.length,
				canvasWidth: width,
				canvasHeight: height,
				name: '折线' + (controls.length + 1),
				code: controls.length + 1,
			})
			this.type = 'ROUND'
			controls.push(circle)
			this.$refs.canvas.canvas.setActiveObject(circle)
			this.$emit('showLevel', circle)
		},
		drawLine() {
			const active = this.$refs.canvas.canvas.getActiveObject()
			if (active) {
				this.$Message.info('存在已选中的控件')
				return
			}
			this.isDrawingMode = true
			this.$emit('showLevel', { type: 'LINE_SEGMENT_EDITOR' })
			const allObjects = this.$refs.canvas.canvas.getObjects()
			allObjects.forEach(item => {
				item.set({
					selectable: false,
					evented: false,
				})
			})
			linePath = []
			editLineObject = []
			circleObject = []
			this.prevCircleInstance = {}
		},
		// 删除obj
		removeObj(arrObj) {
			this.$refs.canvas.removeObj(arrObj)
		},
		// 结束绘制折线
		endDrawPolyline() {
			const { width, height } = this.$refs.canvas.getCanvasSize()
			this.isDrawingMode = false
			this.$refs.canvas.removeObj(editLineObject)
			// const gradient = this.$refs.canvas.createGradient(linePath)

			let polyLine = this.$refs.canvas.createPolyline({
				xyData: linePath,
				opt: {
					stroke: '#497fff',
					animateColor: '#ddd',
					type: 'LINE_SEGMENT',
					// opacity: 0.3,
					canvasWidth: width,
					canvasHeight: height,
					index: controls.length,
					name: '折线' + (controls.length + 1),
					code: controls.length + 1,
					strokeDashArray: [],
				},
			})
			// polyLine.set('stroke', gradient)
			const allObjects = this.$refs.canvas.canvas.getObjects()
			allObjects.forEach(item => {
				item.set({
					selectable: true,
					evented: true,
				})
			})
			this.type = 'LINE_SEGMENT'
			this.$refs.canvas.canvas.setActiveObject(polyLine)
			this.$refs.canvas.toBottomLayer()
			controls.push(polyLine)
			const { left, top } = polyLine
			polyLine.initValue = { left, top }
			this.$emit('showLevel', polyLine)
		},
		// 绘制线段模式下 点击画布事件 添加圆形点
		handleMouseUp(options) {
			console.log(' ~ handleMouseUp ~ ', options)

			const {
				pointer: { x, y },
				target,
			} = options
			// target存在时 是在移动元素 不另外添加circle
			if (!this.isDrawingMode || target) return
			linePath.push({
				x: x,
				y: y,
			})
			const c = this.$refs.canvas.createCircle({
				left: x,
				top: y,
				radius: 1,
			})
			c.hasControls = c.hasBorders = false
			const l = this.$refs.canvas.createLine({
				x: this.prevCircleInstance.left,
				y: this.prevCircleInstance.top,
				x1: x,
				y1: y,
				strokeColor: '#497fff',
				strokeWidth: 3,
				evented: false,
			})
			l.hasControls = l.hasBorders = false
			c.line = l
			c.index = linePath.length - 1
			this.prevCircleInstance.prev = l
			this.prevCircleInstance = c
			editLineObject.push(c)
			editLineObject.push(l)
			circleObject.push(c)

			if (this.isDrawingMode === true) {
				const { width, height } = this.$refs.canvas.getCanvasSize()
				this.$emit('showLevel', {
					type: 'LINE_SEGMENT_EDITOR',
					linePath,
					canvasWidth: width,
					canvasHeight: height,
				})
			}
		},
		modified(option) {
			this.handleMouseDown(option)
		},
		handleMouseDown(options) {
			console.log('handleMouseDown', options)
			const { target } = options
			if (target === null || selectObj.length > 1) {
				// 显示背景属性或者选中了多个控件
				if (this.isDrawingMode !== true) {
					const { width, height } = this.$refs.canvas.getCanvasSize()
					this.$emit('showLevel', {
						type: 1,
						canvasWidth: width,
						canvasHeight: height,
					})
					this.type = null
				}
			} else {
				if (target.type !== 'circle') {
					this.type = target.type
					this.$emit('showLevel', target)
				}
			}
			if (this.showButton === false && target && target.type === 'TEXT') {
				// TODO
				console.log('发送消息', JSON.stringify(target))
				const { itemName, itemRealCode, type } = target
				window.top.postMessage({ itemName, itemRealCode, type }, '*')
			}
		},
		handleMoving(options) {
			if (!this.isDrawingMode) return
			const {
				target: { line, prev, left, top, index },
			} = options
			linePath[index].x = left
			linePath[index].y = top
			line && line.set({ x2: left, y2: top })
			prev && prev.set({ x1: left, y1: top })
			this.$refs.canvas.renderAll()
			if (this.isDrawingMode === true) {
				const { width, height } = this.$refs.canvas.getCanvasSize()
				this.$emit('showLevel', {
					type: 'LINE_SEGMENT_EDITOR',
					linePath,
					canvasWidth: width,
					canvasHeight: height,
				})
			}
		},
		handleDelete() {
			let obj = this.$refs.canvas.removeCurrentObj()
			const { index, type } = obj
			if (type === 'LINE_SEGMENT') {
				let newArr = []
				this.animateLines.forEach(item => {
					if (item.index !== index) {
						newArr.push(item)
					}
				})
				this.animateLines = newArr
			}

			// controls.splice(index, 1)
			this.$emit('handleDelete', index)
			this.type = null
		},

		// 改变当前控件属性
		changeControl(attr, value, opts) {
			if (attr === 'animate') {
				if (value.animate) {
					this.svgFlag = false
					// this.animateLines = []
					let inseterFlag = true
					this.animateLines.forEach((item, i) => {
						if (item.index === value.index) {
							// item = { ...item, ...value }
							// this.$set(item, 'left', value.left)
							// this.$set(item, 'top', value.top)
							this.animateLines[i] = { ...item, ...value }
							inseterFlag = false
						}
					})

					if (inseterFlag) {
						this.animateLines.push(value)
					}
					this.svgFlag = true
				} else {
					let newArr = []
					this.animateLines.forEach(item => {
						if (item.index !== value.index) {
							newArr.push(item)
						}
					})
					this.animateLines = newArr
				}
				return
			}
			if (this.isDrawingMode) {
				const { index } = opts

				circleObject[index][attr] = value
				this.handleMoving({ target: circleObject[index] })
				return
			}

			let obj = this.$refs.canvas.changeControl(attr, value, opts)
			// 显示标题、选择视频
			if (
				attr === 'hideTitle' ||
				attr === 'hideUnit' ||
				attr === 'fontSize' || // 文本的大小改变需要重新选中
				attr === 'height' || // 文本的大小改变需要重新选中
				attr === 'video'
			) {
				this.$emit('showLevel', obj)
			}
			// 折线动画颜色
			if (attr === 'animateColor') {
				// this.$emit('showLevel', obj)
				this.animateLines.forEach(line => {
					if (line.index === value.index) {
						line.animateColor = value.animateColor
					}
				})
			}
		},
		changeObjControlAttr(obj, attr, value) {
			this.$refs.canvas.changeObjControlAttr(obj, attr, value)
			return obj
		},
		changeObjControl(obj, attr, value) {
			this.$refs.canvas.changeObjControl(obj, attr, value)
			return obj
		},

		createImg() {
			let dataUrl = this.$refs.canvas.toDataUrl()
			// console.log(dataUrl);
			this.imgUrl = dataUrl
		},
		getActive() {
			// debugger
			let obj = this.$refs.canvas.canvas.getActiveObject()
			if (!obj) {
				return
			}
			const { width, scaleX, scaleY, height, left, top } = obj
			console.log('宽度：', width * scaleX)
			console.log('高度：', height * scaleY)
			console.log('x：', left)
			console.log('y：', top)
		},
		// 选择实例对象
		selected(option) {
			if (option.selected.length > 1) {
				// this.type = null
				// option.target.lockScalingX = true
				// option.target.lockScalingY = true
				// option.target.lockMovementX = true
				// option.target.lockMovementY = true
				// option.target.lockRotation = true
				// selectObj = option.selected

				// TODO
				this.$refs.canvas.canvas.discardActiveObject()
			}
		},
		// 清除选择
		cleared() {
			selectObj = []
		},
		// 创建文本
		createIText(content, option) {
			option.index = controls.length
			const { width, height } = this.$refs.canvas.getCanvasSize()
			let control = this.$refs.canvas.createText(content, {
				...option,
				canvasWidth: width,
				canvasHeight: height,
				originX: 'left',
				originY: 'center',
				fontSize: 30,
			})

			this.$refs.canvas.canvas.setActiveObject(control)
			this.$emit('showLevel', control)
			controls.push(control)
			return control
		},
		createTextBox(content, option) {
			option.index = controls.length
			const { width, height } = this.$refs.canvas.getCanvasSize()
			let control = this.$refs.canvas.createTextbox(content, {
				...option,
				canvasWidth: width,
				canvasHeight: height,
				originX: 'left',
				originY: 'center',
				fontSize: 12,
			})

			this.$refs.canvas.canvas.setActiveObject(control)
			this.$emit('showLevel', control)
			controls.push(control)
			return control
		},
		toTopLayer(obj) {
			this.$refs.canvas.toTopLayer(obj)
		},
		// 控件绑定数据
		controlBindData(position, data) {
			const { left, top } = position
			const { width, height } = this.$refs.canvas.getCanvasSize()
			data.forEach(async (item, index) => {
				const { itemRealCode, sysCode, stationCode: code } = item
				let params = [
					{
						itemCodes: [itemRealCode],
						stationCode: code,
						sysCode,
					},
				]
				const obj = await this.itemData(params, item)

				const { itemName, value, unit, stationCode } = obj
				// 默认只展示数值
				const text = `${getVal(value)}`
				let control = this.$refs.canvas.createTextbox(text, {
					top: top + 26 * index,
					left: left,
					// width,
					height,
					index: controls.length,
					type: 'TEXT',
					fill: '#ffffff',
					textAlign: 'center',
					sub: true,
					unit,
					value,
					itemName,
					itemRealCode,
					sysCode,
					stationCode,
					editable: false,
					canvasWidth: width,
					canvasHeight: height,
					backgroundColor: '#0b162c',
					lockScalingX: true,
					lockScalingY: true,
					styles: { color: 'yellow' },
					name: '控件' + (controls.length + 1),
					code: controls.length + 1,
					fontSize: 12,
					content: text,
					hideTitle: true,
					hideUnit: true,
				})
				this.$refs.canvas.canvas.setActiveObject(control)
				this.$emit('showLevel', control)
				controls.push(control)
			})
		},
		// 获取canvas画布上所有元素
		getObjects() {
			return this.$refs.canvas.canvas.getObjects()
		},
		toSvg() {
			return this.$refs.canvas.toSvg()
		},
		toDataURL() {
			return this.$refs.canvas.toDataUrl()
		},
		getPoints(line) {
			// debugger
			const { initValue, points, left, top } = line
			let diffLeft = 0
			let diffTop = 0
			if (initValue) {
				const initLeft = initValue.left
				const initTop = initValue.top
				diffLeft = left - initLeft
				diffTop = top - initTop
			}

			let arr = []
			points.forEach(item => {
				arr.push(item.x + 0 + diffLeft + ',' + (item.y + 0 + diffTop))
			})
			return arr.join(' ')
		},
	},
	beforeDestroy() {
		controls = []
		selectObj = []
		linePath = []
		editLineObject = []
		circleObject = []
	},
}
</script>

<style lang="less" scoped>
.content-wrapper {
	// width: 100%;
	// height: 100%;
	position: relative;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: -moz-box;
	display: -webkit-box;
	display: flex;
	-moz-box-flex: 1;
	box-flex: 1;
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	flex: 1;
	justify-content: space-between;
	height: 100vh;
	.button {
		position: absolute;
		z-index: 2;
		left: 50%;
		top: 20px;
	}
	.list-wraper,
	.site-list-wraper {
		width: 300px;
		height: 100%;
		overflow-y: auto;
		flex-shrink: 0;
		box-sizing: border-box;

		display: -webkit-flex;
		display: -ms-flexbox;
		display: -moz-box;
		display: -webkit-box;
		display: flex;
		-webkit-box-orient: vertical;
		-moz-box-orient: vertical;
		-webkit-flex-direction: column;
		flex-direction: column;

		.image-wrapper {
			padding: 20px;
			display: -ms-flexbox;
			display: -moz-box;
			display: -webkit-box;
			display: -webkit-flex;
			display: flex;

			flex-shrink: 0;
			box-pack: center;
			-webkit-box-pack: center;
			-moz-box-pack: center;
			-moz-box-pack: center;
			-webkit-justify-content: center;
			justify-content: center;
			box-align: center;
			-moz-box-align: center;
			-webkit-box-align: center;
			-webkit-align-items: center;
			align-items: center;
			border-bottom: 1px solid #eee;
			position: relative;
			img {
				height: 120px;
			}
		}
	}
	.site-list-wraper {
		border-right: 1px solid #efefef;
	}
	.list-wraper {
		border-left: 1px solid #efefef;
	}
	.flow-svg {
		position: absolute;
		width: 100%;
		height: 100%;
		pointer-events: none;
		// transform: scale(1) translate(-50%, -0%);
		transform-origin: 0 0;
		position: absolute;
		// left: 50%;
		// top: 50%;
		// transition: .3s;
		overflow: hidden;
		// left: calc(50% - 692.5px) !important;
	}
	.polyline {
		fill: transparent;
		stroke: #ddd;
		stroke-width: 2;
		stroke-dasharray: 5000;
		stroke-dashoffset: 1000;
		animation: run 10s linear infinite;
	}
}
</style>
