<!--
 * @Description:
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-07-19 09:01:33
 * @LastEditors: shenxh
 * @LastEditTime: 2024-06-04 09:25:33
-->
<template lang="pug">
.change-config
	.change-config-container
		water-row(justify='space-between', align='center')
			water-row(justify='flex-start', align='center')
				.change-config-title 登录时间:
				Date-picker(type='datetimerange', placeholder='选择日期和时间', style='width: 200px', v-model='searchObj.time')
				.change-config-title.water-margin-left-8 用户:
				Select.water-margin-left-8(
					v-model='searchObj.username',
					placeholder='请选择',
					style='width: 200px',
					:clearable='true',
					:transfer='true'
				)
					Option(v-for='(item, index) in list', :key='index', :value='item.value') {{ item.label }}
				Button.water-margin-left-8(type='primary', @click='getList()') 查询
		.change-config-content
			es-table.change-config-table.water-table(
				border,
				:columns='columns',
				:data='tableData',
				:loading='loading',
				showPage,
				:pageData='pageData',
				@on-page-num-change='handlePageChange'
			)
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { getLoginLog, getUsers } from '@/api/log.js'
export default {
	components: {
		WaterTable,
		WaterRow,
	},
	data() {
		return {
			searchObj: {
				time: [],
				username: '',
			},
			list: [],
			columns: [
				{
					title: '日志ID',
					key: 'id',
					align: 'center',
					width: 120,
				},
				{
					title: '用户名',
					key: 'name',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '用户ID',
					key: 'userId',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '登录时间',
					key: 'createTime',
					align: 'center',
					minWidth: 120,
				},

				{
					title: '登出时间',
					key: 'logoutTime',
					align: 'center',
					minWidth: 120,
				},
				{
					title: '来源IP',
					key: 'remoteIp',
					align: 'center',
					minWidth: 120,
				},

				{
					title: '会话ID',
					key: 'sessionId',
					align: 'center',
					minWidth: 120,
				},
			],
			tableData: [],
			loading: false,
			pageData: {
				showTotal: true,
				current: 1,
				pageSize: 20,
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	mounted() {
		this.getUserFn()
		this.handleQuery()
	},
	methods: {
		// 获取用户信息
		getUserFn() {
			getUsers().then(res => {
				this.list = res.result.map(ele => {
					return {
						label: ele,
						value: ele,
					}
				})
			})
		},
		getList() {
			this.pageData.current = 1
			this.handleQuery()
		},
		handleQuery() {
			const params = {
				pageNum: this.pageData.current,
				pageSize: this.pageData.pageSize,
				beginTime: this.searchObj.time[0] ? this.$moment(this.searchObj.time[0]).format('YYYY-MM-DD') : '',
				endTime: this.searchObj.time[1] ? this.$moment(this.searchObj.time[1]).format('YYYY-MM-DD') : '',
				username: this.searchObj.username,
			}
			getLoginLog(params).then(res => {
				const { result = '' } = res
				const { list = [], total = 0 } = result
				this.tableData = list
				this.pageData.total = total
			})
		},

		handlePageChange(pageNum) {
			this.pageData.current = pageNum
			this.handleQuery()
		},
	},
}
</script>
<style lang="less" scoped>
.change-config {
	height: 100%;
	padding: 16px;
	background-color: rgb(242 242 242) !important;
	display: flex;
	flex-direction: column;
	&-container {
		height: 100%;
		padding: 16px;
		background: #fff;
		flex: 1;
	}
	&-content {
		margin-top: 16px;
		height: calc(100% - 48px);
	}
	&-title {
		white-space: nowrap;
		margin-right: 4px;
	}
}
</style>
