<template lang="pug">
Collapse.control(@on-change='handleCollapseChange')
	Panel(name='1')
		| 云台控制
		template(v-slot:content)
			//- 圆形控制
			div(style='height: 192px')
				drag-arc(
					v-if='!firstShowFlag',
					v-model='arcValue',
					:disabled='isDisabled',
					@action='handleAction',
					@direction='handleDirection'
				)
			//- 预置点设置
			Tabs.preset(type='card', :animated='false') 
				TabPane(label='预置点')
					.preset-content
						Select(
							v-model='presetValue',
							:disabled='isDisabled',
							transfer,
							size='small',
							label-in-value,
							style='width: 104px',
							@on-change='handlePresetChange'
						)
							Option(v-for='item in presetOption', :key='item.presetPointIndex', :value='item.presetPointIndex') {{ item.presetPointName }}
						.btns-wrap
							Poptip(
								v-model='editPoptipVis',
								transfer-class-name='preset-poptip',
								placement='top-end',
								:width='150',
								transfer
							)
								Button(icon='md-create', :disabled='isDisabled', @click='handleEdit')
								template(v-slot:content)
									.edit-title 预置点设置
									Input(v-model='editValue', size='small')
									.edit-btns
										Button(size='small', @click='editPoptipVis = false') 取消
										Button(type='primary', size='small', @click='handleSavePreset') 保存
							Button(icon='md-arrow-dropright-circle', :disabled='isDisabled || !playBtnDisabled', @click='handleToPreset')
							Poptip(confirm, placement='top-end', transfer, title='确定要删除吗？', @on-ok='handleDeletePreset')
								Button(icon='md-trash', :disabled='isDisabled || !playBtnDisabled')
</template>

<script>
import DragArc from './DragArc.vue'

import {
	queryPrecastPoint,
	videoLensControl,
	videoDirectionControl,
	toPrecastPoint,
	deletePrecastPoint,
	savePrecastPoint,
} from '@/api/security-manage'

export default {
	components: {
		DragArc,
	},
	props: {
		id: {
			type: [Number, String],
			default: '',
		},
	},
	computed: {
		isDisabled() {
			console.log('this,id: ', this.id)
			return this.id ? false : true
		},
		// 预置点播放按钮禁用
		playBtnDisabled() {
			const index = this.curPresetObj.value
			const value = this.validPreset.find(item => {
				return item.presetPointIndex === index
			})
			return Boolean(value)
		},
	},
	watch: {
		id() {
			if (this.id) {
				this.getPresetList()
			} else {
				this.initPresetOptions()
			}
		},
	},
	mounted() {
		this.initPresetOptions()
	},
	data() {
		return {
			// 第一次点击折叠面板标记
			firstShowFlag: true,
			collapseValue: '',
			arcValue: 50,

			// 预置点值
			presetValue: '1',
			// 预置点下拉框options
			presetOption: [],
			// 当前预设点对象
			curPresetObj: {},
			// 实际预置点数据
			validPreset: [],

			// 编辑弹窗显隐
			editPoptipVis: false,
			// 设置输入框的值
			editValue: '',
		}
	},
	methods: {
		// 初始化预置点下拉框
		initPresetOptions() {
			// 初始化预置点下拉框数据
			this.presetOption = Array.from({ length: 20 }, (item, index) => ({
				presetPointIndex: index + 1,
				cameraIndexCode: '',
				presetPointName: `预置点${index + 1}`,
			}))
			const { presetPointIndex, presetPointName } = this.presetOption[0]
			this.presetValue = presetPointIndex
			this.curPresetObj = {
				value: presetPointIndex,
				label: presetPointName,
			}
		},
		// 镜头控制
		handleAction(data) {
			const params = {
				cameraIndexCode: this.id,
				speed: this.arcValue,
				...data,
			}
			videoLensControl(params).then(() => {})
		},
		// 方向控制
		handleDirection(data) {
			const params = {
				cameraIndexCode: this.id,
				speed: this.arcValue,
				...data,
			}
			videoDirectionControl(params).then(() => {})
		},
		// 查询预置点下拉列表
		getPresetList() {
			this.initPresetOptions()
			queryPrecastPoint({ cameraIndexCode: this.id }).then(res => {
				this.validPreset = res.result.list
				this.validPreset.forEach(item => {
					const index = this.presetOption.findIndex(it => {
						return it.presetPointIndex === item.presetPointIndex
					})
					if (index > -1) {
						this.presetOption.splice(index, 1, { ...item })
					}
				})
			})
		},
		// 保存预置点
		handleSavePreset() {
			savePrecastPoint({
				cameraIndexCode: this.id,
				presetName: this.editValue,
				presetIndex: this.curPresetObj.value,
			})
				.then(() => {
					this.getPresetList()
					this.$Message.success('预置点保存成功！')
					this.editPoptipVis = false
				})
				.catch(() => {
					this.$Message.error('预置点保存失败！')
					this.editPoptipVis = false
				})
		},
		// 移动到预设点
		handleToPreset() {
			toPrecastPoint({
				cameraIndexCode: this.id,
				action: 0,
				command: 'GOTO_PRESET',
				speed: this.arcValue,
				presetIndex: this.curPresetObj.value,
			}).then(() => {})
		},
		// 删除预置点
		handleDeletePreset() {
			deletePrecastPoint({
				cameraIndexCode: this.id,
				presetIndex: this.curPresetObj.value,
			})
				.then(() => {
					this.getPresetList()
					this.$Message.success('预置点删除成功！')
				})
				.catch(() => {
					this.$Message.error('预置点删除失败！')
				})
		},
		// 云台控制折叠面板change事件
		handleCollapseChange() {
			if (this.firstShowFlag) {
				this.firstShowFlag = false
			}
		},
		// 预设点下拉框改变
		handlePresetChange(data) {
			this.curPresetObj = data
		},
		// 编辑
		handleEdit() {
			this.editValue = this.curPresetObj.label
		},
	},
}
</script>

<style lang="less" scoped>
.edit-title {
	color: #333;
	margin-bottom: 6px;
	// border: 1px solid #e1e2ec;
}
.edit-btns {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
	.ivu-btn + .ivu-btn {
		margin-left: 6px;
	}
}
.control {
	border: none;
	border-top: 1px solid #ebecf4;
	/deep/ .ivu-collapse-header {
		display: flex;
		flex-direction: row-reverse;
		align-items: center;
		justify-content: space-between;
		height: 32px;
		line-height: 32px;
		border: none !important;
		padding-left: 8px;
		color: #333;
	}
	/deep/ .ivu-collapse-content {
		padding: 0;
	}
	/deep/ .ivu-collapse-content > .ivu-collapse-content-box {
		padding-top: 0;
		padding-bottom: 0;
	}
	/deep/ .ivu-tabs.ivu-tabs-card {
		> .ivu-tabs-bar {
			.ivu-tabs-nav-container,
			.ivu-collapse-content,
			.ivu-tabs-tab {
				height: 24px;
				line-height: 15px;
				font-size: 12px;
			}
		}
	}
	/deep/ .ivu-select-small.ivu-select-single {
		.ivu-select-selection {
			.ivu-select-placeholder,
			.ivu-select-selected-value {
				height: 28px;
				line-height: 28px;
				font-size: 12px;
			}
		}
	}
	/deep/.ivu-select-disabled {
		.ivu-select-selection {
			background-color: transparent;
		}
	}
	.preset {
		padding: 12px 10px 6px 10px;
		.preset-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			.btns-wrap {
				display: flex;
				align-items: center;
				justify-content: space-around;
				/deep/.ivu-btn {
					width: 24px;
					height: 24px;
					padding: 0;
					border: none;
					.ivu-icon {
						color: #3f435e;
						line-height: 1;
						font-size: 20px;
					}
				}
				/deep/.ivu-btn[disabled] > * {
					color: #babbca;
				}
				.ivu-btn[disabled],
				.ivu-btn[disabled]:hover {
					background-color: transparent;
				}
				.ivu-btn:focus {
					box-shadow: unset;
				}
			}
		}
	}
}
</style>
<style lang="less">
.preset-poptip {
	.ivu-poptip-body {
		padding: 10px;
	}
}
.ivu-message {
	top: 0 !important;
}
</style>
