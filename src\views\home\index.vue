<!--
 * @Description: 首页
 * @Autor: Fengjialing
 * @Date: 2025-04-17 16:57:10
 * @LastEditors: Fengjialing
 * @LastEditTime: 2025-04-17 10:36:00
-->
<template>
	<div class="container">
		<div class="left-menu">
			<Menu active-name="1" @on-select="handleClick">
				<MenuItem v-for="item in menuList" :key="item.value" :name="item.name">
					{{ item.label }}
				</MenuItem>
			</Menu>
		</div>
		<div class="menu-container">
			<router-view />
		</div>
	</div>
</template>
<script>
export default {
	data() {
		return {
			menuList: [
				{
					label: '告警一张图',
					name: '1',
					route: '/alarmOnePicture',
				},
				{
					label: '告警配置',
					name: 'device-alarm-config-home',
					route: '/device-alarm-config-home',
				},
				{
					label: '告警记录',
					name: 'alarm-record',
					route: '/alarm-record',
				},
				{
					label: '基础数据项',
					name: 'item-list-new',
					route: '/base-item/item-list-new',
				},
				{
					label: '通用设备类型配置',
					name: 'device-type-config',
					route: '/device-type-config',
				},
				// 专用报警配置
				// {
				// 	label: '专用报警方案',
				// 	name: 'special-use-alarm',
				// 	route: '/special-use-alarm',
				// },
				// {
				// 	label: '通用报警方案',
				// 	name: 'common-alarm',
				// 	route: '/common-alarm',
				// },
			],
		}
	},
	methods: {
		handleClick(path) {
			let to = ''
			this.menuList.forEach(item => {
				if (item.name === path) {
					to = item.route
				}
			})
			this.$router.push(to)
		},
	},
	created() {
		// this.$router.push(this.menuList[0].route)
	},
}
</script>
<style lang="less" scoped>
.container {
	display: flex;
	width: 100%;
	height: 100%;
	overflow: auto;
	.left-menu {
		width: 10%;
		/deep/ .ivu-menu {
			height: 100%;
			width: 100% !important;
			overflow: auto;
		}
	}
	.menu-container {
		width: 90%;
		height: 100%;
		padding: 8px;
	}
}
</style>
