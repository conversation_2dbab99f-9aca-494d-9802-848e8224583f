<template>
	<div
		class="flow-chart"
		:style="{
			height: height + 'px',
			width: width + 'px',
			background: `url(${processImage}) no-repeat`,
		}"
		v-if="show"
	>
		<!-- 取水泵房 -->
		<flow-tip-box
			v-for="(data, index) in dataList"
			:key="index"
			class="tip-special-box"
			:top="data.y"
			:left="data.x"
		>
			<div
				v-for="(item, index) in data.values"
				:key="item.itemCode + index"
				class="tips"
				:class="getParentClassName(item)"
			>
				<!-- 4代表无框6代表状态图 -->
				<div class="title" v-show="item.showType !== 4 && item.showType !== 3 && item.showType !== 6">
					{{ item && item.itemName }}
				</div>

				<div :class="getClassName(item)" v-if="item.showType === 6">
					<img :src="getImgUrl(item.value)" />
				</div>

				<div class="value" :class="getClassName(item, data)" v-else>
					<template v-if="item.showType === 3">
						{{ item && item.value }}
					</template>
					<template v-else>
						{{ item && item.value }}
						{{ item && item.unit }}
					</template>
				</div>
			</div>
		</flow-tip-box>

		<!-- 水流svg -->
		<svg class="flow-svg" xmlns="http://www.w3.org/2000/svg" version="1.1">
			<template v-for="(line, index) in lines">
				<polyline :key="index" class="polyline" :points="getPoints(line)" />
			</template>
		</svg>
	</div>
</template>

<script>
import FlowTipBox from './FlowTipBox.vue'

export default {
	components: {
		FlowTipBox,
	},
	props: {
		abnormal: {
			type: Boolean,
			default: false,
		},
		processImage: {
			type: String,
			default: 'https://eslink-iot.oss-cn-beijing.aliyuncs.com/flow-chart.svg',
		},
		dataList: {
			type: Array,
			default: function () {
				return [
					{
						groupConfigs: [
							{
								equipmentName: '新徽商二期-变频器报警',
								groupId: 1,
								id: 56,
								mtMonitorDataState: {
									dataState: 0,
									monitorData: '0.000',
									monitorTypeName: '报警状态',
									monitorTypeUnit: '',
									mtMonitorObjectId: 'a7d42680744849f18dfe8a79e1ebb5b1',
									mtMonitorTypeId: 'ESLinkIotPlatformMonitorType0026',
								},
								mtMonitorObjectId: 'a7d42680744849f18dfe8a79e1ebb5b1',
								mtMonitorTypeId: 'ESLinkIotPlatformMonitorType0026',
								showType: 0,
							},
						],
						id: 1,
						remarkName: '测试01',
						x: '87.00',
						y: '95.00',
						selectOptions: [
							{
								value: 'a7d42680744849f18dfe8a79e1ebb5b1',
								label: '新徽商二期-变频器报警',
							},
						],
						deviceIds: ['a7d42680744849f18dfe8a79e1ebb5b1'],
						devices: {
							a7d42680744849f18dfe8a79e1ebb5b1: [
								{
									equipmentName: '新徽商二期-变频器报警',
									groupId: 1,
									id: 56,
									mtMonitorDataState: {
										dataState: 0,
										monitorData: '0.000',
										monitorTypeName: '报警状态',
										monitorTypeUnit: '',
										mtMonitorObjectId: 'a7d42680744849f18dfe8a79e1ebb5b1',
										mtMonitorTypeId: 'ESLinkIotPlatformMonitorType0026',
									},
									mtMonitorObjectId: 'a7d42680744849f18dfe8a79e1ebb5b1',
									mtMonitorTypeId: 'ESLinkIotPlatformMonitorType0026',
									showType: 0,
								},
							],
						},
						value: 'a7d42680744849f18dfe8a79e1ebb5b1',
					},
					{
						groupConfigs: [
							{
								equipmentName: '新徽商二期-变频器报警',
								groupId: 2,
								id: 2,
								mtMonitorDataState: {
									dataState: 0,
									monitorData: '0.000',
									monitorTypeName: '报警状态',
									monitorTypeUnit: '',
									mtMonitorObjectId: 'a7d42680744849f18dfe8a79e1ebb5b1',
									mtMonitorTypeId: 'ESLinkIotPlatformMonitorType0026',
								},
								mtMonitorObjectId: 'a7d42680744849f18dfe8a79e1ebb5b1',
								mtMonitorTypeId: 'ESLinkIotPlatformMonitorType0026',
								showType: 0,
							},
						],
						id: 2,
						remarkName: '112233',
						x: '603.00',
						y: '-9.00',
						selectOptions: [
							{
								value: 'a7d42680744849f18dfe8a79e1ebb5b1',
								label: '新徽商二期-变频器报警',
							},
						],
						deviceIds: ['a7d42680744849f18dfe8a79e1ebb5b1'],
						devices: {
							a7d42680744849f18dfe8a79e1ebb5b1: [
								{
									equipmentName: '新徽商二期-变频器报警',
									groupId: 2,
									id: 2,
									mtMonitorDataState: {
										dataState: 0,
										monitorData: '0.000',
										monitorTypeName: '报警状态',
										monitorTypeUnit: '',
										mtMonitorObjectId: 'a7d42680744849f18dfe8a79e1ebb5b1',
										mtMonitorTypeId: 'ESLinkIotPlatformMonitorType0026',
									},
									mtMonitorObjectId: 'a7d42680744849f18dfe8a79e1ebb5b1',
									mtMonitorTypeId: 'ESLinkIotPlatformMonitorType0026',
									showType: 0,
								},
							],
						},
						value: 'a7d42680744849f18dfe8a79e1ebb5b1',
					},
				]
			},
		},
		lines: {
			type: Array,
			default: function () {
				return [
					{
						label: '折线',
						points: [
							{
								x: '1248.00',
								y: '132.00',
								key: 'img0_0',
							},
							{
								x: '1071.00',
								y: '133.00',
								key: 'img0_1',
							},
							{
								x: '1069.00',
								y: '81.00',
								key: 'img0_2',
							},
							{
								x: '1057.00',
								y: '79.00',
								key: 'img0_3',
							},
						],
					},
					{
						label: '折线',
						points: [
							{
								x: '957.00',
								y: '78.00',
								key: 'img1_0',
							},
							{
								x: '941.00',
								y: '79.00',
								key: 'img1_1',
							},
						],
					},
				]
			},
		},
	},
	computed: {
		stationId() {
			return this.$route.query.id
		},
	},
	// mounted() {
	// 	// this.init()
	// },
	data() {
		return {
			// 工艺图数据
			flowChartData: [],
			// 取水泵房下拉框
			getWaterSelect: '',
			getWaterSelectData: [],
			// 送水泵房下拉框
			sendWaterSelect: '',
			sendWaterSelectData: {
				// 0: 停用 1:启用
				status: 0,
				Hz: 0,
				current: 0,
				voltage: 0,
			},

			width: 0,
			height: 0,
			show: false,
		}
	},
	methods: {
		getImgUrl(value) {
			let src = 'https://eslink-iot.oss-cn-beijing.aliyuncs.com/WSD-abnormal-valve.svg'
			if (value == 1) {
				src = 'https://eslink-iot.oss-cn-beijing.aliyuncs.com/WSD-normal-valve.svg'
			}
			return src
		},
		init() {
			let img = new Image()
			img.src = this.processImage
			img.onload = () => {
				this.width = img.naturalWidth
				this.height = img.naturalHeight
				this.modalWidth = img.naturalWidth + 10 * 3
				this.show = true
			}
		},
		// 获取类名称
		getClassName(item, data) {
			let obj = {}
			let name = ''
			switch (item.showType) {
				case 4:
					name = 'singleValue'
					break
				case 5:
					name = 'noborder'
					break
				case 6:
					name = 'flow-status-alarm'
					if (item.value == 1) {
						obj['pump-status-normal'] = true
					} else {
						obj['pump-status-abnormal'] = true
					}
					break
				case 3:
					name = 'showTime'
					break

				default:
					switch (data.remarkName) {
						case '原水水质':
						case '出水水质':
						case '2滤后':
						case '1滤后':
							name = 'width-92'
							break
						case '进泥':
						case '次钠前加流量':
						case '前投加累积量次钠':
							name = 'width-70'
							break

						default:
							break
					}
					break
			}
			obj[name] = true
			return obj
		},
		getParentClassName(item) {
			let name = ''
			switch (item.showType) {
				case 5:
					name = 'noMargin'
					break
				case 4:
					name = 'flexCenter'
					break
				default:
					break
			}
			return name
		},
		// 获取折线的点集合
		getPoints(line) {
			let arr = []
			line.points.forEach(item => {
				arr.push(item.x + ',' + item.y)
			})
			return arr.join(' ')
		},
	},
	watch: {
		processImage: {
			handler() {
				this.init()
			},
		},
	},
}
</script>

<style lang="less" scoped>
.flow-chart {
	position: relative;
	// width: 1292px;
	// height: 831px;
	// margin-top: 60px;
	// margin-left: 10px;
	::v-deep {
		.ivu-select-dropdown {
			top: 32px !important;
		}
	}
	.legend {
		position: absolute;
		bottom: 10px;
		left: 0;
		width: 140px;
		padding: 12px 10px;
		background-color: #133a5e;
		border-radius: 8px;
		color: #fff;
		font-size: 14px;
		.legend-item {
			display: flex;
			align-items: center;
		}
		i {
			margin-right: 8px;
		}
		.icon-shuizhijianceyi {
			color: #40ffdd;
		}
		.icon-liuliangji1 {
			color: #51e0ff;
		}
		.icon-yaliji,
		.icon-kaiguan {
			color: #87fff1;
		}
		.icon-line {
			display: inline-block;
			width: 14px;
			height: 2px;
			&.water {
				background-color: #428dff;
			}
			&.sewage {
				background-color: #ff9d42;
			}
		}
	}
	.mb-8 {
		margin-bottom: 8px;
	}
	.tip-special-box {
		// width: 1px;
		/deep/.ivu-select-selection {
			height: 30px;
			.ivu-select-selected-value,
			.ivu-select-placeholder {
				height: 30px;
				line-height: 30px;
			}
		}
		.tips {
			.value {
				width: 62px;
				white-space: nowrap;
			}
			.width-92 {
				width: 92px;
			}
			.width-70 {
				width: 70px;
			}
			.singleValue {
				width: auto;
				// padding: 2px 8px;
				border: none;
				background-color: #081a339e;
			}
			.noborder {
				border: none;
				background: none;
				min-height: 18px;
				height: 18px;
				line-height: 18px;
			}
			.showTime {
				border: none;
				background: none;
				color: black;
				font-weight: 600;
			}
		}
		.noMargin {
			margin-bottom: 0px;
		}
		.flexCenter {
			justify-content: center;
		}
	}
	.flow-svg {
		position: absolute;
		width: 100%;
		height: 100%;
	}
	.polyline {
		fill: transparent;
		stroke: rgba(0, 0, 0, 0.4);
		stroke-width: 9;
		stroke-dasharray: 1000;
		stroke-dashoffset: 1000;
		animation: run 15s linear infinite;
	}
	@keyframes run {
		from {
			stroke-dasharray: 20, 5;
		}
		to {
			stroke-dasharray: 25, 5;
		}
	}

	.flow-alarm {
		border: 1px solid red !important;
		border-radius: 5px;
		font-size: 20px;
		cursor: pointer;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
		overflow: hidden;
		background-image: -webkit-gradient(linear, left top, left bottom, from(#fe6ca2), to(red));
		animation-timing-function: ease-in-out;
		animation-name: breathe;
		animation-duration: 700ms;
		animation-iteration-count: infinite;
		animation-direction: alternate;
	}
	.flow-status-alarm {
		width: 18px;
		height: 18px;
		border-radius: 9px;
		display: flex;
		align-content: center;
		justify-content: space-around;
		border: 1px solid red !important;
		cursor: pointer;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
		overflow: hidden;
		background-image: -webkit-gradient(linear, left top, left bottom, from(#fe6ca2), to(red));
		animation-timing-function: ease-in-out;
		animation-name: breathe;
		animation-duration: 1000ms;
		animation-iteration-count: infinite;
		animation-direction: alternate;
	}

	.pump-status-normal {
		border: 1px solid green !important;
		background-image: -webkit-gradient(linear, left top, left bottom, from(#6cfeba), to(green));
		animation-name: breathe-normal;
	}
	.pump-status-abnormal {
		border: 1px solid red !important;
	}

	@keyframes breathe {
		0% {
			opacity: 0.5;
			box-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
		}
		100% {
			opacity: 1;
			border: 1px solid rgba(59, 235, 235, 1);
			box-shadow: 0 1px 18px red;
		}
	}
	@keyframes breathe-normal {
		0% {
			opacity: 0.5;
			box-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
		}
		100% {
			opacity: 1;
			border: 1px solid rgba(59, 235, 235, 1);
			box-shadow: 0 1px 18px green;
		}
	}
}
</style>
