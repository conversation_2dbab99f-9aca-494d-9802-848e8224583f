<template>
	<div class="run-board">
		<!-- 地图 -->
		<map-bg ref="map" @open-detail="handleOpenDetail"></map-bg>
		<!-- 搜索框 -->
		<search ref="search"></search>
		<!-- 设备类别 -->
		<category-tab ref="category" @getInitView="getInitView" @open-detail="handleOpenDetail"></category-tab>
		<!-- 弹窗组件 -->
		<Modal v-if="modalRendered" :value="showDetailDialog" @on-visible-change="handleDialogClosed">
			<device-detail :deviceId="selectedDeviceId" class="rounded-dialog" />
		</Modal>
	</div>
</template>

<script>
import MapBg from './components/MapBg.vue'
import Search from './components/Search.vue'
import CategoryTab from './components/CategoryTab.vue'
import DeviceDetail from './detail/index.vue'

export default {
	name: 'runBoard',
	components: { MapBg, Search, CategoryTab, DeviceDetail },
	props: {},
	data() {
		return {
			showDetailDialog: false, // 控制弹窗显示
			modalRendered: false, // 新增状态：是否已经渲染过弹窗
			selectedDeviceId: null, // 当前选中的设备ID
		}
	},
	computed: {},
	created() {},
	mounted() {},
	methods: {
		getInitView(list) {
			this.$refs.map.initAMap(list)
		},
		handleOpenDetail(deviceId) {
			console.log('handleOpenDetail', deviceId)
			this.selectedDeviceId = deviceId
			this.modalRendered = true // 先标记为“已渲染”
			this.showDetailDialog = true
		},
		handleDialogClosed() {
			this.selectedDeviceId = null
		},
	},
}
</script>
<style lang="less" scoped>
.run-board {
	height: 100%;
	width: 100%;
	position: relative;
}
.rounded-dialog {
	min-width: 600px;
	min-height: 400px;
	border-radius: 16px;
	margin: 40px;
}
</style>
