<template lang="pug">
// 数据绑定
Modal#flowModel(
	:value='show',
	:styles='{ top: "15px" }',
	:mask-closable='false',
	:width='modalWidth',
	@on-cancel='handleCancel',
	title='预览'
)
	EsProcessFlow(:dataList='dataList', :lines='lines', :processImage='processImage')
	div(slot='footer', style='height: 0; border-bottom: none')
</template>

<script>
import { get2dProcessConfigList, get2dProcessList } from '@/api/setting'

import EsProcessFlow from '@/components/gc-process-flow'

export default {
	components: { EsProcessFlow },
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '新增',
		},
	},
	computed: {
		stationListLength() {
			return this.pointList.length
		},
	},
	mounted() {},
	data() {
		return {
			lines: [],
			dataList: [],
			processImage: '',
			modalWidth: '',
			height: '',
			width: '',
			id: '',
			stationCode: '',
			applicationName: '',
		}
	},
	methods: {
		init(row) {
			debugger
			this.id = row.id
			this.applicationName = row.applicationName
			this.stationCode = row.stationCode
			this.processImage = row.processImage
			this.get2DPlantFlowChart()
			this.get2dProcessList()
		},

		get2dProcessList() {
			this.lines = []
			get2dProcessList({
				applicationName: this.applicationName,
				stationCode: this.stationCode,
				processId: this.id,
			}).then(res => {
				if (!res.result.length) {
					return
				}
				const { flow } = res.result[0]
				if (flow) this.lines = JSON.parse(flow)
			})
		},

		// 获取折线的点集合
		getPoints(line) {
			let arr = []
			line.points.forEach(item => {
				arr.push(item.x + ',' + item.y)
			})
			return arr.join(' ')
		},
		// TODO: 水厂的工艺图也从接口中获取背景图片
		// 获取二维水厂工艺图
		get2DPlantFlowChart() {
			get2dProcessConfigList({
				processId: this.id,
			}).then(res => {
				const { dataList } = res.result
				// debugger
				// result.forEach(item => {
				// 	let devices = {}
				// 	item.selectOptions = []
				// 	item.groupConfigs.forEach(config => {
				// 		if (!devices[config.mtMonitorObjectId]) {
				// 			devices[config.mtMonitorObjectId] = []
				// 			item.selectOptions.push({
				// 				value: config.mtMonitorObjectId,
				// 				label: config.equipmentName,
				// 			})
				// 		}
				// 		devices[config.mtMonitorObjectId].push(config)
				// 	})

				// 	item.deviceIds = Object.keys(devices)
				// 	item.devices = devices
				// 	item.value = item.deviceIds[0]

				// 	if (Object.keys(devices).length > 1) {
				// 		item.selectGroup = true
				// 	}
				// })
				// console.log('result', result)
				this.dataList = dataList
				//deal data
			})
		},

		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},

		handleSelectChange() {},
	},
}
</script>
<style lang="less" scoped>
::v-deep {
	.ivu-modal-header {
		margin-top: 0;
	}
	.ivu-modal-body {
		padding: 0;
		// background: #00101f;
		background: #23313e;
		display: flex;
		justify-content: space-around;
		align-items: center;
		height: 990px;
	}
	.ivu-modal-content {
		padding: 10px !important;
		display: flex;
		flex-direction: column;
		flex: 1;
		width: 100%;
	}
	.ivu-modal-footer {
		border-top: none;
		height: auto;
		padding: 0;
		overflow: hidden;
	}
	.ivu-modal-header {
		border-bottom: none;
		height: auto;
		padding: 0;
		margin-top: -2px;
	}
}
.table_right {
	height: 32px;
	line-height: 32px;
}
.table_top {
	// border-top: 1px solid #f2f2f2;
	margin-top: 10px;
}
.flow-chart {
	position: relative;
	// margin-top: 60px;
	// margin-left: 10px;
	::v-deep {
		.ivu-select-dropdown {
			top: 32px !important;
		}
	}
	.legend {
		position: absolute;
		bottom: 10px;
		left: 0;
		width: 140px;
		padding: 12px 10px;
		background-color: #133a5e;
		border-radius: 8px;
		color: #fff;
		font-size: 14px;
		.legend-item {
			display: flex;
			align-items: center;
		}
		i {
			margin-right: 8px;
		}
		.icon-shuizhijianceyi {
			color: #40ffdd;
		}
		.icon-liuliangji1 {
			color: #51e0ff;
		}
		.icon-yaliji,
		.icon-kaiguan {
			color: #87fff1;
		}
		.icon-line {
			display: inline-block;
			width: 14px;
			height: 2px;
			&.water {
				background-color: #428dff;
			}
			&.sewage {
				background-color: #ff9d42;
			}
		}
	}
	.mb-8 {
		margin-bottom: 8px;
	}
	.tip-special-box {
		// width: 1px;
		::v-deep {
			.ivu-select-selection {
				height: 30px;
				.ivu-select-selected-value,
				.ivu-select-placeholder {
					height: 30px;
					line-height: 30px;
				}
			}
			.tips {
				justify-content: right;
				.value {
					width: 62px;
				}
			}
		}
	}
	.flow-svg {
		position: absolute;
		width: 100%;
		height: 100%;
	}
	.polyline {
		fill: transparent;
		stroke: rgba(0, 0, 0, 0.4);
		stroke-width: 9;
		stroke-dasharray: 1000;
		stroke-dashoffset: 1000;
		animation: run 15s linear infinite;
	}
}
</style>
