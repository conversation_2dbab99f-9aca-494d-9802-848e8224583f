<!--
 * @Description: 站点工艺流程管理
 * @Author: shenxh
 * @Date: 2022-03-22 13:15:24
 * @LastEditors: shenxh
 * @LastEditTime: 2022-03-22 13:37:47
-->
<template lang="pug">
.model-template
	es-search.es-search(col='4', :modules='moduleList', @on-search='handleSubForm')

	es-table.water-table(
		:columns='columns',
		:data='tableData',
		:loading='loading',
		border,
		showPage,
		:pageData='pageData',
		@on-page-num-change='handlePageNum',
		@on-page-size-change='handlePageSize'
	)
		template(slot-scope='{ row }', slot='action')
			Button(type='primary', @click='handleClick("edit", row)', size='small') 编辑
			Button(type='primary', @click='handleClick("delete", row)', size='small') 删除
			Button(type='primary', size='small', @click='handleClick("dataLabelConfig", row)') 数据标签配置
			Button(type='primary', size='small', @click='handleClick("technologyPreview", row)') 工艺预览
</template>

<script>
import { getTechnologySiteList } from '@/api/setting'

export default {
	name: 'model-template',
	components: {},
	props: {},
	data() {
		return {
			loading: false,
			tableData: [],
			moduleList: [
				{
					label: '',
					value: '1',
					isDefault: true,
					keys: ['stationName', 'processName', '', ''],
					model: {
						stationName: '',
						processName: '',
					},
					data: [
						{
							type: 1,
							key: 'stationName',
							formItemProps: {
								label: '站点名称',
								prop: 'stationName',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
						},
						{
							type: 1,
							key: 'processName',
							formItemProps: {
								label: '工艺流程模版名称',
								prop: 'processName',
								labelWidth: 110,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
						},
					],
				},
			],
			columns: [
				{
					title: '站点名称',
					align: 'center',
					key: 'stationName',
				},
				{
					title: '站点类型',
					align: 'center',
					key: 'stationType',
				},
				{
					title: '工艺流程模版名称',
					align: 'center',
					key: 'processName',
				},
				{
					title: '站点工艺流程图名称',
					align: 'center',
					key: 'stationProcessName',
				},
				{
					title: '操作',
					slot: 'action',
					align: 'center',
					width: 400,
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 30, 40, 50],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		this._getTableData()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 查询
		handleSubForm() {
			this._getTableData()
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum
			this._getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize
			this._getTableData()
		},

		// 点击事件
		handleClick(form, row) {
			this.$emit('tableClick', form, row)
		},

		_getTableData() {
			const { stationName, processName } = this.moduleList[0].model
			const { current: pageNum, pageSize } = this.pageData

			this._getTechnologySiteList({
				stationName,
				processName,
				pageNum,
				pageSize,
			})
		},

		_getTechnologySiteList(params) {
			this.loading = true
			getTechnologySiteList(params).then(res => {
				const data = res.result

				if (data) {
					this.loading = false
					this.tableData = data.list
					this.pageData.total = data.total
				}
			})
		},
	},
}
</script>

<style lang="less" scoped>
.model-template {
	.water-table {
		.ivu-btn {
			&:not(:last-child) {
				margin-right: 5px;
			}
		}
	}
}
</style>
