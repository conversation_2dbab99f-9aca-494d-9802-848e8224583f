<!--
 * @Description: 创建系统弹窗
 * @Author: shenxh
 * @Date: 2022-04-01 15:54:51
 * @LastEditors: shenxh
 * @LastEditTime: 2024-05-22 15:29:28
-->
<template lang="pug">
.create-system-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='650',
		v-model='showModal',
		:title='type === 1 ? "编辑" : "新增"',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :label-width='100', :rules='formRules', :key='formKey')
					FormItem(label='设备/对象', prop='stationName')
						Input(v-model='equipmentName', disabled, clearable)
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='原始位号', prop='originalCode')
							AutoComplete(
								v-model='formData.originalCode',
								:data='originalCodeList',
								transfer,
								clearable,
								placeholder='请输入并选择原始位号',
								style='width: 200px',
								@on-search='queryTransOriginalCode',
								@on-select='handleOriginalCode'
							)
						FormItem(label='原始描述', prop='originalName')
							Input(v-model='formData.originalName', clearable, style='width: 200px')
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='变量类型', prop='baseItemCode')
							Select(
								v-model='formData.baseItemCode',
								filterable,
								transfer,
								clearable,
								:loading='baseItemCodeCodeLoading',
								style='width: 200px'
							)
								Option(
									v-for='(item, index) in baseItemCodeList',
									:value='item.baseItemCode',
									:key='index',
									@click.native='handleBaseItemCode(item, index)'
								) {{ item.baseItemName }}
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='平台变量', prop='itemCode')
							Input(v-model='formData.itemCode', placeholder='请输入平台变量', clearable, style='width: 200px')
						FormItem(label='数据项名称', prop='itemName')
							Input(v-model='formData.itemName', placeholder='请输入数据项名称', clearable, style='width: 200px')
					FormItem(label='备注', prop='memo')
						Input(v-model='formData.memo', type='textarea', placeholder='请输入')
					FormItem(label='数据格式', prop='formulaType')
						Radio-group(v-model='formData.formulaType')
							Radio(label='0') 普通数据
							Radio(label='3') 状态数据
					FormItem(label='状态选项', prop='statusType', v-if='formData.formulaType == "3"')
						.self-box
							.head
								.head-item 状态
								.head-item 描述语
							.main
								.main-line(v-for='(item, index) in formData.formulas')
									.main-item
										Input(placeholder='请输入...', style='width: 80%', v-model='item.state')
									.main-item
										Input(placeholder='请输入...', style='width: 80%', v-model='item.desc')
									.main-item(style='width: 10%')
										img.icon(src='@/assets/icon/close.svg', @click='removeLine(index)')
							.addbtn(@click='addLine(formData.formulas)') +
					FormItem(label='读写状态', prop='readWriteFlag')
						Radio-group(v-model='formData.readWriteFlag')
							Radio(label='0') 只读
							Radio(label='1') 可读写

		template(slot='footer')
			Button(@click='handleClose') 取消
			Button(:loading='btnLoading', type='primary', @click='handleSubForm') 保存
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import { addTransRule, updateTransRule, queryTransOriginalCode } from '@/api/data-acquisition-config'
import { queryPage } from '@/api/base-item'

export default {
	name: 'create-system-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		WaterRow,
	},
	props: {
		showModal: Boolean,
		equipmentData: Object,
		collectRoadData: Object,
		data: Object,
		transChannelList: Array,
		type: Number, // 0-添加; 1-编辑
	},
	data() {
		const validateCode = (rule, value, callback) => {
			if (!value || !value.trim()) {
				callback(new Error('请输入'))
			} else {
				callback()
			}
		}
		return {
			formKey: 0,
			btnLoading: false,
			originalCodeLoading: false,
			baseItemCodeCodeLoading: false,
			formData: { formulaType: '0', readWriteFlag: '0', formulas: [] },
			baseItemCodeList: [],
			originalCodeList: [],
			originalCodeDataList: [],
			formRules: {
				originalCode: [
					{
						required: true,
						validator: validateCode,
						trigger: 'blur',
					},
				],
				baseItemCode: [
					{
						required: true,
						message: '请输入',
						trigger: 'change',
					},
				],
				itemCode: [
					{
						required: true,
						validator: validateCode,
						trigger: 'blur',
					},
				],
				itemName: [
					{
						required: true,
						validator: validateCode,
						trigger: 'blur',
					},
				],
			},
			testArr: [],
		}
	},
	computed: {
		equipmentName() {
			let str = ''

			if (this.type === 0) {
				const { stationName = '', stationCode = '' } = this.equipmentData

				if (stationCode) {
					str = `${stationName} (${stationCode})`
				}
			} else {
				const { stationName = '', stationCode = '' } = this.data

				if (stationCode) {
					str = `${stationName} (${stationCode})`
				}
			}

			return str
		},
	},
	watch: {
		collectRoadData() {
			this.getBaseDataItems()
		},
		'formData.formulaType': {
			handler() {
				if (this.type == '1') {
					this.formData.formulas = this.data.formulas || []
				} else {
					this.formData.formulas = []
				}
			},
		},
	},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 新增数据状态列
		addLine() {
			this.formData.formulas.push({ state: '', desc: '' })
		},
		removeLine(index) {
			this.formData.formulas.splice(index, 1)
		},
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				this.formData = { ...this.formData, ...this.data }
				console.log('新增', this.formData)
			} else {
				setTimeout(() => {
					this.$refs.form.resetFields()
					this.formKey++
				}, 200)
			}
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					Object.assign(this.formData, {
						sysCode: this.collectRoadData.sysCode,
						transChannelCode: this.collectRoadData.channelCode,
						stationCode: this.equipmentData.stationCode || '',
					})
					this.btnLoading = true
					if (this.formData.formulaType == '3') {
						if (this.formData.formulas.length == 0) {
							this.$Message.info('状态选项不能为空')
						} else {
							let num = 0
							for (let i = 0; i < this.formData.formulas.length; i++) {
								if (this.formData.formulas[i].state == '' || this.formData.formulas[i].desc == '') {
									num++
								}
							}
							if (num > 0) {
								this.$Message.info('状态选项相关内容不能为空')
								return false
							}
						}
					}
				}
				if (this.type === 0) {
					this.addTransRule(this.formData)
				} else {
					this.updateTransRule(this.formData)
				}
			})
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
			this.formData = {
				formulaType: '0',
				readWriteFlag: '0',
				formulas: [],
			}
		},

		// 点击原始位号
		handleOriginalCode(val) {
			const data = this.originalCodeDataList.find(item => item.originalCode === val) || {}
			const { originalName = '' } = data

			this.formData.originalName = originalName
		},

		// 点击变量类型
		handleBaseItemCode(data = {}) {
			const { id, baseItemName, baseItemCode } = data

			this.formData = {
				...this.formData,
				baseItemId: id,
				baseItemName,
				itemCode: baseItemCode + '_',
			}
		},

		// 新增采集规则
		addTransRule(params) {
			addTransRule(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		// 编辑采集规则
		updateTransRule(params) {
			updateTransRule(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		setResData(res) {
			const { responseCode } = res || {}

			if (responseCode === '100000') {
				this.$Message.success('操作成功')

				this.handleClose()
			}

			this.btnLoading = false
			this.$emit('submit-form', res)
		},

		// 原始位号下拉框查询
		queryTransOriginalCode(val = '') {
			this.originalCodeLoading = true
			queryTransOriginalCode({
				transChannelCode: this.collectRoadData.transChannelCode,
				originalCode: val.trim(),
				pageNum: 1,
				pageSize: 10,
			}).then(res => {
				const { result = {} } = res || {}
				const { list = [] } = result

				this.originalCodeList = list.map(item => item.originalCode)
				this.originalCodeDataList = list
				this.originalCodeLoading = false
			})
		},

		// 变量类型下拉框
		getBaseDataItems(val) {
			this.baseItemCodeCodeLoading = true
			queryPage({
				baseItemName: val,
				type: this.collectRoadData.sysCode,
				needPage: false,
				// pageNum: 1,
				// pageSize: 10,
			}).then(res => {
				const { result = {} } = res || {}
				const { list = [] } = result

				this.baseItemCodeList = list
				this.baseItemCodeCodeLoading = false
			})
		},
	},
}
</script>

<style lang="less" scoped>
.create-system-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
	.self-box {
		width: 100%;
		.head {
			width: 100%;
			display: flex;
			.head-item {
				width: 33.33%;
				text-align: center;
			}
		}
		.main {
			width: 100%;
			.main-line {
				width: 100%;
				display: flex;
				align-items: center;
				margin-bottom: 8px;
				.main-item {
					width: 33.33%;
					display: flex;
					align-items: center;
					justify-content: center;
					.icon {
						width: 20px;
					}
				}
			}
		}
		.addbtn {
			width: 61%;
			margin-left: 3%;
			height: 32px;
			line-height: 32px;
			text-align: center;
			border-radius: 10px;
			border: 1px solid #5cadff;
			font-size: 28px;
		}
	}
}
</style>
