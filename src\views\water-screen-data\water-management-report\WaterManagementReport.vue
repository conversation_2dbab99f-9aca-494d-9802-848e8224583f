<!--
 * @Description: 大屏水务经营填报
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-30 14:34:21
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-05-31 10:10:20
-->
<template lang="pug">
.water-management-report
	.water-management-report-content
		Tabs(v-model='tabValue', :animated='false')
			TabPane(label='经营数据填报', name='fill')
				fill-list
			TabPane(label='填报记录', name='record')
				record-list
</template>
<script>
import FillList from './modules/WaterManagementFill.vue'
import RecordList from './modules/WaterManagementRecord.vue'
export default {
	name: 'data-fill-management',
	components: {
		FillList,
		RecordList,
	},
	data() {
		return {
			tabValue: 'fill',
		}
	},
}
</script>
<style lang="less" scoped>
.water-management-report {
	height: 100%;
	padding: 16px;
	background-color: rgb(242 242 242) !important;
	&-content {
		height: 100%;
		padding: 16px;
		background: #fff;
	}
	::v-deep {
		.ivu-tabs {
			display: flex;
			flex-direction: column;
			height: 100%;
			.ivu-tabs-content {
				flex: 1;
			}
			.ivu-tabs-tabpane {
				height: 100%;
			}
		}
	}
}
</style>
