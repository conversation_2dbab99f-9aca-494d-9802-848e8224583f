<!--
 * @Description: 设备列表
 * @Author: shenxh
 * @Date: 2023-04-04 15:18:31
 * @LastEditors: shenxh
 * @LastEditTime: 2023-07-21 09:04:49
-->

<template lang="pug">
.equipment-list
	Input.search(
		v-model='searchVal',
		search,
		enter-button='查询',
		placeholder='请输入内容',
		size='small',
		@on-search='handleSearch'
	)
	.equipment-list-nocontent(v-if='loading')
		Spin
	ul.equipment-list-content(v-else)
		li(
			v-for='(item, index) in listData',
			:key='index',
			:class='{ active: selectedItem.id === item.id }',
			:title='`${item.stationName} (${item.stationCode})`'
		)
			Checkbox(v-model='item.active', @on-change='handleItem(item, index)') {{ item.stationName }} ({{ item.stationCode }})

	Page.page(
		:current='pageData.pageNum',
		:page-size='pageData.pageSize',
		:total='total',
		simple,
		size='small',
		@on-change='changePage'
	)
</template>

<script>
import { queryStationInfo } from '@/api/data-acquisition-config'

let selectObj = {}

export default {
	name: 'equipment-list',
	components: {},
	props: {
		collectRoadData: Object,
	},
	data() {
		return {
			loading: false,
			searchVal: '',
			selectedItem: {},
			pageData: {
				pageNum: 1,
				pageSize: 20,
			},
			listData: [],
			total: 0,
			selectKey: [],
		}
	},
	computed: {},
	watch: {
		collectRoadData: {
			handler() {
				this.selectedItem = {}
				this.pageData.pageNum = 1

				this.queryStationInfo(true)
			},
			deep: true,
		},
	},
	created() {
		// this.queryStationInfo()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		handleSearch() {
			this.queryStationInfo()
		},

		handleItem(itm) {
			const { active, stationCode } = itm
			console.log(itm, active)
			if (active) {
				selectObj[stationCode] = itm
			} else {
				delete selectObj[stationCode]
			}

			this.$emit('handle-item', Object.keys(selectObj), Object.values(selectObj))
		},

		changePage(pageNum) {
			this.pageData.pageNum = pageNum

			this.queryStationInfo()
		},

		queryStationInfo(flag) {
			this.loading = true
			queryStationInfo({
				transChannelCode: this.collectRoadData.transChannelCode,
				stationWord: this.searchVal,
				pageNum: this.pageData.pageNum,
				pageSize: this.pageData.pageSize,
			}).then(res => {
				const { result = {} } = res || {}
				const { list = [], total } = result

				this.listData = list.map((item, index) => {
					const { stationCode } = item
					if (flag && index === 0) {
						selectObj = {}
						selectObj[stationCode] = {
							active: true,
							...item,
						}

						return selectObj[stationCode]
					} else {
						if (selectObj[stationCode]) {
							return {
								active: selectObj[stationCode].active,
								...item,
							}
						} else {
							return item
						}
					}
				})
				this.total = total
				this.loading = false
				flag && this.$emit('handle-item', [list[0].stationCode], [list[0]])
			})
		},
	},
}
</script>

<style lang="less" scoped>
.equipment-list {
	display: flex;
	justify-content: space-between;
	flex-direction: column;
	width: 200px;
	height: 100%;
	padding: 10px 10px 0;
	border: 1px solid #e6e7f0;
	.search {
		flex-shrink: 0;
	}
	.equipment-list-nocontent {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-grow: 1;
	}
	.equipment-list-content {
		flex-grow: 1;
		margin-top: 10px;
		border: 1px solid #efefef;
		overflow: auto;
		li {
			display: flex;
			align-items: center;
			height: 50px;
			padding: 0 10px;
			&:not(:last-child) {
				border-bottom: 1px solid #efefef;
			}
			&:hover {
				background-color: #f5f7fa;
			}
			&.active {
				background-color: #ecf5ff;
			}
			span {
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				width: 100%;
			}
		}
		/deep/ .ivu-checkbox-default {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			width: 100%;
			height: 50px;
			line-height: 50px;
			.ivu-checkbox {
				margin-right: 6px;
			}
		}
	}
	.page {
		flex-shrink: 0;
	}
}
</style>
