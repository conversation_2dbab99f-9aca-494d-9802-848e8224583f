<!--
 * @Description: 视频绑定
 * @Author: shenxh
 * @Date: 2022-04-01 09:50:38
 * @LastEditors: shenxh
 * @LastEditTime: 2022-06-23 11:28:51
-->

<template lang="pug">
.video-binding
	.header-wrap
		es-header.header(title='视频绑定')
	.video-binding-wrap
		.video-binding-tree
			tree(
				ref='tree',
				:data='treeData',
				:load='loadNode',
				lazy,
				highlight-current,
				search-type='lazy',
				@node-click='nodeClick',
				@search='searchNode'
			)
		.video-binding-content
			es-search.es-search(
				col='4',
				:show-collapse='false',
				:modules='moduleList',
				@on-search='handleSearchBtn',
				@on-reset='handleResetBtn'
			)
			water-row.table-btn-wrap(justify='space-between', align='center')
				i
				Button(type='primary', :disabled='bindBtnDisabled', @click='handleBind') 绑定设置

			.security-platform-manage-content
				es-table.security-platform-manage-table(
					:columns='columns',
					:data='tableData',
					:loading='tableLoading',
					border,
					showPage,
					:pageData='pageData',
					@on-page-num-change='handlePageNum',
					@on-page-size-change='handlePageSize'
				)
					template(slot-scope='{ row }', slot='name')
						span.equipment-name {{ row.name }}
					template(slot-scope='{ row }', slot='metaData')
						.meta-data {{ row.metaData }}
					template(slot-scope='{ row }', slot='platformId')
						span {{ getPlatformName(row.platformId) }}
					template(slot-scope='{ row }', slot='action')
						Button(type='text', :style='{ color: "#3AA7D8" }', @click='handleUpd(row)', size='small') 编辑
						Poptip(transfer, confirm='', title='这条方案配置信息确定删除吗？', @on-ok='handleDel(row)')
							Button(type='text', :style='{ color: "#EC5151" }', size='small') 删除

	//- 绑定视频弹窗
	bind-video(
		v-model='showBindVideoModal',
		:selected-node='selectedNode',
		:equipment-form-data='moduleList[0].model',
		@bind-video='_getTableData()'
	)
	update-equipment-popup(
		v-model='showUpdateEquipmentPopup',
		:data='supdateEquipmentPopupData',
		@submit-form='_getTableData()'
	)
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import Tree from '@/components/gc-tree'
import BindVideo from './components/BindVideo.vue'
import UpdateEquipmentPopup from './components/UpdateEquipmentPopup.vue'
import { getBindEquipment, getSecurityArea, cancelBindVideoEqu } from '@/api/security-manage'

export default {
	name: 'video-binding',
	components: {
		WaterRow,
		Tree,
		BindVideo,
		UpdateEquipmentPopup,
	},
	props: {},
	data() {
		return {
			searchVal: '',
			supdateEquipmentPopupData: {},
			selectedNode: {},
			bindBtnDisabled: true,
			showUpdateEquipmentPopup: false,
			showBindVideoModal: false,
			tableLoading: false,
			tableData: [],
			treeData: [],
			platformList: [
				{
					label: '海康安防管理平台',
					value: 1,
				},
				{
					label: '大华安防管理平台',
					value: 2,
				},
			],
			moduleList: [
				{
					label: '',
					value: '1',
					isDefault: true,
					keys: ['name', '', '', ''],
					model: {
						name: '',
					},
					data: [
						{
							type: 1,
							key: 'name',
							formItemProps: {
								label: '设备名称',
								prop: 'name',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
					],
				},
			],
			columns: [
				{
					title: '序号',
					type: 'index',
					align: 'center',
					width: 65,
				},
				{
					title: '设备名称',
					slot: 'name',
				},
				{
					title: '设备编码',
					key: 'code',
				},
				{
					title: '所属平台',
					slot: 'platformId',
				},
				{
					title: '原始名称',
					key: 'sourceName',
				},
				{
					title: '元数据',
					slot: 'metaData',
				},
				{
					title: '操作',
					align: 'center',
					slot: 'action',
					width: 130,
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 50, 100],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	computed: {},
	watch: {
		showBindVideoModal(val) {
			if (!val) {
				setTimeout(() => {
					this._getTableData()
				}, 200)
			}
		},
	},
	created() {
		// this._getTreeData()
		this._getTableData()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 搜索按钮
		handleSearchBtn() {
			this.pageData.current = 1

			this._getTableData()
		},

		// 重置按钮
		handleResetBtn() {
			this.pageData.current = 1

			this._getTableData()
		},

		// 绑定按钮
		handleBind() {
			this.showBindVideoModal = true
		},

		// 编辑按钮
		handleUpd(row) {
			this.supdateEquipmentPopupData = row || {}
			this.showUpdateEquipmentPopup = true
		},

		// 删除按钮
		handleDel(row) {
			this._cancelBindVideoEqu(row.id)
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum

			this._getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.current = 1
			this.pageData.pageSize = pageSize

			this._getTableData()
		},

		// 节点被点击时的回调
		nodeClick(data) {
			this.pageData.current = 1
			this.selectedNode = data || {}
			if (data && data.id) {
				this.bindBtnDisabled = false
			} else {
				this.bindBtnDisabled = true
			}

			this._getTableData()
		},

		// 加载节点
		loadNode(node, resolve) {
			const { id } = node.data

			getSecurityArea({
				id,
				name: this.searchVal,
			}).then(res => {
				resolve(this._setTreeData(res))
			})
		},

		// 搜索节点
		searchNode(val) {
			this.searchVal = val

			this._getTreeData(val)
		},

		// 获取所属平台名称
		getPlatformName(id) {
			let arr = this.platformList.filter(item => {
				return String(item.value) === String(id)
			})

			return arr[0]?.label
		},

		// 视频设备取消绑定
		_cancelBindVideoEqu(id) {
			cancelBindVideoEqu({ id }).then(() => {
				this.$Message.success('删除成功')
				this.pageData.current = 1

				this._getTableData()
			})
		},

		// 获取表格数据
		_getTableData() {
			const { current: pageNum, pageSize } = this.pageData
			const formData = {
				...this.moduleList[0].model,
				// areaCode: this.selectedNode.code,
				areaId: this.selectedNode.id,
				pageNum,
				pageSize,
			}

			this.tableLoading = true
			getBindEquipment(formData)
				.then(res => {
					const data = res.result

					this.tableData = []
					this.pageData.total = 0
					this.tableLoading = false
					if (data) {
						this.tableData = data.list
						this.pageData.total = data.total
					}
				})
				.catch(() => {
					this.tableData = []
					this.pageData.total = 0
					this.tableLoading = false
				})
		},

		// 获取树形图数据
		_getTreeData(name, id) {
			getSecurityArea({
				name,
				id,
			}).then(res => {
				this.treeData = this._setTreeData(res) || []
			})
		},

		// 设置树形图数据
		_setTreeData(res) {
			let data = res.result.map(item => {
				return {
					...item,
					leaf: !item.hasChild,
				}
			})
			data.sort((a, b) => {
				return a.sort - b.sort
			})

			return data
		},
	},
}
</script>

<style lang="less" scoped>
.video-binding {
	width: 100%;
	height: 100%;
	overflow: hidden;
	.header-wrap {
		padding: 0 16px;
		.header {
			border-bottom: 1px solid #efefef;
		}
	}
	.video-binding-wrap {
		display: flex;
		width: 100%;
		height: calc(100% - 40px);
		.video-binding-tree {
			flex-shrink: 0;
			width: 200px;
			padding: 16px;
		}
		.video-binding-content {
			width: calc(100% - 200px);
			border-left: 1px solid #efefef;
			padding: 0 16px;
			height: 100%;
			.es-search {
				padding-top: 12px;
				height: 60px;
			}
			.table-btn-wrap {
				margin: 10px 0;
			}
			.security-platform-manage-content {
				width: 100%;
				height: calc(100% - 130px);
				.equipment-name {
					color: #1076b1;
				}
				.meta-data {
					max-height: 50px;
					overflow: auto;
				}
			}
		}
	}
}
</style>
