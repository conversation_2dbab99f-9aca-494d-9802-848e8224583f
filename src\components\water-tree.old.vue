<!--
 * @Description: 树形组件
 * @Author: shenxh
 * @Date: 2022-03-07 10:17:29
 * @LastEditors: shenxh
 * @LastEditTime: 2022-04-25 15:39:56
-->

<template lang="pug">
.water-tree
	Input.water-tree-search(
		v-if='search',
		v-model='searchVal',
		clearable,
		placeholder='请输入内容',
		@on-change='changeSearchVal'
	)
		Icon(type='ios-search', slot='prefix')
	.water-tree-content
		Spin(v-if='isLoading', fix)
		Tree.tree(ref='tree', :data='treeData', show-checkbox, @on-check-change='checkChange')
</template>

<script>
import { querySysConfigTree } from '@/api/common.js'

let timer = null

export default {
	name: 'water-tree',
	components: {},
	model: {
		prop: 'value',
		event: 'set-value',
	},
	props: {
		value: Array,
		sysCode: {
			type: String,
			default: 'dc',
		},
		multiple: Bo<PERSON>an,
		search: <PERSON><PERSON><PERSON>,
	},
	data() {
		return {
			isLoading: false,
			searchVal: '',
			searchNodeList: [],
			dataList: [],
			treeAllList: [], // 所有树形图数据以同级列表存放
			hasSelected: false,
			currentNode: {},
		}
	},
	computed: {
		treeData() {
			return this._getTree(this.dataList)
		},
	},
	watch: {
		sysCode() {
			this._querySysConfigTree()
		},
	},
	created() {
		this._querySysConfigTree()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 点击复选框时触发
		checkChange(selectedArr, currentNode) {
			let arr = this.multiple ? selectedArr : [currentNode]

			// 单选
			if (!this.multiple) {
				if (this.currentNode && this.currentNode.indexCode) {
					let data = this.treeData

					this.currentNode.indexCode.split('-').forEach(item => {
						let tmp = data[Number(item)]

						if (tmp.children && tmp.children.length) {
							data = tmp.children
						} else {
							data = tmp
						}
					})

					// 点击不同节点
					if (currentNode.indexCode !== this.currentNode.indexCode) {
						data.checked = false
					} else {
						if (this.value && this.value.length) {
							arr = []
						}
					}
				}
				this.currentNode = currentNode
			}

			this.$emit('set-value', arr)
			this.$emit('check-change', {
				selectedArr: [currentNode],
				currentNode,
			})
		},

		// 搜索
		changeSearchVal() {
			if (timer) {
				clearTimeout(timer)
				timer = null
			}

			timer = setTimeout(() => {
				this.searchNodeList = []
				for (let i = 0; i < this.treeAllList.length; i++) {
					const item = this.treeAllList[i]

					if (item.title.includes(this.searchVal)) {
						this.searchNodeList.push(item)
					}
				}

				this.$emit('change', this.searchVal)
			}, 500)
		},

		// 获取树状结构
		_getTree(list, idx = '') {
			if (!list || !list.length) {
				return []
			}

			return list.map((item, index) => {
				let tmpDataItem = {}
				let checked = false
				let selected = false
				let indexCode

				if (idx === '') {
					indexCode = index + ''
				} else {
					indexCode = idx + '-' + index
				}
				if (this.value && this.value.length) {
					this.value.forEach(dataItem => {
						const dataItemVal = dataItem.nodeType + '-' + dataItem.nodeId
						const treeItemVal = item.nodeType + '-' + item.id

						if (dataItemVal === treeItemVal) {
							checked = true
							tmpDataItem = dataItem
						}
					})
				}

				if (this.searchNodeList && this.searchNodeList.length) {
					this.searchNodeList.forEach(item1 => {
						if (item.uniqueCode == item1.uniqueCode) {
							selected = true
						}
					})
				}

				this.treeAllList.push(item)

				return {
					...item,
					title: item.title,
					expand: true,
					indexCode,
					nodeType: item.nodeType,
					nodeId: item.id, // 树的节点id
					id: tmpDataItem.id, // 数据保存后回显的id
					disabled: this.$route.query.type === 'read' || item.nodeType == 'system' || item.nodeType == 'area',
					checked,
					selected,
					render: (h, { data }) => {
						return h(
							'span',
							{
								style: {
									display: 'inline-block',
									width: '100%',
								},
							},
							[
								h('span', [
									h('i', {
										class: 'iconfont ' + this._getIcon(item.nodeType),
										style: {
											marginRight: '2px',
										},
									}),
									h('span', data.title),
								]),
							],
						)
					},
					children: this._getTree(item.children, indexCode),
				}
			})
		},

		// 获取icon
		_getIcon(type) {
			let icon = ''

			switch (type) {
				case 'system': // 系统
					icon = 'icon-xitong'
					break
				case 'area': // 区域
					icon = 'icon-quyu'
					break
				case 'station': // 站点
					icon = 'icon-zhandian-01'
					break
				default:
					// 设备
					icon = 'icon-shebei-01'
			}

			return icon
		},

		// 查询系统【站点树】
		_querySysConfigTree() {
			this.isLoading = true
			querySysConfigTree({
				sysCode: this.sysCode === '_all' ? 'dc' : this.sysCode || 'dc',
			})
				.then(res => {
					const data = res.result

					this.dataList = [data]
					this.isLoading = false
				})
				.catch(() => {
					this.dataList = []
					this.isLoading = false
				})
		},
	},
}
</script>

<style lang="less" scoped>
.water-tree {
	display: flex;
	flex-direction: column;
	height: 100%;
	.water-tree-search {
		flex-shrink: 0;
		margin: 16px 0;
	}
	.water-tree-content {
		position: relative;
		flex-grow: 1;
		overflow: auto;
	}
}
</style>
