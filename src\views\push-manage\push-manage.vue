<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-08-08 14:04:36
 * @LastEditors: houyan
 * @LastEditTime: 2024-08-08 14:35:18
-->
<template lang="pug">
.push-manage
	.push-manage-content
		Tabs(v-model='tabValue', :animated='false')
			TabPane(label='推送记录', name='record')
				Record(v-if='tabValue === "record"')
			TabPane(label='推送统计', name='statistics')
				Statistics(v-if='tabValue === "statistics"')
</template>
<script>
import Record from './modules/Records.vue'
import Statistics from './modules/Statistic.vue'
export default {
	name: '',
	components: {
		Record,
		Statistics,
	},
	data() {
		return {
			tabValue: 'record',
		}
	},
}
</script>
<style lang="less" scoped>
.push-manage {
	height: 100%;
	padding: 16px;
	background-color: rgb(242 242 242) !important;
	&-content {
		height: 100%;
		padding: 16px;
		background: #fff;
	}
	::v-deep {
		.ivu-tabs {
			display: flex;
			flex-direction: column;
			height: 100%;
			.ivu-tabs-content {
				flex: 1;
			}
			.ivu-tabs-tabpane {
				height: 100%;
			}
		}
	}
}
</style>
