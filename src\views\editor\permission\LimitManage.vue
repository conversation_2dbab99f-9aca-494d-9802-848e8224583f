<template lang="pug">
Row.h100.limit-manage
	Col.h100(span='4')
		.fill
			.left-title
				.title 角色列表
				Icon.icon(type='md-add', color='#149be6', size='20', @click='addRoleBtn')
			.list
				.item-tree(
					v-for='(item, index) in roleList',
					:key='index',
					@click='checkRole(index)',
					:class='{ active: active === index }'
				)
					.name {{ item.roleName }}
					.op
						Icon(type='ios-create-outline', color='blue', size='20', @click='editRoleBtn(item)')
						Icon(type='ios-trash-outline', color='blue', size='20', @click='deleteRoleBtn(item)')
	Col.h100(span='20')
		.fill
			.btnbox
				Button(type='primary', @click='handleBatchUnbindUser') 批量解绑
				Button.lf16(type='primary', @click='choseUserBtn') 选择用户
			Table(:columns='columns1', border, :data='tableData', :height='600', @on-selection-change='handleSelectChange')
				template(slot-scope='{ row, index }', slot='edit')
					Button(type='primary', @click='handleUnbindUser(row)') 解绑

	rolePopup(:show.sync='modalShow', @initList='queryUsers', ref='itemPopup')
	//- 权限表单
	permission-form(v-show='showForm', ref='form', @close='handleCloseAuth', @bindSuc='handleBindSuc')
</template>

<script>
import { queryUsers, unbindUser, deleteRole } from '@/api/promise.js'
import rolePopup from '@/views/editor/permission/components/RolePopup.vue'
import permissionForm from './components/PermissionForm.vue'

let selection = []
export default {
	name: 'limitManage',
	components: {
		rolePopup,
		permissionForm,
	},
	props: {},
	data() {
		return {
			active: 0,
			modalShow: false,
			showForm: false,
			choseUserShow: false,
			columns1: [
				{
					type: 'selection',
					width: 60,
					align: 'center',
				},
				{
					title: '用户名称',
					key: 'name',
				},
				{
					title: '操作',
					slot: 'edit',
					align: 'center',
					key: 'edit',
				},
			],
			tableData: [],
			roleList: [],
		}
	},
	computed: {},
	watch: {},
	created() {
		this.queryUsers()
	},
	mounted() {},
	methods: {
		queryUsers() {
			queryUsers().then(data => {
				this.roleList = data.result.map(item => {
					item.checked = false
					return item
				})
				this.tableData = data.result[this.active].users
			})
		},

		checkRole(index) {
			this.active = index
			this.tableData = this.roleList[this.active].users
		},
		addRoleBtn() {
			this.modalShow = true
		},
		// 表格选中
		handleSelectChange(val) {
			selection = val
		},
		// 选择用户按钮
		choseUserBtn() {
			this.showForm = true

			const currentRow = this.roleList[this.active]

			this.$refs.form.initData(currentRow)
		},
		// 编辑角色
		editRoleBtn(row) {
			this.modalShow = true
			this.$refs.itemPopup.setData(row)
		},
		// 删除角色
		deleteRoleBtn(row) {
			this.$Modal.confirm({
				title: '提示',
				content: '确定要删除这条数据?',
				loading: true,
				onOk: () => {
					this.$Modal.remove()
					if (row.users) {
						this.$Message.error('此角色已绑定人员！')
						return
					}
					deleteRole({ id: row.id }).then(() => {
						this.$Message.info('删除成功')
						this.queryUsers()
					})
				},
			})
		},
		handleCloseAuth() {
			this.showForm = false
		},
		handleBindSuc() {
			this.showForm = false
			this.queryUsers()
		},
		// 解绑
		handleUnbindUser(row) {
			const id = this.roleList[this.active].id
			const users = [
				{
					name: row.name,
					id: row.id,
				},
			]
			this.$Modal.confirm({
				title: '提示',
				content: '确定要解绑这条数据?',
				loading: true,
				onOk: () => {
					this.$Modal.remove()
					unbindUser({ id, users }).then(() => {
						this.$Message.info('解绑成功')
						this.queryUsers()
					})
				},
			})
		},
		// 批量解绑
		handleBatchUnbindUser() {
			const id = this.roleList[this.active].id
			this.$Modal.confirm({
				title: '提示',
				content: '确定要批量解绑这些数据?',
				loading: true,
				onOk: () => {
					this.$Modal.remove()
					unbindUser({ id, users: selection }).then(() => {
						this.$Message.info('解绑成功')
						this.queryUsers()
					})
				},
			})
		},
	},
}
</script>

<style lang="less" scoped>
.h100 {
	height: 100%;
}
.fill {
	width: 100%;
	height: 100%;
	background: #fff;
	display: flex;
	flex-direction: column;
	padding: 16px;
}
.left-title {
	// padding: 0 16px;
	height: 50px;
	border-bottom: 1px solid #d1d1de;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding-top: 12px;
	.title {
		position: relative;
		font-size: 16px;
		padding-left: 12px;
		&::before {
			position: absolute;
			content: '';
			background: #149be6;
			width: 4px;
			height: 12px;
			left: 4px;
			top: 6px;
			border-radius: 4px;
		}
	}
	.icon {
		cursor: pointer;
	}
}
.btnbox {
	width: 100%;
	height: 50px;
	display: flex;
	justify-content: flex-end;
	align-items: center;
}
.tree {
	flex: 1;
}
.lf16 {
	margin-left: 16px;
}
.flex_b {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.list {
	flex: 1;
	.item-tree {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		height: 40px;
		cursor: pointer;
		padding: 0 8px;
		&:hover {
			background-color: #cfe9fa;
		}
		&.active {
			background-color: #cfe9fa;
		}
		.name {
			font-size: 14px;
		}
		.op {
			display: flex;
			align-items: center;
			cursor: pointer;
		}
	}
}
</style>
