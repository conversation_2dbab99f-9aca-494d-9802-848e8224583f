<template>
	<div class="table-wrap">
		<div class="table-second">
			<span class="title">{{ title }}</span>
			<div class="content">
				<Input clearable v-model="message" class="content-input" placeholder="站点名称"></Input>
				<Button type="primary" @click="handleSearch">查询</Button>
			</div>
		</div>
		<Table
			border
			highlight-row
			height="260"
			:columns="showSelectation ? selectColumn : column"
			:data="data"
			@on-selection-change="handleSelect"
		></Table>
		<div class="page-container">
			<div>共{{ pageData.total }}条记录</div>
			<Select v-model="model1" @on-change="handlePageSizeChange">
				<Option v-for="(item, index) in pageSizeList" :value="item.value" :key="index">
					{{ item.label }}
				</Option>
			</Select>
			<Page
				simple
				show-total
				:page-size="pageSize"
				:total="pageData.total"
				:current="pageData.current"
				@on-change="handlePageChange"
			></Page>
		</div>
	</div>
</template>

<script>
export default {
	name: 'table-model',

	components: {},

	props: {
		data: {
			type: Array,
			default: function () {
				return []
			},
		},
		show: {
			type: Boolean,
			default: false,
		},
		showSelectation: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '已关联',
		},
		pageData: {
			type: Object,
			default: function () {
				return {
					total: 0,
					current: 1,
				}
			},
		},
	},

	computed: {},

	watch: {},

	data() {
		return {
			loading: false,
			message: '',
			column: [
				{
					title: '所属项目',
					key: 'applicationName',
				},
				{
					title: '站点名称',
					key: 'stationName',
				},
			],
			selectColumn: [
				{
					type: 'selection',
					width: 40,
					align: 'center',
				},
				{
					title: '所属项目',
					key: 'applicationName',
				},
				{
					title: '站点名称',
					key: 'stationName',
				},
			],
			pageSize: 10,
			model1: 10,
			pageSizeList: [
				{
					value: 10,
					label: '10条/页',
				},
				{
					value: 20,
					label: '20条/页',
				},
				{
					value: 50,
					label: '50条/页',
				},
			],
		}
	},

	methods: {
		// 模态窗口状态变化事件
		modalStateChange(state) {
			if (state === false) {
				this.$emit('cancel')
			}
		},

		// 关闭模态窗
		clickCancel() {
			this.$emit('cancel')
		},
		// 搜索
		handleSearch() {
			this.$emit('search', this.message)
		},
		//多选
		handleSelect(selection, row) {
			// console.log(selection, row)
			this.$emit('on-select', { selection, row })
		},
		// 分页回调
		handlePageChange(pageNum) {
			this.$emit('on-change', pageNum)
		},
		//修改分页大小
		handlePageSizeChange(pageSize) {
			this.pageSize = pageSize
			this.$emit('on-page-size-change', pageSize)
		},
	},
}
</script>

<style lang="less" scoped>
.table-wrap {
	display: flex;
	flex-direction: column;
	.table-first {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		.content {
			flex: 1;
			border: 1px solid #cccc;
			background: #f8f8f8;
			padding: 4px;
			border-radius: 4px;
		}
		.title {
			width: 100px;
		}
	}
	.table-second {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		.title {
			width: 100px;
			// color: #459dee;
		}
		.content {
			flex: 1;
			display: flex;
		}
		.content-input {
			margin-right: 10px;
		}
	}
	.page-container {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 5px;
	}
	::v-deep {
		.content-input {
			margin-right: 10px;
		}
		.ivu-select {
			width: 24%;
		}
		.ivu-page {
			display: flex;
			justify-content: flex-end;
		}
	}
}
</style>
