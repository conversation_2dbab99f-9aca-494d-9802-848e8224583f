<!--
 * @Description: 海康视频插件
 * @Author: shenxh
 * @Date: 2022-09-20 09:42:04
 * @LastEditors: shenxh
 * @LastEditTime: 2023-12-14 13:52:22
-->

<template lang="pug">
.video-native-hk
	#playWnd(v-show='isPluginInstall', ref='videoRef')
	video-tip(v-if='!isPluginInstall')
</template>

<script>
import VideoTip from './VideoTip.vue'
import { WebControl } from '@/assets/js/hkvideo-plateform/jsWebControl-1.0.0.min.js'
import { JSEncrypt } from '@/assets/js/hkvideo-plateform/jsencrypt.min.js'
import { getSecurityPlatform } from '@/api/security-manage'

var initCount = 0
let pubKey = ''

export default {
	name: 'video-native-hk',
	components: {
		VideoTip,
	},
	props: {
		list: Array,
		// 播放模式：0-预览，1-回放
		playMode: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			isPluginInstall: true,
			initData: {},
			oWebControl: null,
			wndId: 1,
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {
		// 步骤1：监听父页面的消息
		window.addEventListener('message', e => {
			console.log(e)
			if (e && e.data) {
				if (e.data.action && e.data.action === 'updateInitParam') {
					if (e.data.page && e.data.page.show) {
						this.oWebControl && this.oWebControl.JS_ShowWnd()
					} else {
						this.oWebControl && this.oWebControl.JS_HideWnd()
					}
				}
			}
		})
		this._getSecurityPlatform()
		// this.initPlugin()
		//  离开页面销毁视频
		this.$once('hook:beforeDestroy', () => {
			window.removeEventListener('resize', this.resizeVideo())
			this.WebControlDistory()
		})

		// 监听页面窗口变化，调整视频大小
		window.onresize = () => {
			this.resizeVideo()
		}
	},
	beforeDestroy() {
		this.oWebControl && this.oWebControl.JS_HideWnd()
		this.WebControlDistory()
	},
	methods: {
		// 获取平台数据
		_getSecurityPlatform() {
			this.loading = true
			getSecurityPlatform({
				// id: 1, // 乐清海康安防管理
				pageNum: 1,
				pageSize: 20,
			}).then(res => {
				const data = res.result

				if (data && data.list && data.list.length) {
					this.initData = data.list[0]

					this.initPlugin()
				}
			})
		},
		initPlugin() {
			this.oWebControl = this.WebControlInit(
				'playWnd',
				this.cbConnectSuccess,
				this.cbConnectError,
				this.cbConnectClose,
			)
		},
		resizeVideo() {
			if (this.oWebControl !== null) {
				this.oWebControl.JS_Resize(
					parseInt(this.$refs.videoRef.offsetWidth),
					parseInt(this.$refs.videoRef.offsetHeight),
				)
			}
		},
		/**
		 * video 初始化及配置
		 * @param {*video挂载的dom id} id
		 * @param {*连接成功的回调} cbConnectSuccess
		 * @param {*连接报错的回调} cbConnectError
		 * @param {*连接关闭的回调} cbConnectClose
		 */
		WebControlInit(id, cbConnectSuccess, cbConnectError, cbConnectClose) {
			return new WebControl({
				szPluginContainer: id,
				iServicePortStart: 15900, // 对应 LocalServiceConfig.xml 中的ServicePortStart值
				iServicePortEnd: 15900, // 对应 LocalServiceConfig.xml 中的ServicePortEnd值
				szClassId: '23BF3B0A-2C56-4D97-9C03-0CB103AA8F11', // 用于IE10使用ActiveX的clsid
				cbConnectSuccess: cbConnectSuccess,
				cbConnectError: cbConnectError,
				cbConnectClose: cbConnectClose,
			})
		},
		//销毁视频控件
		WebControlDistory() {
			if (this.oWebControl) {
				this.oWebControl.JS_DestroyWnd().then(
					() => {
						console.log('JS_DestroyWnd')
					},
					() => {},
				)
				this.oWebControl.JS_StopService('window').then(() => {
					this.oWebControl.JS_Disconnect().then(
						() => {
							console.log('JS_Disconnect')
						},
						() => {},
					)
				})

				this.oWebControl = null
			}
		},
		// 监听视频控件的事件
		cbIntegrationCallBack(oData) {
			console.log(oData, '监听')
			const { responseMsg = {} } = oData || {}
			const { msg = {} } = responseMsg
			const { wndId = 1 } = msg
			const code = oData.responseMsg.msg.cameraIndexCode

			if (oData.responseMsg.type === 1) {
				this.$emit('cb-integration', code)
			}
			if (oData.responseMsg.type === 2 && oData.responseMsg.msg.wndId === this.list.length) {
				this.$emit('cb-integration', code)
			}

			this.wndId = wndId
		},
		cbConnectSuccess() {
			const { offsetTop = 96, offsetLeft = 200 } = this.$route.query

			this.oWebControl
				.JS_StartService('window', {
					// WebControl实例创建成功后需要启动服务
					dllPath: './VideoPluginConnect.dll', // 值"./VideoPluginConnect.dll"写死
				})
				.then(
					() => {
						this.oWebControl.JS_SetWindowControlCallback({
							// 设置窗口控制回调
							cbIntegrationCallBack: this.cbIntegrationCallBack,
						})
						this.oWebControl.oDocOffset.top = +offsetTop
						this.oWebControl.oDocOffset.left = +offsetLeft
						// 启动插件服务成功
						this.oWebControl
							.JS_CreateWnd(
								'playWnd',
								parseInt(this.$refs.videoRef.offsetWidth),
								parseInt(this.$refs.videoRef.offsetHeight),
								{
									bEmbed: true,
									cbSetDocTitle: function () {
										this.oWebControl && (this.oWebControl._pendBg = false)
									},
								},
							)
							.then(() => {
								//JS_CreateWnd创建视频播放窗口，宽高可设定
								this.isPluginInstall = true
								console.log('JS_CreateWnd success')
								this.initVideo() // 创建播放实例成功后初始化
							})
					},
					error => {
						// 启动插件服务失败
						console.log(error)
					},
				)
		},
		cbConnectError() {
			console.log('cbConnectError')
			this.oWebControl = null
			this.isPluginInstall = false
			document.querySelector('#playWnd').html('插件未启动，正在尝试启动，请稍候...')
			WebControl.JS_WakeUp('VideoWebPlugin://') // 程序未启动时执行error函数，采用wakeup来启动程序
			initCount++
			if (initCount < 3) {
				setTimeout(() => {
					this.initPlugin()
				}, 3000)
			} else {
				document.querySelector('#playWnd').html('插件启动失败，请检查插件是否安装！')
			}
			console.error('确认本地进程是否已安装并开启成功！')
		},
		cbConnectClose() {
			console.log('cbConnectClose')
			this.oWebControl = null
		},
		// 视频初始化
		initVideo() {
			this.getPubKey(() => {
				this.initPlayVideo()
			})
		},
		initPlayVideo() {
			const { appkey, secret, address: ip } = this.initData
			console.log('init-video-success')
			// var appkey = '21246478' //综合安防管理平台提供的appkey，必填
			// var secret = this.setEncrypt('XM8IGIT8WuSeKr6eOcYC') //综合安防管理平台提供的secret，必填
			// var ip = '************' //综合安防管理平台IP地址，必填
			var playMode = this.playMode //初始播放模式：0-预览，1-回放
			// var port = 443 //综合安防管理平台端口，若启用HTTPS协议，默认443
			var snapDir = 'D:\\SnapDir' //抓图存储路径
			var videoDir = 'D:\\VideoDir' //紧急录像或录像剪辑存储路径
			var layout = this.playMode === 0 ? '3x3' : '1x1' //playMode指定模式的布局
			var enableHTTPS = 1 //是否启用HTTPS协议与综合安防管理平台交互，这里总是填1
			var encryptedFields = 'secret' //加密字段，默认加密领域为secret
			var showToolbar = 1 //是否显示工具栏，0-不显示，非0-显示
			var showSmart = 1 //是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示

			////////////////////////////////// 请自行修改以上变量值	////////////////////////////////////

			this.oWebControl
				.JS_RequestInterface({
					funcName: 'init',
					argument: JSON.stringify({
						appkey, //API网关提供的appkey
						secret: this.setEncrypt(secret), //API网关提供的secret
						ip: ip.split(':')[0], //API网关IP地址
						playMode: playMode, //播放模式（决定显示预览还是回放界面）
						port: Number(ip.split(':')[1]), //端口
						snapDir: snapDir, //抓图存储路径
						videoDir: videoDir, //紧急录像或录像剪辑存储路径
						layout, //布局
						enableHTTPS: enableHTTPS, //是否启用HTTPS协议
						encryptedFields: encryptedFields, //加密字段
						showToolbar: showToolbar, //是否显示工具栏
						showSmart: showSmart, //是否显示智能信息
						buttonIDs: '0,16,257,258',
						toolBarButtonIDs: '2048,2049,2050,2304,2306,2305,2307,2308,2309,4096,4099,4098,4097,4100', //4097是切换布局的
					}),
				})
				.then(() => {
					// this.playVideo()
					// this.resizeVideo()
					// this.oWebControl.JS_CuttingPartWindow(372, 200) // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
				})
		},
		//获取公钥
		getPubKey(callback) {
			this.oWebControl
				.JS_RequestInterface({
					funcName: 'getRSAPubKey',
					argument: JSON.stringify({
						keyLength: 1024,
					}),
				})
				.then(oData => {
					if (oData.responseMsg.data) {
						pubKey = oData.responseMsg.data
						callback()
					}
				})
		},
		setEncrypt(value) {
			var encrypt = new JSEncrypt()
			encrypt.setPublicKey(pubKey)
			return encrypt.encrypt(value)
		},
		// 播放视频
		playVideo(wndId, cameraIndexCode = 'fbe3bdcff2774bfda75ecf05b5214e7e') {
			const params = {
				cameraIndexCode, //监控点编号
				ezvizDirect: 0,
				streamMode: 0, //主⼦码流标识：0-主码流，1-⼦码流
				transMode: 1, //传输协议：0-UDP，1-TCP
				gpuMode: 0, //是否启⽤GPU硬解，0-不启⽤，1-启⽤
				wndId: wndId || this.wndId, //可指定播放窗⼝
				cascade: 1,
			}
			let obj = {}

			if (this.playMode === 1) {
				obj = {
					recordLocation: 1, // 录像存储位置: 0-中心存储 1-设备存储
					startTimeStamp:
						new Date(this.$moment().subtract(30, 'days').format('YYYY-MM-DD') + ' 00:00:00').getTime() /
						1000,
					endTimeStamp: new Date(this.$moment().format('YYYY-MM-DD') + ' 23:59:59').getTime() / 1000,
				}
			}
			this.oWebControl
				.JS_RequestInterface({
					funcName: this.playMode === 0 ? 'startPreview' : 'startPlayback',
					argument: JSON.stringify({ ...params, ...obj }),
				})
				.then(() => {
					console.log('预览成功')
				})
		},
		//停⽌全部预览
		stopAllPreview() {
			this.oWebControl.JS_RequestInterface({
				funcName: 'stopAllPreview',
			})
		},
	},
}
</script>

<style lang="less" scoped>
.video-native-hk {
	width: 100%;
	height: 100%;
	#playWnd {
		width: 100%;
		height: 100%;
	}
}
</style>
