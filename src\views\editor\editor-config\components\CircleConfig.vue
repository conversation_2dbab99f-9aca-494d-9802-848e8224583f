<template lang="pug">
.text-config(v-if='value.controls[index]')
	.text-config-title 值属性
	water-row.water-margin-top-16(justify='flex-start', align='center')
		.water-margin-right-4.text 填充色
		ColorPicker(
			v-if='value.controls[index]',
			v-model='value.controls[index].fill',
			@on-pick-success='handleChange("fill")'
		)

	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.text x
		Slider(
			v-if='value.controls[index]',
			@on-input='handleChange("left")',
			v-model='value.controls[index].left',
			show-input,
			:max='value.controls[index].canvasWidth',
			show-tip='never',
			style='width: 200px'
		)
	water-row.water-margin-top-16(justify='space-between', align='center')
		.water-margin-right-4.text y
		Slider(
			v-if='value.controls[index]',
			@on-input='handleChange("top")',
			:max='value.controls[index].canvasHeight',
			v-model='value.controls[index].top',
			show-input,
			show-tip='never',
			style='width: 200px'
		)
</template>
<script>
import WaterRow from '@/components/gc-water-row'
export default {
	name: '',
	components: { WaterRow },
	props: {
		value: {
			type: Object,
			default: function () {
				return {}
			},
		},
		index: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			backgroundColor: '#19be6b',
			// value: 60,
			showType: '',
			itemValue: '',
			dateTime: new Date(),
			formatText: 'yyyy-MM-dd',
			list: [
				{ text: 'yyyy-MM-dd HH:mm:ss', value: 'yyyy-MM-dd HH:mm:ss' },
				{ text: 'yyyy/MM/dd HH:mm:ss', value: 'yyyy/MM/dd HH:mm:ss' },
				{ text: 'yyyy-M-d H:mm', value: 'yyyy-M-d H:mm' },
				{ text: 'yyyy/M/d HH:mm', value: 'yyyy/M/d HH:mm' },
				{ text: 'yyyy-MM-dd', value: 'yyyy-MM-dd' },
				{ text: 'yyyy/MM/dd', value: 'yyyy/MM/dd' },
				{ text: 'HH:mm:ss', value: 'HH:mm:ss' },
				{ text: 'yyyy/MM/dd', value: 'yyyy/MM/dd' },
				{ text: 'yyyy年MM月dd日', value: 'yyyy年MM月dd日' },
				{ text: 'yyyy年M月dd日', value: 'yyyy年M月d日' },
			],
		}
	},
	methods: {
		handleChange(type) {
			this.$emit('changeControl', type, this.value.controls[this.index][type])
		},
	},
}
</script>
<style lang="less" scoped>
.text-config {
	padding: 8px;
	&-title {
		color: #000;
		font-weight: bold;
	}
}
::v-deep {
	.ivu-input-number {
		width: 56px;
		margin-top: 0;
	}
	.ivu-input {
		border: none;
		background: transparent;
		border-radius: 0;
		cursor: pointer;
		height: 28px;
		&:focus {
			box-shadow: none;
		}
	}
}
</style>
