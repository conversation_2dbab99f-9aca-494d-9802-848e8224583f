<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: heliping
 * @Date: 2023-09-18 09:02:39
 * @LastEditors: heliping
 * @LastEditTime: 2023-09-18 14:24:54
-->
<template lang="pug">
.allocation-modal
	water-row.water-margin-top-8.water-margin-left-8.water-margin-right-8(justify='space-between', align='center')
		span.title 权限分配
		Icon(type='md-close', size='24', style='cursor: pointer', @click='handleClose')
	.header(@click='handleNewFloder')
		Icon.add(custom='iconfont icon-tianjiaa01')
		span 添加新权限
	.content
		.content-item(v-for='(item, index) in authList', :key='item.sourceCode')
			water-row.item-row(justify='space-between', align='center')
				div 
					span.item-row-title-name 角色名称
					span {{ item.roleName }}
				.content-icon
					Icon.water-margin-right(type='md-create', size='20', @click.stop='handleEdit(item, index)')
					Icon(type='md-trash', size='20', @click.stop='handleDelete(item)')
			water-row.item-row(justify='space-between', align='center')
				div 
					span.item-row-title-name 可见工艺图数
					span {{ item.processFlowCharts ? item.processFlowCharts.length : 0 }}个
				div {{ item.authType === 'WRITE' ? '读写' : '只读' }}
</template>
<script>
import WaterRow from '@/components/gc-water-row'

// import { Input } from '@eslink/esvcp-pc-ui'
import { queryAuthInfo, deleteAuth } from '@/api/editor'
export default {
	name: '',
	components: { WaterRow },
	props: {
		bindId: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			// 应用端
			newRoleName: '',
			showForm: false,
			authList: [],
		}
	},
	mounted() {
		this.initData()
	},
	methods: {
		// 初始化
		initData() {
			queryAuthInfo().then(res => {
				const { result } = res
				this.authList = result
			})
		},

		// 新增操作
		handleNewFloder() {
			// this.showForm = true
			this.$emit('close', 'add')
			// this.$nextTick(() => {
			// 	this.$refs.form.initData()
			// })
			// this.$emit('changeStatue', 1)
		},
		// 编辑操作
		handleEdit(item, index) {
			console.log(item, index)
			// this.showForm = true
			this.$emit('close', 'edit', item)
			// this.$emit('changeStatue', 1)
		},
		// 删除操作
		handleDelete(item) {
			// const { id } = item
			// if (id) {
			this.$Modal.confirm({
				title: '提示',
				content: '确定要删除这条数据?',
				loading: true,
				onOk: () => {
					this.$Modal.remove()
					deleteAuth({ id: item.id }).then(() => {
						this.$Message.info('删除权限成功')
						this.initData()
					})
				},
			})
			// }
		},
		// 关闭弹框
		handleClose() {
			this.$emit('close', 'closeInfo')
		},
	},
}
</script>
<style lang="less" scoped>
.allocation-modal {
	position: fixed;
	right: 100px;
	top: 56px;
	// margin-left: -200px;
	width: 400px;
	// max-height: 400px;
	background-color: #fff;
	border: 1px solid #e8e8e8;
	z-index: 10;
	box-shadow: 0px 8px 24px -4px rgba(24, 39, 75, 0.08);
}
.title {
	font-size: 18px;
	font-weight: 700;
}
.header {
	padding: 8px;
	font-size: 16px;
	display: flex;
	align-items: center;
	cursor: pointer;
	.add {
		font-size: 24px;
		font-weight: 600;
		color: #232222;
		margin-right: 6px;
	}
}
.content {
	max-height: 400px;
	overflow-y: auto;
}
.content-item {
	margin: 8px;
	padding: 6px;
	// font-size: 18px;
	font-size: 14px;
	border-radius: 4px;
	color: #4b4b4b;
	background: #efefef;
	.item-row {
		margin: 6px 0;
	}
	.item-row-title-name {
		// color: #1f1e1e;
		font-weight: 600;
		margin-right: 8px;
	}
	.content-icon {
		cursor: pointer;
	}
}
</style>
