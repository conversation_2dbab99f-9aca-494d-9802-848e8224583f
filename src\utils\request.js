/*
 * @Descripttion:
 * @version:
 * @Author: heliping
 * @Date: 2021-07-06 15:04:10
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2024-08-21 15:01:39
 */
import { ESVHttpUtil } from 'esvcp-pc'
import { Message } from '@eslink/esvcp-pc-ui'
// import store from '@/vuex/store'

const axios = ESVHttpUtil.axios
axios.defaults.timeout = 60000
axios.defaults.baseURL = process.env.VUE_APP_API
axios.defaults.withCredentials = true

// 请求拦截
axios.interceptors.request.use(
	async config => {
		const online = process.env.VUE_APP_ENV === 'online'

		if (process.env.VUE_APP_API_FLAG) {
			if (config.url.indexOf('/iwater/') > -1 || config.url.indexOf('/screen/') > -1) {
				config.baseURL = online ? process.env.VUE_APP_PLAT_DP_URL : window.VUE_PLAT_DP_URL
			}
			if (config.url.indexOf('/pos/') > -1) {
				config.baseURL = online ? process.env.VUE_APP_PLAT_DD_URL : window.VUE_PLAT_DD_URL
			}
			if (config.url.indexOf('/dma/') > -1) {
				config.baseURL = online ? process.env.VUE_APP_PLAT_DMA_URL : window.VUE_PLAT_DMA_URL
			}
			if (config.url.indexOf('/swetbc/') > -1) {
				config.baseURL = online ? process.env.VUE_APP_PLAT_ETBC_URL : window.VUE_PLAT_ETBC_URL
			}
			if (
				config.url.indexOf('/security/') > -1 ||
				config.url.indexOf('/alarmFilter/') > -1 ||
				config.url.indexOf('/alarm/') > -1 ||
				config.url.indexOf('/alarmConfig/') > -1 ||
				config.url.indexOf('/waterPlat/') > -1 ||
				config.url.indexOf('/transRule/') > -1 ||
				config.url.indexOf('/influxDb/') > -1 ||
				config.url.indexOf('/stationItemDataFix/') > -1 ||
				config.url.indexOf('/processFlowChart/') > -1 ||
				config.url.indexOf('/backgroundImage/') > -1 ||
				config.url.indexOf('/sysConfig/') > -1 ||
				config.url.indexOf('/processAuth/') > -1 ||
				config.url.indexOf('/stationItemDataRepair/') > -1
			) {
				config.baseURL = online ? process.env.VUE_APP_PLAT_URL : window.VUE_PLAT_URL
			}
			if (config.url.match(/^\/ssy/)) {
				config.baseURL = online ? process.env.VUE_APP_PLAT_EG_URL : window.VUE_PLAT_EG_URL
			}
			if (config.url.match(/^\/villageWater/)) {
				config.baseURL = online ? process.env.VUE_APP_PLAT_DC_URL : window.VUE_PLAT_DC_URL
			}
			if (config.url.indexOf('/smart/') > -1) {
				config.baseURL = online ? process.env.VUE_APP_PLAT_URL : window.VUE_PLAT_URL
			}
		}
		if (config.hideLoading) {
			return config
		}
		// store.commit('loadingStart')
		return config
	},
	error => {
		return Promise.reject(error)
	},
)

// 响应拦截
axios.interceptors.response.use(
	res => {
		if (res.config && res.config.url && res.config.url.indexOf('FileCenter/ManagerService.asmx/uploadFile') > -1) {
			res.data = {
				...res.data,
				responseCode: '100000',
			}
		}
		if (res.config && res.config.url && res.config.url.indexOf('backgroundImage/image/upload') > -1) {
			return res.data
		}
		const { responseCode, message = '', success } = res.data

		if (success || res.config.url.includes('/monitor') > -1) {
			return Promise.resolve(res.data)
		}

		switch (responseCode) {
			case '100000':
				return Promise.resolve(res.data)
			case '101002':
				return Promise.reject(res.data)
			case '900003': // 无设备信息时展示文案
				return Promise.reject(message)
			default:
				Message.error(message)
				return Promise.reject(res.data)
		}
	},
	error => {
		Message.error(`服务器异常：${error}`)
		// store.commit('loadingEnd')
		return Promise.reject(error)
	},
)

// get post
const GET = ESVHttpUtil.get
const POST = ESVHttpUtil.post
export { GET, POST, axios }
