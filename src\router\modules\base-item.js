/*
 * @Description: 数据项模板
 * @Author: lrr
 */

export default [
	// 基础数据项界面
	{
		path: '/base-item/item-list',
		name: 'item-list',
		component: (/* webpackChunkName: 'item-list' */) => import('@/views/base-item/item-list'),
	},
	// 基础数据项界面(鸡泽)
	{
		path: '/base-item/item-list-new',
		name: 'item-list-new',
		component: (/* webpackChunkName: 'item-list-new' */) => import('@/views/base-item/item-list-new'),
	},
	// 站点模板查询界面
	{
		path: '/base-item/item-template',
		name: 'item-template',
		component: (/* webpackChunkName: 'item-template' */) => import('@/views/base-item/item-template/indexVersion2'),
	},
	//站点数据项详情
	{
		path: '/base-item/station-template',
		name: 'station-template',
		component: (/* webpackChunkName: 'station-template' */) => import('@/views/base-item/station-template'),
	},
	//站点数据项详情
	{
		path: '/base-item/station-template2',
		name: 'station-template',
		component: (/* webpackChunkName: 'station-template' */) =>
			import('@/views/base-item/station-template/indexVersion2'),
	},
	//系统参数配置
	{
		path: '/base-item/config',
		name: 'config',
		component: (/* webpackChunkName: 'config' */) => import('@/views/base-item/config'),
	},
	//水务搜索引擎
	{
		path: '/base-item/water-search-engine',
		name: 'water-search-engine',
		component: (/* webpackChunkName: 'water-search-engine' */) => import('@/views/base-item/water-search-engine'),
	},
]
