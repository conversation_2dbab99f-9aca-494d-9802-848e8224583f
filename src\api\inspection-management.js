/*
 * @Description: 巡检接口
 * @Version: 2.0
 * @Autor: z<PERSON>yi
 * @Date: 2022-04-26 09:45:49
 * @LastEditors: houyan
 * @LastEditTime: 2022-09-06 09:08:37
 */
import { POST, GET, axios } from '@/utils/request'

// 巡检计划列表
export function getVillagePlanList(params) {
	return GET({
		url: '/screen/gis/proxy/Patroling/VillagePatrolPlan',
		params,
	})
}
export function getSupplyPlanList(params) {
	return GET({
		url: '/screen/gis/proxy/Patroling/CreatePatrolPlan',
		params,
	})
}
// 新增巡检任务
export function addVillagePlan(params) {
	return POST({
		url: '/screen/gis/proxy/Patroling/VillagePatrolPlan',
		params,
		requestType: 'json',
	})
}
export function addSupplyPlan(params) {
	return POST({
		url: '/screen/gis/proxy/Patroling/CreatePatrolPlan',
		params,
		requestType: 'json',
	})
}
// 修改巡检任务
export function updateVillagePlan(Id, params) {
	const url = `/screen/gis/proxy/Patroling/VillagePatrolPlan/${Id}`
	return axios({
		method: 'put',
		url,
		requestType: 'json',
		data: params,
	})
}
export function updateSupplyPlan(Id, params) {
	const url = `/screen/gis/proxy/Patroling/CreatePatrolPlan/${Id}`
	return axios({
		method: 'put',
		url,
		requestType: 'json',
		data: params,
	})
}
//查看巡检计划详情
export function getVillagePlanDetail(params) {
	return GET({
		url: '/screen/gis/proxy/Patroling/VillagePatrolPlan',
		params,
	})
}
export function getSupplyPlanDetail(params) {
	return GET({
		url: '/screen/gis/proxy/Patroling/CreatePatrolPlan',
		params,
	})
}
// 删除巡检计划
export function deleteVillagePlan(Id) {
	const url = `/screen/gis/proxy/Patroling/VillagePatrolPlan/${Id}`
	return axios.delete(url)
}
export function deleteSupplyPlan(Id) {
	const url = `/screen/gis/proxy/Patroling/CreatePatrolPlan/${Id}`
	return axios.delete(url)
}
// 巡检任务列表 和 任务详情
export function getPatrolTaskList(params) {
	return GET({
		url: '/screen/gis/proxy/Patroling/PatrolTask',
		params,
	})
}

export function getUId(params) {
	return GET({
		url: '/screen/gis/proxy/Patroling/GetUserUid',
		params,
	})
}
//手动确认
export function confirmTask(Id, data) {
	const url = `/screen/gis/proxy/Patroling/PatrolTask/${Id}`
	return axios({
		method: 'put',
		url,
		headers: { 'Content-type': 'application/json' },
		data: data,
	})
}
// 单村所属片区和乡镇
export function getTownShipList(params) {
	return POST({
		url: '/villageWater/pc/area/map',
		params,
		hideLoading: true,
	})
}
export function getAreaList(params) {
	return POST({
		url: '/ssy/pc/area/map',
		params,
		hideLoading: true,
	})
}
//获取单村供水站点
export function getStationList(params) {
	return POST({
		url: '/villageWater/pc/station/queryStationList',
		params,
	})
}
export function getPumpList(params) {
	return POST({
		url: '/ssy/pc/pumpStation/getPumpStationListByDep',
		params,
	})
}
export function getImageUrl(MediaId, uid) {
	return GET({
		url: `/screen/gis/proxy/FileCenter/IMG/Read/${MediaId}/para?uid=${uid}`,
	})
}
export function getParentAreaId(params) {
	return POST({
		url: '/ssy/pc/area/queryParentId',
		params,
	})
}
// 获取二供片区以及泵房
export function getAreaAndStationMap(params) {
	return POST({
		url: '/ssy/pc/area/areaAndStationMap',
		params,
	})
}
