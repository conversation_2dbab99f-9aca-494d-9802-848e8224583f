<!--
 * @Description: h5 video
 * @Author: shenxh
 * @Date: 2023-06-17 10:49:51
 * @LastEditors: shenxh
 * @LastEditTime: 2023-08-28 15:25:26
-->

<template>
	<div class="video-h5">
		<EasyPlayer :videoUrl="videoSrc" fluent autoplay live stretch />
	</div>
</template>

<script>
import EasyPlayer from '@easydarwin/easyplayer'
import { getBindEquipment } from '@/api/security-manage'

export default {
	name: 'video-h5',
	components: {
		EasyPlayer,
	},
	props: {
		stationCode: String,
		url: String,
	},
	data() {
		return {
			pathList: [],
			videoSrc: '',
		}
	},
	watch: {
		stationCode() {
			this.getBindEquipment()
		},
	},
	created() {},
	mounted() {
		if (this.url) {
			this.videoSrc = this.url
			return
		}
		this.getBindEquipment()
	},
	methods: {
		getBindEquipment() {
			getBindEquipment({
				areaCode: this.stationCode,
				pageNum: 1,
				pageSize: 1,
			}).then(res => {
				const { result = {} } = res
				const { list = [] } = result

				if (!list.length) return

				this.videoSrc = list[0].url
			})
		},
	},
}
</script>

<style lang="less" scoped>
.video-h5 {
	width: 100%;
	height: 100%;
}
</style>
