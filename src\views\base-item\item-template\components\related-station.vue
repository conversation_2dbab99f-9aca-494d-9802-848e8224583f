<template>
	<Modal
		class-name="custom-modal"
		width="480"
		class="station-modal"
		:value="show"
		:transfer="false"
		:title="title"
		@on-cancel="handleCancel"
		@on-ok="handleCheck"
	>
		<Spin fix v-if="listLoading">加载中。。。</Spin>
		<div>
			<Form ref="formValidate" :model="formItem" :label-width="80">
				<!--<Form-item label="站点选择" prop="stationCodes">
					<Select
						filterable
						transfer
						multiple
						v-model="formItem.stationCodes"
						placeholder="请选择"
					>
						<Option
							style="width: 80px"
							v-for="(item, key) in stationList"
							:value="item.nodeCode"
							:key="key"
						>
							{{ item.title }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="已选择" prop="stationCodes">
					<Select
						transfer
						hidden="false"
						multiple
						v-model="formItem.stationCodes"
						placeholder="请选择"
					>
						<Option
							hidden="false"
							style="width: 80px"
							v-for="(item, key) in stationList"
							:value="item.nodeCode"
							:key="key"
						>
							{{ item.nodeCode }}
						</Option>
					</Select>
				</Form-item>-->
				<div>
					手动填写

					<Form-item :prop="`inputCodes.${index}`" v-for="(item, index) in formItem.inputCodes" :key="index">
						<Input v-model="formItem.inputCodes[index]" placeholder="站点编号"></Input>
					</Form-item>

					<div class="dotted" @click="add">+新增</div>
				</div>
			</Form>
		</div>
	</Modal>
</template>

<script>
import { queryStations, relatedStationTemps } from '@/api/base-item'

export default {
	name: 'relatedStation',
	components: {},
	props: {
		type: {
			type: String,
		},
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '新增',
		},
		id: {
			type: Number,
		},
	},
	created() {},
	data() {
		return {
			listLoading: false,
			stationList: [],
			formItem: {
				inputCodes: [''],
				stationCodes: [],
				templateId: this.id,
				type: this.type,
			},
		}
	},
	methods: {
		add() {
			this.formItem.inputCodes.push('')
		},
		queryStations() {
			let params = { sysCode: this.type }
			queryStations(params).then(res => {
				this.stationList = res.result
			})
		},

		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
			this.formItem = {
				inputCodes: [''],
				stationCodes: [],
				templateId: '',
				type: '',
			}
		},

		// 确定
		handleCheck() {
			this.formItem.templateId = this.id
			this.formItem.type = this.type
			relatedStationTemps(this.formItem).then(() => {
				this.$Message.success('提交成功!')
				this.handleCancel()
			})
		},
	},
}
</script>
<style lang="less">
.station-modal {
	.custom-modal {
		.ivu-modal {
			height: 48% !important;
		}
		.ivu-modal-body {
			height: calc(100% - 96px);
		}
	}
}
</style>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
.demo-upload-list {
	display: inline-block;
	width: 60px;
	height: 60px;
	text-align: center;
	line-height: 60px;
	border: 1px solid transparent;
	border-radius: 4px;
	overflow: hidden;
	background: #fff;
	position: relative;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
	margin-right: 4px;
}
.demo-upload-list img {
	width: 100%;
	height: 100%;
}
.demo-upload-list-cover {
	display: none;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.6);
}
.demo-upload-list:hover .demo-upload-list-cover {
	display: block;
}
.demo-upload-list-cover i {
	color: #fff;
	font-size: 20px;
	cursor: pointer;
	margin: 0 2px;
}
.dotted {
	margin-left: 80px;
	width: 200px;
	display: flex;
	justify-content: center;
	align-items: center;
	border-style: dotted;
	background-color: azure;
	border-width: 1px;
	margin-top: 10px;
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	// .ivu-input-number-input {
	// 	color: #fff;
	// 	background: #133a5e;
	// 	border: none;
	// }
}
</style>
