<!--
 * @Description: 自定义下拉框
 * @Author: shenxh
 * @Date: 2022-04-06 14:49:08
 * @LastEditors: shenxh
 * @LastEditTime: 2022-04-11 11:44:49
-->
<template lang="pug">
.drop-down
	.drop-down-content(
		:style='{ height: (value ? foldHeight : unfoldHeight) + "px", overflow: value ? "hidden" : "auto" }'
	)
		slot
	.drop-down-button
		.btn(@click='handleFoldBtn')
			span {{ value ? '展开' : '折叠' }}
			Icon.icon(type='md-arrow-dropdown', :class='value ? "" : "active"')
</template>

<script>
export default {
	name: 'drop-down',
	model: {
		prop: 'value',
		event: 'setVal',
	},
	components: {},
	props: {
		value: {
			type: Boolean,
			default: true,
		},
		// 折叠高度
		foldHeight: {
			type: Number,
			default: 25,
		},
		// 展开高度
		unfoldHeight: {
			type: Number,
			default: 200,
		},
	},
	data() {
		return {
			// isFold: true,
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 点击折叠按钮
		handleFoldBtn() {
			this.$emit('setVal', !this.value)
			// this.isFold = !this.isFold
		},
	},
}
</script>

<style lang="less" scoped>
.drop-down {
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	background-color: #fff;
	border: 1px solid #efefef;
	padding: 10px;
	border-radius: 4px;
	.drop-down-content {
		transition: all 0.25s;
	}
	.drop-down-button {
		flex-shrink: 0;
		margin-left: 5px;
		width: 50px;
		cursor: pointer;
		color: #2d8cf0;
		.btn {
			.icon {
				transition: all 0.25s;
				&.active {
					transform: rotate(180deg);
				}
			}
		}
	}
}
</style>
