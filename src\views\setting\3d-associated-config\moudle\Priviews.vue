<!--
 * @Description: 制水工艺流程
 * @Version: 
 * @Autor: ho<PERSON>an
 * @Date: 2021-09-03 10:34:57
 * @LastEditors: heliping
 * @LastEditTime: 2022-03-18 09:57:36
-->
<template lang="pug">
.alarm-craft
	EsThreeModel(ref='three')
</template>
<script>
// import Three from '@/components/water-three-model.vue'
import { getProcessFlowDiagramList, getTemplateModels } from '@/api/setting'
// import { , EsProcessFlow } from '@eslink/esvcp-pc-ui'
import { EsThreeModel } from '@eslink/esvcp-pc-ui'
export default {
	components: {
		// Three,
		EsThreeModel,
	},
	props: {
		alarmModal: {
			type: Boolean,
			default: false,
		},
		alarmType: {
			type: String,
			default: 'WATER_WORKS',
		},
		row: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		// stationId: {
		// 	get() {
		// 		return this.$route.query.id
		// 	},
		// 	set() {},
		// },
		type: function () {
			if (this.alarmType === 'SINGLE') {
				return 'ZSSCSPALARM'
			} else if (this.alarmType === 'SECOND_PUMP') {
				return 'SGWFYALARM'
			} else {
				return 'ETZSCSPALARM'
			}
		},
	},
	watch: {
		// 监听url上id值的改变
		// stationId() {
		// 	this.initWaterIndexPage()
		// },
	},
	data() {
		return {
			// type: 'SGWFYALARM', // 水厂ETZSCSP,泵房SGWFY,单村
			iconList: ['icon-yuanqu', 'icon-a-2dgongyi'],
			indicatorList: [],
		}
	},
	mounted() {
		// setTimeout(() => {
		// 	this.debugObj()
		// })
		// EventBus.$on('changeType', ({ index }) => {
		// })
	},
	methods: {
		viewModel(row) {
			const { id } = row
			getTemplateModels({ id }).then(res => {
				// res.result.processFlowDiagramModels[126].positionZ =
				// 	'-' + res.result.processFlowDiagramModels[126].positionZ
				// res.result.processFlowDiagramModels[126].positionY =
				// 	'-' +
				// 	(res.result.processFlowDiagramModels[126].positionY - '0')
				// console.log(res.result.processFlowDiagramModels[126])
				const diagramData = res.result
				this.$refs.three.setDiagram({ info: diagramData })
			})
		},
		debugObj(row) {
			const { processId, stationCode } = row
			// const configType = 4
			// let diagramData = this.$refs.three.getDiagram(this.type, configType)
			// diagramData.pointGroups = diagramData.points
			// diagramData.debug = true
			getProcessFlowDiagramList({
				stationId: stationCode,
				diagramId: processId,
			}).then(res => {
				const diagramData = res.result[0]
				debugger
				this.$refs.three.setDiagram({ info: diagramData })
			})
			// this.renderModelFlag = true
		},
	},
}
</script>
<style lang="less" scoped>
.alarm-craft {
	overflow: hidden;
	position: relative;
	width: 100%;
	height: 100%;
	background: #021221;
}
</style>
