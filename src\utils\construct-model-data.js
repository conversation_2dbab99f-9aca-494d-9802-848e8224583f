/*
 * @Descripttion: 根据模型文件获取地址结果值
 * @version:
 * @Author: heliping
 * @Date: 2021-09-14 19:50:56
 * @LastEditors: heliping
 * @LastEditTime: 2022-02-24 15:08:07
 */

// const fs = require('fs')
// const dir = 'D://file/glb'
// const dirdae = 'D://file'
// const files = fs.readdirSync(dir)
// const path = require('path')
// let file = path.resolve(__dirname, 'D://file/result.txt')

let fileObj = {}
let array = []

// debugger
// fs.readFile('D://file/zuobiao.dae', 'utf-8', (err, data) => {
// const files = context.keys()
// if (err) {
// 	return console.error(err, '读取失败,请确认文件是否存在')
// }
// debugger
export function getJson(files, data) {
	// for (const file of files) {
	// 	debugger
	// 	let name = file.split('.')[0]
	// 	let key = 'name="' + name + '"'
	// 	matchString(data, key)
	// }
	files.forEach(file => {
		let name = file.substring(file.lastIndexOf('/') + 1, file.length - 4)
		let key = 'name="' + name + '"'
		matchString(data, key)
	})

	for (const key in fileObj) {
		if (Object.hasOwnProperty.call(fileObj, key)) {
			const element = fileObj[key]

			element.forEach(item => {
				const startIndex = item.indexOf('sid="translate">')
				if (startIndex > 0) {
					const endIndex = item.indexOf('</translate>')

					array.push({
						name: key.substring(6, key.length - 1),
						type: key.substring(6, key.length - 1),
						position: item.substring(startIndex + 16, endIndex),
					})
				}
			})
		}
	}
	return array
}

// 寻找字符串匹配，截取前100位字符
export function matchString(data, key, start = 0) {
	let index = data.indexOf(key, start)
	if (index > 0) {
		if (!fileObj[key]) {
			fileObj[key] = []
		}
		fileObj[key].push(data.substr(index, 100))
		let newStart = index + 1
		matchString(data, key, newStart)
	}
}
