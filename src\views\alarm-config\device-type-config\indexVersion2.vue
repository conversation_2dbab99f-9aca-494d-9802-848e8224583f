<!--
 * @Descripttion: 
 * @version: 
 * @Author: heliping
 * @Date: 2022-4-08 15:37:46
 * @LastEditors: liranran
-->
<template>
	<div class="layout">
		<div class="fixed">
			<span class="header-button">
				<Button class="button" @click="deleteShow = true" size="small">删除</Button>
				<Button class="button" type="primary" @click="handleAdd" size="small">新建</Button>
				<Button class="button" type="primary" @click="handleRelation" size="small">应用</Button>
			</span>
		</div>
		<div class="layout-content">
			<Row>
				<i-col span="5">
					<Input
						style="width: 230px; margin-right: 10px"
						clearable
						v-model="name"
						placeholder="请输入名称"
					></Input>
					<Button type="primary" @click="handleSearch">查询</Button>

					<Table
						:loading="listLoading"
						style="width: 300px"
						:show-header="false"
						:border="false"
						highlight-row
						height="800"
						:columns="columns"
						:data="data"
						@on-row-click="handleSelect"
					></Table>
				</i-col>
				<i-col span="19">
					<div style="margin-left: 16px; font-size: 18px; border-bottom: 1px solid #dadae2">模板信息</div>
					<Form
						ref="formValidate"
						:model="formItem"
						:rules="formRules"
						:label-width="80"
						style="margin-top: 10px"
						:loading="formLoading"
						inline
					>
						<Form-item label="类型名称" prop="name" :label-width="100">
							<Input
								:disabled="isReadonly"
								v-model="formItem.name"
								placeholder="模板名称"
								style="width: 200px"
							></Input>
						</Form-item>
						<Form-item style="margin-left: 190px" label="类型编号" prop="deviceTypeCode" :label-width="100">
							<Input
								:disabled="isReadonly"
								style="width: 200px"
								v-model="formItem.deviceTypeCode"
								placeholder="请输入"
							></Input>
						</Form-item>
						<Form-item label="类型说明" prop="memo" :label-width="100">
							<Input
								:disabled="isReadonly"
								:rows="2"
								type="textarea"
								style="width: 700px"
								v-model="formItem.memo"
								placeholder="请输入"
							></Input>
						</Form-item>
						<div style="margin-left: 16px; margin-top: 36px; margin-bottom: 36px; font-size: 18px">
							数据项
						</div>
						<div style="display: flex; justify-content: flex-end; margin-bottom: 16px; margin-right: 16px">
							<div>
								<Button v-if="isReadonly" type="primary" icon="plus" @click="handlerAdd()">
									新增类型
								</Button>
							</div>
						</div>
						<Table
							style="margin: 16px"
							height="400"
							border
							:columns="allTableColumns"
							:data="formItem.baseTemplateDetails"
							:loading="formLoading"
						>
							<template slot-scope="{ row, index }" slot="baseDataItemId">
								<FormItem
									v-if="editIndex === index || button === 'add'"
									style="margin: auto"
									:prop="`baseTemplateDetails.${index}.baseDataItemId`"
									:label-width="0"
									:rules="formRules.baseDataItemId"
								>
									<Select
										v-model="formItem.baseTemplateDetails[index].baseDataItemId"
										transfer
										style="width: 130px"
									>
										<Option v-for="item in baseDataItems" :key="item.id" :value="item.id">
											{{ item.baseItemName }}
										</Option>
									</Select>
								</FormItem>
								<span v-else>
									{{ findBaseItemName(row.baseDataItemId) }}
								</span>
							</template>
							<template slot-scope="{ row, index }" slot="itemRealCode">
								<FormItem
									v-if="editIndex === index || button === 'add'"
									style="margin: auto"
									:prop="`baseTemplateDetails.${index}.itemRealCode`"
									:label-width="0"
									:rules="formRules.itemRealCode"
								>
									<Input v-model="formItem.baseTemplateDetails[index].itemRealCode" />
								</FormItem>
								<span v-else>{{ row.itemRealCode }}</span>
							</template>
							<template slot-scope="{ row, index }" slot="itemName">
								<FormItem
									v-if="editIndex === index || button === 'add'"
									style="margin: auto"
									:prop="`baseTemplateDetails.${index}.itemName`"
									:label-width="0"
									:rules="formRules.itemName"
								>
									<Input v-model="formItem.baseTemplateDetails[index].itemName" />
								</FormItem>
								<span v-else>{{ row.itemName }}</span>
							</template>
							<template slot-scope="{ row, index }" slot="formulaType">
								<FormItem
									v-if="editIndex === index || button === 'add'"
									style="margin: auto"
									:prop="`baseTemplateDetails.${index}.formulaType`"
									:label-width="0"
									:rules="formRules.formulaType"
								>
									<Select v-model="formItem.baseTemplateDetails[index].formulaType" transfer>
										<Option v-for="item in formulaTypes" :key="item.value" :value="item.value">
											{{ item.label }}
										</Option>
									</Select>
								</FormItem>
								<span v-else>
									{{ findFormulaType(row.formulaType) }}
								</span>
							</template>
							<template slot-scope="{ row, index }" slot="formula">
								<FormItem
									v-if="editIndex === index || button === 'add'"
									style="margin: auto"
									:prop="`baseTemplateDetails.${index}.formula`"
									:label-width="0"
								>
									<Input v-model="formItem.baseTemplateDetails[index].formula" />
								</FormItem>
								<span v-else>{{ row.formula }}</span>
							</template>
							<template slot-scope="{ row, index }" slot="orderBy">
								<FormItem
									v-if="editIndex === index || button === 'add'"
									style="margin: auto"
									:prop="`baseTemplateDetails.${index}.orderBy`"
									:label-width="0"
								>
									<Input v-model="formItem.baseTemplateDetails[index].orderBy" />
								</FormItem>
								<span v-else>{{ row.orderBy }}</span>
							</template>
							<template slot-scope="{ row, index }" slot="edit">
								<Button
									v-if="editIndex === -1 && isReadonly"
									size="small"
									type="primary"
									@click="editIndex = index"
								>
									修改
								</Button>
								<Button
									v-if="editIndex === index && isReadonly"
									size="small"
									type="primary"
									@click="handleUpdateStationItem(row, index)"
								>
									保存
								</Button>
								<Button
									v-if="editIndex === index && isReadonly"
									size="small"
									type="primary"
									@click="handleCancelRow"
									style="margin-left: 13px"
								>
									取消
								</Button>
								<Button type="text" size="small" @click="delItem(row, index)" style="color: #3b95e9">
									删除
								</Button>
							</template>
						</Table>
						<div class="dotted" @click="addItem" v-if="!isReadonly">新增数据项</div>

						<div class="rightButton">
							<Button v-if="!isReadonly" class="button" @click="handleCancel" size="small">取消</Button>
							<Button
								v-if="!isReadonly"
								class="button"
								type="primary"
								@click="handleSubForm"
								size="small"
							>
								保存
							</Button>
						</div>
					</Form>
				</i-col>
			</Row>
		</div>
		<related-station
			:show.sync="modalShow"
			ref="modalRef"
			:type="this.type"
			:id="this.selectedId"
		></related-station>
		<EsConfirmModal
			v-model="deleteShow"
			title="提示"
			content="是否删除当前项"
			@on-ok="handleDelete"
			@on-cancel="deleteShow = false"
		></EsConfirmModal>
		<EsConfirmModal
			v-model="deleteItemShow"
			title="提示"
			content="是否删除当前项"
			@on-ok="handleItemDelete"
			@on-cancel="deleteItemShow = false"
		></EsConfirmModal>
		<add-item
			:show.sync="showAddModal"
			ref="addItem"
			:type="this.type"
			:id="this.selectedId"
			@init="getDetail()"
		></add-item>
	</div>
</template>
<script>
import {
	queryTemplateByName,
	queryItemTemplateById,
	deleteItemTemplateById,
	queryPage,
	saveItemTemplateAndDatails,
	updateItemTemplateAndDatails,
	deleteTemplateDetailById,
	updateBaseTemplateDetail,
} from '@/api/base-item'
import { EsConfirmModal } from '@eslink/esvcp-pc-ui'
import RelatedStation from '@/views/base-item/item-template/components/related-station'
import AddItem from '@/views/base-item/item-template/components/add-item'

export default {
	components: { AddItem, RelatedStation, EsConfirmModal },
	props: {},
	computed: {},
	mounted() {},
	data() {
		return {
			showAddModal: false,
			templateDetailId: null,
			deleteItemShow: false,
			editIndex: -1,
			button: 'edit',
			type: '',
			modalShow: false,
			index: null,
			deleteShow: false,
			selectedId: null,
			isReadonly: true,
			listLoading: false,
			formLoading: false,
			name: '',
			columns: [{ key: 'name' }],
			data: [],
			baseDataItems: [],
			formulaTypes: [
				{
					value: '0',
					label: '原始数据',
				},
				{
					value: '1',
					label: '单位换算',
				},
				{
					value: '2',
					label: '公式计算',
				},
				{
					value: '3',
					label: '状态数据',
				},
			],
			tableColumns: [],
			allTableColumns: [
				{
					title: '基础数据项',
					slot: 'baseDataItemId',
					align: 'center',
					width: '160px',
				},
				{
					title: '数据项编号',
					slot: 'itemRealCode',
					align: 'center',
					width: '160px',
				},
				{
					title: '数据项名称',
					slot: 'itemName',
					align: 'center',
				},
				{
					title: '公式类型',
					slot: 'formulaType',
					align: 'center',
					type: 'text',
					width: '120px',
				},
				{
					title: '公式',
					slot: 'formula',
					align: 'center',
				},
				{
					title: '序号',
					slot: 'orderBy',
					align: 'center',
					width: '80px',
				},
				{
					width: '180px',
					title: '操作',
					slot: 'edit',
					align: 'center',
					key: 'edit',
				},
			],

			modalWidth: '',
			tabValue: '',
			formItem: {
				id: '',
				name: '',
				memo: '',
				deviceTypeCode: '',
				itemRealCode: '',
				itemName: '',
				baseTemplateDetails: [],
			},
			formRules: {
				name: [
					{
						required: true,
						message: '请输入模板名称',
						trigger: 'change',
					},
				],
				deviceTypeCode: [
					{
						required: true,
						message: '请输入设备编号',
						trigger: 'change',
					},
				],
				baseDataItemId: [
					{
						required: true,
						message: '请选择基础数据项',
						trigger: 'change',
						type: 'number',
					},
				],
				itemName: [
					{
						required: true,
						message: '请输入数据项名称',
						trigger: 'change',
					},
				],
				itemRealCode: [
					{
						required: true,
						message: '请输入数据项编号',
						trigger: 'change',
					},
				],
				formulaType: [
					{
						required: true,
						message: '请选择公式类型',
						trigger: 'change',
					},
				],
			},
		}
	},
	created() {
		this.handleSearch()
	},
	watch: {
		type: {
			handler() {
				this.getBaseDataItems()
			},
			immediate: true,
		},
	},
	methods: {
		findBaseItemName(id) {
			if (this.baseDataItems?.length && this.baseDataItems?.length > 0) {
				return this.baseDataItems.find(a => a.id == id)?.baseItemName
			} else {
				return '--'
			}
		},
		findFormulaType(formulaType) {
			return this.formulaTypes.find(a => a.value == formulaType).label
		},

		handleUpdateStationItem(row, index) {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					updateBaseTemplateDetail(this.formItem.baseTemplateDetails[index])
						.then(() => {
							this.$Message.success('提交成功!')
						})
						.catch(() => {
							this.listLoading = false
						})
					this.editIndex = -1
				}
			})
		},
		handleAdd() {
			this.formItem = {
				name: '',
				type: 'jz',
				memo: '',
				baseTemplateDetails: [
					{
						baseDataItemId: null,
						itemRealCode: '',
						itemName: '',
						formulaType: null,
						formula: '',
						orderBy: null,
					},
				],
			}
			this.isReadonly = false
			this.button = 'add'
		},
		handleCancelRow() {
			this.editIndex = -1
			this.getDetail()
		},
		handleCancel() {
			this.isReadonly = false
			// this.editIndex = -1
			this.button = 'edit'
			this.handleSearch()
		},
		addItem() {
			this.formItem.baseTemplateDetails.push({
				baseDataItemId: '',
				itemRealCode: '',
				itemName: '',
				formulaType: null,
				formula: '',
				orderBy: null,
			})
		},
		handleTablePageChange(pageNum) {
			this.tablePageData.current = pageNum
			this.getDetail()
		},

		getBaseDataItems() {
			queryPage({ needPage: false, type: this.type }).then(res => {
				this.baseDataItems = res.result.list
				console.log('this.baseDataItems', this.baseDataItems)
			})
		},
		handleDelete() {
			let params = { templateId: this.selectedId }
			deleteItemTemplateById(params).then(() => {
				this.$Message.success('操作成功')
				this.deleteShow = false
				this.handleSearch()
			})
		},
		handleItemDelete() {
			let params = { id: this.templateDetailId }
			deleteTemplateDetailById(params).then(() => {
				this.$Message.success('操作成功')
				this.deleteItemShow = false
				this.getDetail()
			})
		},

		delItem(row, index) {
			if (this.button == 'add') {
				this.formItem.baseTemplateDetails.splice(index, 1)
			} else {
				this.deleteItemShow = true
				this.templateDetailId = row.id
			}
		},
		handleRelation() {
			this.$refs.modalRef.id = this.selectedId

			this.$refs.modalRef.type = 'jz'
			this.modalShow = true
		},

		handleSelect(selection) {
			this.type = selection.type
			this.selectedId = selection.id
			this.isReadonly = true
			this.editIndex = -1
			this.getDetail()
			this.button = 'edit'
		},

		onload() {
			this.getBaseDataItems()
			this.$nextTick(() => {
				this.getDetail()
			})
		},
		//查看详情
		getDetail() {
			this.formLoading = true
			queryItemTemplateById({ templateId: this.selectedId }).then(res => {
				this.formItem = res.result
				this.isReadonly = true
				this.button = 'edit'
			})
			this.formLoading = false
			this.editIndex = -1
		},
		// 按钮-查询
		handleSearch() {
			this.editIndex = -1
			let params = { name: this.name }
			queryTemplateByName(params).then(res => {
				this.data = res.result
				if (this.data && this.data.length && this.data.length > 0) {
					this.data[0]._highlight = true
					this.type = this.data[0]?.type
					this.selectedId = this.data[0]?.id
				}
				this.getDetail()
				this.isReadonly = true
			})
		},
		handlerAdd() {
			this.$refs.addItem.getBaseDataItems()
			this.showAddModal = true
		},
		// 按钮-保存
		handleSubForm: function () {
			console.log('this.formItem', this.formItem)

			this.$refs.formValidate.validate(valid => {
				if (valid) {
					if (this.button === 'edit') {
						this.formItem.id = this.selectedId
						updateItemTemplateAndDatails(this.formItem)
							.then(() => {
								this.$Message.success('提交成功!')
								this.handleSearch()
							})
							.catch(() => {
								this.listLoading = false
							})
					} else if (this.button === 'add') {
						saveItemTemplateAndDatails(this.formItem)
							.then(() => {
								this.$Message.success('提交成功!')
								this.handleSearch()
							})
							.catch(() => {
								this.listLoading = false
							})
					}
				}
			})
		},
	},
}
</script>
<style lang="less" scoped>
.header-button {
	margin-left: auto;
}
.layout {
	height: 100%;
}
.layout-content {
	margin: 15px;
	overflow-y: auto;
	border-radius: 4px;
	height: calc(100% - 56px);
	::v-deep {
		.ivu-row {
			height: 100%;
			overflow: hidden;
		}
	}
}

.dotted {
	display: flex;
	justify-content: center;
	align-items: center;
	border-style: dotted;
	background-color: azure;
	border-width: 1px;
	margin: 10px 15px 0 15px;
}
.fixed {
	display: flex;
	justify-content: space-between;
}
.button {
	margin-left: 16px;
	margin-top: 16px;
}
.rightButton {
	display: flex;
	justify-content: flex-end;
	margin-right: 10px;
}
/deep/ .ivu-form-item {
	margin-bottom: 20px;
}
/deep/ .btn-bar .ivu-btn {
	margin-right: 8px;
}
</style>
