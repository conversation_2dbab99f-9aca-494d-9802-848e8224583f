<!--
 * @Description: 设备原始档案
 * @Author: shenxh
 * @Date: 2023-04-03 09:22:40
 * @LastEditors: shenxh
 * @LastEditTime: 2023-05-08 17:07:22
-->

<template lang="pug">
.equipment-files
	.header-wrap
		es-header.header(title='设备原始档案')
	es-search.es-search(
		col='4',
		:show-collapse='false',
		:modules='moduleList',
		@on-search='handleSearchBtn',
		@on-reset='handleResetBtn'
	)
	water-row.table-btn-wrap(justify='space-between', align='center')
		i
		Button(type='primary', @click='handleCreate') 新增
	.table-wrap
		es-table(
			:columns='columns',
			:data='tableData',
			:loading='loading',
			border,
			showPage,
			:pageData='pageData',
			@on-page-num-change='handlePageNum',
			@on-page-size-change='handlePageSize'
		)
			template(slot-scope='{ row }', slot='memo')
				span(v-html='getText(row.memo)')
			template(slot-scope='{ row }', slot='action')
				Button(type='text', :style='{ color: "#3AA7D8" }', @click='handleRowUpd(row)', size='small') 编辑
				Poptip(transfer, confirm='', title='确定删除吗？', @on-ok='handleRowDel(row)')
					Button(type='text', :style='{ color: "#EC5151" }', size='small') 删除

	create-system-popup(
		v-model='showModal',
		:data='currentRow',
		:type='popupType',
		:trans-channel-list='moduleList[0].data[0].dataSourceList',
		@submit-form='handleSubForm'
	)
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import CreateSystemPopup from './components/CreateSystemPopup.vue'
import { queryTransChannel, queryStationInfo, deleteStationInfo } from '@/api/data-acquisition-config'

export default {
	name: 'equipment-files',
	components: {
		WaterRow,
		CreateSystemPopup,
	},
	props: {},
	data() {
		return {
			popupType: 0,
			showModal: false,
			loading: false,
			form: {},
			currentRow: {},
			tableData: [],
			moduleList: [
				{
					label: '',
					value: '1',
					isDefault: true,
					keys: ['transChannelCode', 'queryMethod', 'stationWord', 'xxx'],
					model: {
						transChannelCode: '',
						queryMethod: 'keyWord',
						stationWord: '',
					},
					data: [
						{
							type: 2,
							key: 'transChannelCode',
							formItemProps: {
								label: '采集渠道',
								prop: 'transChannelCode',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
						{
							type: 2,
							key: 'queryMethod',
							formItemProps: {
								label: '查询方式',
								prop: 'queryMethod',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: false,
								disabled: false,
							},
							dataSourceList: [
								{
									label: '综合查询',
									value: 'keyWord',
								},
								{
									label: '平台唯一编号',
									value: 'stationCode',
								},
								{
									label: '设备名称',
									value: 'stationName',
								},
								{
									label: '表号',
									value: 'meterNo',
								},
								{
									label: '外部id',
									value: 'objectId',
								},
								{
									label: '户号',
									value: 'userNo',
								},
								{
									label: '通讯编码',
									value: 'communicateNo',
								},
							],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
						{
							type: 1,
							key: 'stationWord',
							formItemProps: {
								label: '',
								prop: 'stationWord',
								labelWidth: 10,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
					],
				},
			],
			columns: [
				{
					type: 'index',
					title: '序号',
					align: 'center',
					width: 80,
				},
				{
					title: '平台唯一编码',
					key: 'stationCode',
				},
				{
					title: '采集渠道',
					key: 'transChannelName',
				},
				{
					title: '站点/对象名称',
					key: 'stationName',
				},
				{
					title: '表号（钢印号）',
					key: 'meterNo',
				},
				{
					title: '表卡编号（户号）',
					key: 'userNo',
				},
				{
					title: '通讯编码',
					key: 'communicateNo',
				},
				{
					title: '外部ID',
					key: 'objectId',
				},
				{
					title: '地址',
					key: 'address',
				},
				{
					title: '生产厂家',
					key: 'producerName',
				},
				{
					title: '备注',
					slot: 'memo',
				},
				{
					title: '口径',
					key: 'caliber',
				},
				{
					title: '最后更新时间',
					key: 'updateTime',
				},
				{
					title: '操作人',
					key: 'createUser',
				},
				{
					title: '操作',
					slot: 'action',
					align: 'center',
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 50, 100, 200],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		this.queryTransChannel()
		this.getTableData()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 搜索按钮
		handleSearchBtn(params) {
			this.form = params

			this.getTableData()
		},

		// 重置按钮
		handleResetBtn() {
			this.form = {}
			this.pageData.current = 1

			this.getTableData()
		},

		// 添加按钮
		handleCreate() {
			this.currentRow = {}
			this.popupType = 0
			this.showModal = true
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum

			this.getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize

			this.getTableData()
		},

		// 行-编辑
		handleRowUpd(row) {
			this.currentRow = row
			this.popupType = 1
			this.showModal = true
		},

		// 行-删除
		handleRowDel(row) {
			this.deleteStationInfo(row)
		},

		// 弹窗按钮-保存
		handleSubForm() {
			this.getTableData()
		},

		getText(str = '') {
			return str.replace(/\n/g, '<br>')
		},

		// 获取表格数据
		getTableData() {
			this.loading = true
			const params = {
				transChannelCode: this.form.transChannelCode,
			}

			if (this.form.queryMethod) {
				params[this.form.queryMethod] = this.form.stationWord
			}
			queryStationInfo({
				...params,
				pageNum: this.pageData.current,
				pageSize: this.pageData.pageSize,
			}).then(res => {
				const { result = {} } = res || {}
				const { list = [], total } = result

				this.tableData = list
				this.pageData.total = total
				this.loading = false
			})
		},

		// 采集渠道分页列表查询
		queryTransChannel() {
			queryTransChannel({
				pageNum: 1,
				pageSize: 99,
			}).then(res => {
				const { result = {} } = res || {}
				const { list = [] } = result

				this.moduleList[0].data[0].dataSourceList = list.map(item => {
					return {
						...item,
						label: item.name,
						value: item.channelCode,
					}
				})
			})
		},

		// 删除采集渠道
		deleteStationInfo(params) {
			deleteStationInfo(params).then(res => {
				const { responseCode } = res || {}

				if (responseCode === '100000') {
					this.$Message.success('操作成功')

					this.getTableData()
				}
			})
		},
	},
}
</script>

<style lang="less" scoped>
.equipment-files {
	padding: 0 16px;
	.es-search {
		height: inherit;
		padding: 8px 0;
		margin-bottom: 8px;
	}
	.table-btn-wrap {
		margin-bottom: 8px;
	}
	.table-wrap {
		width: 100%;
		height: calc(100vh - 150px);
	}
}
</style>
