<!--
 * @Description: 巡检任务列表
 * @Version: 2.0
 * @Autor: z<PERSON>yi
 * @Date: 2022-02-21 17:10:08
 * @LastEditors: ya<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-06-08 16:46:59
-->
<template lang="pug">
.task-list
	water-row.task-list-header(justify='flex-start', align='center')
		water-row.task-list-form(justify='flex-start', align='center')
			.task-list-form-title 巡检任务名称:
			Input(v-model='taskName', placeholder='请输入名称', style='width: 180px') 
		water-row.task-list-form(justify='flex-start', align='center')
			.task-list-form-title 巡检人:
			div(style='width: 320px')
				personnel-config-select(
					placeholder='请选择巡检人',
					:selected-value='notifyUserIdsArr',
					:selected-label='notifyUserNamesArr'
				)
		water-row.task-list-form(justify='flex-start', align='center')
			.task-list-form-title 任务执行日期:
			DatePicker(
				:editable='false',
				v-model='startTime',
				type='date',
				format='yyyy-MM-dd',
				clearable,
				placeholder='请选择开始时间',
				style='width: 170px'
			)
			.task-list-form-title.water-margin-left-4 至
			DatePicker(
				:editable='false',
				v-model='endTime',
				type='date',
				clearable,
				format='yyyy-MM-dd',
				placeholder='请选择结束时间',
				style='width: 170px'
			)

		Button(type='primary', @click='getList()') 查询
	.task-list-content
		es-table.task-list-table.water-table(
			border,
			showPage,
			:pageData='pageData',
			:columns='columns',
			:data='tableData',
			:loading='loading',
			@on-page-num-change='handlePageChange',
			@on-page-size-change='handlePageSize'
		)
			template(slot-scope='{ row }', slot='action')
				Button(type='primary', @click='handleCheck(row)', size='small') 查看详情
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import PersonnelConfigSelect from '@/views/alarm-config/components/PersonnelConfigSelect.vue'
import { getPatrolTaskList } from '@/api/inspection-management.js'
import { EventBus } from '@/utils/eventBus.js'
export default {
	name: 'task-list',
	components: {
		WaterRow,
		PersonnelConfigSelect,
	},
	computed: {
		sysCode() {
			return this.$route.query.sysCode || 'dc'
		},
	},
	data() {
		return {
			loading: false,
			tableData: [],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 30, 40, 50],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
			list: [],
			notifyUserIdsArr: [],
			notifyUserNamesArr: [],
			taskName: '',
			startTime: '',
			endTime: '',
			columns: [
				{
					title: '序号',
					key: 'key',
					width: 80,
				},
				{
					title: '巡检名称',
					key: 'Name',
					minwidth: 120,
				},
				{
					title: '巡检人',
					key: 'Executor',
					minwidth: 120,
				},
				{
					title: '巡检进度',
					key: 'Progress',
					minwidth: 120,
				},
				{
					title: '巡检时段',
					key: 'Execution_Time',
					minwidth: 120,
				},
				{
					title: '实际完成时间',
					key: 'CompletionTime',
					minwidth: 120,
				},
				{
					title: '创建人',
					key: 'CreateUser',
					minwidth: 120,
				},
				{
					title: '创建时间',
					key: 'CreateTime',
					minwidth: 120,
				},
				{ title: '操作', slot: 'action', minwidth: 80 },
			],
		}
	},
	mounted() {
		this.handleQuery()
		EventBus.$on('fresh-task-list', () => {
			this.handleQuery()
		})
	},
	methods: {
		//查询照片
		getList() {
			this.pageData.current = 1
			this.handleQuery()
		},
		//获取列表
		handleQuery() {
			let params = {
				PageIndex: this.pageData.current,
				PageSize: this.pageData.pageSize,
				Source: this.sysCode === 'dc' ? '单村' : '二供',
			}
			if (this.startTime) {
				params.StartTime = this.$moment(this.startTime).format('YYYY-MM-DD')
			}
			if (this.endTime) {
				params.EndTime = `${this.$moment(this.endTime).format('YYYY-MM-DD')}`
			}
			if (this.taskName) {
				params.Name = this.taskName
			}
			if (this.notifyUserIdsArr.length > 0) {
				params.Patroler = this.notifyUserIdsArr.join(',')
			}
			getPatrolTaskList(params)
				.then(res => {
					const { result = [] } = res
					const { Datas = [], Total = 0 } = result
					this.tableData = Datas.map((item, index) => {
						return {
							key: index + 1,
							...item,
						}
					})
					this.pageData.total = Total
				})
				.catch(() => {
					this.loading = false
					this.tableData = []
				})
		},
		//查看详情
		handleCheck(row) {
			this.$emit('check', row)
		},
		//切换页码
		handlePageChange(page) {
			this.pageData.current = page
			this.handleQuery()
		},
		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize
			this.handleQuery()
		},
	},
}
</script>
<style lang="less" scoped>
.task-list {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-content {
		margin-top: 16px;
		height: 100%;
	}
	&-form {
		width: fit-content;
		margin-right: 16px;
		&-title {
			margin-right: 4px;
		}
		.select {
			width: 100px;
		}
	}
}
::v-deep {
	.personnel-config-select .select-dropdown {
		top: 390px !important;
	}
	.personnel-config-select .select-label-wrap {
		max-height: 32px;
		overflow-y: auto;
	}
	.personnel-config-select .select-placeholder-wrap {
		height: 32px;
		line-height: 32px;
	}
}
</style>
