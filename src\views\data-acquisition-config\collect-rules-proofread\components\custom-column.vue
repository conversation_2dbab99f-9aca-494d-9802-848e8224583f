<template lang="pug">
.custom-column
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='300',
		:value='show',
		:title='title',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Table(:columns='columns', :data='tableData', @on-selection-change='handleSelectChange')
		template(slot='footer')
			<PERSON><PERSON>(@click='handleClose') 关闭
			Button(type='primary', @click='handleSubForm') 保存
</template>

<script>
// import { saveBaseDataItem, updateBaseDataItem } from '@/api/base-item.js'
// import { querySysList } from '@/api/common.js'
// import dataRepairVue from '../../../data-repair/data-repair.vue'
// import { addRole, updateRole } from '@/api/promise.js'
let selections = []
export default {
	name: 'CreateItemPopup',
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			title: '添加',
			formData: {
				id: '',
				roleName: '',
				memo: '',
			},
			options: [],
			formRules: {
				roleName: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
			},
			columns: [
				{
					type: 'selection',
					width: 60,
					align: 'center',
				},
				{
					title: '列名',
					key: 'name',
				},
			],
			tableData: [
				{
					name: '平台变量',
					nameKey: 'itemCode',
					_checked: true,
				},
				{
					name: '平台描述',
					nameKey: 'itemName',
					_checked: true,
				},
				{
					name: '原始位号',
					nameKey: 'originalCode',
					_checked: true,
				},
				{
					name: '原始描述',
					nameKey: 'originalName',
					_checked: true,
				},
				{
					name: '数据类型描述',
					nameKey: 'baseItemName',
					_checked: true,
				},
				{
					name: '数值',
					slot: 'itemValue',
					_checked: true,
				},
				{
					name: '单位',
					nameKey: 'unit',
					_checked: true,
				},
				{
					name: '读写状态',
					slot: 'readWriteFlag',
					_checked: true,
				},
				{
					name: '最后上传时间',
					nameKey: 'updateTime',
					_checked: true,
				},
			],
		}
	},
	computed: {},
	watch: {},
	created() {
		// querySysList().then(res => {
		// 	this.options = res.result
		// })
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// handleSelect(selection) {
		// 	console.log('log', this.tableData)
		// 	console.log(selection)
		// },
		handleSelectChange(selection) {
			selections = selection
			// this.$emit('select-columns', selections)
		},
		// 显示状态发生变化时触发
		changeModal() {
			// this.$refs.form.resetFields()
		},

		// 按钮-保存
		handleSubForm() {
			this.$emit('update:show', false)
			this.$emit('select-columns', selections)
		},

		setData(data) {
			const { id, memo, roleName } = data
			this.title = '编辑'
			// this.formData.id = id
			// this.formData.memo = memo
			// this.formData.roleName = roleName
			this.formData = { id, memo, roleName }
		},

		// 按钮-关闭
		handleClose() {
			// this.$refs.form.resetFields()
			// this.formData = {
			// 	id: '',
			// 	roleName: '',
			// 	memo: '',
			// }
			this.$emit('update:show', false)
			// this.$emit('initList')
		},
	},
}
</script>

<style lang="less" scoped>
.custom-column {
	.es-modal {
		.popup-content {
			width: 100%;
			padding: 0 40px;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
.es-modal.fixed-height .ivu-modal-content {
	border-radius: 0px;
}
.es-modal .ivu-modal {
	margin-left: 20px;
}
.custom-column .es-modal .popup-content {
	width: 100%;
	padding: 0 0px;
}
</style>
