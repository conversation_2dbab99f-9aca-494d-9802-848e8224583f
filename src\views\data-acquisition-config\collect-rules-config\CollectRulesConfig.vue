<!--
 * @Description: 采集规则配置
 * @Author: shenxh
 * @Date: 2023-04-03 09:22:40
 * @LastEditors: shenxh
 * @LastEditTime: 2024-10-25 10:13:41
-->

<template lang="pug">
.collect-rules-config
	.tab-wrap
		Tabs(:value='tabData.transChannelCode', @on-click='changeTab')
			TabPane(v-for='(item, index) in transChannelCodeList', :key='index', :label='item.label', :name='item.value')
	.collect-rules-config-wrap
		equipment-list.equipment-list(:collect-road-data='tabData', @handle-item='handleEqu')
		.collect-rules-config-con
			es-search.es-search(
				ref='es-search',
				col='4',
				:show-collapse='false',
				:modules='moduleList',
				@on-search='handleSearchBtn',
				@on-reset='handleResetBtn'
			)
			water-row.table-btn-wrap(justify='space-between', align='center')
				i
				.inflex
					Button.margin-r8(type='primary', @click='handleCreate') 新增
					Button(type='primary', @click='quickFn') 快捷配置
			.table-wrap
				es-table(
					:key='tableKey',
					:columns='columns',
					:data='tableData',
					:loading='loading',
					border,
					showPage,
					:pageData='pageData',
					@on-page-num-change='handlePageNum',
					@on-page-size-change='handlePageSize'
				)
					template(slot-scope='{ row }', slot='memo')
						span(v-html='getText(row.memo)')
					template(slot-scope='{ row }', slot='action')
						Button(type='text', :style='{ color: "#3AA7D8" }', @click='handleRowUpd(row)', size='small') 编辑
						Poptip(transfer, confirm='', title='确定删除吗？', @on-ok='handleRowDel(row)')
							Button(type='text', :style='{ color: "#EC5151" }', size='small') 删除

	create-system-popup(
		v-model='showModal',
		:data='popupType == 1 ? currentRow : {}',
		:equipment-data='currentEqu',
		:collect-road-data='tabData',
		:type='popupType',
		:trans-channel-list='moduleList[0].data[0].dataSourceList',
		@submit-form='handleSubForm'
	)
	quick-configuration-popup(
		v-model='showQuickModal',
		:collect-road-data='tabData',
		:station-code='currentEqu.stationCode',
		:tab-code='tabData.transChannelCode'
	)
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import EquipmentList from './components/equipment-list.vue'
import CreateSystemPopup from './components/create-system-popup.vue'
import QuickConfigurationPopup from './components/quick-configuration-popup.vue'
import { queryTransChannelNoEnableRule, queryTransRuleList, deleteTransRule } from '@/api/data-acquisition-config'

export default {
	name: 'collect-rules-config',
	components: {
		WaterRow,
		EquipmentList,
		CreateSystemPopup,
		QuickConfigurationPopup,
	},
	props: {},
	data() {
		return {
			tableKey: 0,
			popupType: 0,
			showModal: false,
			showQuickModal: false,
			loading: false,
			form: {},
			tabData: {},
			currentRow: {},
			currentEqu: {},
			transChannelCodeList: [],
			tableData: [],
			moduleList: [
				{
					label: '',
					value: '1',
					isDefault: true,
					keys: ['queryMethod', 'stationWord', 'xxx', 'xxx'],
					model: {
						queryMethod: 'keyWord',
						stationWord: '',
					},
					data: [
						{
							type: 2,
							key: 'queryMethod',
							formItemProps: {
								label: '查询方式',
								prop: 'queryMethod',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: false,
								disabled: false,
							},
							dataSourceList: [
								{
									label: '综合查询',
									value: 'keyWord',
								},
								{
									label: '原始位号',
									value: 'originalCode',
								},
								{
									label: '原始描述',
									value: 'originalName',
								},
								{
									label: '平台变量',
									value: 'itemCode',
								},
								{
									label: '数据项名称',
									value: 'itemName',
								},
							],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
						{
							type: 1,
							key: 'stationWord',
							formItemProps: {
								label: '',
								prop: 'stationWord',
								labelWidth: 10,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
					],
				},
			],
			columns: [
				{
					type: 'index',
					title: '序号',
					align: 'center',
					width: 80,
				},
				{
					title: '设备/对象名称',
					key: 'stationName',
				},
				{
					title: '平台唯一编码',
					key: 'stationCode',
				},
				{
					title: '原始位号',
					key: 'originalCode',
				},
				{
					title: '原始描述',
					key: 'originalName',
				},
				{
					title: '数据类型',
					key: 'baseItemCode',
				},
				{
					title: '平台变量',
					key: 'itemCode',
				},
				{
					title: '平台数据项名称',
					key: 'itemName',
				},
				{
					title: '读写状态',
					key: 'readWriteValue',
				},
				{
					title: '备注',
					slot: 'memo',
				},
				{
					title: '最后更新时间',
					key: 'updateTime',
				},
				{
					title: '操作人',
					key: 'createUser',
				},
				{
					title: '操作',
					slot: 'action',
					align: 'center',
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 50,
				pageSizeOpts: [10, 20, 50, 100, 200],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		this.queryTransChannelNoEnableRule()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 快捷配置
		quickFn() {
			if (!this.currentEqu.stationCode) {
				console.log('0000')
				this.$Message.warning('请先选择站点')
			} else {
				this.showQuickModal = true
			}
		},
		// 搜索按钮
		handleSearchBtn(params) {
			this.form = params

			this.getTableData()
		},

		// 重置按钮
		handleResetBtn() {
			this.form = {}
			this.pageData.current = 1

			this.getTableData()
		},

		// 添加按钮
		handleCreate() {
			this.currentRow = {}
			this.popupType = 0
			this.showModal = true
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum

			this.getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize

			this.getTableData()
		},

		// 行-编辑
		handleRowUpd(row) {
			this.currentRow = row
			this.popupType = 1
			this.showModal = true
		},

		// 行-删除
		handleRowDel(row) {
			this.deleteTransRule(row)
		},

		// 弹窗按钮-保存
		handleSubForm() {
			this.getTableData()
		},

		getText(str = '') {
			return str.replace(/\n/g, '<br>')
		},

		// 改变tab
		changeTab(val) {
			this.currentEqu = {}
			this.tabData = this.getTabData(val)
			this.tableKey++

			this.getTableData()
		},

		getTabData(code) {
			return this.transChannelCodeList.find(item => item.transChannelCode === code)
		},

		// 点击设备
		handleEqu(itm) {
			this.currentEqu = itm
			console.log('当前点解', this.currentEqu)

			this.getTableData()
		},

		// 获取表格数据
		getTableData() {
			const form = this.$refs['es-search']?.formProps?.model || {}
			const { queryMethod = '', stationWord = '' } = form

			this.loading = true
			const params = {
				transChannelCode: this.tabData.transChannelCode,
				stationCode: this.currentEqu.stationCode,
			}

			if (queryMethod && stationWord) {
				params[queryMethod] = stationWord
			}
			queryTransRuleList({
				...params,
				pageNum: this.pageData.current,
				pageSize: this.pageData.pageSize,
			})
				.then(res => {
					const { result = {} } = res || {}
					const { list = [], total } = result

					this.tableData = list
					this.pageData.total = total
					this.loading = false
				})
				.catch(() => {
					this.loading = false
				})
		},

		// 查询支持配置的所有采集渠道
		queryTransChannelNoEnableRule() {
			queryTransChannelNoEnableRule().then(res => {
				const { result = [] } = res || {}

				this.transChannelCodeList = result.map(item => {
					return {
						...item,
						label: item.name,
						value: item.channelCode,
						transChannelCode: item.channelCode,
					}
				})
				if (this.transChannelCodeList.length) {
					// this.transChannelCode = this.transChannelCodeList[0].value
					this.tabData = this.transChannelCodeList[0]
					this.$nextTick(() => {
						this.getTableData()
					})
				}
			})
		},

		// 删除采集渠道
		deleteTransRule(params) {
			deleteTransRule({
				id: params?.id,
			}).then(res => {
				const { responseCode } = res || {}

				if (responseCode === '100000') {
					this.$Message.success('操作成功')

					this.getTableData()
				}
			})
		},
	},
}
</script>

<style lang="less" scoped>
.collect-rules-config {
	padding: 0 16px;
	height: 100%;
	.tab-wrap {
		height: 44px;
	}

	.collect-rules-config-wrap {
		display: flex;
		width: 100%;
		height: calc(100vh - 45px);
		.equipment-list {
			flex-shrink: 0;
			margin-right: 10px;
			height: calc(100% - 10px);
		}
		.collect-rules-config-con {
			flex: 1;
			min-width: 0;
			height: 100%;
			.es-search {
				height: 55px;
				padding: 8px 0;
				margin-bottom: 8px;
			}
			.table-btn-wrap {
				margin-bottom: 8px;
				.inflex {
					display: flex;
				}
				.margin-r8 {
					margin-right: 8px;
				}
			}
			.table-wrap {
				width: 100%;
				height: calc(100% - 105px);
			}
		}
	}
}
</style>
