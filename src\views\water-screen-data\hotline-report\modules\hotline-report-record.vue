<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-05-30 16:35:42
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-10 15:24:05
-->
<template lang="pug">
.hotline-record-list
	water-row(justify='flex-start', align='center')
		.hotline-record-list-form-title 日期选择:
		DatePicker.water-margin-right-16(
			v-model='date',
			format='yyyy-MM-dd',
			:clearable='false',
			type='date',
			:options='options',
			style='width: 215px',
			@on-change='handleQuery()',
			placement='bottom-end'
		)
		Button.water-margin-left-16(type='primary', @click='handleQuery()') 查询
		Button.water-margin-left-16(type='primary', @click='handleExport') 导出
	#qrcodeDowm(style='display: none')
	WaterTable.hotline-record-list-table(border, :columns='columns', :data='tableData', :loading='loading')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { queryHotlineData } from '@/api/water-screen-data.js'
import { EventBus } from '@/utils/eventBus.js'
import { exportFile } from '@/utils/function.js'
export default {
	components: {
		WaterTable,
		WaterRow,
	},
	data() {
		return {
			date: this.$moment(new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).format('YYYY-MM-DD'),
			tableData: [],
			columns: [
				{
					title: '内容明细',
					key: 'content',
					minWidth: 160,
					align: 'center',
				},
				{
					title: '数值',
					key: 'itemValue',
					minWidth: 160,
					align: 'center',
				},
				{
					title: '最近修改时间',
					key: 'updateTime',
					minWidth: 160,
					align: 'center',
				},
			],
			loading: false,
			itemValueObj: {
				callInNum: {
					content: '昨日呼入次数',
					desc: '次',
				},
				giveUpCallNum: {
					content: '未接次数（放弃呼叫次数）',
					desc: '次',
				},
				callOutNum: {
					content: '呼出次数',
					desc: '次',
				},
				callInTime: {
					content: '呼入时长',
					desc: '分钟（不足一分钟按一分钟算）',
				},
				callOutTime: {
					content: '呼出时长',
					desc: '分钟（不足一分钟按一分钟算）',
				},
				callTotalTime: {
					content: '服务时长',
					desc: '呼入时长+呼出时长',
				},
				callLoseRate: {
					content: '呼损率',
					desc: '昨日未接次数/昨日话务量',
				},
				callTotalNum: {
					content: '昨日话务量',
					desc: '(次)昨日呼入+呼出',
				},
			},
			options: {
				disabledDate(date) {
					return date.getTime() > new Date(new Date().getTime() - 24 * 60 * 60 * 1000).getTime()
				},
			},
		}
	},
	mounted() {
		this.handleQuery()
		EventBus.$on('fresh-hotline-record', () => {
			this.handleQuery()
		})
	},
	methods: {
		handleQuery() {
			queryHotlineData({
				date: this.$moment(this.date).format('YYYY-MM-DD'),
			})
				.then(res => {
					const { result = [] } = res
					this.tableData = result.map(item => {
						return {
							content: this.itemValueObj[item.itemCode]
								? this.itemValueObj[item.itemCode].content
								: item.item,
							itemCode: item.itemCode,
							itemValue: item.value,
							updateTime: item.updateTime,
						}
					})
				})
				.catch(() => {
					this.tableData = []
				})
		},
		handleExport() {
			const baseUrl = `${location.origin}/api`
			let url =
				baseUrl + '/waterPlat/fillData/service/export?date=' + this.$moment(this.date).format('YYYY-MM-DD')
			exportFile(url)
		},
	},
}
</script>
<style scoped lang="less">
.hotline-record-list {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-form {
		margin-right: 24px;
		&-title {
			margin-right: 4px;
		}
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
}
</style>
