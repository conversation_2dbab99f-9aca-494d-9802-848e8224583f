<template>
	<!--数据绑定-->
	<Modal
		:value="show"
		id="flowModel"
		:styles="{ top: '15px' }"
		:mask-closable="false"
		:width="modalWidth"
		@on-cancel="handleCancel"
		title="流量设置"
	>
		<div style="display: flex; flex-direction: row">
			<!-- 左侧图形 -->
			<div
				id="flowfaderId"
				@dragover="allowDrop"
				@drop="drop($event)"
				:style="{
					width: imgw + 'px',
					height: imgh + 'px',
					position: 'relative',
				}"
			>
				<img :src="processImage" id="flowbottomImg" style="position: relative" width="100%" height="100%" />
				<template v-for="(line, index) in editLines">
					<!-- width: '16px', -->
					<img
						style="position: absolute; cursor: pointer"
						:style="{
							left: point.x + 'px',
							top: point.y + 'px',
							width: '6px',
						}"
						:title="point.key"
						:id="point.key"
						v-for="(point, pindex) in line.points"
						:key="index + pindex + point.key"
						src="https://eslink-iot.oss-cn-beijing.aliyuncs.com/WDS-editPoint.png"
					/>
				</template>
			</div>
			<!-- 右侧数据展示 -->
			<div id="flowRight">
				<Row style="width: 267px; display: flex; justify-content: flex-end">
					<i-col
						span="12"
						id="imgf"
						style="height: 20px; position: relative; margin-left: 86px; margin-top: 10px; width: 20%"
					></i-col>
					<i-col span="12">
						<Button
							type="primary"
							icon="md-add"
							size="small"
							@click="butClick('addLine')"
							style="margin-right: 5px"
						>
							加线
						</Button>
						<Button type="primary" size="small" @click="butClick('reserveBtn')">保存</Button>
					</i-col>
				</Row>
				<Row
					class="form-content"
					style="width: 267px; padding-left: 10px; padding-top: 10px; overflow-y: auto; overflow-x: hidden"
					:style="{
						height: imgh - 50 + 'px',
					}"
				>
					<template v-for="(line, index) in lines">
						<i-col span="24" class="table_top" :key="index">
							<i-col span="8" class="table_right" style="font-weight: 800">
								{{ line.label }}{{ index }}
							</i-col>
							<i-col span="10" class="line-height">
								<Button @click="butClick('addPoint', index)" size="small" style="margin-right: 2px">
									加点
								</Button>
								<!-- @click="butClick('addPoint', index)" -->
								<!-- <Button size="small">预览</Button> -->
							</i-col>
							<i-col span="4" class="line-height" v-if="index === 0"></i-col>
							<i-col span="4" class="line-height" v-else>
								<Button
									type="error"
									shape="circle"
									icon="md-remove"
									@click="butClick('deleteLine', index)"
									size="small"
								></Button>
							</i-col>
						</i-col>
						<i-col
							span="24"
							class="table_border"
							:key="index + pIndex + position.key"
							v-for="(position, pIndex) in line.points"
						>
							<i-col span="24" class="table_top">
								<i-col span="6" class="table_right">坐标{{ index + '_' + pIndex }}:</i-col>
								<i-col span="8" class="line-height table-point">
									{{ position.x + '/' + position.y }}
								</i-col>
								<i-col span="10" class="line-height table-button">
									<Icon
										type="md-arrow-round-up"
										title="向上偏移"
										@click="butClick('up', index, pIndex)"
									/>
									<Icon
										type="md-arrow-round-down"
										title="向下偏移"
										@click="butClick('down', index, pIndex)"
									/>
									<Icon
										type="md-arrow-round-back"
										title="向左偏移"
										@click="butClick('left', index, pIndex)"
									/>
									<Icon
										type="md-arrow-round-forward"
										title="向右偏移"
										@click="butClick('right', index, pIndex)"
									/>
									<Button
										shape="circle"
										icon="md-remove"
										@click="butClick('deletePoint', index, pIndex)"
										size="small"
									></Button>
								</i-col>
							</i-col>
						</i-col>
					</template>
				</Row>
			</div>
		</div>
		<div slot="footer" style="height: 0; border-bottom: none"></div>
	</Modal>
</template>

<script>
import { get2dProcessConfigList, getEquipmentList, getEquipmentTypeList, saveOrUpdate2dProcess } from '@/api/setting'

export default {
	components: {},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '新增',
		},
	},
	computed: {
		stationListLength() {
			return this.pointList.length
		},
	},
	mounted() {
		this.screenWidth = document.body.clientWidth //屏幕可用宽度
		this.screenHeight = document.body.clientHeight //屏幕可用高度
		this.ableImgWidth = this.screenWidth - 15 * 2 - 10 * 2 - 267
		this.ableImgHeight = this.screenHeight - 15 * 2 - 10 * 2 - 26
	},
	data() {
		return {
			listLoading: true,
			pointList: [],
			formItem: {
				applicationName: 'iwater',
				mtStationId: '',
				processIndex: '',
				processName: '',
				dataInterval: 0,
			},
			processImage: '',
			modalWidth: 500,
			imgw: null,
			imgh: null,
			imgList: [],
			equipmentList: [],
			typeList: [],
			cityList: [
				{
					value: 0,
					label: '展示数据',
				},
				{
					value: 1,
					label: '展示图片',
				},
				{
					value: 2,
					label: '展示日期',
				},
				{
					value: 3,
					label: '展示时间',
				},
			],
			screenWidth: '',
			screenHeight: '',
			ableImgWidth: '',
			ableImgHeight: '',
			currentKey: '',
			lines: [
				{
					label: '折线',
					points: [],
				},
			],
			editLines: [],
			currentLineIndex: 0,
			currentPointIndex: 0,
			currentRow: {},
		}
	},
	methods: {
		init() {
			get2dProcessConfigList({ applicationName: 'iwater' }).then(res => {
				this.pointList = res.result.list
			})
		},

		allowDrop(e) {
			e.preventDefault()
		},
		//放置
		drop(e) {
			// let modal = document.getElementsByClassName('ivu-modal')
			// let left = modal[0].offsetLeft
			// let top = modal[0].offsetTop
			let data = e.dataTransfer.getData('Text')
			if (data === this.currentKey) {
				// if (e.offsetX < 10) return
				let flowfaderId = document.getElementById('flowfaderId')
				this.imgLeft = (e.offsetX - this.initw).toFixed(2)
				this.imgTop = (e.offsetY - this.inith).toFixed(2)
				console.log(e.offsetX, this.initw, e.offsetY, this.inith)
				var img = document.getElementById(this.currentKey)
				img.style.display = 'block'
				img.style.position = 'absolute'
				img.style.left = this.imgLeft + 'px'
				img.style.top = this.imgTop + 'px'

				this.lines[this.currentLineIndex].points[this.currentPointIndex].x = this.imgLeft
				this.lines[this.currentLineIndex].points[this.currentPointIndex].y = this.imgTop
				flowfaderId.appendChild(document.getElementById(data))
			} else {
				if (document.getElementById(this.currentKey)) {
					const img = document.getElementById(this.currentKey)
					img.style.position = 'absolute'
					img.style.display = 'block'
				}
			}
		},

		// 新增数据点图标
		newImg() {
			const _this = this
			this.currentStatus = true
			let imgid = document.getElementById('imgf')
			//创建一个新的点在右侧，准备拖拽用
			var img = document.createElement('img')
			img.setAttribute('draggable', true)

			this.currentPointIndex = this.lines[this.currentLineIndex].points.length
			this.currentKey = 'img' + this.currentLineIndex + '_' + this.currentPointIndex
			img.setAttribute('id', this.currentKey)
			img.style.width = '6px'

			this.lines[this.currentLineIndex].points.push({
				x: '0',
				y: '0',
				key: this.currentKey,
			})

			img.src = 'https://eslink-iot.oss-cn-beijing.aliyuncs.com/WDS-editPoint.png'
			img.style.position = 'absolute'
			img.style.cursor = 'pointer'
			img.title = this.currentKey

			img.ondragstart = function (e) {
				const obj = document.getElementById('flowModel')
				const modalW = obj.getElementsByClassName('ivu-modal')[0].offsetLeft
				const modalT = obj.getElementsByClassName('ivu-modal')[0].offsetTop
				const RightL = document.getElementById('flowRight').offsetLeft
				const RightT = document.getElementById('flowRight').offsetTop
				const dragBlockL = document.getElementById('imgf').offsetLeft
				const dragBlockT = document.getElementById('imgf').offsetTop
				const mainPicL = document.getElementById('flowfaderId').offsetLeft
				const mainPicT = document.getElementById('flowfaderId').offsetTop
				if (e.path[1].id === 'imgf') {
					_this.initw = e.pageX - RightL - dragBlockL - modalW
					_this.inith = e.pageY - RightT - dragBlockT - modalT
					console.log('initw:' + _this.initw, 'inith:' + _this.inith)
					console.log(RightL, dragBlockL, modalW)
				} else if (e.path[1].id === 'flowfaderId') {
					_this.initw = e.pageX - e.target.offsetLeft - modalW - mainPicL
					_this.inith = e.pageY - e.target.offsetTop - modalT - mainPicT
					console.log('initw:' + _this.initw, 'inith:' + _this.inith)
				}
				e.dataTransfer.setData('Text', e.target.id)
			}
			img.onclick = function () {
				_this.lines[_this.currentLineIndex].points[_this.currentKey] = {
					x: _this.imgLeft,
					y: _this.imgTop,
					key: _this.currentKey,
				}
				// _this.point.x = _this.imgLeft
				// _this.point.y = _this.imgTop
			}
			imgid.appendChild(img)
		},

		//保存新增数据点
		reserveBtn() {
			// 需要真实数据
			const _this = this
			const lines = JSON.parse(JSON.stringify(this.lines))
			lines.forEach(line => {
				line.points.forEach(point => {
					point.x = ((point.x * _this.naturalWidth) / _this.imgw).toFixed(2)
					point.y = ((point.y * _this.naturalHeight) / _this.imgh).toFixed(2)
				})
			})
			const param = { ...this.currentRow, flow: JSON.stringify(lines) }
			saveOrUpdate2dProcess(param).then(res => {
				console.log(res)
				this.$Message.success('提交成功!')
				this.$emit('seccessBack')
			})
			// localStorage.setItem('flow', JSON.stringify(lines))
			// this.lines[this.currentLineIndex].points[this.currentKey] = parmas
			// this.currentKey = ''
			// this.currentStatus = false
			// var box = document.getElementById(this.currentKey)
			// box.parentNode.removeChild(box)
			// })
		},

		// 取消新增数据点
		unreserveBtn() {
			this.currentStatus = false
			var box = document.getElementById(this.currentKey)
			box.parentNode.removeChild(box)
		},

		// 获取站点列表
		getStationList() {
			this.listLoading = true
		},
		// 弹窗显隐事件
		handleVisibleChange(value) {
			// 每次展示时
			if (value) {
				// 获取站点列表
				this.getStationList()
			}
		},

		//新增点入口
		openGy(data) {
			this.editLines = []
			this.lines = []
			this.processImage = data.processImage
			this.currentRow = data
			this.deal(data)
		},
		deal(data) {
			const _this = this
			this.equipmentList = []
			this.processImage = data.processImage
			this.techProcessId = data.id
			this.parentID = data.id
			if (document.getElementById(this.currentKey)) {
				var box = document.getElementById(this.currentKey)
				box.parentNode.removeChild(box)
				this.currentStatus = false
			}
			//获取绑定的设备数据信息
			this.shebList(data.applicationName, data.stationCode)
			let img = new Image()
			// let showImg = document.getElementById('flowbottomImg')
			img.src = data.processImage
			img.onload = function () {
				// console.log('图片实际宽度----', img.naturalWidth, _this.ableImgWidth)
				// console.log('图片实际高度----', img.naturalHeight, _this.ableImgHeight)
				// console.log('modal宽度-------',_this.modalWidth)
				// console.log('浏览器可用宽度-----',_this.screenWidth)
				// img.naturalWidth   图片实际宽度
				// img.naturalHeight   图片实际高度
				// this.screenWidth      浏览器可用宽度
				// this.modalWidth     modal宽度
				let conversion = false
				// if (
				// 	img.naturalWidth <= _this.ableImgWidth &&
				// 	img.naturalHeight <= _this.ableImgHeight
				// ) {
				//第一种情况,图片长宽小于可容图片的长宽，按实际图片长宽来显示
				_this.modalWidth = img.naturalWidth + 10 * 2 + 267
				_this.imgw = img.naturalWidth
				_this.imgh = img.naturalHeight
				// } else if (
				// 	_this.ableImgWidth / img.naturalWidth <
				// 	_this.ableImgHeight / img.naturalHeight
				// ) {
				// 	//第二种情况,按照可容宽计算出fix图片的长度
				// 	_this.imgw = _this.ableImgWidth
				// 	_this.imgh =
				// 		(_this.ableImgWidth * img.naturalHeight) /
				// 		img.naturalWidth
				// 	_this.modalWidth = _this.ableImgWidth + 10 * 2 + 267
				// 	conversion = true
				// } else if (
				// 	_this.ableImgWidth / img.naturalWidth >
				// 	_this.ableImgHeight / img.naturalHeight
				// ) {
				// 	//第三情况,按照可容高度算出fix图片的宽度
				// 	_this.imgh = _this.ableImgHeight
				// 	_this.imgw =
				// 		(_this.ableImgHeight * img.naturalWidth) /
				// 		img.naturalHeight
				// 	_this.modalWidth = _this.imgw + 10 * 2 + 267
				// 	conversion = true
				// }
				_this.naturalWidth = img.naturalWidth
				_this.naturalHeight = img.naturalHeight
				//已有数据坐标
				_this.getDian(data.flow, conversion)
				// setTimeout(() => {
				//     _this.addGYmodel = true
				// }, 2000)
			}
		},

		//在工艺图上已有数据坐标打点
		getDian(lines, conversion) {
			// console.log('222222222222', id)
			console.log('33333333', conversion)
			this.addGYmodel = true
			// this.parmas = {
			// 	id: id,
			// }
			// let lines = localStorage.getItem('flow')
			// get2dProcessList({
			// 	applicationName: 'iwater',
			// }).then(res => {
			// 	console.log(res)
			// })
			if (!lines) {
				return
			}
			lines = JSON.parse(lines)
			debugger
			if (conversion) {
				lines.forEach(line => {
					line.points.forEach(item => {
						item.x = (this.imgw * item.x) / this.naturalWidth
						item.y = (this.imgh * item.y) / this.naturalHeight
					})
				})
			}
			this.lines = lines
			this.editLines = JSON.parse(JSON.stringify(lines))
			// get2dProcessConfigList(id).then(res => {
			// 	console.log(res)
			// 	this.pointList = res.result
			// 	}
			// })
		},
		butClick(from, index, pIndex) {
			switch (from) {
				case 'reserveBtn':
					this.reserveBtn()
					break
				case 'addLine':
					this.lines.push({
						label: '折线',
						points: [],
					})
					break
				case 'deleteLine':
					this.deleteLine(index)
					break
				case 'addPoint':
					this.currentLineIndex = index
					this.newImg()
					break
				case 'deletePoint':
					this.deletePoint(index, pIndex)
					break
				case 'up':
					this.upPoint(index, pIndex)
					break
				case 'down':
					this.downPoint(index, pIndex)
					break
				case 'left':
					this.leftPoint(index, pIndex)
					break
				case 'right':
					this.rightPoint(index, pIndex)
					break
				default:
					break
			}
		},
		deleteLine(index) {
			let points = this.lines[index].points
			if (points.length > 0) {
				points.forEach(point => {
					const box = document.getElementById(point.key)
					box.parentNode.removeChild(box)
				})
			}
			this.lines.splice(index, 1)
		},
		upPoint(index, pIndex) {
			const dom = this.lines[index].points[pIndex].key
			const box = document.getElementById(dom)
			// box.parentNode.removeChild(box)
			const newY = box.offsetTop - 1
			box.style.top = newY + 'px'
			// box.offsetTop = newY
			this.lines[index].points[pIndex].y = newY
		},
		downPoint(index, pIndex) {
			const dom = this.lines[index].points[pIndex].key
			const box = document.getElementById(dom)
			// box.parentNode.removeChild(box)
			const newY = box.offsetTop + 1
			box.style.top = newY + 'px'
			// box.offsetTop = newY
			this.lines[index].points[pIndex].y = newY
		},
		leftPoint(index, pIndex) {
			const dom = this.lines[index].points[pIndex].key
			const box = document.getElementById(dom)
			const newX = box.offsetLeft - 1
			box.style.left = newX + 'px'
			this.lines[index].points[pIndex].x = newX
		},
		rightPoint(index, pIndex) {
			const dom = this.lines[index].points[pIndex].key
			const box = document.getElementById(dom)
			const newX = box.offsetLeft + 1
			box.style.left = newX + 'px'
			this.lines[index].points[pIndex].x = newX
		},
		deletePoint(index, pIndex) {
			const dom = this.lines[index].points[pIndex].key
			const box = document.getElementById(dom)
			box.parentNode.removeChild(box)
			this.lines[index].points.splice(pIndex, 1)
		},
		shebList(name, code) {
			getEquipmentList({ applicationName: name, stationCode: code }).then(res => {
				this.equipmentList = res.result
			})
		},
		handleRemove() {
			this.formItem.processImage = ''
		},
		// 弹窗取消事件
		handleCancel() {
			this.$emit('update:show', false)
		},
		// 重置
		handleReset() {},
		// 确定
		handleCheck() {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					console.log(111)
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
		// 根据关联设备获取数据类型列表
		getType(val) {
			const _this = this
			getEquipmentTypeList({
				applicationName: 'iwater',
				mtMonitorObjectId: val,
			}).then(res => {
				_this.typeList = res.result
			})
		},
	},
}
</script>
<style lang="less">
.station-modal {
	.custom-modal {
		.ivu-modal {
			height: 48% !important;
		}
		.ivu-modal-body {
			height: calc(100% - 96px);
		}
	}
}
</style>
<style lang="less" scoped>
.station-modal-content {
	overflow-x: hidden;
	.ivu-checkbox-wrapper {
		display: inline-block;
		margin: 0 16px 16px 0;
	}
}
.demo-upload-list {
	display: inline-block;
	width: 60px;
	height: 60px;
	text-align: center;
	line-height: 60px;
	border: 1px solid transparent;
	border-radius: 4px;
	overflow: hidden;
	background: #fff;
	position: relative;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
	margin-right: 4px;
}
.demo-upload-list img {
	width: 100%;
	height: 100%;
}
::v-deep {
	.ivu-modal-header {
		margin-top: 0;
	}
	.ivu-modal-body {
		padding: 0;
	}
	.ivu-modal-content {
		padding: 10px !important;
		display: flex;
		flex-direction: column;
		flex: 1;
		width: 100%;
	}
	.ivu-modal-footer {
		border-top: none;
		height: auto;
		padding: 0;
		overflow: hidden;
	}
	.ivu-modal-header {
		border-bottom: none;
		height: auto;
		padding: 0;
		margin-top: -2px;
	}
}
.table_right {
	height: 32px;
	line-height: 32px;
}
.demo-upload-list-cover {
	display: none;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.6);
}
.demo-upload-list:hover .demo-upload-list-cover {
	display: block;
}
.demo-upload-list-cover i {
	color: #fff;
	font-size: 20px;
	cursor: pointer;
	margin: 0 2px;
}
.table_top {
	// border-top: 1px solid #f2f2f2;
	margin-top: 10px;
}
.form-content {
	&::-webkit-scrollbar {
		width: 2px;
	}
	&::-webkit-scrollbar-thumb {
		// height: 64px;
		background: linear-gradient(180deg, #117cae 0%, #124078 100%) !important;
	}
	&::-webkit-scrollbar-track,
	&::-webkit-scrollbar-track:hover {
		background: transparent;
	}
	&::-webkit-scrollbar-corner {
		background-color: #00101f;
	}
}
.line-height {
	height: 32px;
	line-height: 32px;
	padding-left: 5px;
}
.table-button {
	background: #f1f1f1cc;
	i {
		cursor: pointer;
		padding: 0 2px;
	}
}
.table-point {
	cursor: not-allowed;
}
::v-deep {
	.ivu-select {
		z-index: inherit;
	}
	.ivu-input-number-input {
		color: #fff;
		background: #133a5e;
		border: none;
	}
}
</style>
