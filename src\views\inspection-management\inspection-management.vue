<!--
 * @Description: 巡检管理
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-02-18 16:57:10
 * @LastEditors: ho<PERSON>an
 * @LastEditTime: 2022-09-08 10:36:00
-->
<template lang="pug">
.inspection-management
	.inspection-management-content
		Tabs(v-model='tabValue', :animated='false')
			TabPane(label='巡检计划', name='plan')
				plan-list(@create='handleCreate')
			TabPane(label='巡检任务列表', name='task')
				task-list(@check='handleCheck')
	//- 创建计划弹窗
	add-plan(ref='addPlan', :show.sync='showPlanModal', :type='type')
	//- 任务详情弹窗
	task-detail(ref='taskDetail', :show.sync='showTaskModal')
</template>
<script>
import PlanList from './modules/PlanList.vue'
import TaskList from './modules/TaskLists.vue'
import AddPlan from './modules/AddPlan.vue'
import TaskDetail from './modules/TaskDetail.vue'
export default {
	name: '',
	components: {
		PlanList,
		TaskList,
		AddPlan,
		TaskDetail,
	},
	data() {
		return {
			tabValue: 'plan',
			type: 'add',
			showPlanModal: false,
			showTaskModal: false,
		}
	},
	created() {
		window.parent.postMessage(
			{
				cmd: 'getCommonInfo',
			},
			'*',
		)
	},
	methods: {
		handleCreate({ type, row }) {
			this.type = type
			this.showPlanModal = true
			if (type !== 'add') {
				this.$refs.addPlan.init(row)
			}
		},
		handleCheck(row) {
			this.showTaskModal = true
			this.$refs.taskDetail && this.$refs.taskDetail.getTaskDetail(row.Id)
		},
	},
}
</script>
<style lang="less" scoped>
.inspection-management {
	height: 100%;
	padding: 16px;
	background-color: rgb(242 242 242) !important;
	&-content {
		height: 100%;
		padding: 16px;
		background: #fff;
	}
	::v-deep {
		.ivu-tabs {
			display: flex;
			flex-direction: column;
			height: 100%;
			.ivu-tabs-content {
				flex: 1;
			}
			.ivu-tabs-tabpane {
				height: 100%;
			}
		}
	}
}
</style>
