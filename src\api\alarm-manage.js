/*
 * @Description: 报警管理
 * @Author: shenxh
 * @Date: 2022-03-09 10:17:23
 * @LastEditors: <PERSON><PERSON>an
 * @LastEditTime: 2024-08-08 14:15:41
 */

import { POST } from '@/utils/request'

// 处理报警（消缺）
export function disposeAlarmByEliminate(params) {
	return POST({
		url: '/alarm/disposeAlarmByEliminate',
		params,
	})
}

// 处理报警（创建工单）
export function disposeAlarmByWorkOrder(params) {
	return POST({
		url: '/alarm/disposeAlarmByWorkOrder',
		params,
	})
}

// 报警查询
export function queryPage(params) {
	return POST({
		url: '/alarm/queryPage',
		params,
	})
}

// 报警统计（数量）
export function queryAlarmInfoCount(params) {
	return POST({
		url: '/alarm/queryAlarmInfoCount',
		params,
	})
}
// 一段时间内报警次数
export function queryAlarmInfoTotal(params) {
	return POST({
		url: '/alarm/queryAlarmInfoTotal',
		params,
	})
}
// 报警top5
export function queryAlarmInfoGroupByStationTop(params) {
	return POST({
		url: '/alarm/queryAlarmInfoGroupByStationTop',
		params,
	})
}
// 告警次数统计分布
export function queryAlarmInfoGroupByArea(params) {
	return POST({
		url: '/alarm/queryAlarmInfoGroupByArea',
		params,
	})
}
