<template>
	<div style="height: 20px" class="flex">
		<div class="block"></div>
		<div class="title_text" :style="{ 'margin-top': top + 'px', width: width + '%' }">
			<slot></slot>
		</div>
	</div>
</template>
<script>
export default {
	props: ['top', 'width'],
	data() {
		return {}
	},
	mounted() {},
	methods: {},
}
</script>
<style scoped>
.block {
	width: 10px;
	height: 10px;
	background: #3b95e9;
}
.title_text {
	margin-left: 10px;
	font-size: 14px;
}
</style>
