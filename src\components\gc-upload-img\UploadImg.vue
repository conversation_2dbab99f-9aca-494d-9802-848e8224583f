<!--
 * @Description: 图片上传
 * @Author: shenxh
 * @Date: 2024-03-12 11:25:29
 * @LastEditors: shenxh
 * @LastEditTime: 2024-03-12 15:39:10
-->

<template lang="pug">
.upload-img
	.demo-upload-list(v-for='(item, index) in uploadList || []')
		template(v-if='item')
			img(:src='item.url')
			.demo-upload-list-cover
				Icon(type='ios-eye-outline', @click.native='handleView(item)')
				Icon(type='ios-trash-outline', @click.native='handleRemove(item, index)')
	Upload(
		v-if='uploadList.length < maxNum',
		ref='upload',
		:show-upload-list='false',
		:default-file-list='uploadList',
		:accept='accept',
		:format='format',
		:before-upload='handleBeforeUpload',
		type='drag',
		:disabled='disabled',
		action='',
		style='display: inline-block; width: 58px'
	)
		div(style='width: 58px; height: 58px; line-height: 58px')
			Icon(type='ios-camera', size='20')
	Modal(title='View Image', v-model='showModal', :title='currentImg.name || ""')
		img(v-if='showModal', :src='currentImg.url', style='width: 100%')
</template>

<script>
export default {
	name: 'upload-img',
	components: {},
	model: {
		prop: 'value',
		event: 'setVal',
	},
	props: {
		value: Array,
		accept: {
			type: String,
			default: '.jpg,.jpeg,.png,.svg,.gif,.webp',
		},
		format: {
			type: Array,
			default: () => ['jpg', 'jpeg', 'png', 'svg', 'gif', 'webp'],
		},
		disabled: Boolean,
		maxNum: {
			type: Number,
			default: 1,
		},
		maxSize: {
			type: Number,
			default: 200, // kb
		},
	},
	data() {
		return {
			showModal: false,
			uploadList: [],
			currentImg: {},
		}
	},
	computed: {},
	watch: {
		value: {
			handler(val) {
				this.uploadList = val || []
			},
			deep: true,
		},
	},
	created() {
		this.uploadList = this.value || []
	},
	mounted() {
		this.uploadList = this.$refs.upload.fileList
	},
	beforeDestroy() {},
	methods: {
		handleView(itm) {
			this.currentImg = itm || {}
			this.showModal = true
		},

		handleRemove(itm, idx) {
			this.uploadList.splice(idx, 1)
			this.$emit('setVal', this.uploadList)
		},

		handleBeforeUpload(file) {
			if (file.size / 1024 > this.maxSize) {
				this.$Message.warning('文件大小超过限制')

				return false
			}

			const data = new FormData()

			data.append('files', file)

			this.$axios.post('/backgroundImage/image/upload', data).then((res = {}) => {
				const { imageUrl, name } = res.result[0] || {}

				this.uploadList.push({
					name,
					url: imageUrl,
				})

				this.$emit('setVal', this.uploadList)
			})

			return false
		},
	},
}
</script>

<style lang="less" scoped>
.upload-img {
	.demo-upload-list {
		display: inline-block;
		width: 60px;
		height: 60px;
		text-align: center;
		line-height: 60px;
		border: 1px solid transparent;
		border-radius: 4px;
		overflow: hidden;
		background: #fff;
		position: relative;
		box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
		margin-right: 4px;
	}
	.demo-upload-list img {
		width: 100%;
		height: 100%;
	}
	.demo-upload-list-cover {
		display: none;
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		background: rgba(0, 0, 0, 0.6);
	}
	.demo-upload-list:hover .demo-upload-list-cover {
		display: block;
	}
	.demo-upload-list-cover i {
		color: #fff;
		font-size: 20px;
		cursor: pointer;
		margin: 0 2px;
	}
}
</style>
