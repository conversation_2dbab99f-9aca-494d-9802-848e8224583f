/*
 * @Descripttion:
 * @version:
 * @Author: heliping
 * @Date: 2021-07-06 13:37:19
 * @LastEditors: shenxh
 * @LastEditTime: 2024-09-18 10:58:19
 */

import Vue from 'vue'
import App from './App.vue'
import router from './router'
import moment from 'moment'
import { ESVCommon } from 'esvcp-pc'
import { ESVHttpUtil } from 'esvcp-pc'
import VueUeditorWrap from 'vue-ueditor-wrap'

import './iview'
import './styles/index.less'
import '@/assets/iconfont/iconfont.css'
// 公共方法模块
Vue.prototype.$moment = moment
Vue.prototype.$common = ESVCommon
const axios = ESVHttpUtil.axios
Vue.prototype.$axios = axios

Vue.config.productionTip = false
Vue.component('vue-ueditor-wrap', VueUeditorWrap)

new Vue({
	router,
	render: h => h(App),
}).$mount('#app')
