<template lang="pug">
.security-platform-manage
	.header-wrap
		es-header.header(title='控件配置')
	water-row.table-btn-wrap(justify='space-between', align='center')
		i
		.button
			Button.water-margin-right-8(type='primary', @click='handleCreateGroup') 新增分组
			Button(type='primary', @click='handleCreate') 添加

	.security-platform-manage-content
		Table.security-platform-manage-table(
			:columns='columns',
			:data='tableData',
			:loading='loading',
			:height='700',
			border
		)
			template(slot-scope='{ row }', slot='iconUrl')
				//- span {{ row.type == 0 ? '全局' : '租户' }}
				img(:src='row.iconUrl', :style='{ height: "30px" }')
			template(slot-scope='{ row }', slot='action')
				//- Button(
				//- 	type="text",
				//- 	:style="{ color: '#3AA7D8' }",
				//- 	@click="handleUpd(row)",
				//- 	size="small") 编辑
				Poptip(transfer, confirm='', title='确定删除吗？', @on-ok='handleDel(row)')
					But<PERSON>(type='text', :style='{ color: "#EC5151" }', size='small') 删除

	//- 创建系统弹窗
	create-control-popup(
		:show.sync='showModal',
		:groupIdList='groupIdList',
		:activeGroup='activeGroup',
		:data='currentRow',
		@operation-suc='handleSuc'
	)
	create-group-popup(v-model='showGroupModal', :data='currentRow', @submit-form='handleSuc')
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import CreateControlPopup from './components/CreateControlPopup.vue'
import CreateGroupPopup from './components/CreateGroupPopup.vue'
import { controls, deleteControl, getProcessControls } from '@/api/editor'

export default {
	name: 'security-platform-manage',
	components: {
		WaterRow,
		CreateControlPopup,
		CreateGroupPopup,
	},
	props: {},
	data() {
		return {
			currentRow: {},
			showModal: false,
			showGroupModal: false,
			loading: false,
			platformList: [],
			tableData: [],
			columns: [
				{
					title: '序号',
					type: 'index',
					align: 'center',
					width: 65,
				},
				{
					title: '控件名称',
					key: 'name',
				},
				{
					title: '控件',
					slot: 'iconUrl',
				},
				{
					title: '控件分组',
					key: 'groupName',
				},
				{
					title: '类型',
					key: 'controlsType',
				},
				{
					title: '操作',
					align: 'center',
					slot: 'action',
					width: 130,
				},
			],
			groupIdList: [],
			activeGroup: '',
		}
	},
	computed: {},
	watch: {},
	created() {
		this.getTableData()
		this._getProcessControls()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 获取控件分组
		_getProcessControls() {
			return new Promise((resolve, reject) => {
				getProcessControls()
					.then(res => {
						resolve(res)
						const { result } = res
						if (result && result.length > 0) {
							this.groupIdList = result.map(item => {
								return {
									id: item.id + '',
									name: item.name,
								}
							})
							this.activeGroup = this.groupIdList[0].id
						}
					})
					.catch(err => {
						reject(err)
					})
			})
		},
		// 操作成功方法
		handleSuc() {
			this.getTableData()
			this._getProcessControls()
		},
		// 添加按钮
		handleCreate() {
			this.currentRow = {}
			this.showModal = true
		},
		handleCreateGroup() {
			this.currentRow = {}
			this.showGroupModal = true
		},

		// 编辑按钮
		handleUpd(row) {
			this.currentRow = { ...row }
			this.showModal = true
		},

		// 删除按钮
		handleDel(row) {
			this.currentRow = { ...row }
			this._delSecurityPlatform()
		},

		// 显示/隐藏密码
		handleEye(row, showPwd) {
			let arr = [...this.tableData]

			if (arr[row._index]) {
				arr[row._index].showPwd = showPwd
				this.tableData = arr
			}
		},

		// 设置密码
		setPwd(pwd, showPwd = false) {
			let str = pwd

			if (pwd && !showPwd) {
				str = pwd[0] + '****' + pwd[pwd.length - 1]
			}

			return str
		},

		// 弹窗按钮-保存
		handleSubForm() {
			this.getTableData()
		},

		// 获取表格数据
		getTableData() {
			// const formData = {}
			// const { current: pageNum, pageSize } = this.pageData

			this.loading = true
			controls()
				.then(res => {
					const data = res.result

					this.loading = false
					if (data) {
						this.tableData = data || []
					}
				})
				.catch(() => {
					this.loading = false
				})
		},

		// 删除安防平台
		_delSecurityPlatform() {
			deleteControl({
				id: this.currentRow.id,
			}).then(() => {
				this.$Message.success('删除成功')

				this.getTableData()
			})
		},
	},
}
</script>

<style lang="less" scoped>
.security-platform-manage {
	width: 100%;
	height: 100%;
	padding: 0 16px;
	.table-btn-wrap {
		margin: 10px 0;
	}
	.security-platform-manage-content {
		width: 100%;
		height: calc(100vh - 160px);
		.icon-eye {
			margin-left: 5px;
			cursor: pointer;
			color: #41a8ed;
		}
	}
}
</style>
