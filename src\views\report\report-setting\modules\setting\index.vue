<!--
 * @Description: 报表管理
 * @Version: 
 * @Autor: ho<PERSON><PERSON>
 * @Date: 2024-03-14 16:32:31
 * @LastEditors: houyan
 * @LastEditTime: 2024-03-19 15:38:33
-->
<template lang="pug">
.setting
	water-row(justify='space-between', align='center')
		water-row(justify='flex-start', align='center')
			.query-title 报表名称:
			Input(v-model='formData.name', clearable, placeholder='请输入报表名称', style='width: 200px')
			.query-title.water-margin-left-8 报表编码:
			Input(v-model='formData.code', clearable, placeholder='请输入报表编码', style='width: 200px')
			.query-title.water-margin-left-8 启用状态:
			Select(v-model='formData.status', clearable, style='width: 200px')
				Option(:value='0') 关闭
				Option(:value='1') 启用
			.query-title.water-margin-left-8 所属业务:
			Select(v-model='formData.businessId', style='width: 200px', clearable)
				Option(v-for='item in businessData', :key='item.id', :value='item.id') {{ item.name }}
			Button.water-margin-left-8(type='primary', @click='handleQuery') 查询
		div
			Button.mr10(type='primary', @click='handleSetBusiness') 管理报表业务
			Button(type='primary', @click='handleSet("add")') 添加
	WaterTable.table-container.mt10(border, :columns='columns', :data='tableData', :loading='loading')
	Page(
		:total='pageTotal',
		show-elevator,
		show-sizer,
		@on-page-size-change='handleChangeSize',
		@on-change='handleChangePage'
	)
	report-modal(v-model='isShow', ref='modalRef', :title='reportTitle', @updateSuccess='_getReportList')
	business-modal(v-model='isBusiness', ref='businessRef')
</template>
<script>
import WaterRow from '@/components/gc-water-row'
import ReportModal from './ReportModal.vue'
import BusinessModal from './BusinessModal.vue'
import { getReportList, deleteReportById, updateReport, getBusinessList } from '@/api/report.js'
import WaterTable from '@/components/gc-water-table'
import { Switch, Poptip, Button } from '@eslink/esvcp-pc-ui'
export default {
	components: {
		WaterRow,
		WaterTable,
		ReportModal,
		BusinessModal,
	},
	data() {
		return {
			formData: {
				name: '',
				code: '',
				status: '',
				businessId: '',
			},
			columns: [
				{
					title: '报表名称',
					key: 'name',
					align: 'center',
					width: 140,
				},
				{
					title: '报表编码',
					key: 'code',
					align: 'center',
					width: 110,
				},
				{
					title: '报表url',
					align: 'center',
					key: 'url',
				},
				{
					title: '所属业务',
					key: 'businessName',
					align: 'center',
					width: 200,
				},
				{
					title: '报表参数',
					key: 'param',
					align: 'center',
					width: 200,
				},
				{
					title: '报表排序',
					key: 'sort',
					align: 'center',
					width: 100,
				},
				{
					title: '启用状态',
					key: 'status',
					align: 'center',
					width: 100,
					render: (h, params) => {
						const { row, column, index } = params
						return h(
							Switch,
							{
								props: {
									value: row[column.key],
									trueValue: 1,
									falseValue: 0,
									size: 'large',
								},
								on: {
									'on-change': value => {
										const data = {
											...row,
											status: value,
										}
										console.log(row)

										this.tableData[index].status = value
										this._updateReport(data, index)
									},
								},
							},
							[h('span', { slot: 'open' }, '启用'), h('span', { slot: 'close' }, '关闭')],
						)
					},
				},
				{
					title: '操作',
					key: 'deal',
					align: 'center',
					width: 140,
					render: (h, params) => {
						const arr = [
							h(
								Poptip,
								{
									props: {
										placement: 'bottom-end',
										title: '确定要删除吗？',
										confirm: true,
										transfer: true,
									},
									on: {
										'on-ok': () => {
											const { id } = params.row
											this._deleteReportById(id)
										},
									},
								},
								[
									h(
										Button,
										{
											class: 'mr10',
											props: {
												type: 'error',
												size: 'small',
											},
										},
										'删除',
									),
								],
							),
							h(
								Button,
								{
									props: {
										type: 'primary',
										size: 'small',
									},
									on: {
										click: () => {
											this.handleSet('edit', params.row)
										},
									},
								},
								'修改',
							),
						]
						return h('div', arr)
					},
				},
			],
			tableData: [],
			loading: false,
			pageSize: 10,
			pageNum: 1,
			pageTotal: 0,
			isShow: false,
			reportTitle: '',
			isBusiness: false,
			businessData: [],
		}
	},
	methods: {
		async _getBusinessList() {
			this.businessData = []
			const { result } = await getBusinessList({ needPage: false })

			const { list = [] } = result

			if (list && list.length) {
				this.businessData = list
			}
		},
		handleQuery() {
			this.pageNum = 1
			this.tableData = []
			this._getReportList()
		},
		_getReportList() {
			const params = {
				...this.formData,
				businessId: this.formData.businessId === undefined ? '' : this.formData.businessId,
				pageNum: this.pageNum,
				pageSize: this.pageSize,
			}
			params.status = params.status === undefined ? '' : params.status

			getReportList(params)
				.then(res => {
					const { result } = res
					const { list, total } = result

					this.tableData = list
					this.pageTotal = total
				})
				.catch(error => {
					console.error(error)
				})
		},
		_deleteReportById(id) {
			deleteReportById({ id }).then(() => {
				this.$Message.success('删除成功')

				this.handleQuery()
			})
		},
		_updateReport(params, index) {
			delete params['_index']
			delete params['_rowKey']
			const text = params.status === 1 ? '启用' : '关闭'
			this.$Modal.confirm({
				title: '修改启用状态',
				content: `确定要${text}${params.name || ''}报表吗？`,
				onOk: () => {
					updateReport(params).then(() => {
						this.$Message.success('修改成功')
						this._getReportList()
					})
				},
				onCancel: () => {
					const flag = params.status ? 0 : 1

					this.tableData[index].status = flag
				},
			})
		},
		handleChangePage(val) {
			this.pageNum = val
			this._getReportList()
		},
		handleChangeSize(val) {
			this.pageSize = val
			this._getReportList()
		},
		handleSet(value, data = {}) {
			this.isShow = true
			this.reportTitle = value === 'add' ? '新增报表' : '修改报表'

			if (value === 'edit') {
				const obj = this.$common.deepCopy(data)
				delete obj['_index']
				delete obj['_rowKey']
				delete obj['businessName']

				obj.businessId = parseInt(obj.businessId)
				this.$refs.modalRef.initData(obj)
			} else {
				this.$refs.modalRef.initData({
					name: '',
					businessId: '',
					code: '',
					url: '',
					param: '',
					status: 1,
					sort: 1,
				})
			}
		},
		handleSetBusiness() {
			this.isBusiness = true
			this.$nextTick(() => {
				this.$refs.businessRef._getBusinessList()
			})
		},
	},
	mounted() {
		this._getReportList()
		this._getBusinessList()
	},
}
</script>
<style lang="less" scoped>
.setting {
	height: 100%;
	padding: 16px 16px 0 16px;
	display: flex;
	flex-direction: column;
	.query-title {
		white-space: nowrap;
		margin-right: 4px;
	}
	.table-container {
		flex: 1;
	}
	::v-deep {
		.mr10 {
			margin-right: 10px;
		}
	}
}
</style>
