<!--
 * @Description: 树形图
 * @Author: shenxh
 * @Date: 2022-04-02 09:21:44
 * @LastEditors: shenxh
 * @LastEditTime: 2022-04-20 09:58:54
-->

<template lang="pug">
.tree
	.tools-bar(v-if='tools')
		Icon.icon(type='md-add', @click='handleNodeTools("create")')
		Poptip(confirm, :disabled='disabledTrash()', title='是否删除?', @on-ok='handleNodeTools("delete")')
			Icon.icon(type='md-trash', :class='{ disabled: disabledTrash() }')
		Icon.icon(type='md-arrow-up', :class='{ disabled: disabledArrow("up") }', @click='handleNodeTools("up")')
		Icon.icon(type='md-arrow-down', :class='{ disabled: disabledArrow("down") }', @click='handleNodeTools("down")')
	Input.search(v-if='searchType === "filter"', v-model='searchVal', clearable, placeholder='搜索')
	Input.search(v-if='searchType === "lazy"', v-model='searchVal', clearable, placeholder='搜索')
	.tree-wrap(:class='{ tools: tools, "tree-wrap-search": searchType }')
		el-tree.el-tree(
			ref='el-tree',
			accordion,
			:data='data',
			:props='props',
			:load='load',
			:lazy='lazy',
			:node-key='nodeKey',
			:default-expanded-keys='defaultExpandedKeys',
			:expand-on-click-node='expandOnClickNode',
			:highlight-current='highlightCurrent',
			empty-text='暂无数据',
			:filter-node-method='filterNode',
			@node-click='nodeClick'
		)
			template(slot-scope='{ node, data }')
				.tree-row-content
					.icon(v-if='prefixIcon')
						img(v-if='data.type === "area"', src='https://eslink-iot.oss-cn-beijing.aliyuncs.com/区域.svg')
						img(v-else, src='https://eslink-iot.oss-cn-beijing.aliyuncs.com/摄像头.svg')
					span.label {{ node.label }}
		Spin(v-show='loading', fix)
</template>

<script>
import ElTree from 'vue-element-tree'
import 'vue-element-tree/dist/vue-element-tree.css'

let timer = null

export default {
	name: 'tree',
	components: {
		ElTree,
	},
	props: {
		// 显示前缀图标
		prefixIcon: Boolean,
		// 默认展开的节点的 key 的数组
		defaultExpandedKeys: Array,
		// 每个树节点用来作为唯一标识的属性，整棵树应该是唯一的
		nodeKey: {
			type: String,
			default: 'id',
		},
		// 是否在点击节点的时候展开或者收缩节点
		expandOnClickNode: Boolean,
		// 是否高亮当前选中节点
		highlightCurrent: Boolean,
		// loading
		loading: Boolean,
		// 懒加载
		lazy: Boolean,
		// 工具栏
		tools: Boolean,
		load: Function,
		// 搜索框类型
		searchType: {
			type: String,
			default: '', // filter/lazy
		},
		props: {
			type: Object,
			default() {
				return {
					label: 'name',
					children: 'zones',
					isLeaf: 'leaf',
				}
			},
		},
		data: {
			type: Array,
			default() {
				return [
					{
						title: '1',
						expand: true,
						children: [
							{
								title: '1-1',
								expand: true,
							},
						],
					},
				]
			},
		},
	},
	data() {
		return {
			searchVal: '',
			currentNode: {},
			currentParentNode: {},
			brothersNodes: [],
			selectedNode: {
				previousNode: null,
				currentNode: null,
				nextNode: null,
			},
		}
	},
	computed: {},
	watch: {
		searchVal(val) {
			if (this.searchType === 'filter') {
				this.$refs['el-tree'].filter(val)
			}
			if (this.searchType === 'lazy') {
				this._getSearchData(val)
			}
		},
	},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 置灰条件判断-箭头
		disabledArrow(name) {
			let disabled = false

			if (name === 'up') {
				if (!this.selectedNode.previousNode) {
					disabled = true
				}
			}
			if (name === 'down') {
				if (!this.selectedNode.nextNode) {
					disabled = true
				}
			}

			return disabled
		},
		// 置灰条件判断-删除
		disabledTrash() {
			let disabled = false

			if (!this.selectedNode.currentNode) {
				disabled = true
			}

			return disabled
		},

		// 节点被点击时的回调
		nodeClick(data, node, nodeComponent) {
			// 点击当前已选中的节点, 则取消选中
			if (data.id === this.selectedNode.currentNode?.id) {
				this.$refs['el-tree'].setCurrentKey()
				this.$emit('node-click')
				this._clearCurrentNode()

				return
			}
			this.currentParentNode = node?.parent
			this.currentNode = node

			this._clearCurrentNode()
			this.getBrothersNodes(node)

			this.$emit('node-click', data, node, nodeComponent)
		},

		getBrothersNodes(node) {
			const brothersNodes = node?.parent?.childNodes

			this.brothersNodes = brothersNodes.map((item, index) => {
				if (node.data.id === item.data.id) {
					if (brothersNodes[index - 1]) {
						this.selectedNode.previousNode = brothersNodes[index - 1].data
					} else {
						this.selectedNode.previousNode = null
					}
					this.selectedNode.currentNode = item.data
					if (brothersNodes[index + 1]) {
						this.selectedNode.nextNode = brothersNodes[index + 1].data
					} else {
						this.selectedNode.nextNode = null
					}
				}

				return item.data
			})
		},

		// 重新加载当前节点的父节点
		expandNode(initSelfNode = false) {
			const currentNodeParentKey = this.currentParentNode.data?.id
			const currentNodeKey = this.selectedNode.currentNode.id

			if (initSelfNode) {
				this.$refs['el-tree'].getNode(currentNodeKey).loaded = false
				this.$refs['el-tree'].getNode(currentNodeKey).expand()
			} else {
				this.$refs['el-tree'].getNode(currentNodeParentKey).loaded = false
				// 主动调用展开节点方法，重新查询该节点下的所有子节点
				this.$refs['el-tree'].getNode(currentNodeParentKey).expand()
			}
			setTimeout(() => {
				const currentNode = this.$refs['el-tree'].getNode(this.$refs['el-tree'].getCurrentKey())

				this.$refs['el-tree'].setCurrentKey(currentNodeKey)
				this.getBrothersNodes(currentNode)
			}, 200)
		},

		// 清空当前节点
		_clearCurrentNode() {
			this.brothersNodes = []
			this.selectedNode = {
				previousNode: null,
				currentNode: null,
				nextNode: null,
			}
		},

		// 过滤节点
		filterNode(value, data) {
			if (!value) return true
			return data.label.indexOf(value) !== -1
		},

		// 点击节点工具栏选项
		handleNodeTools(name) {
			const { brothersNodes, selectedNode } = this

			// 置灰状态不允许点击
			if (name === 'delete' && this.disabledTrash()) return
			if ((name === 'up' || name === 'down') && this.disabledArrow(name)) return

			this.$emit('handle-node-tools', name, selectedNode, brothersNodes)
		},

		// 节流搜索
		_getSearchData(val) {
			if (timer) {
				clearTimeout(timer)
				timer = null
			}

			timer = setTimeout(() => {
				this.$emit('search', val)
			}, 500)
		},
	},
}
</script>

<style lang="less" scoped>
.tree {
	width: 100%;
	height: 100%;
	overflow: hidden;
	.tools-bar {
		display: flex;
		align-items: center;
		justify-content: space-around;
		height: 20px;
		margin-bottom: 10px;
		.icon {
			font-size: 20px;
			cursor: pointer;
			&.disabled {
				color: #eee;
				cursor: not-allowed;
			}
		}
	}
	.search {
		margin-bottom: 15px;
	}
	.tree-wrap {
		position: relative;
		overflow: auto;
		height: 100%;
		&.tree-wrap-search {
			height: calc(100% - 47px);
		}
		&.tools {
			height: calc(100% - 82px);
		}
		.tree-row-content {
			display: flex;
			align-items: center;
			.icon {
				display: flex;
				align-items: center;
				margin-right: 5px;
			}
		}
		/deep/ .el-tree {
			& > .el-tree-node {
				min-width: 100%;
				display: inline-block;
			}
			.is-current {
				& > .el-tree-node__content {
					background-color: #52e5ff;
				}
			}
		}
	}
}
</style>
