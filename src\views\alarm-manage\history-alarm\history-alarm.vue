<!--
 * @Description: 历史报警
 * @Author: shenxh
 * @Date: 2022-03-04 16:42:36
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-06-28 15:32:18
-->

<template lang="pug">
.history-alarm
	warn-list(:hideRight='false')
</template>

<script>
import WarnList from '../real-time-alarm/modules/WarnList.vue'

export default {
	name: 'history-alarm',
	components: {
		WarnList,
	},

	data() {
		return {}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {},
}
</script>

<style lang="less" scoped>
.history-alarm {
	width: 100%;
	padding: 12px 16px;
}
</style>
