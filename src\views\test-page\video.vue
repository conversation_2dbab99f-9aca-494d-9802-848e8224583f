<template>
	<div class="test-video">
		<video-h5 v-if="stationCode" :stationCode="stationCode"></video-h5>
	</div>
</template>

<script>
import VideoH5 from '@/components/gc-video-h5/VideoH5.vue'

export default {
	name: 'test-video',
	components: {
		VideoH5,
	},
	props: {},
	data() {
		return {
			stationCode: 'SClongMeng',
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {},
}
</script>

<style lang="less" scoped>
.test-video {
	width: 500px;
	height: 300px;
}
</style>
