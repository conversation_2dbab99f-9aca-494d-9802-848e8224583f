<!--
 * @Description: 创建系统弹窗
 * @Author: shenxh
 * @Date: 2022-04-01 15:54:51
 * @LastEditors: shenxh
 * @LastEditTime: 2023-04-14 10:24:33
-->

<template lang="pug">
.create-system-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='650',
		v-model='showModal',
		:title='type === 1 ? "编辑" : "新增"',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='110')
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='渠道编码', prop='channelCode')
							Input(v-model='formData.channelCode', placeholder='请输入渠道编码', clearable, :disabled='type === 1')
						FormItem(label='渠道名称', prop='name')
							Input(v-model='formData.name', placeholder='请输入渠道名称', clearable)
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='采集规则配置', prop='enableRule')
							RadioGroup(v-model='formData.enableRule')
								Radio(label='1')
									span 支持
								Radio(label='0')
									span 不支持
						FormItem(label='系统类型', prop='sysCode')
							Select.select(v-model='formData.sysCode', transfer, filterable, placeholder='请选择')
								Option(v-for='(item, index) in sysList', :key='index', :value='item.code') {{ item.name }}
					FormItem(label='渠道参数', prop='params')
						Input(v-model='formData.params', type='textarea', placeholder='请输入渠道参数')
					FormItem(label='备注', prop='memo')
						Input(v-model='formData.memo', type='textarea', placeholder='请输入备注')
		template(slot='footer')
			Button(@click='handleClose') 取消
			Button(:loading='btnLoading', type='primary', @click='handleSubForm') 保存
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import { addTransChannel, updateTransChannel } from '@/api/data-acquisition-config'
import { querySysList } from '@/api/common.js'

export default {
	name: 'create-system-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		WaterRow,
	},
	props: {
		showModal: Boolean,
		data: Object,
		type: Number, // 0-添加; 1-编辑
	},
	data() {
		const validateCode = (rule, value, callback) => {
			if (!value || !value.trim()) {
				callback(new Error('请输入'))
			} else {
				callback()
			}
		}

		return {
			sysList: [],
			btnLoading: false,
			formData: {},
			formRules: {
				channelCode: [
					{
						required: true,
						validator: validateCode,
						trigger: 'blur',
					},
					{
						max: 32,
						trigger: 'blur',
						message: '不能超过 32 个字符',
					},
				],
				name: [
					{
						required: true,
						validator: validateCode,
						trigger: 'blur',
					},
					{
						max: 64,
						trigger: 'blur',
						message: '不能超过 64 个字符',
					},
				],
				sysCode: [
					{
						required: true,
						trigger: 'change',
						message: '请选择',
					},
				],
				enableRule: [
					{
						required: true,
						trigger: 'change',
						message: '请选择',
					},
				],
				params: [
					{
						max: 256,
						trigger: 'blur',
						message: '不能超过 256 个字符',
					},
				],
				memo: [
					{
						max: 512,
						trigger: 'blur',
						message: '不能超过 512 个字符',
					},
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				querySysList().then(res => {
					console.log('res', res)
					const { result = [] } = res
					this.sysList = result
				})
				this.formData = { ...this.data }
				if (this.formData.enableRule || this.formData.enableRule === 0) {
					this.formData.enableRule += ''
				}
			} else {
				setTimeout(() => {
					this.$refs.form.resetFields()
				}, 200)
			}
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.btnLoading = true
					if (this.type === 0) {
						this.formData.stationWord = this.$route.query.sysCode

						this.addTransChannel(this.formData)
					} else {
						this.updateTransChannel(this.formData)
					}
				}
			})
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		// 新增设备原始档案
		addTransChannel(params) {
			addTransChannel(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		// 编辑设备原始档案
		updateTransChannel(params) {
			updateTransChannel(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		setResData(res) {
			const { responseCode } = res || {}

			if (responseCode === '100000') {
				this.$Message.success('操作成功')

				this.handleClose()
			}

			this.btnLoading = false
			this.$emit('submit-form', res)
		},
	},
}
</script>

<style lang="less" scoped>
.create-system-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
