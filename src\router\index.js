/*
 * @Description: 路由配置
 * @Version: 2.0
 * @Autor: heliping
 * @Date: 2021-03-04 19:11:49
 * @LastEditors: houyan
 * @LastEditTime: 2024-08-08 14:11:08
 */
import Vue from 'vue'
import Router from 'vue-router'
import AlarmConfig from './modules/alarm-config'
import SecurityManage from './modules/security-manage'
import InspectionManagement from './modules/inspection-management'
import WaterScreenData from './modules/water-screen'
import setting from './modules/setting'
import viewing from './modules/viewing'
import editor from './modules/editor'
import BaseItem from './modules/base-item'
import ChangeConfig from './modules/change-setting'
import Other from './modules/other'
import DataAcquisitionConfig from './modules/data-acquisition-config'
import Report from './modules/report'
import PushManage from './modules/push-manage'

Vue.use(Router)

/**
 * 路由配置参数
 * hidden: false             菜单栏是否展示(default is false)
 * redirect: noRedirect     重定向
 */
const router = new Router({
	// base: window.__POWERED_BY_QIANKUN__ ? '/vue' : '/',
	mode: 'hash',
	routes: [
		{
			path: '',
			redirect: '/home',
		},
		{
			path: '/home',
			name: 'Home',
			component: (/* webpackChunkName: 'home' */) => import('@/views/home/<USER>'),
			children: [
				...AlarmConfig,
				...SecurityManage,
				...setting,
				...viewing,
				...editor,
				...BaseItem,
				...InspectionManagement,
				...WaterScreenData,
				...ChangeConfig,
				...Other,
				...DataAcquisitionConfig,
				...Report,
				...PushManage,
			],
		},
	],
})

export default router
