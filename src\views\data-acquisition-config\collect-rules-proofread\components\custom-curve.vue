<template lang="pug">
.custom-column
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='800',
		:value='show',
		:title='title',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				.date-bar
					.menu
						.menu-item(
							v-for='item in dateTypeList',
							:key='item.value',
							:class='currentDateType === item.value ? "active" : ""',
							@click='handleDateType(item)'
						) {{ item.label }}
					.select-wrap
						.label 间隔
						Select.select(v-model='intervals', transfer, filterable, @on-change='handleSelectChange', placeholder='请选择')
							Option(v-for='(item, index) in intervalsList', :key='index', :value='item.value') {{ item.label }}
					.date-picker-wrap
						DatePicker.date-picker(
							v-show='currentDateType === \'custom\'',
							v-model='dateVal',
							type='datetimerange',
							:clearable='false',
							:options='dateOpt',
							@on-change='changeDate'
						)
				.chart
					bar-line(
						ref='chart',
						:y-axis='yAxis',
						:grid='grid',
						:loading='loading',
						:data-zoom='dataZoom',
						:series='series',
						:chart-data='chartData'
					)
		template(slot='footer')
			Button(@click='handleClose') 关闭
			//- Button(type="primary", @click="handleSubForm") 保存
</template>

<script>
// import { querySysList } from '@/api/common.js'
// import dataRepairVue from '../../../data-repair/data-repair.vue'
import { queryDataHistory } from '@/api/data-acquisition-proofread'
import BarLine from './BarLine.vue'
let selections = []
export default {
	name: 'CustomCurve',
	components: {
		BarLine,
	},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		currentIndex: {
			type: Number,
			default: 0,
		},
		currentRow: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			intervals: '1m',
			intervalsList: [
				{ label: '1分钟', value: '1m' },
				{ label: '5分钟', value: '5m' },
				{ label: '15分钟', value: '15m' },
				{ label: '1小时', value: '1h' },
			],
			title: '数据项',
			loading: false,
			dateVal: [
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59',
			],
			dateValOld: [
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59',
			],
			dateValInit: [
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
				this.$moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59',
			],
			currentTab: '0',
			currentDateType: 'today',
			dateTypeList: [
				{
					label: '昨日',
					value: 'yesterday',
				},
				{
					label: '今日',
					value: 'today',
				},
				{
					label: '24小时',
					value: 'hour',
				},
				{
					label: '7天',
					value: 'week',
				},
				{
					label: '自定义',
					value: 'custom',
				},
			],
			chartData: [],
			grid: {
				top: 30,
				left: 20,
				right: 50,
				bottom: 50,
			},
			yAxis: {
				name: '',
				scale: true,
			},
			series: {},
			dataZoom: [
				{
					type: 'inside',
					start: 0,
					end: 100,
				},
				{
					start: 0,
					end: 50,
					textStyle: '#00000000',
				},
			],
			dateOpt: {
				disabledDate(date) {
					return date && date.valueOf() > Date.now()
				},
			},
		}
	},
	computed: {
		dateForm() {
			let date = {}

			// // 今日
			if (this.currentDateType === 'today') {
				date = {
					beginTime: this.$moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
					endTime: this.$moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59',
				}
			}
			// // 昨日
			if (this.currentDateType === 'yesterday') {
				const yesterday = this.$moment().subtract(1, 'days')

				date = {
					beginTime: this.$moment(yesterday).format('YYYY-MM-DD') + ' 00:00:00',
					endTime: this.$moment(yesterday).format('YYYY-MM-DD') + ' 23:59:59',
				}
			}
			// 近24小时
			if (this.currentDateType === 'hour') {
				const newDate = this.$moment().subtract(24, 'hours')

				date = {
					beginTime: this.$moment(newDate).format('YYYY-MM-DD HH:mm:ss'),
					endTime: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
				}
			}
			// 七天
			if (this.currentDateType === 'week') {
				const yesterday = this.$moment().subtract(7, 'days')

				date = {
					beginTime: this.$moment(yesterday).format('YYYY-MM-DD') + ' 00:00:00',
					endTime: this.$moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59',
				}
			}
			//  自定义时间
			if (this.currentDateType === 'custom') {
				date = {
					beginTime: this.$moment(this.dateVal[0]).format('YYYY-MM-DD HH:mm:ss'),
					endTime: this.$moment(this.dateVal[1]).format('YYYY-MM-DD HH:mm:ss'),
				}
			}

			return date
		},
	},
	watch: {},
	created() {
		// querySysList().then(res => {
		// 	this.options = res.result
		// })
	},
	mounted() {
		// this.getChartData()
	},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal(val) {
			if (val == true) {
				console.log('-----------', this.currentRow)
				this.title =
					this.currentRow[`stationName${this.currentIndex}`] +
					'-' +
					this.currentRow[`itemName${this.currentIndex}`]
				this.getChartData()
			}
			// this.$refs.form.resetFields()
		},

		// 按钮-保存
		handleSubForm() {
			this.$emit('update:show', false)
			this.$emit('select-columns', selections)
		},

		// 按钮-关闭
		handleClose() {
			// this.$refs.form.resetFields()
			// this.formData = {
			// 	id: '',
			// 	roleName: '',
			// 	memo: '',
			// }
			this.$emit('update:show', false)
			// this.$emit('initList')
		},

		handleSelectChange() {
			this.getChartData()
		},
		// 选择日期
		changeDate(date) {
			const beginTime = date[0]
			const endTime = date[1]
			// const subtract = this.$moment(endTime).diff(this.$moment(beginTime), 'days')
			// let max = 7
			let _date = [beginTime, endTime.split(' ')[0] + ' 23:59:59']

			this.dateVal = _date

			// if (this.currentDateType === 'month') {
			// 	max = 31
			// }
			// if (subtract > max - 1) {
			// 	this.$Message.warning(`最多选择${max}天`)
			// 	this.dateVal = this.dateValOld

			// 	return
			// }

			// this.dateValOld = _date

			this.getChartData()
		},

		// 选择日期类型
		handleDateType(data) {
			this.currentDateType = data.value
			this.dateVal = this.dateValOld = this.dateValInit

			this.getChartData()
		},

		// 获取数据
		getChartData() {
			// console.log('1111', this.currentIndex, this.currentRow);
			queryDataHistory({
				stationCode: this.currentRow[`stationCode${this.currentIndex}`],
				itemCode: this.currentRow[`itemCode${this.currentIndex}`],
				intervals: this.intervals,
				...this.dateForm,
			}).then(res => {
				const { result = {} } = res
				const { value = [] } = result
				let list = []

				if (value && value.length) {
					const data = value[0]

					list = value.map(item => {
						return {
							name: item.time,
							value: item.value,
						}
					})
					this.yAxis.name = data.unit
				}
				// if (this.currentTab === '3') {
				this.grid.left = 60
				this.grid.right = 70
				// } else {
				// this.grid.left = 20
				// this.grid.right = 30
				// this.grid.bottom = 300
				// }
				this.chartData = [list]
				this.$refs.chart.initChart()
			})
		},
	},
}
</script>

<style lang="less" scoped>
.popup-content {
	.date-bar {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		height: 38px;
		.date-picker {
			width: 350px;
		}
		.select-wrap {
			width: 160px;
			display: flex;
			align-items: center;
			.label {
				margin-right: 8px;
			}
			.select {
				width: 100px;
			}
		}
		.menu {
			display: flex;
			border: 1px solid #d0d0d3;
			margin-right: 12px;
			.menu-item {
				text-align: center;
				width: 46px;
				height: 24px;
				line-height: 24px;
				// background: #041627;
				cursor: pointer;
				&:not(:last-child) {
					border-right: 1px solid #d0d0d3;
				}
				&.active {
					color: #ffffff;
					background: #0094ff;
					border: 1px solid #0094ff;
				}
			}
		}
	}
	.chart {
		height: 480px;
		padding-top: 10px;
	}
}
</style>
