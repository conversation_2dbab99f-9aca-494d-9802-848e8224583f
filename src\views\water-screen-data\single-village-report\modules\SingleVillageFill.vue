<template lang="pug">
.fill-list
	water-row.fill-list-header(justify='flex-start', align='center')
		.fill-list-form-title 年份:
		Select.fill-list-form-select(v-model='yearId', placeholder='请选择', @on-change='changeYear')
			Option(v-for='(item, index) in yearList', :key='index', :value='item.value', :disabled='item.disabled') {{ item.label }}
	WaterTable.fill-list-table(border, :columns='getColumns()', :data='tableData', :loading='loading')
</template>
<script>
import WaterTable from '@/components/gc-water-table'
import WaterRow from '@/components/gc-water-row'
import { yearList } from '@/utils/enums.js'
import { villageSave, villageQuery } from '@/api/water-screen-data.js'
import { EventBus } from '@/utils/eventBus.js'
export default {
	name: 'fill-list',
	components: {
		WaterTable,
		WaterRow,
	},
	data() {
		return {
			loading: false,
			yearId: new Date().getFullYear() + '',
			yearList,
			tableData: [],
		}
	},
	mounted() {
		this.yearList.forEach(y => {
			if (y.value > this.yearId) {
				y.disabled = true
			}
		})
		this.villageQuery()
	},
	methods: {
		villageQuery() {
			villageQuery({ time: this.yearId }).then(res => {
				const { result = [] } = res
				this.handleQuery(result)
			})
		},
		handleQuery(defaultData) {
			this.tableData = []
			defaultData.map((item, index) => {
				const { month = '', updateTime = '', integrityRate = '', waterQualityRate = '' } = item
				const obj = {
					updateTime,
					month,
					key: month + '月',
					integrityRate: integrityRate ? integrityRate : this.getButtonBeforeDisable(index) ? 0 : '',
					waterQualityRate: waterQualityRate ? waterQualityRate : this.getButtonBeforeDisable(index) ? 0 : '',
					editable: this.getButtonDisable(index),
					isBefore: this.getButtonBeforeDisable(index),
					state: this.getButtonBeforeDisable(index) ? false : true,
				}
				this.tableData.push(obj)
			})
		},
		changeYear() {
			this.villageQuery()
			// this.tableData = [].map((item, index) => {
			// 	const disabled = this.getButtonDisable(index)
			// })
		},
		getColumns() {
			return [
				{
					title: '月份',
					key: 'key',
					align: 'center',
					width: 120,
				},
				{
					title: '设备完好率',
					key: 'integrityRate',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { integrityRate, editable, isBefore } = params.row

						return editable
							? h(
									'div',
									{
										style: {
											display: 'flex',
											justifyContent: 'flex-start',
											alignItems: 'center',
										},
									},
									[
										h('Input', {
											props: {
												value: integrityRate,
												maxlength: 4,
												disabled: isBefore,
											},
											on: {
												'on-change': e => {
													this.valueChange(e)
													const value = e.target.value
													this.handleInputValue(params.index, value, 'integrityRate')
												},
												'on-keyup': e => {
													this.valueChange(e)
												},
											},
										}),
										h('span', { style: { marginLeft: '4px' } }, '%'),
									],
							  )
							: h('span', '')
					},
				},
				{
					title: '水质合格率',
					key: 'waterQualityRate',
					align: 'center',
					minWidth: 160,
					render: (h, params) => {
						const { waterQualityRate, editable, isBefore } = params.row
						return editable
							? h(
									'div',
									{
										style: {
											display: 'flex',
											justifyContent: 'flex-start',
											alignItems: 'center',
										},
									},
									[
										h('Input', {
											props: {
												value: waterQualityRate,
												maxlength: 4,
												disabled: isBefore,
											},
											on: {
												'on-change': e => {
													this.valueChange(e)
													const value = e.target.value
													this.handleInputValue(params.index, value, 'waterQualityRate')
												},
												'on-keyup': e => {
													this.valueChange(e)
												},
											},
										}),
										h('span', { style: { marginLeft: '4px' } }, '%'),
									],
							  )
							: h('span', '')
					},
				},
				{
					title: '操作',
					slot: 'action',
					render: (h, params) => {
						const { editable, state } = params.row
						return !editable
							? ''
							: state
							? h('div', [
									h(
										'Button',
										{
											props: {
												type: 'primary',
												size: 'small',
											},
											on: {
												click: event => {
													event.stopPropagation()
													this.handleSubmit(params.row)
												},
											},
										},
										'提交',
									),
							  ])
							: h(
									'Button',
									{
										props: {
											type: 'primary',
											size: 'small',
										},
										on: {
											click: event => {
												event.stopPropagation()
												this.handleMotify(params.index)
											},
										},
									},
									'修改',
							  )
					},
				},
			]
		},
		handleMotify(index) {
			this.handleInputValue(index, false, 'isBefore')
			this.handleInputValue(index, true, 'state')
		},
		handleSubmit(data) {
			let { integrityRate, waterQualityRate, month } = data
			if (integrityRate === '') {
				this.$Message.warning('设备完好率不能为空!')
				return
			}
			if (waterQualityRate === '') {
				this.$Message.warning('水质合格率不能为空!')
				return
			}
			let params = {
				list: [
					{
						month,
						integrityRate,
						waterQualityRate,
					},
				],
				time: this.yearId,
			}
			villageSave(params).then(() => {
				this.$Message.success('提交成功!')
				this.villageQuery()
				EventBus.$emit('fresh-dc-record')
			})
		},
		//输入值
		handleInputValue(index, value, key) {
			this.tableData[index][key] = value
			// const item = this.tableData[index]
			// this.tableData.splice(index, 1, {
			// 	...item,
			// 	[key]: value,
			// })
		},
		//正则
		valueChange(e) {
			e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, '') //去除目标字符串左右两边的空白字符。
			// 全局查找非数字
			const regs = /[^\d.]/g
			e.target.value = e.target.value.replace(regs, '')
			//  保证第一位只能是数字，不能是点
			e.target.value = e.target.value.replace(/^\./g, '')
			// 只能输入一个小数点
			e.target.value = e.target.value.replace(/\.{2,}/g, '.')
			// 保证.只出现一次，而不能出现两次以上
			e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')

			// 小数点后面保留2位
			e.target.value = e.target.value.replace(/^(\\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
			e.target.value = e.target.value.replace(/^(([0-9]{3}\d))(.\d{1,2})?$/, '')
			if (e.target.value > 100) {
				e.target.value = 100
			}
		},
		getButtonDisable(index) {
			const year = new Date().getFullYear()
			if (Number(this.yearId) > year) {
				return false
			} else if (Number(this.yearId) === year) {
				const nowDate = `${this.yearId}-${index + 1}`
				const month = new Date().getMonth()
				const currentDate = `${year}-${month + 1}`
				return !(new Date(nowDate).getTime() > new Date(currentDate).getTime())
			} else {
				return true
			}
		},
		getButtonBeforeDisable(index) {
			const year = new Date().getFullYear()
			if (Number(this.yearId) < year) {
				return true
			} else if (Number(this.yearId) === year) {
				const nowDate = `${this.yearId}-${index + 1}`
				const month = new Date().getMonth()
				const currentDate = `${year}-${month + 1}`
				return new Date(nowDate).getTime() < new Date(currentDate).getTime()
			}
		},
	},
}
</script>
<style scoped lang="less">
.fill-list {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	&-form {
		margin-right: 24px;
		&-title {
			margin-right: 4px;
		}
		&-select {
			width: 160px;
		}
	}
	&-table {
		flex: 1;
		margin-top: 16px;
	}
}
</style>
