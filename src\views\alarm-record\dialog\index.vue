<template>
	<Modal class-name="custom-modal" :value="isShow" :transfer="false" :title="title" @on-cancel="handleCancel">
		<div>
			<Form ref="formValidate" :model="formData" :label-width="80" :rules="formRules">
				<Form-item label="处理方式" prop="processWay">
					<Input v-model="formData.processWay" placeholder="请输入"></Input>
				</Form-item>
				<Form-item label="处理人" prop="processPersonId">
					<Select v-model="formData.processPersonId" placeholder="请选择">
						<Option v-for="(item, index) in processPersonList" :key="index" :value="item.value">
							{{ item.label }}
						</Option>
					</Select>
				</Form-item>
				<Form-item label="处理时间" prop="processTime">
					<DatePicker
						v-model="formData.processTime"
						type="datetime"
						:clearable="true"
						placeholder="选择日期"
						format="yyyy-MM-dd HH:mm:ss"
					></DatePicker>
				</Form-item>
				<Form-item label="处理内容" prop="processContent">
					<Input v-model="formData.processContent" placeholder="请输入" type="textarea"></Input>
				</Form-item>
				<Form-item label="处理图片" prop="processPictures">
					<GcUploadImgNew v-model="formData.processPictures" :maxNum="5"></GcUploadImgNew>
				</Form-item>
				<Form-item label="附件" prop="processAttachment">
					<GcUploadFileNew v-model="formData.processAttachment" :limitNum="1"></GcUploadFileNew>
				</Form-item>
			</Form>
		</div>
		<template #footer>
			<Button @click="handleCancel">取消</Button>
			<Button @click="handleOk" type="primary">确定</Button>
		</template>
	</Modal>
</template>

<script>
import GcUploadImgNew from '@/components/gc-upload-img-new/index'
import GcUploadFileNew from '@/components/gc-upload-file-new/index'
import { apiGetHandlePerson, apiSubmitAlarmRecord } from '@/api/alarm-config.js'
// import { apiGetHandlePerson } from '@/api/alarm-config.js'
export default {
	name: 'AddItem',
	components: {
		GcUploadImgNew,
		GcUploadFileNew,
	},
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		id: {
			type: Number,
			default: 0,
		},
		title: {
			type: String,
			default: '',
		},
	},
	created() {},
	computed: {
		isShow: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit('input', val)
			},
		},
	},
	data() {
		return {
			formData: {
				processWay: '', // 处理方式
				processPersonId: '', //处理人
				processPersonName: '', //处理人姓名
				processTime: '', //处理时间
				processContent: '', //处理内容
				processPictures: [], //处理图片
				processAttachment: [], //附件
				disposestatus: 10,
			},
			formRules: {
				processWay: [
					{
						required: true,
						message: '请输入数据项名称',
						trigger: 'change',
					},
				],
			},
			processPersonList: [],
		}
	},
	methods: {
		resetForm() {
			this.$refs?.formValidate?.resetFields()
		},
		// 弹窗取消事件
		handleCancel() {
			this.isShow = false
			this.resetForm()
		},
		// 确定 风险评价、隐患指派、隐患改派、隐患整改登记联调，（告警记录、告警记录详情）开发中，（基础数据项、设备额类型配置）开发联调
		handleOk() {
			this.$refs.formValidate.validate(valid => {
				if (valid) {
					// 处理图片
					const imgArr =
						this.formData?.processPictures.length > 0
							? this.formData?.processPictures.map(item => {
									return item?.url
							  })
							: []
					// 附件
					const attachArr =
						this.formData?.processAttachment.length > 0
							? this.formData?.processAttachment.map(item => {
									return item?.url
							  })
							: []
					// 根据处理的id获取处理人名称
					const findIndex = this.processPersonList.findIndex(
						item => item.value == this.formData?.processPersonId,
					)

					const processPersonName = findIndex === -1 ? '' : this.processPersonList[findIndex]?.label
					const params = {
						alarmInfoId: this?.id,
						...this.formData,
						processTime: this.formData?.processTime
							? this.$moment(this.formData?.processTime).format('YYYY-MM-DD HH:mm:ss')
							: null,
						processPictures: imgArr,
						processAttachments: attachArr,
						processPersonName,
					}
					if (!params?.processTime) {
						delete params?.processTime
					}
					console.log('登记信息params', params)
					apiSubmitAlarmRecord(params).then(res => {
						if (res?.result) {
							this.$Message.success('登记成功！')
							this.isShow = false
							this.resetForm()
							this.$emit('update')
						}
					})
				} else {
					return
				}
			})
		},
		getHandlePerson() {
			apiGetHandlePerson().then(res => {
				const { result } = res
				if (result) {
					this.processPersonList =
						result?.length > 0
							? result?.map(item => {
									return {
										label: item?.name,
										value: item.id,
									}
							  })
							: []
				}
			})
		},
	},
	mounted() {
		this.getHandlePerson()
	},
}
</script>
<style lang="less" scoped>
::v-deep {
	.ivu-modal-header {
		padding: 8px;
		border: none;
	}
}
</style>
