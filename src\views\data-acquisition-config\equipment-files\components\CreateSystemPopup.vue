<!--
 * @Description: 创建系统弹窗
 * @Author: shenxh
 * @Date: 2022-04-01 15:54:51
 * @LastEditors: shenxh
 * @LastEditTime: 2023-04-26 11:18:29
-->

<template lang="pug">
.create-system-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='650',
		v-model='showModal',
		:title='type === 1 ? "编辑" : "新增"',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :rules='formRules', :label-width='120')
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='平台唯一编码', prop='stationCode')
							Input(v-model='formData.stationCode', :disabled='type === 1', clearable)
						FormItem(label='采集渠道', prop='transChannelCode')
							template(slot='label')
								span.form-item-label(:class='{ active: type === 1 }', @click='handleTransChannelCode') 采集渠道
							Select(
								v-model='formData.transChannelCode',
								transfer,
								filterable,
								clearable,
								:disabled='type === 1',
								@on-change='changeTransChannelCode'
							)
								Option(v-for='item in transChannelList', :key='item.value', :value='item.value') {{ item.label }}
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='设备/对象名称', prop='stationName')
							Input(v-model='formData.stationName', placeholder='请输入设备/对象名称', clearable)
						FormItem(label='生产厂家', prop='producer')
							Select(v-model='formData.producer', filterable, transfer, clearable)
								Option(v-for='(item, index) in manufacturerList', :value='item.producerId', :key='index') {{ item.producerName }}
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='表号', prop='meterNo')
							Input(v-model='formData.meterNo', placeholder='请输入表号', clearable)
						FormItem(label='通讯编码', prop='communicateNo')
							Input(v-model='formData.communicateNo', placeholder='请输入通讯编码', clearable)
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='SIM卡号', prop='simCard')
							Input(v-model='formData.simCard', placeholder='请输入SIM卡号', clearable)
						FormItem(label='外部ID', prop='objectId')
							Input(v-model='formData.objectId', placeholder='请输入外部ID', clearable)
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='户号', prop='userNo')
							Input(v-model='formData.userNo', placeholder='请输入户号', clearable)
						FormItem(label='户名', prop='userName')
							Input(v-model='formData.userName', placeholder='请输入户名', clearable)
					water-row.table-btn-wrap(justify='space-between', align='center')
						FormItem(label='地址', prop='address')
							Input(v-model='formData.address', placeholder='请输入地址', clearable)
					FormItem(label='备注', prop='memo')
						Input(v-model='formData.memo', type='textarea', placeholder='请输入备注')
		template(slot='footer')
			Button(@click='handleClose') 取消
			Button(:loading='btnLoading', type='primary', @click='handleSubForm') 保存

	sync-data-popup(:data='data', v-model='showTransChannelCode')
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import SyncDataPopup from './sync-data-popup'
import { queryTransChannel, addStationInfo, updateStationInfo, producers } from '@/api/data-acquisition-config'

export default {
	name: 'create-system-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	components: {
		WaterRow,
		SyncDataPopup,
	},
	props: {
		showModal: Boolean,
		data: Object,
		// transChannelList: Array,
		type: Number, // 0-添加; 1-编辑
	},
	data() {
		const validateCode = (rule, value, callback) => {
			if (!value || !value.trim()) {
				callback(new Error('请输入'))
			} else {
				callback()
			}
		}

		return {
			btnLoading: false,
			formData: {},
			showTransChannelCode: false,
			transChannelList: [],
			manufacturerList: [],
			formRules: {
				stationCode: [
					{
						required: true,
						validator: validateCode,
						trigger: 'blur',
					},
				],
				transChannelCode: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
					},
				],
				stationName: [
					{
						required: true,
						validator: validateCode,
						trigger: 'blur',
					},
				],
			},
		}
	},
	computed: {
		selectedTransChannel() {
			return (this.transChannelList || []).find(item => this.formData.transChannelCode === item.value) || {}
		},
	},
	watch: {
		showModal(val) {
			if (val) {
				this.manufacturerList = []

				this.queryTransChannel()
			}
		},
	},
	created() {},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 显示状态发生变化时触发
		changeModal(isShow) {
			if (isShow) {
				this.formData = { ...this.data }
			} else {
				setTimeout(() => {
					this.$refs.form.resetFields()
				}, 200)
			}
		},

		// 点击采集渠道
		handleTransChannelCode() {
			if (this.type === 0) return

			this.showTransChannelCode = true
		},

		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.btnLoading = true
					if (this.type === 0) {
						this.formData.sysCode = this.selectedTransChannel.sysCode

						this.addStationInfo(this.formData)
					} else {
						this.updateStationInfo(this.formData)
					}
				}
			})
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},

		changeTransChannelCode() {
			setTimeout(() => {
				this.formData.producer = ''
			}, 30)
			this.manufacturerList = []

			this.producers()
		},

		// 新增设备原始档案
		addStationInfo(params) {
			addStationInfo(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		// 编辑设备原始档案
		updateStationInfo(params) {
			params.producerName = undefined
			updateStationInfo(params)
				.then(res => {
					this.setResData(res)
				})
				.catch(() => {
					this.btnLoading = false
				})
		},

		setResData(res) {
			const { responseCode } = res || {}

			if (responseCode === '100000') {
				this.$Message.success('操作成功')

				this.handleClose()
			}

			this.btnLoading = false
			this.$emit('submit-form', res)
		},

		// 采集渠道分页列表查询
		queryTransChannel() {
			queryTransChannel({
				pageNum: 1,
				pageSize: 99,
			}).then(res => {
				const { result = {} } = res || {}
				const { list = [] } = result

				this.transChannelList = list.map(item => {
					return {
						...item,
						label: item.name,
						value: item.channelCode,
					}
				})

				this.type === 1 && this.producers()
			})
		},

		// 查询厂家列表
		producers() {
			const transChannelCode = this.formData.transChannelCode || this.data.transChannelCode

			producers({
				transChannelCode,
			}).then(res => {
				const { result = [] } = res || {}

				this.manufacturerList = result
			})
		},
	},
}
</script>

<style lang="less" scoped>
.create-system-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
					.form-item-label {
						&.active {
							color: #2d8cf0;
							cursor: pointer;
						}
					}
					Input,
					Select {
						width: 180px;
					}
				}
			}
		}
	}
}
</style>
