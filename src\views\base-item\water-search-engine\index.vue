<template lang="pug">
.fill
	Row
		Col.left(span='6')
			<Input v-model="searchVal" style="width: 90%" search  enter-button="搜索" placeholder="请输入标题" @on-search="getSchemeTreeFn" />
			Tree.demo-tree-render(:data='dataTree', :render='renderContent', @on-select-change='clickFn', ref='tree')
		Col.pad-l16(span='18', v-show='ifEdit && !ifClass')
			Form(:model='formItem', :label-width='80', :rules='formRules', ref='form')
				FormItem(label='所属类别', prop='categoryId')
					Select(v-model='formItem.categoryId', :disabled='!ifEdit', style='width: 200px')
						Option(:value='item.value', v-for='(item, index) in selectTree', :key='index') {{ item.label }}
				FormItem(label='标题', prop='title')
					Input(v-model='formItem.title', placeholder='请输入标题', :disabled='!ifEdit', style='width: 200px')
				FormItem(label='内容', prop='content')
					vue-ueditor-wrap(
						ref='editer',
						v-model='formItem.content',
						:config='editorConfig',
						:destroy='true',
						@ready='readyFn',
						editor-id='editor-demo-01'
					)
			.btn-wrap(v-if='ifEdit')
				Button.mar-r12(@click='cancelFn') 取消
				Button(type='primary', @click='saveSchemeFn') 确定

		Col.pad-l16(span='18', v-show='!ifEdit && !ifClass')
			.desc-item(v-for='(item, index) in itemDesc', :key='index')
				.label {{ item.label }}
				//- .value {{ descVal[item.key] }}
				div(v-html='descVal[item.key]')
		Col.pad-l16(span='18', v-if='ifClass')
			.no-tit 请选择方案进行查看
		CreateItemPopup(:show.sync='modalShow', @initList='getSchemeTreeFn', ref='itemPopup')
</template>

<script>
import { getSchemeTree, delScheme, getInfo } from '@/api/base-item'
import CreateItemPopup from './components/CreateItemPopup'
export default {
	name: '',
	components: { CreateItemPopup },
	props: {},
	data() {
		return {
			ifClass: true,
			descVal: {},
			itemDesc: [
				{
					label: '所属类别:',
					key: 'categoryName',
				},
				{
					label: '标题:',
					key: 'title',
				},
				{
					label: '内容:',
					key: 'content',
					ifHtml: true,
				},
			],
			editType: 'add',
			editObj: {},
			ifEdit: false,
			editIns: null,
			modalShow: false,
			selectTree: [],
			searchVal: '',
			formItem: {
				categoryId: '',
				title: '',
				content: '',
			},
			formRules: {
				categoryId: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
				title: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
				content: [
					{
						required: true,
						message: '请输入',
						trigger: 'blur',
					},
				],
			},
			editorConfig: {
				// 访问 UEditor 静态资源的根路径，可参考 https://hc199421.gitee.io/vue-ueditor-wrap/#/faq
				UEDITOR_HOME_URL: '/UEditor/',
				// 服务端接口（这个地址是我为了方便各位体验文件上传功能搭建的临时接口，请勿在生产环境使用！！！）
				serverUrl: '',
				toolbars: [
					[
						'bold',
						'italic',
						'underline',
						'fontborder',
						'strikethrough',
						'superscript',
						'subscript',
						'removeformat',
						'formatmatch',
						'autotypeset',
						'pasteplain',
						'|',
						'forecolor',
						'backcolor',
						'insertorderedlist',
						'insertunorderedlist',
						'selectall',
						'cleardoc',
						'|',
						'customstyle',
						'paragraph',
						'fontfamily',
						'fontsize',
						'|',
						'directionalityltr',
						'directionalityrtl',
						'indent',
						'|',
						'justifyleft',
						'justifycenter',
						'justifyright',
						'justifyjustify',
						'|',
						'touppercase',
						'tolowercase',
						'|',
						'link',
						'unlink',
						'|',
						'inserttable',
						'deletetable',
						'insertparagraphbeforetable',
						'insertrow',
						'deleterow',
						'insertcol',
						'deletecol',
						'mergecells',
						'mergeright',
						'mergedown',
						'splittocells',
						'splittorows',
						'splittocols',
						'charts',
					],
				],
			},
			dataTree: [
				{
					title: '水务知识库',
					expand: true,
					render: (h, { data }) => {
						return h(
							'span',
							{
								style: {
									display: 'inline-block',
									width: '100%',
								},
							},
							[
								h('span', [
									h('Icon', {
										props: {
											type: 'ios-folder-outline',
										},
										style: {
											marginRight: '8px',
											marginLeft: '8px',
										},
									}),
									h('span', data.title),
								]),
								h(
									'Button',
									{
										props: Object.assign({}, this.buttonProps, {
											type: 'primary',
											// size: 'large',
										}),
										style: {
											marginLeft: '8px',
											padding: '4px 8px',
											height: 'auto',
										},
										on: {
											click: a => {
												a.stopPropagation()
												this.modalShow = true
											},
										},
									},
									'新增类别',
								),
								h('span', {
									style: {
										display: 'inline-block',
										float: 'right',
									},
								}),
							],
						)
					},
					children: [],
				},
			],
			buttonProps: {
				type: 'default',
				size: 'small',
			},
		}
	},
	computed: {},
	watch: {
		ifEdit: {
			handler(val) {
				if (val) {
					this.editIns.setEnabled()
				} else {
					this.editIns.setDisabled()
				}
			},
		},
	},
	created() {},
	mounted() {
		this.getSchemeTreeFn()
	},
	beforeDestroy() {},
	methods: {
		// 寻找高亮
		findHeight() {
			this.dataTree[0].children.forEach(ol => {
				ol.selected = false
				ol.children.forEach(ele => {
					// if (ele.value == value) {
					// 	ele.selected = true
					// } else {
					// 	ele.selected = false
					// }
					ele.selected = false
				})
			})
		},
		findHeight2(value) {
			this.dataTree[0].children.forEach(ol => {
				ol.selected = false
				ol.children.forEach(ele => {
					if (ele.value == value) {
						ele.selected = true
					} else {
						ele.selected = false
					}
					ele.selected = false
				})
			})
		},
		// 寻找整个树的第一个方案
		findFirstFn() {
			let first = this.dataTree[0].children.find(ele => {
				return ele.children && ele.children.length > 0
			})
			if (first) {
				this.clickFn([{ value: first.children[0].value, ifSon: true }])
			} else {
				this.ifClass = true
			}
		},
		cancelFn() {
			this.ifEdit = false
		},
		clickFn(data) {
			if (data.length > 0) {
				if (data[0].ifSon) {
					this.ifClass = false
					getInfo({ id: data[0].value }).then(res => {
						this.descVal = res.result
						for (let key in res.result) {
							this.formItem[key] = res.result[key]
						}
						this.formItem.categoryId = JSON.stringify(res.result.categoryId)
					})

					this.ifEdit = false
				} else {
					this.ifClass = true
					// this.$refs.form.resetFields()
				}
			}
		},
		readyFn(editorInstance) {
			this.editIns = editorInstance
			editorInstance.setDisabled()
		},

		// 新增方案
		saveSchemeFn() {
			this.$refs.form.validate(valid => {
				if (valid) {
					if (this.editType == 'add') {
						this.$axios({
							method: 'post',
							url: '/waterPlat/scheme/saveScheme',
							data: { ...this.formItem },
						}).then(() => {
							this.$Message.success('创建成功!')
							this.descVal = {
								...this.formItem,
								categoryName: this.selectTree.filter(ele => ele.value == this.formItem.categoryId)[0]
									.label,
							}

							this.getSchemeTreeFn()
							this.ifClass = false
							this.ifEdit = false
						})
					} else {
						this.$axios({
							method: 'post',
							url: '/waterPlat/scheme/updateScheme',
							data: { id: this.editObj.value, ...this.formItem },
						}).then(() => {
							this.$Message.success('更新成功!')
							this.ifEdit = false
							this.clickFn([{ value: this.editObj.value, ifSon: true }])
							this.getSchemeTreeFn()
						})
					}
				}
			})
		},
		// 类别方案树
		getSchemeTreeFn() {
			let self = this
			getSchemeTree({
				title: this.searchVal,
			}).then(res => {
				if (res.result.length > 0) {
					self.dataTree[0].children = res.result.map(ele => {
						return {
							title: ele.categoryName,
							value: ele.id,
							expand: true,
							children: ele.schemeInfoVos
								? ele.schemeInfoVos.map(ol => {
										return {
											title: ol.title,
											value: ol.id,
											ifSon: true,
										}
								  })
								: [],
						}
					})

					self.selectTree = res.result.map(ele => {
						return {
							label: ele.categoryName,
							value: JSON.stringify(ele.id),
						}
					})
					// this.findFirstFn()
				} else {
					self.dataTree[0].children = []
				}
			})
		},
		handleSubmit() {},
		renderContent(h, { data }) {
			return h(
				'div',
				{
					style: {
						display: 'inline-flex',
						width: '100%',
						alignItems: 'center',
					},
				},

				[
					h(
						'div',
						{
							style: {
								width: '100%',
								display: 'flex',
								alignItems: 'center',
							},
						},
						[
							h('Icon', {
								props: {
									type: data.ifSon ? 'ios-paper-outline' : 'ios-list',
								},
								style: {
									marginRight: '8px',
									marginLeft: '8px',
								},
							}),
							h(
								'div',
								{
									style: {
										// width: '200px',
										flex: 1,
										whiteSpace: 'wrap',
									},
								},
								data.title,
							),
						],
					),
					h(
						'span',
						{
							style: {
								display: 'inline-block',
								float: 'right',
							},
						},

						[
							!data.ifSon &&
								h('Button', {
									props: Object.assign({}, this.buttonProps, {
										icon: 'ios-add',
										size: 'large',
										// type: 'primary',
										// ghost: true,
									}),
									style: {
										marginLeft: '8px',
									},
									on: {
										click: a => {
											a.stopPropagation()
											this.ifClass = false
											this.editType = 'add'

											this.$refs.form.resetFields()
											this.ifEdit = true
											this.formItem.categoryId = JSON.stringify(data.value)
										},
									},
								}),
							data.ifSon &&
								h('Button', {
									props: Object.assign({}, this.buttonProps, {
										icon: 'ios-create-outline',
										size: 'large',
									}),
									style: {
										marginRight: '8px',
										marginLeft: '8px',
									},
									on: {
										click: a => {
											a.stopPropagation()
											this.editType = 'edit'
											this.editObj = data
											this.clickFn([data])
											this.ifEdit = true
										},
									},
								}),
							data.ifSon &&
								h('Button', {
									props: Object.assign({}, this.buttonProps, {
										icon: 'md-trash',
										size: 'large',
									}),
									on: {
										click: a => {
											a.stopPropagation()
											this.$Modal.confirm({
												title: '提示',
												content: '确定要删除该方案?',
												loading: true,
												onOk: () => {
													this.$Modal.remove()
													delScheme({
														id: data.value,
													}).then(() => {
														this.$Message.info('删除成功')
														this.$refs.form.resetFields()
														this.ifClass = true
														this.getSchemeTreeFn()
													})
												},
											})
										},
									},
								}),
						],
					),
				],
			)
		},
	},
}
</script>

<style scoped lang="less">
.fill {
	width: 100%;
	height: 100%;
	padding: 16px;
	box-sizing: border-box;
	.pad-l16 {
		padding-left: 16px;
	}
	.ivu-row {
		height: 100%;
	}
	.left {
		border-right: 1px solid #ccc;
		height: 100%;
		overflow: auto;
	}
}
.btn-wrap {
	width: 90%;
	display: flex;
	justify-content: flex-end;
}
.mar-r12 {
	margin-right: 12px;
}
.no-tit {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
}
.desc-item {
	display: flex;
	font-size: 16px;
	margin-bottom: 12px;
	.label {
		margin-right: 12px;
		flex-shrink: 0;
	}
}
/deep/.ivu-col {
	height: 100%;
}
/deep/.edui-editor-iframeholder {
	height: 400px !important;
}
/deep/.ivu-select-dropdown {
	z-index: 9999;
}
// /deep/.edui-editor {
// 	width: 80% !important;
// }
/deep/.ivu-select-disabled .ivu-select-selection {
	color: #777;
}
/deep/.ivu-tree-title {
	width: 90%;
	// padding-right: 50px;
}
/deep/.ivu-btn-icon-only.ivu-btn-large {
	width: 28px !important;
	height: 28px !important;
}
</style>
