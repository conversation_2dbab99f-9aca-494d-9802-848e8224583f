<template>
	<div class="home">
		<div class="view-container" ref="threeDBox"></div>
		<div class="tooltip-box" :style="tooltipPosition" ref="tooltipBox">
			<div class="container">
				<div class="title">标题：{{ tooltopContent.title }}</div>
				<div class="explain">说明：{{ tooltopContent.text }}</div>
			</div>
		</div>
		<p class="title-text" ref="titleBox" :style="titlePosition">
			{{ tooltopContent.title }}
		</p>
	</div>
</template>

<script>
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
// import gsap from 'gsap'
import TWEEN from '@tweenjs/tween.js'
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js'
import { CSS3DRenderer, CSS3DObject } from 'three/examples/jsm/renderers/CSS3DRenderer.js'
import { itemDate } from '@/api/editor'

let timer = 0
let css2DRenderer = null
let css3DRenderer = null

export default {
	name: 'Home',
	data() {
		return {
			scene: null,
			camera: null,
			controls: null,
			renderer: null,
			axisHelper: null,
			sphere: null,
			time: {
				value: 0,
			},
			labelDataList: [],
			lightPointList: [],
			dataList: [
				{
					image: require('@/assets/image/bg.jpg'),
					tipsList: [
						{
							position: { x: -35, y: -10, z: -253 },
							content: {
								title: '进口流量',
								text: '',
								image: 1,
								showTip: false,
								showTitle: true,
								list: [],
							},
						},
						{
							position: { x: 80, y: -8, z: -243 },
							content: {
								title: '进口压力',
								text: '',
								showTip: true,
								showTitle: false,
								list: [],
							},
						},
						{
							position: { x: 200, y: -45, z: -198 },
							content: {
								title: '出口压力',
								text: '',
								showTip: true,
								showTitle: false,
								list: [],
							},
						},
						{
							position: { x: 280, y: 0, z: -140 },
							content: {
								title: '出口流量',
								text: '',
								showTip: true,
								showTitle: false,
								list: [],
							},
						},
						{
							position: { x: 200, y: 25, z: -10 },
							content: {
								title: '水质',
								text: '',
								showTip: true,
								showTitle: false,
								list: [],
							},
						},
						{
							// position: { x: 180, y: 10, z: -350 },
							position: { x: 100, y: 15, z: -230 },
							content: {
								title: '1#泵',
								text: '',
								showTip: true,
								showTitle: false,
								list: [],
							},
						},
						{
							position: { x: 101, y: 15, z: -190 },
							content: {
								title: '2#泵',
								text: '',
								showTip: true,
								showTitle: false,
								list: [],
							},
						},
						{
							position: { x: 102, y: 15, z: -160 },
							content: {
								title: '3#泵',
								text: '',
								showTip: true,
								showTitle: false,
								list: [],
							},
						},
						// {
						// 	position: { x: 86, y: -9, z: 236 },
						// 	content: {
						// 		title: '进入房间',
						// 		text: '',
						// 		showTip: false,
						// 		showTitle: true,
						//	  list: [],
						// 	},
						// },
					],
				},
				{
					image: require('@/assets/image/kitchen.jpg'),
					tipsList: [
						{
							position: { x: -199, y: -24, z: 145 },
							content: {
								title: '进入大厅',
								text: '',
								image: 0,
								showTip: false,
								showTitle: true,
							},
						},
					],
				},
			],
			lightPoineData: [
				{
					position: { x: -40, y: -124, z: -220 },
				},
				{
					position: { x: -40, y: -204, z: 200 },
				},
			],
			tipsSpriteList: [],
			tooltipPosition: {
				top: '-100%',
				left: '-100%',
			},
			titlePosition: {
				top: '-100%',
				left: '-100%',
			},
			tooltopContent: {},
		}
	},
	methods: {
		initScene() {
			this.scene = new THREE.Scene()
			this.scene.background = new THREE.Color(0x101010)
			// const axes = new THREE.AxesHelper(100000)
			// this.scene.add(axes)
		},
		initCamera(element) {
			this.camera = new THREE.PerspectiveCamera(45, element.clientWidth / element.clientHeight, 0.1, 1000)
			this.camera.position.set(-70, 20, 50)
		},
		initControls(element) {
			this.controls = new OrbitControls(this.camera, element)
			this.controls.minDistance = 0.1
			this.controls.maxDistance = 200
			this.controls.enablePan = false
		},
		initRenderer(element) {
			this.renderer = new THREE.WebGLRenderer()
			this.renderer.setSize(element.offsetWidth, element.offsetHeight)
			element.appendChild(this.renderer.domElement)
		},

		initContent(index = 0) {
			let sphereGeometry = new THREE.SphereGeometry(16, 50, 50)
			sphereGeometry.scale(16, 16, -16)
			let texture = new THREE.TextureLoader().load(this.dataList[index].image)
			let sphereMaterial = new THREE.MeshBasicMaterial({
				map: texture,
			})
			this.sphere = new THREE.Mesh(sphereGeometry, sphereMaterial)
			this.scene.add(this.sphere)
		},
		initLabel() {
			this.labelDataList.forEach(item => {
				this.sphere.remove(item)
			})
			this.labelDataList = []
			this.dataList[0].tipsList.forEach(item => {
				this.createLabel(item)
			})
		},
		createLabel(data) {
			const { content = {}, position } = data
			const { title, list = [] } = content
			const { x, y, z } = position
			let div = document.createElement('div')
			let tmp = ''

			list.forEach(item => {
				tmp += `<div class="three_title_content_item ${title}">
						${item.itemName}: ${item.value}${item.unit}
					</div>`
			})
			let str = `
				<div class="three_shuichuang_div1 ${title}">
					<div class="three_title ${title}">${title}</div>
					<div class="three_title_content ${title}">
						${tmp}
					</div>
				</div>
				<div class="three_shuichuang_line"></div>
			`

			div.className = 'three-label'
			div.style.position = 'relative'
			div.innerHTML = str
			if (list && list.length) {
				css2DRenderer.domElement.addEventListener('mousemove', e => {
					if (e.target.className === 'three_title ' + title) {
						document.getElementsByClassName('three_title_content ' + title).forEach(item => {
							item.style['display'] = 'block'
						})
					}
				})
				css2DRenderer.domElement.addEventListener('mouseout', e => {
					if (e.target.className === 'three_shuichuang_div1 ' + title) {
						document.getElementsByClassName('three_title_content ' + title).forEach(item => {
							item.style['display'] = 'none'
						})
					}
				})
			}

			const labelObj = new CSS2DObject(div)
			labelObj.position.set(x, y, z)

			this.labelDataList.push(labelObj)
			this.sphere.add(labelObj)
		},
		create2DRenderer() {
			const threeDBoxDom = this.$refs.threeDBox

			css2DRenderer = new CSS2DRenderer()
			css2DRenderer.setSize(threeDBoxDom.clientWidth, threeDBoxDom.clientHeight)
			css2DRenderer.domElement.id = 'label-renderer'
			css2DRenderer.domElement.style.position = 'absolute'
			css2DRenderer.domElement.style.top = '0px'
			threeDBoxDom.appendChild(css2DRenderer.domElement)
		},
		create3DRenderer() {
			const threeDBoxDom = this.$refs.threeDBox
			css3DRenderer = new CSS3DRenderer()
			css3DRenderer.setSize(threeDBoxDom.clientWidth, threeDBoxDom.clientHeight)
			css3DRenderer.domElement.id = 'label-renderer-3d'
			css3DRenderer.domElement.style.position = 'absolute'
			css3DRenderer.domElement.style.top = '0px'
			threeDBoxDom.appendChild(css3DRenderer.domElement)

			this.createLightPoint()
		},
		createLightPoint() {
			this.lightPoineData.forEach(item => {
				const { position } = item
				const { x, y, z } = position
				let div = document.createElement('div')

				div.className = 'three-label-3d'

				div.addEventListener('mousedown', () => {
					// let tween = new TWEEN.Tween({ x: -70, y: 20, z: 50 })

					new TWEEN.Tween(this.camera)
						.to({ position: new THREE.Vector3(-70, 20, z) }, 2000)
						.easing(TWEEN.Easing.Sinusoidal.InOut)
						.start()
					// this.camera.position.set(-70, 20, z)
				})

				let lightPointObj = new CSS3DObject(div)

				lightPointObj.position.set(x, y, z)
				lightPointObj.lookAt(x, 0, z)

				this.lightPointList.push(lightPointObj)
				this.scene.add(lightPointObj)
			})
		},

		changeContentAndtips(index) {
			this.scene.children = this.scene.children.filter(item => String(item.type) !== 'Sprite')
			this.tipsSpriteList = []
			let texture = new THREE.TextureLoader().load(this.dataList[index].image)
			let sphereMaterial = new THREE.MeshBasicMaterial({
				map: texture,
				transparent: true,
				opacity: 0,
			})
			this.sphere.material = sphereMaterial
			// gsap.to(sphereMaterial, {
			// 	transparent: true,
			// 	opacity: 1,
			// 	duration: 2,
			// })
			this.camera.updateProjectionMatrix()
			this.addTipsSprite(index)
		},
		render() {
			this.controls.update()
			this.renderer.render(this.scene, this.camera)
			this.renderer.sortObjects = false
			this.timer = requestAnimationFrame(this.render)
			this.time.value += 0.015
			css2DRenderer && css2DRenderer.render(this.scene, this.camera)
			css3DRenderer && css3DRenderer.render(this.scene, this.camera)

			TWEEN.update()
		},
		onResize() {
			let element = this.$refs.threeDBox
			this.camera.aspect = element.clientWidth / element.clientHeight
			this.camera.updateProjectionMatrix()
			this.renderer.setSize(element.clientWidth, element.clientHeight)
		},
		onMouseClick(e) {
			e.preventDefault()
			let element = this.$refs.threeDBox
			let raycaster = new THREE.Raycaster()
			let mouse = new THREE.Vector2()
			mouse.x = (e.clientX / element.clientWidth) * 2 - 1
			mouse.y = -(e.clientY / element.clientHeight) * 2 + 1
			raycaster.setFromCamera(mouse, this.camera)
			let intersects = raycaster.intersectObjects(this.tipsSpriteList, true)
			if (intersects.length > 0 && intersects[0].object.content.showTitle) {
				this.changeContentAndtips(intersects[0].object.content.image)
				this.handleTooltipHide(e)
			}
		},
		onMousemove(e) {
			e.preventDefault()
			let element = this.$refs.threeDBox
			let raycaster = new THREE.Raycaster()
			let mouse = new THREE.Vector2()
			mouse.x = (e.clientX / element.clientWidth) * 2 - 1
			mouse.y = -(e.clientY / element.clientHeight) * 2 + 1
			raycaster.setFromCamera(mouse, this.camera)
			let intersects = raycaster.intersectObjects(this.tipsSpriteList, true)
			if (intersects.length > 0) {
				let elementWidth = element.clientWidth / 2
				let elementHeight = element.clientHeight / 2
				let worldVector = new THREE.Vector3(
					intersects[0].object.position.x,
					intersects[0].object.position.y,
					intersects[0].object.position.z,
				)
				let position = worldVector.project(this.camera)
				this.tooltopContent = intersects[0].object.content
				if (intersects[0].object.content.showTip) {
					let left = Math.round(
						elementWidth * position.x + elementWidth - this.$refs.tooltipBox.clientWidth / 2,
					)
					let top = Math.round(
						-elementHeight * position.y + elementHeight - this.$refs.tooltipBox.clientHeight / 2,
					)
					this.tooltipPosition = {
						left: `${left}px`,
						top: `${top}px`,
					}
				} else if (intersects[0].object.content.showTitle) {
					let left = Math.round(
						elementWidth * position.x + elementWidth - this.$refs.titleBox.clientWidth / 2,
					)
					let top = Math.round(-elementHeight * position.y + elementHeight)
					this.titlePosition = {
						left: `${left}px`,
						top: `${top}px`,
					}
				}
			} else {
				this.handleTooltipHide(e)
			}
		},
		handleTooltipHide(e) {
			e.preventDefault()
			this.tooltipPosition = {
				top: '-100%',
				left: '-100%',
			}
			this.titlePosition = {
				top: '-100%',
				left: '-100%',
			}
			this.tooltopContent = {}
		},

		// 轮询模块
		setPolling() {
			this.itemDate()
			timer && clearTimeout(timer)
			timer = setInterval(this.itemDate, 5000)
		},

		itemDate() {
			itemDate([
				// 公共
				{
					stationCode: '20220628001_0', // 苍南
					itemCodes: [],
					sysCode: 'eg',
				},
				// 低区
				{
					stationCode: '20220628001_1', // 苍南
					itemCodes: [],
					sysCode: 'eg',
				},
				// 中区
				{
					stationCode: '20220628001_2', // 苍南
					itemCodes: [],
					sysCode: 'eg',
				},
				// 高区
				{
					stationCode: '20220628001_3', // 苍南
					itemCodes: [],
					sysCode: 'eg',
				},
			]).then(res => {
				const { result = [] } = res
				// let obj = {}
				let arr = []
				let arr1 = []
				let arr2 = []
				let arr3 = []
				let arr4 = []
				let arr5 = []
				let arr6 = []
				let arr7 = []
				result.forEach(item => {
					const { stationDataItem = [] } = item

					stationDataItem.forEach(item1 => {
						// 进口压力
						if (item1.itemRealCode === 'YL_YS') {
							arr1.push(item1)
						}
						// 出口压力
						if (item1.itemRealCode === 'YL_CS') {
							arr2.push(item1)
						}
						// 出口流量
						if (item1.itemRealCode === 'SSLL_CS' || item1.itemRealCode === 'LJLL_CS') {
							arr3.push(item1)
						}
						// 水质
						if (
							item1.itemRealCode === 'PH_YS' ||
							item1.itemRealCode === 'ZD_YS' ||
							item1.itemRealCode === 'YLv_YS'
						) {
							arr4.push(item1)
						}
						// 1#泵
						if (
							item1.itemRealCode === 'YXZT_SB1' ||
							item1.itemRealCode === 'PL_BPQ1' ||
							item1.itemRealCode === 'DL_BPQ1' ||
							item1.itemRealCode === 'DY_BPQ1' ||
							item1.itemRealCode === 'YXSJ_BPQ1'
						) {
							arr5.push(item1)
						}
						// 2#泵
						if (
							item1.itemRealCode === 'YXZT_SB2' ||
							item1.itemRealCode === 'PL_BPQ2' ||
							item1.itemRealCode === 'DL_BPQ2' ||
							item1.itemRealCode === 'DY_BPQ2' ||
							item1.itemRealCode === 'YXSJ_BPQ2'
						) {
							arr6.push(item1)
						}
						// 3#泵
						if (
							item1.itemRealCode === 'YXZT_SB3' ||
							item1.itemRealCode === 'PL_BPQ3' ||
							item1.itemRealCode === 'DL_BPQ3' ||
							item1.itemRealCode === 'DY_BPQ3' ||
							item1.itemRealCode === 'YXSJ_BPQ3'
						) {
							arr7.push(item1)
						}
					})
				})
				this.dataList[0].tipsList[0].content.list = arr
				this.dataList[0].tipsList[1].content.list = arr1
				this.dataList[0].tipsList[2].content.list = arr2
				this.dataList[0].tipsList[3].content.list = arr3
				this.dataList[0].tipsList[4].content.list = arr4
				this.dataList[0].tipsList[5].content.list = arr5
				this.dataList[0].tipsList[6].content.list = arr6
				this.dataList[0].tipsList[7].content.list = arr7

				this.initLabel()
			})
		},
	},
	mounted() {
		let element = this.$refs.threeDBox
		this.initScene()
		this.initCamera(element)
		this.initControls(element)
		this.initRenderer(element)
		this.render()
		this.create2DRenderer()
		this.create3DRenderer()
		this.initContent()
		// this.setPolling()
		this.itemDate()
		window.addEventListener('resize', this.onResize, false)
		window.addEventListener('click', this.onMouseClick, false)
		this.renderer.domElement.addEventListener('mousemove', this.onMousemove, false)
		this.$refs.tooltipBox.addEventListener('mouseleave', this.handleTooltipHide, false)
	},
	destroyed() {
		timer && clearTimeout(timer)
		cancelAnimationFrame(this.timer)
	},
}
</script>

<style lang="less" scoped>
.home {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
	.view-container {
		width: 100%;
		height: 100%;
		overflow: hidden;
	}
	.title-text {
		position: absolute;
		width: 66px;
		color: #382129;
	}
	.tooltip-box {
		position: absolute;
		padding: 0px 0px 40px 0px;
		line-height: 30px;
		border-radius: 4px;
		color: #ffffff;
		z-index: 100;
		cursor: pointer;
		.container {
			position: relative;
			width: 240px;
			max-height: 200px;
			padding: 10px;
			background-color: rgba(0, 0, 0, 0.6);
			// &::before {
			//   content: "";
			//   position: absolute;
			//   bottom: -16px;
			//   left: 20%;
			//   border-top: 16px solid rgba(0, 0, 0, 0.8);
			//   border-left: 10px solid transparent;
			//   border-right: 10px solid transparent;
			// }
			.title {
				width: 100%;
				padding: 6px 0;
			}
			.explain {
				width: 100%;
				max-height: 100px;
				font-size: 14px;
				overflow-y: auto;
				&::-webkit-scrollbar {
					width: 4px;
					height: 4px;
				}
				&::-webkit-scrollbar-track {
					background: #353535;
					border-radius: 2px;
				}
				&::-webkit-scrollbar-thumb {
					background: #cdcdcd;
					border-radius: 2px;
				}
				&::-webkit-scrollbar-thumb:hover {
					background: #9c9c9c;
				}
				&::-webkit-scrollbar-corner {
					background: #f6f6f6;
				}
			}
		}
	}
}
</style>
<style lang="less">
.three_title_content {
	display: none;
}
.three_title_content.进口压力,
.three_title_content.出口压力,
.three_title_content.出口流量,
.three_title_content.水质 {
	display: block !important;
}

.three-label-3d {
	width: 20px;
	height: 20px;
	background: url('~@/assets/image/light_point.png') no-repeat 0 0;
	background-size: 100% 100%;
	cursor: pointer;
}
</style>
