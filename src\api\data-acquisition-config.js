import { POST, GET, axios } from '@/utils/request'

// 采集渠道分页列表查询
export function queryTransChannel(params) {
	return POST({
		url: '/waterPlat/transChannel/queryTransChannel',
		params,
	})
}

// 新增采集渠道
export function addTransChannel(params) {
	return POST({
		url: '/waterPlat/transChannel/addTransChannel',
		params,
	})
}

// 编辑采集渠道
export function updateTransChannel(params) {
	return POST({
		url: '/waterPlat/transChannel/updateTransChannel',
		params,
	})
}

// 删除采集渠道
export function deleteTransChannel(params) {
	return POST({
		url: '/waterPlat/transChannel/deleteTransChannel',
		params,
	})
}

// 设备原始档案分页列表查询
export function queryStationInfo(params) {
	return POST({
		url: '/waterPlat/transStationInfo/queryStationInfo',
		params,
	})
}

// 新增设备原始档案
export function addStationInfo(params) {
	return POST({
		url: '/waterPlat/transStationInfo/addStationInfo',
		params,
	})
}

// 编辑设备原始档案
export function updateStationInfo(params) {
	return POST({
		url: '/waterPlat/transStationInfo/updateStationInfo',
		params,
	})
}

// 删除设备原始档案
export function deleteStationInfo(params) {
	return POST({
		url: '/waterPlat/transStationInfo/deleteStationInfo',
		params,
	})
}

// 查询支持配置的所有采集渠道
export function queryTransChannelNoEnableRule(params) {
	return POST({
		url: '/waterPlat/transChannel/queryTransChannelNoEnableRule',
		params,
	})
}

// 新增采集规则
export function addTransRule(data) {
	return axios({
		method: 'post',
		url: '/transRule/addTransRule',
		headers: { 'Content-type': 'application/json' },
		data,
	})
}

// 编辑采集规则
export function updateTransRule(data) {
	return axios({
		method: 'post',
		url: '/transRule/updateTransRule',
		headers: { 'Content-type': 'application/json' },
		data,
	})
}

// 采集规则分页列表查询
export function queryTransRuleList(params) {
	return POST({
		url: '/transRule/queryTransRuleList',
		params,
	})
}

// 删除采集规则
export function deleteTransRule(params) {
	return POST({
		url: '/transRule/deleteTransRule',
		params,
	})
}

// 原始位号下拉框查询
export function queryTransOriginalCode(params) {
	return POST({
		url: '/transRule/queryTransOriginalCode',
		params,
	})
}

// 设备档案同步信息查询
export function queryOriginalStationInfo(params) {
	return POST({
		url: '/waterPlat/transStationInfo/queryOriginalStationInfo',
		params,
	})
}

// 查询厂家列表
export function producers(params) {
	return POST({
		url: '/waterPlat/transStationInfo/producers',
		params,
		requestType: 'json',
	})
}
// 快捷配置
export function transRuleRef(params) {
	return POST({
		url: '/transRule/transRuleRef',
		params,
	})
}
// 意见平台下控
export function transOff(params) {
	return POST({
		url: '/transRule/off',
		params,
	})
}
// 下控显示数据项
export function showItem(params) {
	return POST({
		url: '/transRule/showItem',
		params,
	})
}
// topic配置
export function getTopicList(params) {
	return GET({
		url: '/waterPlat/topic/list',
		params,
	})
}
// topic配置删除
export function getTopicDel(params) {
	return GET({
		url: '/waterPlat/topic/delete',
		params,
	})
}
// topic配置新增
export function addTopic(params) {
	return POST({
		url: '/waterPlat/topic/insert',
		params,
		requestType: 'json',
	})
}
// topic配置编辑
export function updateTopic(params) {
	return POST({
		url: '/waterPlat/topic/update',
		params,
		requestType: 'json',
	})
}
// topic数据源列表
export function getConnects(params) {
	return GET({
		url: '/waterPlat/topic/connects',
		params,
	})
}
