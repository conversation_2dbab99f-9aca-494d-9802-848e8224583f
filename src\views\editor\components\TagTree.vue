<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: zhangyi
 * @Date: 2022-09-14 09:05:31
 * @LastEditors: zhangyi
 * @LastEditTime: 2022-09-14 14:24:20
-->
<template lang="pug">
.tree-list(:style='{ height: height + "px" }')
	.tree-list-content
		es-huge-tree.es-huge-tree(
			ref='huge-tree',
			showCheckbox,
			hasInput,
			expand-level='all',
			placeholder='请输入站点名称进行搜索',
			filter-key='label',
			:multiple='multiple',
			:is-loading='isLoading',
			:defaultcheckedkeys='checkedKeys',
			@onClickCheckbox='onClickCheckbox'
		)
			span(slot-scope='{ slotScope }')
				span {{ slotScope.label }}
			i(slot='loading') 加载中...
</template>
<script>
export default {
	name: '',
	model: {
		prop: 'value',
		event: 'set-value',
	},
	props: {
		value: Array,
		sysCode: {
			type: String,
			default: 'dc',
		},
		height: {
			type: Number,
			default: 520,
		},
		multiple: {
			type: Boolean,
			default: true,
		},
		search: Boolean,
	},
	data() {
		return {
			isChangeCheckbox: false,
			isLoading: false,
			checkedKeys: [],
			currentArr: [],
		}
	},
	watch: {
		value(val) {
			if (!this.isChangeCheckbox && val && val.length) {
				let arr = []

				val.forEach(item => {
					const uniqueCode = item.nodeType + '$_$' + (item.oldId || item.nodeId)

					arr.push(uniqueCode)
				})

				this.checkedKeys = arr
				this.$refs['huge-tree'].setCheckedKeys(arr)
			}
		},
	},
	mounted() {
		// this.getTreeData()
	},
	methods: {
		// 点击复选框时触发
		onClickCheckbox(node) {
			let currentArr = this.value !== undefined ? [...this.value] : this.currentArr
			let idx = null

			this.isChangeCheckbox = true
			if (this.multiple) {
				if (node.checked) {
					currentArr.push(node)
				} else {
					idx = currentArr.findIndex(i => i.id === node.id)
					if (idx !== null) {
						currentArr.splice(idx, 1)
					}
					idx = null
				}
			} else {
				if (node.checked) {
					currentArr = [node]
					this.$refs['huge-tree'].clearChecked()
					this.$refs['huge-tree'].setCheckedKeys([node.id])
				} else {
					currentArr = []
					this.$refs['huge-tree'].clearChecked()
				}
			}
			this.currentArr = currentArr
			this.$emit('set-value', currentArr)
			this.$emit('check-change', {
				selectedArr: currentArr,
				currentNode: node,
			})

			setTimeout(() => {
				this.isChangeCheckbox = false
			}, 200)
		},
		setTreeData(data, sysCode, hideCheckbox = true) {
			data.forEach(item => {
				const { stationDataItem } = item
				item.parentId = null
				item.id = item.stationCode
				item.label = item.stationName
				item.sysCode = sysCode || item.sysCode
				item.hideCheckbox = hideCheckbox
				if (stationDataItem) {
					stationDataItem.forEach((data, index) => {
						data.parentId = item.stationCode
						data.id = item.stationCode + index
						data.label = data.itemName
						data.sysCode = sysCode
						data.isShow = true
						data.hideCheckbox = false
					})
					item.children = stationDataItem
				}
			})
			this.$refs['huge-tree'].setData(data)
		},
	},
}
</script>
<style lang="less" scoped>
.tree-list {
	// height: 520px;
	padding: 0 8px;
	border-bottom: 1px solid #e8e8e8;
	display: flex;
	&-content {
		position: relative;
		flex-grow: 1;
		overflow: auto;
		::v-deep {
			.es-huge-tree {
				border: none;
				.search-bar {
					padding: 0;
					margin-bottom: 10px;
				}
				.search-btn {
					border-radius: 0 4px 4px 0;
				}
				.input .filter-input {
					border-radius: 4px 0 0 4px;
					padding: 6px 10px;
				}
				.expand-node {
					&::before {
						font-size: 13px !important;
						color: #999 !important;
					}
				}
			}
		}
	}
}
</style>
