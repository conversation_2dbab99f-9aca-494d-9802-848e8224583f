<!--
 * @Description: 专用报警配置-表格页
 * @Author: shenxh
 * @Date: 2022-03-07 09:40:12
 * @LastEditors: shenxh
 * @LastEditTime: 2024-10-30 14:18:02
-->

<template lang="pug">
.warn-list
	es-search.es-search(
		col='4',
		:show-collapse='false',
		:modules='moduleList',
		@on-search='handleSearchBtn',
		@on-reset='handleResetBtn'
	)
	water-row(justify='space-between', align='center')
		.water-tabs-wrap
			water-tabs(
				v-show='!this.$route.query.sysCode || this.$route.query.isCommon === \'1\'',
				v-model='currentTab',
				ref='water-tabs',
				:hide-all='$route.name === "special-use-alarm"',
				:is-search.sync='isSearch',
				:form-data='formDataFormat',
				@get-sys-list='getSysList',
				@handle-tab='handleTab'
			)
		water-row.water-margin-top-8(justify='flex-start', align='center')
			Button(:disabled='!canCreatAlarm', @click='handleBatchClearBtn()') 创建报警方案

	.warn-list-content(v-if='sysList.length')
		.water-tree-wrap(:style='{ width: $route.name === "special-use-alarm" ? "270px" : 0 }')
			.tree-type-wrap(v-if='currentTab == "dma"')
				Button.tab-item(
					@click='changeTreeType(item)',
					v-for='(item, index) in dmaTreeType',
					:key='index',
					:class='[treeTypeKey == item.key ? "cur-tab" : ""]'
				) {{ item.title }}
			water-tree(
				v-if='$route.name === "special-use-alarm" && !isJizeTab',
				ref='tree',
				:sys-code='currentTab',
				:sys-list='sysList',
				:stationType='treeTypeKey',
				search,
				v-model='selectedNodes',
				@check-change='checkChange',
				@change='changeSearchVal'
			)
			div(v-if='$route.name === "special-use-alarm" && isJizeTab')
				.acheme-left-header
					Input(style='width: 200px; margin-right: 10px', clearable, v-model='deviceTypeName', placeholder='请输入名称')
					Button(type='primary', @click='handleSearch') 查询
				Table(
					ref='treeTable',
					:show-header='false',
					style='width: 270px',
					:border='false',
					highlight-row,
					height='800',
					:columns='treeColumns',
					:data='treeData',
					@on-select='handleSelect'
				)
		es-table.water-table.warn-list-table(
			stripe,
			:columns='columns',
			:data='tableData',
			:loading='loading',
			border,
			showPage,
			:pageData='pageData',
			@on-page-num-change='handlePageNum',
			@on-page-size-change='handlePageSize'
		)
			template(slot-scope='{ row }', slot='summary')
				span(v-html='_getSummary(row.ruleList)')
			template(slot-scope='{ row }', slot='siteQua')
				span(v-if='row.refList') {{ row.refList.length }}
			template(slot-scope='{ row }', slot='dateRange')
				span {{ getDateType(row.effectiveDateType) }}
				br
				span {{ row.effectiveDateStr }}
			template(slot-scope='{ row }', slot='startUse')
				i-switch(v-model='row.status === 1', @on-change='changeStartUse($event, row)')
			template(slot-scope='{ row }', slot='action')
				Button(type='text', :style='{ color: "#3DCC1B" }', @click='handleDet(row, "read")', size='small') 查看
				span |
				Button(type='text', :style='{ color: "#3AA7D8" }', @click='handleDet(row, "update")', size='small') 编辑
				span |
				Poptip(transfer, confirm='', title='这条方案配置信息确定删除吗？', @on-ok='handleDel(row)')
					Button(type='text', :style='{ color: "#EC5151" }', size='small') 删除
</template>

<script>
import WaterRow from '@/components/gc-water-row'
import WaterTabs from '@/components/gc-water-tabs'
import WaterTree from '@/components/gc-water-tree'
import { queryAlarmLevelList } from '@/api/common.js'
import { getAlarmConfig, setSwitch, deleteAlarmConfigs } from '@/api/alarm-config.js'
import { queryTemplateByName } from '@/api/base-item'

export default {
	components: {
		WaterRow,
		WaterTabs,
		WaterTree,
	},
	data() {
		return {
			treeColumns: [
				{
					type: 'selection',
					width: 30,
					align: 'center',
				},
				{ key: 'name' },
			], //设备类型或设备左侧列表
			treeData: [],
			name: '',
			dmaTreeType: [
				{ title: '区域配置', key: 'AREA' },
				{ title: '站点配置', key: 'STATION' },
			],
			treeTypeKey: '',
			sysList: [],
			subCode: '',
			disCreat: true,
			// sysCode: this.$route.name === 'special-use-alarm' ? 'dc' : '',
			sysCode: '',
			formDataFormat: {},
			isSearch: false,
			loading: false,
			tableData: [],
			startUseVal: false,
			// currentTab:
			// 	this.$route.query.sysCode ||
			// 	(this.$route.name === 'special-use-alarm' ? 'dc' : '_all'),
			currentTab: '',
			currentTabObj: {},
			selectedNodes: [],
			tabsData: [
				{
					label: '全部',
					value: 123,
					name: '0',
				},
				{
					label: '单村',
					value: 2,
					name: '1',
				},
				{
					label: '运行调度',
					value: 10,
					name: '2',
				},
				{
					label: '二次供水',
					value: 10,
					name: '3',
				},
			],
			moduleList: [
				{
					label: '',
					value: '1',
					isDefault: true,
					keys: ['alarmLevelId', 'scenarioName', '', ''],
					model: {
						alarmLevelId: '',
						scenarioName: '',
					},
					data: [
						{
							type: 2,
							key: 'alarmLevelId',
							formItemProps: {
								label: '报警等级',
								prop: 'alarmLevelId',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
						{
							type: 1,
							key: 'scenarioName',
							formItemProps: {
								label: '方案名称',
								prop: 'scenarioName',
								labelWidth: 80,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
					],
				},
			],
			columns: [],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 30, 40, 50],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	watch: {
		treeTypeKey: {
			handler(val) {
				if (val && this.$route.name == 'special-use-alarm') {
					this.$nextTick(() => {
						this.$refs.tree._querySysConfigTree()
					})
				}
			},
		},
		sysCode: {
			handler() {},
		},
		selectedNodes: {
			handler(val) {
				console.log('selectedNodes-------------', val)
			},
			deep: true,
		},
		$route: {
			handler: function () {
				this._getTableData()
				this.$refs['water-tabs']._querySysList()
			},
		},
	},
	computed: {
		canCreatAlarm() {
			// return (
			// 	this.$route.name !== 'special-use-alarm' ||
			// 	(this.selectedNodes && this.selectedNodes.length)
			// )

			return (
				(this.$route.name !== 'special-use-alarm' || (this.selectedNodes && this.selectedNodes.length)) &&
				this.currentTab !== '_all' &&
				this.disCreat
			)
		},
		isJizeTab() {
			return this.sysList.findIndex(item => item?.code == 'jz') !== -1
		},
	},
	created() {
		this._queryAlarmLevelList()
		this._setPageData()
	},
	mounted() {
		this._getTableData()
		this.handleSearch()
	},
	methods: {
		// 修改树类型
		changeTreeType(item) {
			this.selectedNodes = []
			this.treeTypeKey = item.key
			this.disCreat = true
			this.tableData = []
			this.pageData.total = 0
			this.pageData.current = 1
			this._getTableData()
		},
		// 搜索按钮
		handleSearchBtn() {
			this.pageData.current = 1

			// this.isSearch = true
			this._getTableData()
		},

		// 重置按钮
		handleResetBtn() {
			this.pageData.current = 1

			this._getTableData()
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum
			// this.isSearch = true
			this._getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize
			// this.isSearch = true
			this._getTableData()
		},

		// 切换启用开关
		changeStartUse(status, row) {
			const { id } = row

			setSwitch({
				id,
				status: Number(status),
			}).then(() => {
				this.$Message.success('操作成功')

				this._getTableData()
			})
		},

		// 创建报警方案
		handleBatchClearBtn() {
			const { sysCode, subCode } = this.getRealCode(this.currentTab)
			if (sysCode === 'dma') {
				this.$router.push({
					path: '/dma-alarm-scheme',
					query: {
						type: 'create',
						configType: this.$route.name === 'common-alarm' ? 'common' : 'special',
						sysCode,
						stationType: this.treeTypeKey || 'AREA',
						nodeId:
							this.selectedNodes && this.selectedNodes.length ? this.selectedNodes[0].oldId : undefined,
						nodeType:
							this.selectedNodes && this.selectedNodes.length
								? this.selectedNodes[0].nodeType
								: undefined,
						nodeCode:
							this.selectedNodes && this.selectedNodes.length
								? this.selectedNodes[0].nodeCode
								: undefined,
						isCommon: this.$route.query.sysCode && this.$route.query.isCommon !== '1' ? 0 : 1,
					},
				})
			} else {
				if (this.currentTab == 'jz') {
					this.$router.push({
						path: '/device-alarm-config',
						query: {
							type: 'create',
							configType: this.$route.name === 'common-alarm' ? 'common' : 'special',
							sysCode: 'jz',
							subSysCode: subCode || '',
							stationType: this.treeTypeKey,
							nodeId:
								this.selectedNodes && this.selectedNodes.length ? this.selectedNodes[0]?.id : undefined,
							nodeType:
								this.selectedNodes && this.selectedNodes.length
									? this.selectedNodes[0]?.deviceTypeCode
									: undefined,
							isCommon: this.$route.query.sysCode && this.$route.query.isCommon !== '1' ? 0 : 1,
						},
					})
				} else {
					this.$router.push({
						path: '/alarm-scheme',
						query: {
							type: 'create',

							configType: this.$route.name === 'common-alarm' ? 'common' : 'special',
							sysCode,
							subSysCode: subCode || '',
							stationType: this.treeTypeKey,
							nodeId:
								this.selectedNodes && this.selectedNodes.length
									? this.selectedNodes[0].oldId
									: undefined,
							nodeType:
								this.selectedNodes && this.selectedNodes.length
									? this.selectedNodes[0].nodeType
									: undefined,
							nodeCode:
								this.selectedNodes && this.selectedNodes.length
									? this.selectedNodes[0].nodeCode
									: undefined,
							isCommon: this.$route.query.sysCode && this.$route.query.isCommon !== '1' ? 0 : 1,
						},
					})
				}
			}
		},

		// 查看详情/编辑
		handleDet(row, type) {
			const { refList = [] } = row || {}
			const { nodeId, nodeType, nodeCode } = refList[0] || {}
			const { sysCode, subCode } = this.getRealCode(this.currentTab)
			row.sysCode = 'jz'
			if (row.sysCode === 'dma') {
				this.$router.push({
					path: '/dma-alarm-scheme',
					query: {
						id: row.id,
						type,
						configType: this.$route.name === 'common-alarm' ? 'common' : 'special',
						sysCode: row.sysCode || sysCode,
						stationType: row.stationType || '',
						isCommon: this.$route.query.isCommon || (this.$route.query.sysCode ? 0 : 1),
						nodeId,
						nodeType,
						nodeCode,
					},
				})
			} else if (row.sysCode === 'jz') {
				this.$router.push({
					path: '/device-alarm-config',
					query: {
						id: row.id,
						type,
						configType: this.$route.name === 'common-alarm' ? 'common' : 'special',
						sysCode: row.sysCode || sysCode,
						subSysCode: subCode || '',
						stationType: row.stationType || '',
						isCommon: this.$route.query.isCommon || (this.$route.query.sysCode ? 0 : 1),
						nodeId,
						nodeType,
						nodeCode,
					},
				})
			} else {
				this.$router.push({
					path: '/alarm-scheme',
					query: {
						id: row.id,
						type,
						configType: this.$route.name === 'common-alarm' ? 'common' : 'special',
						sysCode: row.sysCode || sysCode,
						subSysCode: subCode || '',
						stationType: row.stationType || '',
						isCommon: this.$route.query.isCommon || (this.$route.query.sysCode ? 0 : 1),
						nodeId,
						nodeType,
						nodeCode,
					},
				})
			}
		},

		// 删除按钮
		handleDel(row) {
			this._deleteAlarmConfigs([row.id])
		},

		// 获取 tab 列表
		getSysList(data) {
			this.sysList = data
			const { subSysCode, sysCode, stationType } = this.$route.query
			this.sysCode = this.$route.name === 'special-use-alarm' ? data[0].code : ''

			const queryCode = subSysCode ? sysCode + '&&' + subSysCode : sysCode
			const dataCode = data[0].code

			this.currentTab = queryCode || dataCode

			if (stationType && this.$route.name == 'special-use-alarm') {
				this.treeTypeKey = stationType
			}
			this._getTableData(this.currentTab)
		},

		// 点击 tab
		handleTab({ currentTabObj }) {
			this.currentTabObj = currentTabObj

			this.selectedNodes = []
			this.pageData.current = 1
			this.sysCode = this.currentTab === '_all' ? '' : this.currentTab
			// 初始化dma树tab
			if (this.$route.name == 'special-use-alarm' && currentTabObj.code === 'dma') {
				this.treeTypeKey = 'AREA'
			}

			this._getTableData()
			this._setPageData()
		},

		// 点击树形多选框触发
		checkChange() {
			this._getTableData()
		},

		// 搜索
		changeSearchVal() {},

		// 获取日期类型
		getDateType(code) {
			let name = ''

			switch (code) {
				case 'ALL':
					name = '所有日期'
					break
				case 'HOLIDAY':
					name = '假日'
					break
				case 'WORKINGDAY':
					name = '工作日'
					break
				case 'CUSTOM':
					name = '自定义'
					break
			}

			return name
		},

		// 设置页面data
		_setPageData() {
			if (this.$route.name === 'common-alarm') {
				this.columns = [
					{
						type: 'index',
						width: 65,
						align: 'center',
						title: '序号',
					},
					{
						title: '报警方案名称',
						key: 'name',
					},
					{
						title: '方案编号',
						key: 'code',
					},
					{
						title: '报警配置摘要',
						slot: 'summary',
					},
					{
						title: '生效时间范围',
						slot: 'dateRange',
					},
					{
						title: this.$route.query.sysCode == 'dma' ? '生效对象数' : '生效站点数(个)',
						slot: 'siteQua',
						width: 130,
					},
					{
						title: '启用',
						slot: 'startUse',
						align: 'center',
						width: 100,
					},
					{
						title: '操作',
						slot: 'action',
						width: 200,
					},
				]
				if (this.$route.query.sysCode == 'dma') {
					this.columns.splice(6, 0, {
						title: '类型',
						key: 'type',
						align: 'center',
						width: 100,
					})
				}
			} else {
				this.columns = [
					{
						type: 'index',
						width: 65,
						align: 'center',
						title: '序号',
					},
					{
						title: '报警方案名称',
						key: 'name',
					},
					{
						title: '报警配置摘要',
						slot: 'summary',
					},
					{
						title: '生效时间范围',
						slot: 'dateRange',
					},
					{
						title: '启用',
						slot: 'startUse',
						align: 'center',
						width: 100,
					},
					{
						title: '操作',
						slot: 'action',
						width: 200,
					},
				]
			}
		},

		// 获取摘要信息
		_getSummary(list) {
			let str = ''

			if (list && list.length) {
				list.forEach(item => {
					str += item.alarmAbstract + '<br>'
				})
			}

			return str
		},

		// 获取表格数据
		_getTableData() {
			const formData = this.moduleList[0].model

			this.formDataFormat = formData

			this._getAlarmConfig(formData)
			// this.$nextTick(() => {
			// 	if (this.$refs['water-tabs']) {
			// 		this.$refs['water-tabs']._querySysList()
			// 	}
			// })
		},

		// 查询【报警等级列表】
		_deleteAlarmConfigs(ids) {
			deleteAlarmConfigs({
				ids,
			}).then(() => {
				this.$Message.success('删除成功')

				this._getTableData()
			})
		},

		// 查询报警方案配置
		_getAlarmConfig(params) {
			const { scenarioName: name, alarmLevelId } = params
			const { current: pageNum, pageSize } = this.pageData
			const { sysCode, subCode } = this.getRealCode(this.currentTab)
			this.loading = true
			getAlarmConfig({
				alarmConfigType: this.$route.name === 'special-use-alarm' ? 2 : 1,
				sysCode,
				subSysCode: subCode || '',
				stationType: this.sysCode == 'dma' ? this.treeTypeKey : '',
				name,
				needPage: true,
				alarmLevelId,
				nodeId: this.selectedNodes.length ? this.selectedNodes[0].oldId : undefined,
				pageNum,
				pageSize,
			}).then(res => {
				const { list: data, total } = res.result

				if (data) {
					this.tableData = data
				}
				this.pageData.total = total
				this.loading = false
			})
		},

		// 删除报警方案
		_queryAlarmLevelList() {
			queryAlarmLevelList().then(res => {
				const data = res.result

				if (data && data.length) {
					this.moduleList[0].data[0].dataSourceList = data.map(item => {
						return {
							label: item.name,
							value: item.id,
						}
					})
				}
			})
		},
		// 获取真正参数
		getRealCode(sysCode) {
			if (!sysCode) {
				return ''
			}
			if (sysCode.includes('&&')) {
				// this.subCode = sysCode.split('&&')[1]
				// return sysCode.split('&&')[0]
				return {
					sysCode: sysCode.split('&&')[0],
					subCode: sysCode.split('&&')[1],
				}
			}
			if (sysCode === '_all') {
				sysCode = ''
			}
			return { sysCode, subCode: '' }
		},
		// 左侧模板树选择点击
		handleSelect(selection, row) {
			this.selectedNodes = []
			this.selectedNodes.push(row)
		},
		// 查询左侧模板树
		handleSearch() {
			let params = { name: this.name }
			queryTemplateByName(params).then(res => {
				this.treeData = res.result
				if (this.treeData && this.treeData.length && this.treeData.length > 0) {
					this.treeData[0]._highlight = true
					this.type = this.treeData[0]?.type
					this.selectedId = this.treeData[0]?.id
				}
			})
		},
	},
}
</script>

<style scoped lang="less">
.warn-list {
	width: 100%;
	height: 100%;
	.es-search {
		padding-top: 4px;
		/deep/ .ivu-input-wrapper {
			overflow: hidden;
		}
		/deep/ .ivu-btn {
			background-color: #57a3f3;
			border-color: #57a3f3;
			color: #fff;
			padding: 0 10px;
			margin-left: 10px;
			&:last-child {
				color: #515a6e;
				background-color: #fff;
				border-color: #dcdee2;
			}
			.ivu-icon {
				display: none;
			}
			span {
				margin: 0;
			}
		}
	}
	&-content {
		padding-top: 8px;
		height: calc(100vh - 130px);
		.status-btn {
			cursor: default;
			color: #ca7cfc;
			border: 1px solid #ca7cfc;
			&.success {
				color: #19be6b;
				border: 1px solid #19be6b;
			}
		}
	}
	&-table {
		width: 100%;
	}
	&-form-item {
		width: fit-content;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-right: 16px;
		.title {
			margin-right: 4px;
		}
		.select {
			width: 140px;
		}
	}
	.data-info {
		display: flex;
		align-items: center;
		margin-right: 20px;
		.group {
			display: flex;
			align-items: center;
			color: #535567;
			font-size: 14px;
			margin-left: 10px;
			.value {
				&.active {
					color: #ca7cfc;
				}
			}
		}
	}
	.void {
		border: 1px solid #3aa7d8;
		color: #3aa7d8;
		background-color: #fff;
	}
	.warn-list-content {
		display: flex;
		.water-tree-wrap {
			flex-shrink: 0;
			display: flex;
			flex-direction: column;
			margin-right: 10px;
			.tree-type-wrap {
				display: flex;
				margin-top: 8px;
				border: 1px solid #b4c8fd;
				padding-bottom: 2px;
				border-radius: 4px;
				width: 215px;

				.tab-item {
					width: 114px;
					cursor: pointer;
					text-align: center;
					// line-height: 36px;
					color: #5f627d;
					font-weight: 400;
					font-size: 14px;
					background: #fff;
					border: none;
				}
				.cur-tab {
					color: #282c42;
					font-weight: 700;
					font-size: 14px;
					background: #b4c8fd;
				}
			}
			.water-tree {
				flex: 1;
				width: 230px;
				padding-right: 16px;
			}
			.acheme-left-header {
				display: flex;
			}
		}
	}
}
</style>
