<template lang="pug">
.canvas-wrapper
	canvas(crossorigin='anonymous', :id='id', :width='width', :height='height')
</template>

<script type="text/ecmascript-6">
import Utils from './utils'
import { getVal } from '@/utils/function.js'
import { fabric } from 'fabric';
import {fabricGif} from './utils/fabricGif'

import initAligningGuidelines from './core/initAligningGuidelines';

const dotCircleImg = require('../../assets/images/dot-circle.png')
const rotateMdrImg = require('../../assets/images/rotate-mdr.png')
export default {
	name: 'fabric',
	props: {
		id: {
			type: String,
			required: false,
			default: 'fabricCanvas',
		},
		width: {
			type: Number,
			required: true,
		},
		height: {
			type: Number,
			required: true,
		},
	},
	data() {
		return {
			canvas: null,
			currentObj: null,
		}
	},
	created() {},
	mounted() {
		this.canvas = new fabric.Canvas(this.id, {
			preserveObjectStacking: true,
		})

		// var originalCenter = {
		// 	left: this.canvas.getWidth() / 2,
		// 	top: this.canvas.getHeight() / 2
		// };

		window.newCanvas = this.canvas

		initAligningGuidelines(this.canvas)

		// fabric.Canvas.prototype.customiseControls({
		// 	tl: {
		// 		action: 'scale',
		// 		// cursor: '../../assets/dot-circle.png',
		// 	},
		// 	tr: {
		// 		action: 'scale',
		// 	},
		// 	bl: {
		// 		action: 'scale',
		// 		cursor: 'pointer',
		// 	},
		// 	br: {
		// 		action: 'scale',
		// 		cursor: 'pointer',
		// 	},
		// 	mb: {
		// 		action: 'scale',
		// 		cursor: 'pointer',
		// 	},
		// 	// mr: {
		// 	//     // action: function(e, target) {
		// 	//     //     target.set({
		// 	//     //         left: 200,
		// 	//     //     });
		// 	//     //     canvas.renderAll();
		// 	//     // },
		// 	//     action: 'scale',
		// 	//     cursor: 'pointer',
		// 	// },
		// 	mt: {
		// 		// action: {
		// 		//   rotateByDegrees: 30
		// 		// },
		// 		action: 'scale',
		// 		cursor: 'pointer',
		// 	},
		// 	// only is hasRotatingPoint is not set to false
		// 	mtr: {
		// 		action: 'rotate',
		// 		// cursor: '../../assets/cow.png',
		// 	},
		// })

		// 设置图形角落和顶部的操作按钮
		// this.setCornerIcons({})
		// this.setRemoveIcon()
		this.setBackgroundColor('transparent')
		// canvas.renderAll();
		// this.canvas.push(canvas);
		let that = this
		this.canvas.controlsAboveOverlay = false
		this.canvas.skipOffscreen = true

		this.canvas.on('selection:created', function (options) {
			// console.log('selection:created');
			that.$emit('selection:created', options)
		})
		this.canvas.on('mouse:down', function (options) {
			// console.log('mouse:down');
			that.$emit('mouse:down', options)
		})
		this.canvas.on('mouse:up', function (options) {
			// console.log('mouse:up');
			that.$emit('mouse:up', options)
		})
		this.canvas.on('mouse:move', function (options) {
			// console.log('mouse:move');
			that.$emit('mouse:move', options)
		})
		this.canvas.on('mouse:dblclick', function (options) {
			// console.log('mouse:dblclick');
			that.$emit('mouse:dblclick', options)
		})
		this.canvas.on('mouse:over', function (options) {
			// console.log('mouse:over');
			that.$emit('mouse:over', options)
		})
		this.canvas.on('mouse:out', function (options) {
			// console.log('mouse:out');
			that.$emit('mouse:out', options)
		})
		this.canvas.on('mouse:wheel', function () {
			// console.log('mouse:wheel');
			// that.$emit('mouse:wheel', options)
		})
		this.canvas.on('object:added', function (options) {
			// console.log('mouse:added');
			that.$emit('object:added', options)
		})
		this.canvas.on('object:removed', function (options) {
			// console.log('mouse:removed');
			that.$emit('object:removed', options)
		})
		this.canvas.on('object:modified', function (options) {
			that.$emit('object:modified', options)
		})
		this.canvas.on('object:rotating', function (options) {
			that.$emit('object:rotating', options)
		})
		this.canvas.on('object:scaling', function (options) {
			that.$emit('object:scaling', options)
		})
		this.canvas.on('object:moving', function (options) {
			that.$emit('object:moving', options)
		})
		this.canvas.on('selection:updated', function (options) {
			// console.log('selection:updated');
			that.$emit('selection:updated', options)
		})
		this.canvas.on('selection:cleared', function (options) {
			// console.log('selection:cleared');
			that.$emit('selection:cleared', options)
		})
		this.canvas.on('before:selection:cleared', function (options) {
			// console.log('before:selection:cleared');
			that.$emit('before:selection:cleared', options)
		})
	},
	methods: {
		setCornerIcons({
			size = 12,
			borderColor = '#e4e4e4',
			cornerBackgroundColor = '#fff',
			cornerShape = 'rect',
			tl = dotCircleImg,
			mt = dotCircleImg,
			tr = dotCircleImg,
			bl = dotCircleImg,
			mb = dotCircleImg,
			br = dotCircleImg,
			ml = dotCircleImg,
			mr = dotCircleImg,
			mtr = rotateMdrImg,
		}) {
			// basic settings
			fabric.Object.prototype.customiseCornerIcons({
				settings: {
					borderColor: borderColor,
					cornerSize: size,
					cornerShape, // 'rect', 'circle'
					cornerBackgroundColor: cornerBackgroundColor,
				},
				tl: {
					icon: tl,
				},
				mt: {
					icon: mt,
				},
				tr: {
					icon: tr,
				},
				bl: {
					icon: bl,
				},
				mb: {
					icon: mb,
				},
				br: {
					icon: br,
				},
				ml: {
					icon: ml,
				},
				mr: {
					icon: mr,
				},
				// only is hasRotatingPoint is not set to false
				mtr: {
					icon: mtr,
				},
			})
		},
		setRemoveIcon(){
			const that = this
			var deleteIcon = "data:image/svg+xml,%3C%3Fxml version='1.0' encoding='utf-8'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg version='1.1' id='Ebene_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' width='595.275px' height='595.275px' viewBox='200 215 230 470' xml:space='preserve'%3E%3Ccircle style='fill:%23F44336;' cx='299.76' cy='439.067' r='218.516'/%3E%3Cg%3E%3Crect x='267.162' y='307.978' transform='matrix(0.7071 -0.7071 0.7071 0.7071 -222.6202 340.6915)' style='fill:white;' width='65.545' height='262.18'/%3E%3Crect x='266.988' y='308.153' transform='matrix(0.7071 0.7071 -0.7071 0.7071 398.3889 -83.3116)' style='fill:white;' width='65.544' height='262.179'/%3E%3C/g%3E%3C/svg%3E";
			var img = document.createElement('img');
			img.src = deleteIcon;
			fabric.Object.prototype.controls.deleteControl = new fabric.Control({
				x: 0.5,
				y: -1,
				offsetY: 16,
				cursorStyle: 'pointer',
				mouseUpHandler: deleteObject,
				render: renderIcon,
				cornerSize: 18
			});

			function deleteObject() {
				// var target = transform.target;
				// var canvas = target.canvas;
				// canvas.remove(target);
				// canvas.requestRenderAll();
				that.$emit('handleDelete')
			}

			function renderIcon(ctx, left, top, styleOverride, fabricObject) {
				var size = this.cornerSize;
				ctx.save();
				ctx.translate(left, top);
				ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
				ctx.drawImage(img, -size / 2, -size / 2, size, size);
				ctx.restore();
			}
		},


		// rotate (degrees) {
		// 	let canvasCenter = new fabric.Point(this.canvas.getWidth() / 2, this.canvas.getHeight() / 2) // center of canvas
		// 	let radians = fabric.util.degreesToRadians(degrees)

		// 	canvas.getObjects().forEach((obj) => {
		// 		let objectOrigin = new fabric.Point(obj.left, obj.top)
		// 		let new_loc = fabric.util.rotatePoint(objectOrigin, canvasCenter, radians)
		// 		obj.top = new_loc.y
		// 		obj.left = new_loc.x
		// 		obj.angle += degrees //rotate each object by the same angle
		// 		obj.setCoords()
		// 	});
		// 	this.canvas.renderAll()
		// },
		drawDottedline(options) {
			options = Object.assign(
				{
					x: 0,
					y: 0,
					x1: 10,
					y1: 10,
					color: '#B2B2B2',
					drawWidth: 2,
					offset: 6,
					empty: 3,
				},
				options,
			)
			let canvasObject = new fabric.Line(
				[options.x, options.y, options.x1, options.y1],
				{
					strokeDashArray: [options.offset, options.empty],
					stroke: options.color,
					strokeWidth: options.drawWidth,
					...options,
				},
			)
			this.canvas.add(canvasObject)
			this.canvas.renderAll()
		},

		rotate (degrees) {
			let canvasCenter = new fabric.Point(this.canvas.getWidth() / 2, this.canvas.getHeight() / 2) // center of this.canvas
			let radians = fabric.util.degreesToRadians(degrees)

			this.canvas.getObjects().forEach((obj) => {
				let objectOrigin = new fabric.Point(obj.left, obj.top)
				let new_loc = fabric.util.rotatePoint(objectOrigin, canvasCenter, radians)
				obj.top = new_loc.y
				obj.left = new_loc.x
				obj.angle += degrees //rotate each object by the same angle
				obj.setCoords()
			});

			// let backOrigin = new fabric.Point(0, 0)
			// let new_loc = fabric.util.rotatePoint(backOrigin, canvasCenter, radians)
			// this.canvas.backgroundImage.top = new_loc.y
			// this.canvas.backgroundImage.left = new_loc.x
			this.canvas.backgroundImage.angle += degrees

			this.canvas.renderAll()
		},

		drawArrowLine(options) {
			options = Object.assign(
				{
					x: 0,
					y: 0,
					x1: 0,
					y1: 0,
					color: '#B2B2B2',
					drawWidth: 2,
					fillColor: 'rgba(255,255,255,0)',
					theta: 35,
					headlen: 35,
				},
				options,
			)
			let canvasObject = new fabric.Path(
				this.drawArrowBase(
					options.x,
					options.y,
					options.x1,
					options.y1,
					options.theta,
					options.headlen,
				),
				{
					stroke: options.color,
					fill: options.fillColor,
					strokeWidth: options.drawWidth,
					...options,
				},
			)
			this.canvas.add(canvasObject)
			this.canvas.renderAll()
		},
		drawArrowBase(fromX, fromY, toX, toY, theta, headlen) {
			theta = typeof theta !== 'undefined' ? theta : 30
			headlen = typeof theta !== 'undefined' ? headlen : 10
			// 计算各角度和对应的P2,P3坐标
			var angle = (Math.atan2(fromY - toY, fromX - toX) * 180) / Math.PI,
				angle1 = ((angle + theta) * Math.PI) / 180,
				angle2 = ((angle - theta) * Math.PI) / 180,
				topX = headlen * Math.cos(angle1),
				topY = headlen * Math.sin(angle1),
				botX = headlen * Math.cos(angle2),
				botY = headlen * Math.sin(angle2)
			var arrowX = fromX - topX,
				arrowY = fromY - topY
			var path = ' M ' + fromX + ' ' + fromY
			path += ' L ' + toX + ' ' + toY
			arrowX = toX + topX
			arrowY = toY + topY
			path += ' M ' + arrowX + ' ' + arrowY
			path += ' L ' + toX + ' ' + toY
			arrowX = toX + botX
			arrowY = toY + botY
			path += ' L ' + arrowX + ' ' + arrowY
			return path
		},
		freeDrawConfig(options) {
			options = Object.assign({ color: '#b2b2b2', drawWidth: 2 }, options)

			this.canvas.isDrawingMode = options.isDrawingMode
			this.canvas.freeDrawingBrush.color = options.color // 设置自由绘颜色
			this.canvas.freeDrawingBrush.width = options.drawWidth
			this.canvas.renderAll()
		},
		eraseDrawConfig(options) {
			options = Object.assign({ color: 'white', drawWidth: 2 }, options)

			this.canvas.freeDrawingBrush.color = options.color // 设置自由绘颜色
			this.canvas.freeDrawingBrush.width = options.drawWidth
			this.canvas.renderAll()
		},
		removeCurrentObj() {
			let obj = this.canvas.getActiveObject()

			this.canvas.remove(obj)
			this.canvas.renderAll()
			return obj
		},
		getFilter(index) {
			var obj = this.canvas.getActiveObject()
			return obj.filters[index]
		},
		applyFilterValue(index, prop, value, obj) {
			if (!obj) {
				obj = this.canvas.getActiveObject()
			}
			if (obj.filters[index]) {
				obj.filters[index][prop] = value
				obj.applyFilters()
				this.canvas.renderAll()
			}
		},
		applyFilter(index, filter) {
			let obj = this.canvas.getActiveObject()
			obj.filters[index] = filter
			obj.applyFilters()
			this.canvas.renderAll()
		},
		setImageGamma(val, gamma, obj) {
			let filter = false
			if (val) {
				filter = new fabric.Image.filters.Gamma({
					gamma,
				})
			}

			this.applyFilter(17, filter, obj)
		},
		changeControl(attr, value, extraData) {
			if (attr === 'red' || attr === 'green' || attr === 'blue') {
				// debugger
				// let current = this.getFilter(17).gamma
				let current = value
				this.applyFilterValue(17, 'gamma', current)
			} else if (attr === 'bottomLayer') {
				this.toBottomLayer()
			} else if (attr === 'hideTitle') {
				// debugger
				// this.toBottomLayer()
				let obj = this.canvas.getActiveObject()

				let text = value ? `${getVal(obj.value)}` : `${obj.itemName}:${getVal(obj.value)}`
				text = extraData ? text : `${text}${obj.unit}`

				obj.set('hideTitle', value)
				obj.set('text', text).setCoords()
				this.canvas.requestRenderAll()
				return obj
			} else if (attr === 'hideUnit') {
				let obj = this.canvas.getActiveObject()

				let text = extraData ? `${getVal(obj.value)}` : `${obj.itemName}:${getVal(obj.value)}`
				text = value ? text : `${text}${obj.unit}`

				obj.set('hideUnit', value)
				obj.set('text', text).setCoords()
				this.canvas.requestRenderAll()
				return obj
			} else if (attr === 'flipX') {
				let obj = this.toggleMirror({ flip: 'X' })
				console.log(obj);
				return obj
			} else if (attr === 'flipY') {
				let obj = this.toggleMirror({ flip: 'Y' })
				return obj
			} else if (attr === 'animateColor') {
				let obj = this.canvas.getActiveObject()
				obj.animateColor = value.animateColor
			}
			// else if (attr === 'scaleY' || attr === 'scaleX') {
			// 	let obj = this.canvas.getActiveObject()
			// 	obj.scale(parseFloat(value)).setCoords();
			// 	this.canvas.requestRenderAll()
			// 	return obj
			// }
			 else {
				// debugger
				let obj = this.canvas.getActiveObject()
				obj.set(attr, value).setCoords()
				this.canvas.requestRenderAll()
				return obj
			}
		},
		changeObjControlAttr(obj, attr, value){
			if (!obj) {
				obj = this.canvas.getActiveObject()
			}
			obj.set(attr, value).setCoords()
			this.canvas.requestRenderAll()
			return obj
		},
		changeObjControl(obj, attr, value) {
			// 添加灰色滤镜
			if (value === 'Grayscale') {
				obj.filters[17] = new fabric.Image.filters.Grayscale()
				obj.applyFilters()
				this.canvas.renderAll()
			} else {
				if (obj.filters[17]) {
					obj.filters[17].gamma = value
					obj.applyFilters()
				} else {
					// 添加过滤器
					obj.filters[17] = new fabric.Image.filters.Gamma({
						gamma: value,
					})
					// 应用过滤器并重新渲染画布执行
					obj.applyFilters()
					this.canvas.renderAll()
				}
			}
		},
		removeObj(arr) {
			arr.forEach(element => {
				this.canvas.remove(element)
			})
			this.canvas.renderAll()
		},
		getEditObj() {
			let obj = this.canvas.getActiveObject()
			this.removeCurrentObj()
			return obj
		},
		setEditObj(obj) {
			this.canvas.add(obj)
			this.canvas.renderAll()
		},
		setRotate(deg = 36) {
			let obj = this.canvas.getActiveObject()
			let angle = obj.angle
			obj.rotate(angle + deg)
			this.canvas.renderAll()
		},
		discardActive() {
			this.canvas.discardActiveObject()
			// this.canvas.discardActiveGroup();
			this.canvas.renderAll()
		},
		deactivateAll() {
			// this.canvas.deactivateAll().renderAll();
		},
		deactivateOne(obj) {
			var activeGroup = this.canvas.getActiveGroup()
			activeGroup.removeWithUpdate(obj)
			this.canvas.renderAll()
		},
		setSelection(flag) {
			this.canvas.selection = flag
			this.canvas.renderAll()
		},
		moveTo() {
			let obj = this.canvas.getActiveObject()
			console.log(this.canvas.sendBackwards)
			this.canvas.sendBackwards(obj, true)
			this.canvas.discardActiveObject()
			// this.canvas.discardActiveGroup();
		},
		createRect(options) {
			options = Object.assign(
				{
					width: 100,
					height: 100,
					top: 100,
					left: 100,
					fill: '#0E0E0E',
					stroke: '#EEE7E7',
				},
				options,
			)
			let rect = new fabric.Rect({
				...options,
			})
			this.canvas.add(rect)
			this.canvas.renderAll()
			return rect
		},
		createCircle(options) {
			options = Object.assign(
				{
					left: 100,
					top: 100,
					radius: 30,
					fillColor: 'rgba(255, 255, 255, 0)',
					color: '#B2B2B2',
					drawWidth: 2,
				},
				options,
			)
			let defaultOption = {
				fill: options.fillColor,
				strokeWidth: options.drawWidth,
				stroke: options.color,
				...options,
			}
			let Circle = new fabric.Circle(defaultOption)
			this.canvas.add(Circle)
			this.canvas.renderAll()
			return Circle
		},
		createTriangle(options) {
			options = Object.assign(
				{
					x: 0,
					y: 0,
					x1: 0,
					y1: 0,
					x2: 0,
					y2: 0,
					left: 100,
					top: 100,
					color: '#B2B2B2',
					drawWidth: 2,
					fillColor: 'rgba(255, 255, 255, 0)',
					id: 'triangle',
				},
				options,
			)
			var path =
				'M ' +
				options.x +
				' ' +
				options.y +
				' L ' +
				options.x1 +
				' ' +
				options.y1 +
				' L ' +
				options.x2 +
				' ' +
				options.y2 +
				' z'
			let canvasObject = new fabric.Path(path, {
				stroke: options.color,
				strokeWidth: options.drawWidth,
				fill: options.fillColor,
				...options,
			})
			this.canvas.add(canvasObject)
			this.canvas.renderAll()
		},
		createEqualTriangle(options) {
			options = Object.assign(
				{
					left: 100,
					top: 100,
					width: 50,
					height: 80,
					fillColor: 'rgba(255, 255, 255, 0)',
					color: '#B2B2B2',
					drawWidth: 2,
				},
				options,
			)
			// console.log(defaultOption);
			let triangle = new fabric.Triangle({
				fill: options.fillColor,
				strokeWidth: options.drawWidth,
				stroke: options.color,
				...options,
			})
			this.setContronVisibility(triangle)
			this.canvas.add(triangle)
			this.canvas.renderAll()
		},
		createLine(options) {
			options = Object.assign(
				{
					// x: 100,
					// y: 0,
					// x1: 100,
					// y1: 100,
					fillColor: 'rgba(255, 255, 255, 0)',
					strokeColor: '#B0B0B0',
					strokeWidth: 5,
				},
				options,
			)
			let line = new fabric.Line(
				[options.x, options.y, options.x1, options.y1],
				{
					fill: options.fillColor,
					stroke: options.strokeColor,
					...options,
				},
			)
			this.canvas.add(line)
			this.canvas.renderAll()
			return line
		},
		createGradient (linePath){
			let gradient = new fabric.Gradient({
				type: 'linear',
				gradientUnits: 'pixels', // or 'percentage'
				coords: { x1: linePath[0].x, y1: linePath[0].y, x2: linePath[1].x, y2: linePath[1].x },
				colorStops:[
					{ offset: 0, color: '#000' },
					{ offset: 1, color: '#fff'}
				]
			})
			return gradient
		},
		createPolyline(options) {
			const option = Object.assign(
				{
					fill: 'transparent',
					stroke: '#B0B0B0',
					strokeWidth: 5,
					hasControls: false,
					lockMovementX: false,
					lockMovementY: false,
				},
				options.opt,
			)
			let polyline = new fabric.Polyline(options.xyData, option)
			this.canvas.add(polyline)
			this.canvas.renderAll()
			return polyline
		},
		createEllipse(options) {
			options = Object.assign(
				{
					rx: 100,
					ry: 200,
					fillColor: 'rgba(255, 255, 255, 0)',
					angle: 90,
					strokeColor: '#B0B0B0',
					strokeWidth: 3,
					left: 50,
					top: 50,
				},
				options,
			)
			var ellipse = new fabric.Ellipse({
				fill: options.fillColor,
				stroke: options.strokeColor,
				...options,
			})
			this.canvas.add(ellipse)
			this.canvas.renderAll()
		},
		createText(text, options) {
			options = Object.assign({ left: 100, top: 100, width: 200, }, options)
			var canvasObj = new fabric.Text(text, options)
			this.canvas.add(canvasObj)
			this.canvas.requestRenderAll()
			return canvasObj
		},
		createItext(text, options) {
			options = Object.assign(
				{ left: 100, top: 100 },
				options,
			)
			let IText = new fabric.IText(text, options)
			// if (options.width) {
			// 	// debugger
			// IText.set('width', 1000).setCoords()
			// }
			this.canvas.add(IText)
			return IText
		},
		createTextbox(text, options) {
			// _fontSizeMult: 5,
			options.fillColor = options.fillColor
				? options.fillColor
				: options.fill
			options = Object.assign(
				{
					fontSize: 14,
					fill: '#000',
					// registeObjectEvent: false,
					splitByGrapheme: true,
					width: 200,
					left: 100,
					top: 100,
				},
				options,
			)
			var canvasObj = new fabric.Textbox(text, {
				fill: options.fillColor,
				...options,
			})
			// let arr = canvasObj._splitTextIntoLines(text);
			// console.log(arr);
			this.canvas.add(canvasObj)
			// if (options.registeObjectEvent) {
			// 	Utils.registeObjectEvent(this, canvasObj)
			// }
			this.canvas.renderAll()
			return canvasObj
		},
		createImageByImg(img, options) {
			options = options || {}
			let canvas = this.canvas
			let that = this
			// let maxWidth = that.width;
			let width = 0
			let height = 0
			width = img.width
			height = img.height
			// if (img.width > img.height) {
			//   if (img.width > maxWidth) {
			//     width = maxWidth;
			//     height = (img.height / img.width) * width;
			//   } else {
			//     width = img.width;
			//     height = img.height;
			//   }
			// } else {
			//   if (img.height > maxWidth) {
			//     height = maxWidth;
			//     width = (img.width / img.height) * height;
			//   } else {
			//     width = img.width;
			//     height = img.height;
			//   }
			// }
			if (options && options.width) {
				width = options.width
			}
			if (options && options.height) {
				height = options.height
			}
			let leftP = that.width / 2
			let topP = that.height / 2
			if ((options && options.left) || (options && options.left == 0)) {
				leftP = options.left + width / 2
			}
			if ((options && options.top) || (options && options.top == 0)) {
				topP = options.top + height / 2
			}
			let imgOptions = Object.assign(options, {
				id: options && options.id ? options.id : 'image',
				left: leftP,
				top: topP,
				scaleX: width / img.width,
				scaleY: height / img.height,
				originX: 'center',
				originY: 'center',
				cornerStrokeColor: 'blue',
				crossOrigin: 'anonymous'
			})
			delete imgOptions.width
			delete imgOptions.height
			var canvasImage = new fabric.Image(img, imgOptions)

			canvasImage.hasControls = true
			canvasImage.hasBorders = true

			canvas.add(canvasImage) // 把图片添加到画布上
			if (options && options.registeObjectEvent) {
				Utils.registeObjectEvent(that, canvasImage)
			}
			canvas.renderAll.bind(canvas)
		},
		createImage(url, options) {
			const suffix = url.substring(url.lastIndexOf("."));
			return new Promise((resolve) => {
				options = options || {}
				let canvas = this.canvas
				let that = this
				fabric.Image.fromURL(url, async function(img) {
					if (options.color) {
						const { red, green, blue } = options
						// 添加过滤器
						img.filters[17] = new fabric.Image.filters.Gamma({
							gamma: [red || 0, green || 0, blue || 0],
						})
						// 应用过滤器并重新渲染画布执行
						img.applyFilters()
					}
					let width = 0
					let height = 0
					width = img.width
					height = img.height
					if (options && options.width) {
						width = options.width
					}
					if (options && options.height) {
						height = options.height
					}
					let imgOptions = Object.assign(options, {
						id: options && options.id ? options.id : 'image',
						left: options.left || 0,
						top: options.top || 0,
						scaleX: options.scaleX || width / img.width,
						scaleY: options.scaleY || height / img.height,
						originX: 'center',
						originY: 'center',
						cornerStrokeColor: 'blue',
						stroke: options.stroke || 'red',
						flipX: options.flipX || false,
						flipY: options.flipY || false,
						color: 'red',
					})
					delete imgOptions.width
					delete imgOptions.height

						// 支持gif图加载
					if (suffix.indexOf('gif') !== -1) {
						try {
							const gif = await fabricGif(url);
							gif.set(imgOptions);
							canvas.add(gif);
							resolve(gif)
							fabric.util.requestAnimFrame(function render() {
								canvas.renderAll();
								fabric.util.requestAnimFrame(render);
							});
						} catch (error) {
							// gif内部操作失败（比如本身不是gif，却是.gif格式 无法拿到分解的动画导致报错）
							// 直接按正常的图片直接渲染
							img.set(imgOptions)
							img.hasControls = true
							img.hasBorders = true
							canvas.add(img)
							if (options && options.registeObjectEvent) {
								Utils.registeObjectEvent(that, img)
							}
							canvas.renderAll.bind(canvas)
							resolve(img)
						}
					} else {
						img.set(imgOptions)

						img.hasControls = true
						img.hasBorders = true
						canvas.add(img)
						if (options && options.registeObjectEvent) {
							Utils.registeObjectEvent(that, img)
						}
						canvas.renderAll.bind(canvas)
						resolve(img)
					}
				}, {
					crossOrigin: 'anonymous',
				})
			})
		},
		test(imgEl, options) {
			return new Promise(resolve => {
				const imgInstance = new fabric.Image(imgEl, {
					crossOrigin: 'anonymous',
				})
				imgInstance.filters.push(new fabric.Image.filters.Contrast());
				// 渲染背景
				this.canvas.setBackgroundImage(
					imgInstance,
					this.canvas.renderAll.bind(
						this.canvas,
					),
					{ ...options },
				)
				this.canvas.renderAll()
				this.canvas.requestRenderAll()
				resolve()
			})
		},

		toJson() {
			let json = this.canvas.toJSON()
			return json
		},
		toDataUrl() {
			let canvas = this.canvas
			let dataURL = canvas.toDataURL()
			return dataURL
		},
		loadFromJSON(json, cb) {
			let canvas = this.canvas
			canvas.loadFromJSON(
				json,
				canvas.renderAll.bind(canvas),
				function (o, object) {
					// `o` = json object
					// `object` = fabric.Object instance
					// ... do some stuff ...
					cb(o)
					object.setControlsVisibility({
						bl: true,
						br: true,
						mb: false,
						ml: true,
						mr: true,
						mt: false,
						mtr: true,
						tl: true,
						tr: true,
					})
				},
			)
		},
		clear() {
			this.canvas.clear()
		},
		getObjects() {
			return this.canvas.getObjects()
		},
		renderAll() {
			this.canvas.renderAll(this.canvas)
		},
		renderTop() {
			this.canvas.renderTop()
		},
		setBackgroundColor(color) {
			let canvas = this.canvas
			this.canvas.setBackgroundColor(color, canvas.renderAll.bind(canvas))
		},
		setBackgroundImage(options) {
			let canvas = this.canvas
			let opt = {
				opacity: 1,
				left: 0,
				top: 0,
				angle: 0,
				crossOrigin: null,
				originX: 'left',
				originY: 'top',
				scaleX: options.scale,
				scaleY: options.scale,
			}
			// console.log(options);
			if (Object.prototype.toString.call(options) == '[object String]') {
				console.log('字符串兼容')
				opt.imgUrl = options
			} else {
				opt = Object.assign(opt, options)
			}

			// canvas.setBackgroundImage(opt.imgUrl, canvas.renderAll.bind(canvas), {
			//   opacity: opt.opacity,
			//   angle: opt.angle,
			//   left: opt.left,
			//   top: opt.top,
			//   originX: 'left',
			//   originY: 'top',
			//   crossOrigin: opt.crossOrigin,
			//   ...opt
			// });

			fabric.Image.fromURL(opt.imgUrl, function (img) {
				img.set({
					width: opt.width ? opt.width : canvas.width,
					height: opt.height ? opt.height : canvas.height,
					originX: 'left',
					originY: 'top',
					scaleX: opt.scaleX,
					scaleY: opt.scaleY,
				})
				canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
					...opt,
					crossOrigin: 'anonymous',
				})
			}, {
				crossOrigin: 'anonymous',
			})
		},
		setCanvas(width, height) {
			this.canvas.setWidth(width);
			this.canvas.setHeight(height);
			this.canvas.renderAll()
		},
		getCanvasSize() {
			const {width, height} = this.canvas
			return {width, height}
		},
		toSvg() {
			return this.canvas.toSVG({
				width: '150px',
				height: '100px',
			})
		},
		drawControls() {
			let canvas = document.createElement('canvas')
			var ctx = canvas.getContext('2d')
			ctx.setLineDash([])
			ctx.beginPath()
			ctx.ellipse(100, 100, 50, 75, (45 * Math.PI) / 180, 0, 2 * Math.PI) // 倾斜45°角
			ctx.stroke()
			ctx.setLineDash([5])
			ctx.moveTo(0, 200)
			ctx.lineTo(200, 0)
			ctx.stroke()
			this.canvas.drawControls(ctx)
			// this.canvas.controlsAboveOverlay=true;
		},
		setContronVisibility(obj) {
			obj.setControlsVisibility({
				bl: true,
				br: true,
				mb: false,
				ml: true,
				mr: true,
				mt: false,
				mtr: true,
				tl: true,
				tr: true,
			})
		},
		// 设置mirror
		toggleMirror(options) {
			options = options || {}
			options = Object.assign({ flip: 'X' }, options)
			let img = this.canvas.getActiveObject()
			// if (img && img.type == 'image') {
			if (options.flip === 'X') {
				img.toggle('flipX')
			} else {
				img.toggle('flipY')
			}
			this.renderAll()
			return img
			// }
		},
		// 设置层级
		toNextLayer() {
			let obj = this.canvas.getActiveObject()
			if (!obj) {
				return
			}
			obj.sendBackwards(true)
			this.renderTop()
			// this.canvas.setActiveObject(obj);
		},
		toBottomLayer(obj) {
			if (!obj) {
				obj = this.canvas.getActiveObject()
			}
			obj.sendToBack()
			this.renderTop()
			// this.canvas.setActiveObject(obj);
		},
		toLastLayer() {
			let obj = this.canvas.getActiveObject()
			if (!obj) {
				return
			}
			obj.bringForward(true)
			this.renderTop()
		},
		toTopLayer(obj) {
			// let obj = this.canvas.getActiveObject()
			if (!obj) {
				return
			}
			obj.bringToFront()
			this.renderTop()
		},
		drawByPath(pathArray, options) {
			options = Object.assign(
				{
					fillColor: 'rgba(255, 255, 255, 0)',
					left: 150,
					top: 150,
					strokeColor: '#B0B0B0',
					strokeWidth: 3,
				},
				options,
			)
			let pathStr = 'M '
			for (let item of pathArray) {
				pathStr = pathStr + item[0] + ' ' + item[1] + ' '
			}
			pathStr = pathStr + 'z'
			console.log(pathStr)
			var path = new fabric.Path(pathStr)
			path.set({
				stroke: options.strokeColor,
				fill: options.fillColor,
				...options,
			})
			this.canvas.add(path)
		},
	},
}
</script>

<style lang="less" scoped>
.canvas-wrapper {
	display: flex;
	justify-content: center;
	width: 100%;
	flex: 1;
}
</style>
