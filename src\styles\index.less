// @import '../assets/icon-font/iconfont.css';
@import 'cover.less';
.water-padding-top-4 {
	padding-top: 4px;
}
.water-padding-top-8 {
	padding-top: 8px;
}
.water-padding-top-16 {
	padding-top: 16px;
}
.water-padding-bottom-4 {
	padding-bottom: 4px;
}
.water-padding-bottom-8 {
	padding-bottom: 8px;
}
.water-padding-bottom-16 {
	padding-bottom: 16px;
}
.water-padding-left-8 {
	padding-left: 8px;
}
.water-padding-left-16 {
	padding-left: 16px;
}
.water-padding-right-8 {
	padding-right: 8px;
}
.water-padding-right-16 {
	padding-right: 16px;
}
.water-margin-left-4 {
	margin-left: 4px;
}
.water-margin-left-8 {
	margin-left: 8px;
}
.water-margin-left-16 {
	margin-left: 16px;
}
.water-margin-left-24 {
	margin-left: 24px;
}
.water-margin-left-32 {
	margin-left: 32px;
}
.water-margin-right-4 {
	margin-right: 4px;
}
.water-margin-right-8 {
	margin-right: 8px;
}
.water-margin-right-10 {
	margin-right: 10px;
}
.water-margin-right-16 {
	margin-right: 16px;
}
.water-margin-right-12 {
	margin-right: 12px;
}
.water-margin-right-24 {
	margin-right: 24px;
}
.water-margin-right-32 {
	margin-right: 32px;
}
.water-margin-top-4 {
	margin-top: 4px;
}
.water-margin-top-8 {
	margin-top: 8px;
}
.water-margin-top-12 {
	margin-top: 12px;
}
.water-margin-top-16 {
	margin-top: 16px;
}
.water-margin-top-24 {
	margin-top: 24px;
}
.water-margin-top-32 {
	margin-top: 32px;
}
.water-fixed-wrapper {
	height: 100%;
	width: 100%;
	z-index: 1;
	background-color: #00101f;
}
/**单行省略号 */
.water-ellipsis1 {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

@media screen and (min-width: 7680px) {
	body > .ivu-date-picker-transfer {
		transform: translateX(100px) scale(2);
		transition: none;
	}
	body > .ivu-tooltip-popper {
		transform: translateY(-30px) scale(2);
		width: auto;
		text-align: center;
		word-break: break-all;
	}
	@keyframes ivuTransitionDropIn {
		0% {
			opacity: 0;
		}
		100% {
			opacity: 1;
		}
	}

	@keyframes ivuTransitionDropOut {
		0% {
			opacity: 1;
		}
		100% {
			opacity: 0;
		}
	}
}
@media screen and (min-width: 3840px) {
	.ivu-message {
		transform: scale(2);
	}
}
@media screen and (min-width: 5760px) {
	.ivu-message {
		transform: scale(3.5);
	}
}

@keyframes breathe {
	0% {
		opacity: 0.5;
		box-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
	}
	100% {
		opacity: 1;
		border: 1px solid rgba(59, 235, 235, 1);
		box-shadow: 0 1px 18px red;
	}
}
@keyframes run {
	from {
		stroke-dasharray: 20, 5;
	}
	to {
		stroke-dasharray: 25, 5;
	}
}
.map-label {
	width: 224px;
	box-sizing: border-box;
	border-radius: 4px 4px 0 0;
	overflow: hidden;
	&-container {
		position: relative;
		&.danger {
			.map-label-name {
				background-color: #ec5151;
			}
			.map-label-content {
				border-color: #ec5151;
			}
			.under {
				color: #ec5151;
			}
		}
		&.primary {
			.map-label-name {
				background-color: #3aa7d8;
			}
			.map-label-content {
				border-color: #3aa7d8;
			}
			.under {
				color: #3aa7d8;
			}
		}
	}
	.triangle-down {
		position: relative;
		z-index: 3;
		text-align: center;
		.under {
			font-size: 42px;
			display: inline-block;
		}
		.inner {
			position: absolute;
			display: inline-block;
			color: #ffffff;
			font-size: 14px;
			top: 56%;
			left: 0;
			right: 0;
			margin: 0 auto;
			transform: translateY(-50%);
		}
	}
	&-name {
		font-weight: 600;
		font-size: 14px;
		line-height: 24px;
		color: #ffffff;
		padding: 4px 8px;
	}
	&-content {
		padding: 8px;
		width: 100%;
		background-color: #ffffff;
		border: 1px solid;
	}
	&-value {
		color: #535567;
		font-size: 12px;
		line-height: 16px;
	}
}

html {
	/* 滚动条样式 */
	::-webkit-scrollbar {
		width: 6px;
		height: 6px;
	}
	::-webkit-scrollbar-track-piece {
		background-color: #eee;
	}
	::-webkit-scrollbar-thumb {
		background: #bbb;
		min-width: 150px;
		border-radius: 10px;
	}
	::-webkit-scrollbar-thumb:vertical:hover {
		background: #999;
	}
	::-webkit-scrollbar-thumb:horizontal:hover {
		background: #999;
	}
}
.amap-sug-result {
	z-index: 999999 !important;
	border: none;
}
