<template lang="pug">
// 数据绑定
Modal(:value='show', :mask-closable='false', width='1150', @on-cancel='handleCancel', title='设置多个点视频')
	Form(ref='formDynamic', :model='formDynamic', :label-width='80', height='500')
		.formItem(v-for='(item, index) in formDynamic.items', :key='index')
			Form-item(
				:label='"名称"',
				:prop='"items." + index + ".name"',
				:rules=`{
						required: true,
						message: '名称'  + '不能为空',
						trigger: 'blur',
					}`
			)
				Input(type='text', v-model='item.name', placeholder='请输入...')
			Form-item(
				:label='"位置点"',
				:prop='"items." + index + ".position"',
				:rules=`{
						required: true,
						message: '位置点'  + '不能为空',
						trigger: 'blur',
					}`
			)
				Input(type='text', v-model='item.position', placeholder='请输入...')
			Form-item(
				:label-width='120',
				label='视频标识',
				:prop='"items." + index + ".property"',
				:rules=`{
						required: true,
						message: '视频标识'  + '不能为空',
						trigger: 'blur',
					}`
			)
				Input(type='text', v-model='item.property', placeholder='请输入...')
			Button(@click='handleRemove(index, item)')
				| 删除
		Form-item
			Button(style='width: 120px', type='dashed', long, @click='handleAdd', icon='plus-round') 新增
	div(slot='footer')
		Button(type='primary', @click='handleSubmit')
			| 保存
</template>

<script>
import { savePositionVideo, getPositionVideo, deletPositionLabel } from '@/api/setting'

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '新增',
		},
	},
	computed: {
		stationListLength() {
			return this.pointList.length
		},
	},
	mounted() {},
	data() {
		return {
			formDynamic: {
				items: [
					{
						name: '',
						position: '',
						property: '',
						id: '',
						typeList: [],
					},
				],
			},
		}
	},
	methods: {
		async init(row) {
			const { stationId, modelId, applicationName } = row
			this.stationId = stationId
			this.modelId = modelId
			this.applicationName = applicationName
			// 详情
			this.formDynamic.items = []
			const res = await this.getPositionVideo(modelId, stationId)
			let obj = {}
			for (let index = 0; index < res.result.length; index++) {
				const item = res.result[index]
				obj[item.position] = {
					...item,
					id: item.id,
				}
			}
			for (const key in obj) {
				if (Object.hasOwnProperty.call(obj, key)) {
					const element = obj[key]
					this.formDynamic.items.push(element)
				}
			}
		},
		getPositionVideo(modelId, stationId) {
			return getPositionVideo({ modelId, stationId }).then(res => {
				return res
			})
		},
		handleSubmit() {
			this.$refs.formDynamic.validate(valid => {
				if (valid) {
					let params = []
					this.formDynamic.items.forEach(item => {
						let param = {
							name: item.name,
							position: item.position,
							property: item.property,
							stationCode: this.stationId,
							stationId: this.stationId,
							modelId: this.modelId,
						}
						item.id && (param.id = item.id)
						params.push(param)
					})
					savePositionVideo(params).then(() => {
						this.$emit('update:show', false)
						this.$Message.success('保存成功！')
					})
				} else {
					this.$Message.error('表单验证失败!')
				}
			})
		},
		handleReset() {
			this.$refs.formDynamic.resetFields()
		},
		handleAdd() {
			this.formDynamic.items.push({
				name: '',
				position: '',
				deviceId: '',
				contentList: [{ dataId: '' }],
				id: '',
				typeList: [],
			})
		},
		addSmallItem(index) {
			this.formDynamic.items[index].contentList.push({
				dataId: '',
			})
		},
		handleRemove(index, item) {
			const { id } = item
			if (id) {
				this.$Modal.confirm({
					title: '提示',
					content: '确定要删除这条数据?',
					loading: true,
					onOk: () => {
						this.$Modal.remove()
						deletPositionLabel({ id }).then(() => {
							this.formDynamic.items.splice(index, 1)
							this.$Message.info('删除成功')
						})
					},
				})
			} else {
				this.formDynamic.items.splice(index, 1)
			}
		},
		// 弹窗取消事件
		handleCancel() {
			this.handleReset()
			this.$emit('update:show', false)
		},
	},
}
</script>
<style lang="less" scoped>
.flex {
	display: flex;
	justify-content: space-around;
}
.formItem {
	display: flex;
}
::v-deep {
	.ivu-modal-body {
		height: 500px;
		overflow: auto;
	}
	.ivu-form-item {
		min-width: 240px;
	}
}
</style>
