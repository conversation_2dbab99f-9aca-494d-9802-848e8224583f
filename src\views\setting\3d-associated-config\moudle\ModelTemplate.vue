<!--
 * @Description: 工艺流程模版管理
 * @Author: shenxh
 * @Date: 2022-03-18 11:09:54
 * @LastEditors: shenxh
 * @LastEditTime: 2022-03-21 11:11:01
-->

<template lang="pug">
.model-template
	es-search.es-search(col='4', :modules='moduleList', @on-search='handleSubForm')

	es-table.water-table(
		:columns='columns',
		:data='tableData',
		:loading='loading',
		border,
		showPage,
		:pageData='pageData',
		@on-page-num-change='handlePageNum',
		@on-page-size-change='handlePageSize'
	)
		template(slot-scope='{ row }', slot='action')
			Button(v-if='row.processType === 1', type='primary', @click='handleClick("tagConfig", row)', size='small') 标签配置
			Button(v-if='row.processType === 2', type='primary', @click='handleClick("associatedSite", row)', size='small') 关联站点
			Button(type='primary', @click='handleClick("templatePreview", row)', size='small') 工艺流程模板预览
</template>

<script>
import { getTemplateList } from '@/api/setting'

export default {
	name: 'model-template',
	components: {},
	props: {},
	data() {
		return {
			loading: false,
			tableData: [],
			moduleList: [
				{
					label: '',
					value: '1',
					isDefault: true,
					keys: ['processName', 'processType', '', ''],
					model: {
						processName: '',
						processType: '',
					},
					data: [
						{
							type: 1,
							key: 'processName',
							formItemProps: {
								label: '工艺流程名称',
								prop: 'processName',
								labelWidth: 110,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
						{
							type: 2,
							key: 'processType',
							formItemProps: {
								label: '工艺流程类型',
								prop: 'processType',
								labelWidth: 110,
							},
							widgetProps: {
								clearable: true,
								disabled: false,
							},
							dataSourceList: [],
							events: {
								onChange: () => {
									// console.log(this.formProps.model);
								},
							},
						},
						// 解决单个输入框按回车键自动提交表单bug
						// {
						// 	type: 1,
						// 	key: 'test',
						// 	formItemProps: {
						// 		label: '',
						// 		prop: 'test',
						// 		labelWidth: '0',
						// 		width: '0',
						// 	},
						// },
					],
				},
			],
			columns: [
				{
					title: '工艺流程名称',
					key: 'processName',
					align: 'center',
				},
				{
					title: '工艺流程类型',
					key: 'processType',
					align: 'center',
					render: (h, params) => {
						return h('div', this.typeInit(params.row.processType))
					},
				},
				{
					title: '工艺流程模型数量',
					align: 'center',
					key: 'processNumber',
				},
				{
					title: '已关联站点数量',
					align: 'center',
					key: 'relationNumber',
				},
				{
					title: '操作',
					slot: 'action',
					align: 'center',
				},
			],
			pageData: {
				showTotal: true,
				showSizer: true,
				current: 1,
				pageSize: 20,
				pageSizeOpts: [10, 20, 30, 40, 50],
				total: 0,
				simple: false, // 是否简单模式
				pageDisabled: true, // 是否禁止输入
			},
		}
	},
	computed: {},
	watch: {},
	created() {
		this._getTableData()
	},
	mounted() {},
	beforeDestroy() {},
	methods: {
		// 查询
		handleSubForm() {
			this._getTableData()
		},

		// 改变页码
		handlePageNum(pageNum) {
			this.pageData.current = pageNum
			this._getTableData()
		},

		// 改变分页
		handlePageSize(pageSize) {
			this.pageData.pageSize = pageSize
			this._getTableData()
		},

		// 类型转换
		typeInit(t) {
			if (t === 1) return '全局'
			if (t === 2) return '站点'
		},

		// 点击事件
		handleClick(form, row) {
			this.$emit('tableClick', form, row)
		},

		_getTableData() {
			const {
				processName: processFlowDiagramName,
				processType: processFlowDiagramType,
			} = this.moduleList[0].model
			const { current: pageNum, pageSize } = this.pageData

			this._getTemplateList({
				processFlowDiagramName,
				processFlowDiagramType,
				pageNum,
				pageSize,
			})
		},

		_getTemplateList(params) {
			this.loading = true
			getTemplateList(params).then(res => {
				const data = res.result

				if (data) {
					this.loading = false
					this.tableData = data.list
					this.pageData.total = data.total
				}
			})
		},
	},
}
</script>

<style lang="less" scoped>
.model-template {
	.water-table {
		.ivu-btn {
			&:not(:last-child) {
				margin-right: 5px;
			}
		}
	}
}
</style>
