<!--
 * @Description: 创建系统弹窗
 * @Author: shenxh
 * @Date: 2022-04-01 15:54:51
 * @LastEditors: shenxh
 * @LastEditTime: 2024-05-27 09:01:38
-->
<template lang="pug">
.create-system-popup
	//- 弹窗
	es-modal.es-modal(
		:transfer='false',
		:is-direct-close-modal='false',
		width='400',
		v-model='showModal',
		title='快捷配置',
		@on-cancel='handleClose',
		@on-visible-change='changeModal'
	)
		template(slot='body')
			.popup-content
				Form.form(ref='form', :model='formData', :label-width='100', :rules='formRules')
					FormItem(label='变量类型', prop='stationCodeRef')
						Select(v-model='formData.stationCodeRef', filterable, transfer, clearable, style='width: 200px')
							Option(
								v-for='(item, index) in baseItemCodeList',
								:value='item.stationCode',
								:disabled='item.stationCode == stationCode',
								:key='index'
							) {{ item.stationName }}

					FormItem(label='数据格式', prop='refType')
						Checkbox-group(v-model='formData.refType')
							Checkbox(label='rule') 采集规则配置
							Checkbox(label='downControl') 下控配置

		template(slot='footer')
			Button(@click='handleClose') 取消
			Button(type='primary', @click='handleSubForm') 一键配置
</template>

<script>
import { queryStationInfo, transRuleRef } from '@/api/data-acquisition-config'
export default {
	name: 'quick-configuration-popup',
	model: {
		prop: 'showModal',
		event: 'set-value',
	},
	props: {
		showModal: Boolean,
		collectRoadData: Object,
		stationCode: String,
		tabCode: String,
	},
	data() {
		// const validateCode = (rule, value, callback) => {
		// 	if (!value || !value.trim()) {
		// 		callback(new Error('请输入'))
		// 	} else {
		// 		callback()
		// 	}
		// }

		return {
			baseItemCodeList: [],
			formData: { refType: ['rule', 'downControl'] },
			formRules: {
				stationCodeRef: [
					{
						required: true,
						message: '请选择',
						trigger: 'change',
					},
				],
				refType: [
					{
						required: true,
						type: 'array',
						min: 1,
						message: '至少选择一个',
						trigger: 'change',
					},
				],
			},
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {
		this.getCopyList()
	},
	beforeDestroy() {},
	methods: {
		// 获取设备数据
		getCopyList() {
			queryStationInfo({
				needPage: false,
				transChannelCode: this.collectRoadData.transChannelCode,
			}).then(res => {
				this.baseItemCodeList = res.result.list
			})
		},
		// 按钮-保存
		handleSubForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.$Modal.confirm({
						title: '提示',
						content:
							'将按照【被复制的设备名称】进行配置，请确认是否配置？注意：一键配置时，会删除当前设备的所有配置',
						onOk: () => {
							let type = this.formData.refType.length == 2 ? 'all' : this.formData.refType[0]
							let pam = {
								stationCode: this.stationCode,
								stationCodeRef: this.formData.stationCodeRef,
								refType: type,
								transChannelCode: this.tabCode,
							}
							transRuleRef(pam).then(() => {
								this.$Message.success('配置成功')
								this.handleClose()
							})
						},
					})
				}
			})
		},

		// 按钮-关闭
		handleClose() {
			this.$emit('set-value', false)
		},
		changeModal() {},
	},
}
</script>

<style lang="less" scoped>
.create-system-popup {
	.es-modal {
		.popup-content {
			width: 100%;
			.form {
				width: 100%;
				/deep/ .ivu-form-item {
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
